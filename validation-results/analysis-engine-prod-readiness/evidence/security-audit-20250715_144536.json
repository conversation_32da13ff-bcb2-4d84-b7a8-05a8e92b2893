{"database": {"advisory-count": 790, "last-commit": "c67f7726a9188b40c37534589293fec688892e42", "last-updated": "2025-07-14T22:21:09+02:00"}, "lockfile": {"dependency-count": 578}, "settings": {"target_arch": [], "target_os": [], "severity": null, "ignore": [], "informational_warnings": ["unmaintained", "unsound", "notice"]}, "vulnerabilities": {"found": false, "count": 0, "list": []}, "warnings": {"unmaintained": [{"kind": "unmaintained", "package": {"name": "term_size", "version": "0.3.2", "source": "registry+https://github.com/rust-lang/crates.io-index", "checksum": "1e4129646ca0ed8f45d09b929036bafad5377103edd06e50bf574b353d2b08d9", "dependencies": [{"name": "libc", "version": "0.2.174", "source": "registry+https://github.com/rust-lang/crates.io-index"}, {"name": "<PERSON>ap<PERSON>", "version": "0.3.9", "source": "registry+https://github.com/rust-lang/crates.io-index"}], "replace": null}, "advisory": {"id": "RUSTSEC-2020-0163", "package": "term_size", "title": "`term_size` is unmaintained; use `terminal_size` instead", "description": "The [`term_size`](https://crates.io/crates/term_size) crate is no longer maintained. Consider using\n[`terminal_size`](https://crates.io/crates/terminal_size) instead.", "date": "2020-11-03", "aliases": [], "related": [], "collection": "crates", "categories": [], "keywords": [], "cvss": null, "informational": "unmaintained", "references": [], "source": null, "url": "https://github.com/clap-rs/term_size-rs/pull/31", "withdrawn": null, "license": "CC0-1.0"}, "affected": null, "versions": {"patched": [], "unaffected": []}}]}}