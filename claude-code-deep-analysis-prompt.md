# SuperClaude Prompt: Comprehensive Project Deep Dive & State Analysis

## 1. Master Command (You have been already provided with this)

/analyze --persona-architect --all-mcp --ultrathink --plan --code --arch --security --performance @claude-code-deep-analysis-prompt.md

## 2. Primary Objective

Your mission is to conduct a comprehensive, multi-faceted, and deeply evidence-based analysis of the entire `episteme` project. You must deconstruct every component, from high-level architecture to individual configurations, to build a profound and holistic understanding of the project's:

1.  **Technical Architecture & Design Patterns**
2.  **Codebase Health & Quality**
3.  **Current Development State, Modality, and Active Priorities**
4.  **Complete Technology Stack & Dependencies**
5.  **Security Posture & Vulnerability Landscape**
6.  **Performance Characteristics & Potential Bottlenecks**
7.  **Testing Strategy & Quality Assurance Maturity**
8.  **Operational Readiness & DevOps Capabilities**

Adhere strictly to the SuperClaude framework, its core philosophy of "Code>docs | Simple→complex | Security→evidence→quality," and all associated best practices.

## 3. Systematic Analysis Methodology

Employ the `Sequential` MCP to conduct this analysis in a structured, interrogative-led process. Your thought process must be transparent, starting from a broad overview and progressively drilling down into specifics.

**Workflow:**
1.  **Initial Scoping (`/plan`):** Begin by using the file system structure to create a high-level map of the project. Identify key directories (`api/`, `services/`, `ml/`, `infrastructure/`, `PRPs/`, `docs/`) and formulate an initial hypothesis about the project's structure.
2.  **Cross-Referencing & Synthesis:** Do not analyze files in isolation. You must actively synthesize information across the project. For example:
    *   Connect dependencies in `package.json` to their usage in `services/` and `api/`.
    *   Use `Context7` to fetch documentation for all external libraries to verify their purpose and version compatibility.
    *   Correlate infrastructure definitions in `docker/` and `infrastructure/` with the services defined in `docker-compose.dev.yml`.
    *   Link active tasks in the `PRPs/active/` directory to the specific code they impact.
3.  **Evidence-Based Reporting:** Every conclusion must be backed by evidence. Use direct quotes, file paths, and measured data. Use the required language ("may," "could," "suggests") and avoid prohibited absolutes ("best," "always").

## 4. Core Analysis Dimensions (What to Investigate)

### A. Architecture & System Design (`--arch`)
*   **High-Level Topology:** Map the system architecture. Is it a monolith, microservices, serverless, or a hybrid? Visualize the data flow between the `api`, `services`, `ml` components, and any databases.
*   **Component Breakdown:** Detail the responsibility of each major service or module. What is the precise role of the `api/`, `services/`, and `contracts/` directories?
*   **Design Patterns:** Identify and document key architectural and software design patterns being used. Look for patterns in `PRPs/architecture-patterns.md` and compare them to the implementation.
*   **Data Modeling:** Analyze `contracts/` and any database schemas to understand the core data models.

### B. Development State & Project Modality
*   **This is a critical and primary focus.** Your goal is to determine "What is the project currently focused on?"
*   **Active Work:** Scrutinize the `PRPs/` directory, especially `PRPs/active/`, `PRPs/reviews/`, and `PRPs/feature-priority-list.md`. What features are in development, under review, or up next?
*   **Recent Changes:** Analyze Git history (if available) for recent commit patterns. What parts of the codebase are most active?
*   **Task Management:** Investigate the `tasks/` directory. How are work items broken down and tracked?

### C. Codebase Health & Quality (`--code`)
*   **Language & Standards:** Identify all programming languages and versions. Analyze `.editorconfig` and `.pre-commit-config.yaml` to understand coding standards.
*   **Code Complexity:** Identify areas with high cyclomatic complexity or potential code smells. Are there parts of the code that look like they need refactoring?
*   **Tech Debt:** Look for `TODO`, `FIXME`, or `HACK` comments. Are there any areas that are explicitly marked as technical debt?
*   **Dependency Health:** Analyze `package.json` and `package-lock.json`. Are dependencies up-to-date? Are there any deprecated packages?

### D. Security Posture (`--security`)
*   **Dependency Vulnerabilities:** Scan all dependencies for known CVEs.
*   **Secrets Management:** Search for hardcoded secrets, API keys, or credentials. Review `.env.example` and configuration files.
*   **Best Practices:** Assess adherence to security best practices (e.g., input validation, output encoding, secure headers). Review `SECURITY.md`.
*   **Dockerfile Security:** Analyze `docker/` files for security misconfigurations (e.g., running as root).

### E. Performance & Optimization (`--performance`)
*   **Potential Bottlenecks:** Hypothesize potential performance bottlenecks. Are there inefficient loops, heavy database queries, or synchronous operations that could be parallelized?
*   **Configuration:** Review configuration files for performance-related settings (e.g., caching, connection pooling).
*   **Build Process:** Analyze the `Makefile` and `package.json` scripts. Is the build process optimized?

### F. Testing & QA
*   **Strategy & Coverage:** Analyze the `tests/` directory. What is the testing strategy (unit, integration, e2e)? What frameworks are used? Hypothesize the test coverage.
*   **CI/CD Integration:** Examine `setup-cicd.sh` and any CI/CD configuration files. How are tests integrated into the development lifecycle?

## 5. Final Deliverable

Produce a single, comprehensive markdown report named `deep-analysis-report.md` and save it to the `.claudedocs/reports/` directory.

The report must contain:
1.  **Executive Summary:** A high-level overview of the project's health, maturity, and current development focus.
2.  **Table of Contents:** A navigable TOC for the report.
3.  **Detailed Sections:** A dedicated section for each of the "Core Analysis Dimensions" listed above, filled with your findings, evidence, and code snippets.
4.  **Actionable Recommendations:** A final section with prioritized, evidence-based recommendations for improvement across all dimensions.
