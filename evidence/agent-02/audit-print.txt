src/services/ai_test.rs:43:        println!("Detected {} AI patterns", patterns.len());
src/services/ai_test.rs:45:            println!("Pattern: {:?} - Confidence: {:.2}", pattern.pattern_type, pattern.confidence);
src/services/ai_test.rs:60:        println!("  Overall Score: {:.2}", assessment.overall_score);
src/services/ai_test.rs:61:        println!("  Maintainability: {:.2}", assessment.maintainability_score);
src/services/ai_test.rs:62:        println!("  Security: {:.2}", assessment.security_score);
src/services/ai_test.rs:63:        println!("  Performance: {:.2}", assessment.performance_score);
src/services/ai_test.rs:85:        println!("  Total Results: {}", results.total_results);
src/services/ai_test.rs:86:        println!("  Search Time: {}ms", results.search_time_ms);
src/services/ai_test.rs:121:        println!("  Overall Health Score: {:.2}", insights.summary.overall_health_score);
src/services/ai_test.rs:122:        println!("  Primary Language: {}", insights.summary.primary_language);
src/services/ai_test.rs:123:        println!("  Architecture Score: {:.2}", insights.architecture_analysis.architecture_score);
src/services/ai_test.rs:124:        println!("  Security Score: {:.2}", insights.security_assessment.security_score);
src/ai_services_validation.rs:43:        println!("   ✓ AI Pattern Detection: {}", feature_toggles.enable_ai_pattern_detection);
src/ai_services_validation.rs:44:        println!("   ✓ Code Quality Assessment: {}", feature_toggles.enable_code_quality_assessment);
src/ai_services_validation.rs:45:        println!("   ✓ Semantic Search: {}", feature_toggles.enable_semantic_search);
src/ai_services_validation.rs:46:        println!("   ✓ Repository Insights: {}", feature_toggles.enable_repository_insights);
src/parser/language_validation_test.rs:52:            Ok(_) => println!("✓ Successfully created parser pool for {}", lang),
src/parser/language_validation_test.rs:53:            Err(e) => println!("⚠ Failed to create parser pool for {}: {:?}", lang, e),
src/parser/language_validation_test.rs:60:    println!("Created {} parser pools out of {} languages", created_count, supported_languages.len());
src/parser/language_validation_test.rs:150:                println!("✓ {} parsing successful", lang);
src/parser/language_validation_test.rs:214:    println!("All {} language detection tests passed!", test_cases.len());
src/parser/language_validation_test.rs:251:    println!("Parser pools created for {} languages", pool_count);
src/bin/test_parsers.rs:14:            println!("Failed to create parser: {}", e);
src/bin/test_parsers.rs:148:            println!("✗ FAILED - couldn't write temp file: {}", e);
src/bin/test_parsers.rs:155:                    println!("✓ SUCCESS - parsed {} nodes", analysis.ast.children.len());
src/bin/test_parsers.rs:158:                    println!("✗ FAILED - detected as {} instead of {}", analysis.language, lang);
src/bin/test_parsers.rs:162:                println!("✗ FAILED - {}", e);
src/bin/test_parsers.rs:171:    println!("Tested {} parsers: {} succeeded, {} failed", 
src/parser/validation_demo.rs:13:            println!("❌ Failed to create parser: {}", e);
src/parser/validation_demo.rs:64:                    println!("✅ {} → {}", filename, detected_lang);
src/parser/validation_demo.rs:74:                println!("❌ {} → Error: {}", filename, e.message);
src/parser/validation_demo.rs:80:    println!("Total language detection tests: {}", test_files.len());
src/parser/validation_demo.rs:81:    println!("Successfully detected: {}", detected_count);
src/parser/validation_demo.rs:93:        println!("• {}", lang);
src/parser/validation_demo.rs:123:                println!("⚠️  {} parsing failed: {}", lang, e.message);
src/bin/test_md_parser.rs:27:    println!("Root node: {:?}", root.kind());
src/bin/test_md_parser.rs:28:    println!("Child count: {}", root.child_count());
src/bin/test_md_parser.rs:33:            println!("  Child {}: {:?}", i, child.kind());
src/bin/test_language_registry.rs:8:    println!("Total languages supported: {}", languages.len());
src/bin/test_language_registry.rs:9:    println!("Languages: {:?}\n", languages);
src/bin/test_language_registry.rs:18:                println!("✓ {} - loaded successfully", lang_name);
src/bin/test_language_registry.rs:22:                println!("✗ {} - FAILED to load", lang_name);
src/bin/test_language_registry.rs:29:    println!("Successful: {}/{}", success_count, languages.len());
src/bin/test_language_registry.rs:31:        println!("Failed: {:?}", failed_languages);
src/bin/test_language_registry.rs:40:            println!("{}: Available ✓", lang);
src/bin/test_language_registry.rs:42:            println!("{}: Not available ✗", lang);
src/bin/test_unsafe_bindings.rs:12:    println!("   Found {} supported languages", languages.len());
src/bin/test_unsafe_bindings.rs:13:    println!("   Sample languages: {:?}", &languages[..5.min(languages.len())]);
src/bin/test_unsafe_bindings.rs:30:            println!("     Language version: {}", lang.version());
src/bin/test_unsafe_bindings.rs:33:            eprintln!("   ✗ Failed to load Rust: {}", e);
src/bin/test_unsafe_bindings.rs:42:            println!("     Language version: {}", lang.version());
src/bin/test_unsafe_bindings.rs:45:            eprintln!("   ✗ Failed to load Python: {}", e);
src/bin/test_unsafe_bindings.rs:54:            println!("     Language version: {}", lang.version());
src/bin/test_unsafe_bindings.rs:57:            eprintln!("   ✗ Failed to load JavaScript: {}", e);
src/bin/test_unsafe_bindings.rs:70:            println!("   ✓ Correctly rejected unsupported language: {}", lang);
src/bin/test_unsafe_bindings.rs:73:            eprintln!("   ✗ Unexpected error type: {}", e);
src/bin/test_unsafe_bindings.rs:92:                eprintln!("\n   Failed to load {}: {}", lang_name, e);
src/bin/test_unsafe_bindings.rs:97:    println!("\n   Results: {} successful, {} failed out of {} total", 
src/bin/test_unsafe_bindings.rs:102:        println!("✅ All {} unsafe calls have been successfully centralized.", languages.len());
src/bin/test_tree_sitter_apis.rs:24:    println!("Testing tree-sitter-{}:", name);
src/bin/test_tree_sitter_apis.rs:28:            println!("  - Language name: {:?}", language);
src/bin/test_ai_services.rs:40:            eprintln!("✗ Failed to initialize AI services: {}", e);
src/bin/test_ai_services.rs:51:            println!("✗ AI Pattern Detection test failed: {}", e);
src/bin/test_ai_services.rs:64:            println!("✗ Code Quality Assessment test failed: {}", e);
src/bin/test_ai_services.rs:76:            println!("✗ Semantic Search test failed: {}", e);
src/bin/test_ai_services.rs:88:            println!("✗ Repository Insights test failed: {}", e);
src/bin/test_ai_services.rs:98:    println!("AI Pattern Detection: {}", if toggles.enable_ai_pattern_detection { "✓ Enabled" } else { "✗ Disabled" });
src/bin/test_ai_services.rs:99:    println!("Code Quality Assessment: {}", if toggles.enable_code_quality_assessment { "✓ Enabled" } else { "✗ Disabled" });
src/bin/test_ai_services.rs:100:    println!("Semantic Search: {}", if toggles.enable_semantic_search { "✓ Enabled" } else { "✗ Disabled" });
src/bin/test_ai_services.rs:101:    println!("Repository Insights: {}", if toggles.enable_repository_insights { "✓ Enabled" } else { "✗ Disabled" });
src/bin/test_ai_services.rs:102:    println!("Embeddings: {}", if toggles.enable_embeddings { "✓ Enabled" } else { "✗ Disabled" });
src/bin/test_ai_services.rs:103:    println!("Fallback Embeddings: {}", if toggles.use_fallback_embeddings { "✓ Enabled" } else { "✗ Disabled" });
src/bin/load_test.rs:97:            println!("{}", results.generate_report());
src/bin/load_test.rs:125:            println!("{}", serde_json::to_string_pretty(&json)?);
src/bin/load_test.rs:129:            println!("total_analyses,{}", results.total_analyses);
src/bin/load_test.rs:130:            println!("successful_analyses,{}", results.successful_analyses);
src/bin/load_test.rs:131:            println!("failed_analyses,{}", results.failed_analyses);
src/bin/load_test.rs:132:            println!("total_loc_processed,{}", results.total_loc_processed);
src/bin/load_test.rs:133:            println!("total_files_processed,{}", results.total_files_processed);
src/bin/load_test.rs:134:            println!("average_analysis_time_ms,{}", results.average_analysis_time.as_millis());
src/bin/load_test.rs:135:            println!("p95_analysis_time_ms,{}", results.p95_analysis_time.as_millis());
src/bin/load_test.rs:136:            println!("p99_analysis_time_ms,{}", results.p99_analysis_time.as_millis());
src/bin/load_test.rs:137:            println!("max_memory_usage_mb,{}", results.max_memory_usage / 1024 / 1024);
src/bin/test_language_api.rs:19:            println!("✓ {}: get_language(\"{}\") works", name, name);
src/bin/test_language_api.rs:20:            println!("  Language node count: {}", language.node_kind_count());
src/bin/test_language_api.rs:23:            println!("✗ {}: get_language(\"{}\") failed", name, name);
