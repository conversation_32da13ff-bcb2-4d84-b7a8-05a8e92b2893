diff --git a/services/analysis-engine/src/services/analyzer/file_processor.rs b/services/analysis-engine/src/services/analyzer/file_processor.rs
index 123abc..456def 100644
--- a/services/analysis-engine/src/services/analyzer/file_processor.rs
+++ b/services/analysis-engine/src/services/analyzer/file_processor.rs
@@ -145,18 +145,12 @@ impl FileProcessor {
                         let _ = progress_tx
                             .send(ProgressUpdate {
                                 analysis_id: analysis_id.clone(),
                                 progress,
-                                stage: format!(
-                                    "Parsed {}/{} files ({:.1}% success)",
-                                    completed, total_files, success_rate
-                                ),
-                                message: Some(format!(
-                                    "Concurrent processing: {} active",
-                                    max_concurrent_files
-                                )),
+                                stage: format!("Parsed {completed}/{total_files} files ({success_rate:.1}% success)"),
+                                message: Some(format!("Concurrent processing: {max_concurrent_files} active")),
                                 timestamp: Utc::now(),
                                 files_processed: Some(completed),
                                 total_files: Some(total_files),
                             })
                             .await;
                     }
 
@@ -175,7 +169,7 @@ impl FileProcessor {
                         tracing::error!("Task panicked during file parsing: {}", e);
                         all_results.push(Err(ParseError {
                             file_path: "unknown".to_string(),
                             error_type: ParseErrorType::Other,
-                            message: format!("Task panicked: {}", e),
+                            message: format!("Task panicked: {e}"),
                             position: None,
                         }));
                     }