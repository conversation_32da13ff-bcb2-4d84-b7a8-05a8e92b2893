src/services/analyzer/storage.rs:55:            .map_err(|e| anyhow!("Failed to get Spanner connection from pool: {:?}", e))?;
src/services/analyzer/storage.rs:145:                Err(anyhow!("Failed to store analysis: {}", e))
src/services/analyzer/storage.rs:160:                    .map_err(|e| anyhow!("Failed to get Spanner connection from pool: {:?}", e))?;
src/services/analyzer/storage.rs:177:                    .map_err(|e| anyhow!("Spanner deletion failed: {}", e))?;
src/services/analyzer/progress.rs:67:        .map_err(|e| anyhow::anyhow!("Failed to send progress: {}", e))
src/services/analyzer/mod.rs:83:                .map_err(|e| anyhow::anyhow!("Failed to create TreeSitter parser: {}", e))?,
src/services/analyzer/events.rs:168:                Err(e) => results.push(Err(anyhow::anyhow!("Task join error: {}", e))),
src/services/language_detector.rs:54:                return Err(anyhow::anyhow!("Unsupported language: {}", lang));
src/storage/redis_client.rs:33:                        Err(anyhow::anyhow!("Redis ping failed: {}", e))
src/storage/redis_client.rs:39:                Err(anyhow::anyhow!("Failed to connect to Redis: {}", e))
src/storage/spanner.rs:563:            .map_err(|e| anyhow::anyhow!("Failed to store analysis: {}", e))?;
src/storage/spanner.rs:575:                .map_err(|e| anyhow::anyhow!("Failed to convert row to AnalysisResult: {}", e))?;
src/storage/spanner.rs:635:                .map_err(|e| anyhow::anyhow!("Failed to convert row to AnalysisResult: {}", e))?;
src/storage/spanner.rs:739:            .map_err(|e| anyhow::anyhow!("Failed to store file analysis: {}", e))?;
src/storage/spanner.rs:770:            .map_err(|e| anyhow::anyhow!("Failed to store pattern details: {}", e))?;
src/storage/spanner.rs:864:            .map_err(|e| anyhow::anyhow!("Failed to get analysis_id: {}", e))?;
src/storage/spanner.rs:866:            .map_err(|e| anyhow::anyhow!("Failed to get repository_url: {}", e))?;
src/storage/spanner.rs:868:            .map_err(|e| anyhow::anyhow!("Failed to get branch: {}", e))?;
src/storage/spanner.rs:870:            .map_err(|e| anyhow::anyhow!("Failed to get status: {}", e))?;
src/storage/spanner.rs:880:            .map_err(|e| anyhow::anyhow!("Failed to get started_at: {}", e))?;
src/storage/spanner.rs:882:            .map_err(|e| anyhow::anyhow!("Failed to parse started_at: {}", e))?
src/storage/spanner.rs:886:            .map_err(|e| anyhow::anyhow!("Failed to get completed_at: {}", e))?
src/storage/spanner.rs:889:            .map_err(|e| anyhow::anyhow!("Failed to parse completed_at: {}", e))?;
src/storage/spanner.rs:892:            .map_err(|e| anyhow::anyhow!("Failed to get duration_seconds: {}", e))?;
src/storage/spanner.rs:896:            .map_err(|e| anyhow::anyhow!("Failed to get metrics: {}", e))?;
src/storage/spanner.rs:898:            .map_err(|e| anyhow::anyhow!("Failed to parse metrics JSON: {}", e))?);
src/storage/spanner.rs:901:            .map_err(|e| anyhow::anyhow!("Failed to get patterns: {}", e))?;
src/storage/spanner.rs:903:            .map_err(|e| anyhow::anyhow!("Failed to parse patterns JSON: {}", e))?;
src/storage/spanner.rs:906:            .map_err(|e| anyhow::anyhow!("Failed to get languages: {}", e))?;
src/storage/spanner.rs:908:            .map_err(|e| anyhow::anyhow!("Failed to parse languages JSON: {}", e))?;
src/storage/spanner.rs:911:            .map_err(|e| anyhow::anyhow!("Failed to get embeddings: {}", e))?;
src/storage/spanner.rs:913:            .map_err(|e| anyhow::anyhow!("Failed to parse embeddings JSON: {}", e))?);
src/storage/spanner.rs:916:            .map_err(|e| anyhow::anyhow!("Failed to get error_message: {}", e))?;
src/storage/spanner.rs:918:            .map_err(|e| anyhow::anyhow!("Failed to get user_id: {}", e))?;
src/storage/spanner.rs:920:            .map_err(|e| anyhow::anyhow!("Failed to get file_count: {}", e))?;
src/storage/spanner.rs:922:            .map_err(|e| anyhow::anyhow!("Failed to get success_rate: {}", e))?;
src/storage/storage.rs:155:                            Err(anyhow::anyhow!("Storage health check failed: {}", list_err))
src/storage/storage.rs:160:                    Err(anyhow::anyhow!("Storage health check failed: {}", e))
src/main.rs:39:                .map_err(|e| anyhow::anyhow!("Failed to create env filter: {}", e))?,
src/api/middleware/auth_layer.rs:864:            .map_err(|e| anyhow::anyhow!("Failed to get redis connection: {}", e))?;
src/api/middleware/auth_layer.rs:887:        .map_err(|e| anyhow::anyhow!("Time calculation error: {}", e))?
src/api/middleware/auth_layer.rs:899:            .map_err(|e| anyhow::anyhow!("Time calculation error: {}", e))?
src/api/middleware/auth_layer.rs:906:            .map_err(|e| anyhow::anyhow!("Time calculation error: {}", e))?
src/backpressure/mod.rs:276:            return Err(anyhow::anyhow!("Circuit breaker open for {}: {:?}", service, reason));
src/backpressure/mod.rs:287:                Err(anyhow::anyhow!("Operation failed for {}: {}", service, e))
src/backpressure/mod.rs:473:            return Err(anyhow::anyhow!("Memory pressure detected: {}MB > {}MB",
src/backpressure/mod.rs:478:            return Err(anyhow::anyhow!("CPU pressure detected: {}% > {}%", 
