src/services/embeddings.rs:159:                        tracing::info!("Retrying failed batch {} with smaller chunks", batch_idx);
src/services/embeddings.rs:175:                        tracing::warn!("Skipping embedding generation for batch {} due to non-retryable error", batch_idx);
src/services/embeddings.rs:492:                tracing::warn!("Failed to generate embeddings for smaller chunk: {}", e);
src/services/code_quality_assessor.rs:272:                tracing::error!("AI quality assessment failed: {}", e);
src/services/analyzer/storage.rs:138:                tracing::info!("Stored analysis {} in Spanner", result.id);
src/services/analyzer/repository.rs:46:                    tracing::info!("Cache hit with fresh commit hash for repository: {}", config.repository_url);
src/services/analyzer/repository.rs:86:                tracing::warn!("Failed to get remote commit hash for cache validation: {}", e);
src/services/analyzer/repository.rs:188:            tracing::warn!("Failed to cache analysis results: {}", e);
src/services/analyzer/mod.rs:93:            tracing::warn!("Failed to prepare cache for concurrent load: {}", e);
src/services/analyzer/mod.rs:131:            tracing::warn!("Failed to prepare cache for concurrent load: {}", e);
src/services/analyzer/mod.rs:178:                tracing::info!("Analysis {} progress: {:?}", analysis_id_clone, update);
src/services/analyzer/mod.rs:182:                    tracing::warn!("Failed to broadcast progress update: {}", e);
src/services/analyzer/mod.rs:187:                    tracing::error!("Failed to publish progress update to PubSub: {}", e);
src/services/analyzer/mod.rs:198:                tracing::info!("Analysis {} completed successfully", analysis_id);
src/services/analyzer/mod.rs:214:                        .unwrap_or_else(|e| tracing::warn!("Failed to send webhook: {}", e));
src/services/analyzer/mod.rs:218:                tracing::error!("Analysis {} failed: {}", analysis_id, e);
src/services/analyzer/mod.rs:279:                tracing::info!("Cache miss for repository: {}", opts.repository_url);
src/services/analyzer/mod.rs:282:                tracing::warn!("Cache check failed: {}", e);
src/services/analyzer/mod.rs:521:                    tracing::warn!("Failed to publish pattern detected event: {}", e);
src/services/analyzer/streaming_processor.rs:92:                            tracing::warn!("Memory pressure detected, throttling: {}", e);
src/services/analyzer/events.rs:195:        tracing::info!("Batch completion event: {}", summary);
src/main.rs:62:        tracing::error!("Failed to create application: {:?}", e);
src/services/analyzer/file_processor.rs:87:                            tracing::warn!("Memory pressure detected, throttling: {}", e);
src/services/analyzer/file_processor.rs:97:                                tracing::warn!("Failed to acquire parsing permit: {}", e);
src/services/analyzer/file_processor.rs:174:                        tracing::error!("Task panicked during file parsing: {}", e);
src/services/embeddings_enhancement.rs:215:                        tracing::info!("Attempting fallback embeddings for batch {}", batch_idx);
src/services/intelligent_documentation.rs:578:                tracing::error!("AI documentation generation failed: {}", e);
src/audit/mod.rs:169:                tracing::error!("Failed to store audit event in database: {:?}", e);
src/audit/mod.rs:299:        tracing::debug!("Stored audit event in database: {}", event.log_id);
src/storage/redis_client.rs:14:        tracing::info!("Attempting to connect to Redis at: {}", redis_url);
src/storage/redis_client.rs:28:                        tracing::info!("Redis connection successful, PING response: {}", response);
src/storage/redis_client.rs:32:                        tracing::error!("Redis PING failed: {}", e);
src/storage/redis_client.rs:38:                tracing::error!("Failed to connect to Redis at {}: {}", redis_url, e);
src/storage/redis_client.rs:236:        tracing::info!("Rate limit reset for key: {}", key);
src/services/repository_insights.rs:463:                tracing::error!("AI repository insights failed: {}", e);
src/services/semantic_search.rs:157:        tracing::info!("Indexed {} files and {} embeddings", analyses.len(), embeddings.len());
src/services/semantic_search.rs:186:                tracing::error!("Semantic search failed: {}", e);
src/storage/spanner.rs:228:                tracing::debug!("Connection pool health check completed: {} active connections", connections.len());
src/storage/spanner.rs:741:        tracing::debug!("Stored file analysis for {} in analysis {}", file_analysis.path, analysis_id);
src/storage/spanner.rs:772:        tracing::debug!("Stored {} patterns for analysis {}", patterns.len(), analysis_id);
src/storage/spanner.rs:932:                tracing::warn!("Failed to deserialize warnings: {}", e);
src/storage/spanner.rs:941:                tracing::warn!("Failed to deserialize successful_analyses: {}", e);
src/storage/pubsub.rs:171:                    tracing::debug!("PubSub topic {} exists and is accessible", name);
src/storage/pubsub.rs:175:                    tracing::error!("PubSub topic {} does not exist", name);
src/storage/pubsub.rs:187:                        tracing::warn!("PubSub health check: Cannot verify topic {} - missing viewer permission. Consider granting roles/pubsub.viewer.", name);
src/storage/pubsub.rs:190:                        tracing::error!("Failed to check PubSub topic {}: {}", name, e);
src/storage/pubsub.rs:199:            tracing::info!("PubSub health check completed with warnings: {}", warnings.join(", "));
src/backpressure/mod.rs:441:        tracing::info!("Preparing backpressure manager for {} concurrent analyses", expected_concurrent_analyses);
src/backpressure/mod.rs:450:        tracing::info!("Backpressure manager configured with permits - Analysis: {}, Parsing: {}, Database: {}, Storage: {}", 
src/backpressure/mod.rs:464:        tracing::info!("Backpressure manager prepared for {} concurrent analyses", expected_concurrent_analyses);
src/storage/gcp_clients.rs:31:            tracing::info!("Using Spanner emulator at: {}", config.endpoint);
src/storage/gcp_clients.rs:38:            tracing::info!("Using service account credentials from: {}", creds_path);
src/storage/gcp_clients.rs:42:            tracing::warn!("GOOGLE_APPLICATION_CREDENTIALS set but file not found: {}", creds_path);
src/storage/gcp_clients.rs:79:            tracing::info!("Using Storage emulator at: {}", config.storage_endpoint);
src/storage/gcp_clients.rs:86:            tracing::info!("Using service account credentials for Storage from: {}", creds_path);
src/storage/gcp_clients.rs:90:            tracing::warn!("GOOGLE_APPLICATION_CREDENTIALS set but file not found: {}", creds_path);
src/storage/gcp_clients.rs:114:                tracing::info!("Storage client initialized for bucket: {}", bucket_name);
src/storage/gcp_clients.rs:151:            tracing::info!("Using Pub/Sub emulator at: {}", config.endpoint);
src/storage/gcp_clients.rs:158:            tracing::info!("Using service account credentials for Pub/Sub from: {}", creds_path);
src/storage/gcp_clients.rs:162:            tracing::warn!("GOOGLE_APPLICATION_CREDENTIALS set but file not found: {}", creds_path);
src/storage/gcp_clients.rs:189:                            tracing::info!("Created Pub/Sub topic: {}", topic_name);
src/storage/gcp_clients.rs:193:                            tracing::warn!("Failed to create topic {}: {}", topic_name, e);
src/storage/gcp_clients.rs:197:                    tracing::debug!("Pub/Sub topic exists: {}", topic_name);
src/storage/gcp_clients.rs:201:                tracing::error!("Failed to check topic existence for {}: {}", topic_name, e);
src/storage/gcp_clients.rs:206:    tracing::info!("Pub/Sub client initialized for project: {}", gcp_settings.project_id);
src/api/mod.rs:56:                    tracing::warn!("Failed to create Redis connection pool: {}. Continuing without Redis caching.", e);
src/api/mod.rs:74:                    tracing::warn!("Failed to create Redis client: {}. Continuing without Redis.", e);
src/api/mod.rs:174:                tracing::warn!("Failed to update backpressure metrics: {}", e);
src/api/handlers/websocket.rs:341:                tracing::error!("Failed to serialize progress update: {}", e);
src/storage/cache.rs:225:                    tracing::warn!("Cache warming failed: {}", e);
src/storage/cache.rs:290:        tracing::debug!("Cache warming completed: {} items warmed", warmed_count);
src/storage/cache.rs:341:            tracing::debug!("Evicted {} items ({} bytes) from hot cache", evicted_count, evicted_size);
src/storage/cache.rs:360:        tracing::info!("Pre-warming cache for {} repositories", repository_urls.len());
src/storage/cache.rs:390:        tracing::info!("Pre-warmed {} repositories in cache", warmed_count);
src/storage/cache.rs:421:                tracing::debug!("Cache hit in HOT tier for key: {}", key);
src/storage/cache.rs:444:                    tracing::debug!("Cache hit in WARM tier for key: {}", key);
src/storage/cache.rs:469:                            tracing::debug!("Promoted cache entry to HOT tier: {}", key);
src/storage/cache.rs:486:                    tracing::warn!("Failed to get cached analysis: {}", e);
src/storage/cache.rs:497:        tracing::debug!("Cache miss for key: {}", key);
src/storage/cache.rs:510:                            tracing::info!("Cache hit with matching commit hash for analysis: {}", analysis_id);
src/storage/cache.rs:513:                            tracing::info!("Cache hit but commit hash mismatch for analysis: {} (cached: {}, current: {})",
src/storage/cache.rs:519:                        tracing::info!("Cache hit but old format for analysis: {}, considering stale", analysis_id);
src/storage/cache.rs:525:                    tracing::warn!("Failed to get cached analysis: {}", e);
src/storage/cache.rs:544:                    tracing::warn!("Failed to cache analysis: {}", e);
src/storage/cache.rs:567:                    tracing::info!("Cached analysis {} with commit hash {}", analysis_id, commit_hash);
src/storage/cache.rs:571:                    tracing::warn!("Failed to cache analysis: {}", e);
src/storage/cache.rs:593:                    tracing::warn!("Failed to get cached patterns: {}", e);
src/storage/cache.rs:612:                    tracing::warn!("Failed to cache patterns: {}", e);
src/storage/cache.rs:634:                    tracing::warn!("Failed to get cached embeddings: {}", e);
src/storage/cache.rs:653:                    tracing::warn!("Failed to cache embeddings: {}", e);
src/storage/cache.rs:678:                    tracing::warn!("Failed to clear analysis cache: {}", e);
src/storage/cache.rs:689:        tracing::info!("Preparing cache for {} concurrent analyses", expected_concurrent_analyses);
src/storage/cache.rs:727:        tracing::info!("Cache prepared for concurrent load with target hot cache size: {} MB", 
src/api/handlers/analysis.rs:128:        tracing::error!("Failed to log audit event: {}", e);
src/api/handlers/analysis.rs:157:            tracing::error!("Failed to start analysis: {}", e);
src/api/handlers/analysis.rs:386:                            tracing::error!("Failed to update analysis status to cancelled: {}", e);
src/api/handlers/analysis.rs:391:                    tracing::error!("Failed to get Spanner connection for cancellation: {:?}", e);
src/api/middleware/auth_layer.rs:221:                tracing::info!("Loaded JWT key with ID: {}", kid);
src/api/middleware/auth_layer.rs:373:                            tracing::debug!("Authentication successful for user: {}", user_id);
src/api/middleware/auth_layer.rs:398:                                tracing::error!("Failed to log audit event: {}", e);
src/api/middleware/auth_layer.rs:419:                            tracing::error!("Rate limiting check failed: {}", e);
src/api/middleware/auth_layer.rs:429:                    tracing::warn!("Authentication failed: {}", error);
src/api/middleware/auth_layer.rs:453:                        tracing::error!("Failed to log audit event: {}", e);
src/api/middleware/auth_layer.rs:550:                tracing::warn!("Error verifying API key: {}", e);
src/api/handlers/health.rs:131:                tracing::warn!("Spanner health check failed: {:?}", e);
src/api/handlers/health.rs:147:            tracing::warn!("Storage health check failed: {}", e);
src/api/handlers/health.rs:158:            tracing::warn!("PubSub health check failed: {}", e);
src/api/auth_extractor.rs:602:        tracing::error!("Failed to log auth success: {}", e);
src/api/auth_extractor.rs:628:        tracing::error!("Failed to log auth failure: {}", e);
src/services/security/types.rs:64:            tracing::error!("Failed to compile pattern '{}': {}", name, e);
src/parser/streaming/progress_reporter.rs:69:        tracing::error!("Parsing error: {}", error);
src/services/security/dependency/parsers/maven.rs:107:                tracing::error!("Error parsing pom.xml: {}", e);
src/services/security/dependency/parsers/dotnet.rs:65:                tracing::error!("Error parsing packages.config: {}", e);
src/services/security/dependency/parsers/dotnet.rs:142:                tracing::error!("Error parsing .NET project file: {}", e);
