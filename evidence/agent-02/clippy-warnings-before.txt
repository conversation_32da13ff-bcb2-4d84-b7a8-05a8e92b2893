149 | | ...                       "Parsed {}/{} files ({:.1}% success)",
150 | | ...                       completed, total_files, success_rate
151 | | ...                   ),
    | |_______________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
    = note: `#[warn(clippy::uninlined_format_args)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/file_processor.rs:152:47
    |
152 |   ...                   message: Some(format!(
--
153 | | ...                       "Concurrent processing: {} active",
154 | | ...                       max_concurrent_files
155 | | ...                   )),
    | |_______________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/file_processor.rs:178:38
    |
178 | ...                   message: format!("Task panicked: {}", e),
    |                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
178 -                             message: format!("Task panicked: {}", e),
178 +                             message: format!("Task panicked: {e}"),
    |
--
157 | |                     "Peak memory usage was {} MB, approaching system limits",
158 | |                     memory_usage_mb
159 | |                 ),
    | |_________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/performance.rs:166:17
    |
166 | /                 format!(
167 | |                     "Peak memory usage was {} MB, consider monitoring memory usage",
168 | |                     memory_usage_mb
169 | |                 ),
    | |_________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: clamp-like pattern without using clamp function
   --> src/services/analyzer/performance.rs:189:13
    |
189 |             (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
--
   --> src/services/analyzer/progress.rs:106:45
    |
106 |             message: file_count.map(|count| format!("Found {} source files", count)),
    |                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
106 -             message: file_count.map(|count| format!("Found {} source files", count)),
106 +             message: file_count.map(|count| format!("Found {count} source files")),
    |
--
126 | |                 "Parsed {}/{} files ({:.1}% success)",
127 | |                 files_processed, total_files, success_rate
128 | |             ),
    | |_____________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/progress.rs:159:48
    |
159 |             message: pattern_count.map(|count| format!("Found {} code patterns", count)),
    |                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
159 -             message: pattern_count.map(|count| format!("Found {} code patterns", count)),
159 +             message: pattern_count.map(|count| format!("Found {count} code patterns")),
    |
--
   --> src/services/analyzer/progress.rs:176:50
    |
176 |             message: embedding_count.map(|count| format!("Generated {} embeddings", count)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
176 -             message: embedding_count.map(|count| format!("Generated {} embeddings", count)),
176 +             message: embedding_count.map(|count| format!("Generated {count} embeddings")),
    |
--
   --> src/services/analyzer/progress.rs:202:27
    |
202 |             message: Some(format!("Error: {}", error)),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
202 -             message: Some(format!("Error: {}", error)),
202 +             message: Some(format!("Error: {error}")),
    |
--
  --> src/services/analyzer/repository.rs:89:21
   |
89 |                     format!("Failed to get remote commit hash: {}", e),
   |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
89 -                     format!("Failed to get remote commit hash: {}", e),
89 +                     format!("Failed to get remote commit hash: {e}"),
   |
--
105 | |                         "High failure rate: {:.1}% of files failed to parse",
106 | |                         failure_rate
107 | |                     ),
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: using `clone` on type `AnalysisStatus` which implements the `Copy` trait
   --> src/services/analyzer/results.rs:197:21
    |
197 |             status: result.status.clone(),
--
   --> src/services/analyzer/storage.rs:181:29
    |
181 |                 errors.push(format!("Spanner deletion failed: {}", e));
    |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
181 -                 errors.push(format!("Spanner deletion failed: {}", e));
181 +                 errors.push(format!("Spanner deletion failed: {e}"));
    |
--
   --> src/services/analyzer/storage.rs:190:25
    |
190 |             errors.push(format!("Cloud storage deletion failed: {}", e));
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
190 -             errors.push(format!("Cloud storage deletion failed: {}", e));
190 +             errors.push(format!("Cloud storage deletion failed: {e}"));
    |
--
   --> src/services/analyzer/streaming_processor.rs:223:26
    |
223 |                 message: format!("Failed to get file metadata: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
223 -                 message: format!("Failed to get file metadata: {}", e),
223 +                 message: format!("Failed to get file metadata: {e}"),
    |
--
   --> src/services/analyzer/streaming_processor.rs:239:26
    |
239 |                 message: format!("Failed to open file: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
239 -                 message: format!("Failed to open file: {}", e),
239 +                 message: format!("Failed to open file: {e}"),
    |
--
   --> src/services/analyzer/streaming_processor.rs:252:26
    |
252 |                 message: format!("Failed to read file chunk: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
252 -                 message: format!("Failed to read file chunk: {}", e),
252 +                 message: format!("Failed to read file chunk: {e}"),
    |
--
   --> src/services/analyzer/streaming_processor.rs:294:26
    |
294 |                 message: format!("Failed to open file: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
294 -                 message: format!("Failed to open file: {}", e),
294 +                 message: format!("Failed to open file: {e}"),
    |
--
   --> src/services/analyzer/streaming_processor.rs:365:28
    |
365 |                 text: Some(format!("Large file with {} lines", line_count)),
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
365 -                 text: Some(format!("Large file with {} lines", line_count)),
365 +                 text: Some(format!("Large file with {line_count} lines")),
    |
--
   --> src/services/analyzer/mod.rs:404:25
    |
404 |                         format!("Failed to generate embeddings: {}", e),
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
404 -                         format!("Failed to generate embeddings: {}", e),
404 +                         format!("Failed to generate embeddings: {e}"),
    |
--
   --> src/services/embeddings.rs:279:31
    |
279 |                     chunk_id: format!("chunk_{:016x}", i),
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
279 -                     chunk_id: format!("chunk_{:016x}", i),
279 +                     chunk_id: format!("chunk_{i:016x}"),
    |
--
   --> src/services/embeddings.rs:386:31
    |
386 |             content.push_str(&format!("\nCode snippet:\n{}", preview));
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
386 -             content.push_str(&format!("\nCode snippet:\n{}", preview));
386 +             content.push_str(&format!("\nCode snippet:\n{preview}"));
    |
--
   --> src/services/embeddings_enhancement.rs:323:31
    |
323 |                     chunk_id: format!("chunk_{:016x}", i),
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
323 -                     chunk_id: format!("chunk_{:016x}", i),
323 +                     chunk_id: format!("chunk_{i:016x}"),
    |
--
   --> src/services/embeddings_enhancement.rs:367:23
    |
367 |             chunk_id: format!("fallback_chunk_{:016x}", idx),
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
367 -             chunk_id: format!("fallback_chunk_{:016x}", idx),
367 +             chunk_id: format!("fallback_chunk_{idx:016x}"),
    |
--
   --> src/services/embeddings_enhancement.rs:442:31
    |
442 |             content.push_str(&format!("\nCode:\n{}", preview));
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
442 -             content.push_str(&format!("\nCode:\n{}", preview));
442 +             content.push_str(&format!("\nCode:\n{preview}"));
    |
--
   --> src/services/ai_pattern_detector.rs:302:34
    |
302 |                 prompt.push_str(&format!("Code:\n```\n{}\n```\n\n", preview));
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
302 -                 prompt.push_str(&format!("Code:\n```\n{}\n```\n\n", preview));
302 +                 prompt.push_str(&format!("Code:\n```\n{preview}\n```\n\n"));
    |
--
   --> src/services/code_quality_assessor.rs:412:30
    |
412 |             prompt.push_str(&format!("Technical debt: {} minutes\n", technical_debt));
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
412 -             prompt.push_str(&format!("Technical debt: {} minutes\n", technical_debt));
412 +             prompt.push_str(&format!("Technical debt: {technical_debt} minutes\n"));
    |
--
   --> src/services/code_quality_assessor.rs:434:34
    |
434 |                 prompt.push_str(&format!("Code preview:\n```\n{}\n```\n\n", preview));
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
434 -                 prompt.push_str(&format!("Code preview:\n```\n{}\n```\n\n", preview));
434 +                 prompt.push_str(&format!("Code preview:\n```\n{preview}\n```\n\n"));
    |
--
   --> src/services/repository_insights.rs:658:34
    |
658 |                 prompt.push_str(&format!("Code sample:\n```\n{}\n```\n", preview));
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
658 -                 prompt.push_str(&format!("Code sample:\n```\n{}\n```\n", preview));
658 +                 prompt.push_str(&format!("Code sample:\n```\n{preview}\n```\n"));
    |
--
   --> src/services/intelligent_documentation.rs:708:26
    |
708 |         prompt.push_str(&format!("Repository URL: {}\n", repository_url));
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
708 -         prompt.push_str(&format!("Repository URL: {}\n", repository_url));
708 +         prompt.push_str(&format!("Repository URL: {repository_url}\n"));
    |
--
   --> src/services/intelligent_documentation.rs:709:26
    |
709 |         prompt.push_str(&format!("Primary language: {}\n", primary_language));
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
709 -         prompt.push_str(&format!("Primary language: {}\n", primary_language));
709 +         prompt.push_str(&format!("Primary language: {primary_language}\n"));
    |
--
   --> src/services/intelligent_documentation.rs:930:30
    |
930 |                 description: format!("{} repository written in {}", repo_name, primary_language),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
930 -                 description: format!("{} repository written in {}", repo_name, primary_language),
930 +                 description: format!("{repo_name} repository written in {primary_language}"),
    |
--
   --> src/services/semantic_search.rs:535:9
    |
535 | ...   format!("This code is related to your query '{}' based on content analysis. The code contains relevant functionality that matches your search criteria.", query)
    |       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
535 -         format!("This code is related to your query '{}' based on content analysis. The code contains relevant functionality that matches your search criteria.", query)
535 +         format!("This code is related to your query '{query}' based on content analysis. The code contains relevant functionality that matches your search criteria.")
    |
--
  --> src/services/security/types.rs:65:13
   |
65 |             format!("Failed to compile pattern '{}': {}", name, e)
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
65 -             format!("Failed to compile pattern '{}': {}", name, e)
65 +             format!("Failed to compile pattern '{name}': {e}")
   |
--
   --> src/services/security/vulnerability/detector.rs:136:38
    |
136 |                         description: format!("Potentially dangerous function call: {}", name),
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
136 -                         description: format!("Potentially dangerous function call: {}", name),
136 +                         description: format!("Potentially dangerous function call: {name}"),
    |
--
   --> src/services/security/vulnerability/ml_classifier.rs:375:37
    |
375 |                        description: format!("{}: Potential SQL injection vulnerability", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
375 -                        description: format!("{}: Potential SQL injection vulnerability", description),
375 +                        description: format!("{description}: Potential SQL injection vulnerability"),
    |
--
   --> src/services/security/vulnerability/ml_classifier.rs:411:37
    |
411 |                        description: format!("{}: Potential XSS vulnerability", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
411 -                        description: format!("{}: Potential XSS vulnerability", description),
411 +                        description: format!("{description}: Potential XSS vulnerability"),
    |
--
   --> src/services/security/vulnerability/ml_classifier.rs:447:37
    |
447 |                        description: format!("{}: Potential command injection", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
447 -                        description: format!("{}: Potential command injection", description),
447 +                        description: format!("{description}: Potential command injection"),
    |
--
   --> src/services/security/vulnerability/ml_classifier.rs:476:37
    |
476 |                        description: format!("{}: Potential path traversal", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
476 -                        description: format!("{}: Potential path traversal", description),
476 +                        description: format!("{description}: Potential path traversal"),
    |
--
   --> src/services/security/vulnerability/ml_classifier.rs:520:37
    |
520 |                        description: format!("{}: Insecure deserialization", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
520 -                        description: format!("{}: Insecure deserialization", description),
520 +                        description: format!("{description}: Insecure deserialization"),
    |
--
   --> src/services/security/vulnerability/ml_classifier.rs:557:37
    |
557 |                        description: format!("{}: Weak cryptography", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
557 -                        description: format!("{}: Weak cryptography", description),
557 +                        description: format!("{description}: Weak cryptography"),
    |
--
   --> src/services/security/vulnerability/ml_classifier.rs:593:37
    |
593 |                        description: format!("{}: Authentication issue", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
593 -                        description: format!("{}: Authentication issue", description),
593 +                        description: format!("{description}: Authentication issue"),
    |
--
   --> src/services/security/vulnerability/ml_classifier.rs:629:37
    |
629 |                        description: format!("{}: Security misconfiguration", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
629 -                        description: format!("{}: Security misconfiguration", description),
629 +                        description: format!("{description}: Security misconfiguration"),
    |
--
   --> src/services/security/vulnerability/ml_classifier.rs:691:37
    |
691 |                        description: format!("{}: Potential race condition", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
691 -                        description: format!("{}: Potential race condition", description),
691 +                        description: format!("{description}: Potential race condition"),
    |
--
   --> src/services/security/vulnerability/ml_classifier.rs:729:37
    |
729 |                        description: format!("{}: Buffer overflow risk", description),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
729 -                        description: format!("{}: Buffer overflow risk", description),
729 +                        description: format!("{description}: Buffer overflow risk"),
    |
--
  --> src/services/security/dependency/parsers/python.rs:49:21
   |
49 |                     format!("{}{}", op, version)
   |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
49 -                     format!("{}{}", op, version)
49 +                     format!("{op}{version}")
   |
--
  --> src/services/security/dependency/parsers/gradle.rs:64:28
   |
64 |                 let name = format!("{}:{}", group, artifact);
   |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
64 -                 let name = format!("{}:{}", group, artifact);
64 +                 let name = format!("{group}:{artifact}");
   |
--
  --> src/services/security/dependency/parsers/go.rs:69:42
   |
69 |                         current_version: format!("v{}", version),
   |                                          ^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
69 -                         current_version: format!("v{}", version),
69 +                         current_version: format!("v{version}"),
   |
--
  --> src/services/security/dependency/parsers/go.rs:96:42
   |
96 |                         current_version: format!("v{}", version),
   |                                          ^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
96 -                         current_version: format!("v{}", version),
96 +                         current_version: format!("v{version}"),
   |
--
   --> src/services/security/dependency/parsers/go.rs:146:27
    |
146 |                 let key = format!("{}@{}", module, version);
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
146 -                 let key = format!("{}@{}", module, version);
146 +                 let key = format!("{module}@{version}");
    |
--
   --> src/services/security/threat/modeler.rs:102:30
    |
102 |                 threat_name: format!("{} Exploitation Threat", vuln_type),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
102 -                 threat_name: format!("{} Exploitation Threat", vuln_type),
102 +                 threat_name: format!("{vuln_type} Exploitation Threat"),
    |
--
   --> src/services/security/threat/modeler.rs:155:30
    |
155 |                 threat_name: format!("Supply Chain Attack via {} Dependencies", severity),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
155 -                 threat_name: format!("Supply Chain Attack via {} Dependencies", severity),
155 +                 threat_name: format!("Supply Chain Attack via {severity} Dependencies"),
    |
--
291 | |             "Identified {} instances of {} vulnerabilities that could be exploited by attackers to compromise the system",
292 | |             count, vuln_type
293 | |         )
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: writing `&mut Vec` instead of `&mut [_]` involves a new object where a slice will do
   --> src/services/security/threat/modeler.rs:355:43
    |
355 |     fn prioritize_threats(&self, threats: &mut Vec<ThreatModel>) {
--
   --> src/api/auth_extractor.rs:246:22
    |
246 |             message: format!("Failed to get database connection: {:?}", e),
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
246 -             message: format!("Failed to get database connection: {:?}", e),
246 +             message: format!("Failed to get database connection: {e:?}"),
    |
--
   --> src/api/auth_extractor.rs:483:22
    |
483 |         .map_err(|e| format!("Database error: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
483 -         .map_err(|e| format!("Database error: {}", e))?;
483 +         .map_err(|e| format!("Database error: {e}"))?;
    |
--
   --> src/api/auth_extractor.rs:488:22
    |
488 |         .map_err(|e| format!("Query error: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
488 -         .map_err(|e| format!("Query error: {}", e))?;
488 +         .map_err(|e| format!("Query error: {e}"))?;
    |
--
   --> src/api/auth_extractor.rs:493:22
    |
493 |         .map_err(|e| format!("Read error: {}", e))?
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
493 -         .map_err(|e| format!("Read error: {}", e))?
493 +         .map_err(|e| format!("Read error: {e}"))?
    |
--
   --> src/api/auth_extractor.rs:497:26
    |
497 |             .map_err(|e| format!("Failed to get user_id: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
497 -             .map_err(|e| format!("Failed to get user_id: {}", e))?;
497 +             .map_err(|e| format!("Failed to get user_id: {e}"))?;
    |
--
   --> src/api/auth_extractor.rs:500:26
    |
500 |             .map_err(|e| format!("Failed to get api_key_hash: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
500 -             .map_err(|e| format!("Failed to get api_key_hash: {}", e))?;
500 +             .map_err(|e| format!("Failed to get api_key_hash: {e}"))?;
    |
--
   --> src/api/auth_extractor.rs:503:26
    |
503 |             .map_err(|e| format!("Failed to get salt: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
503 -             .map_err(|e| format!("Failed to get salt: {}", e))?;
503 +             .map_err(|e| format!("Failed to get salt: {e}"))?;
    |
--
   --> src/api/auth_extractor.rs:506:26
    |
506 |             .map_err(|e| format!("Failed to get rate_limit: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
506 -             .map_err(|e| format!("Failed to get rate_limit: {}", e))?;
506 +             .map_err(|e| format!("Failed to get rate_limit: {e}"))?;
    |
--
   --> src/api/auth_extractor.rs:523:22
    |
523 |         .map_err(|e| format!("Failed to decode salt: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
523 -         .map_err(|e| format!("Failed to decode salt: {}", e))?;
523 +         .map_err(|e| format!("Failed to decode salt: {e}"))?;
    |
--
   --> src/api/errors.rs:159:56
    |
159 |         let mut error = Self::new(ErrorType::NotFound, format!("{} not found", resource));
    |                                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
159 -         let mut error = Self::new(ErrorType::NotFound, format!("{} not found", resource));
159 +         let mut error = Self::new(ErrorType::NotFound, format!("{resource} not found"));
    |
--
   --> src/api/errors.rs:161:35
    |
161 |         error.user_message = Some(format!("The requested {} could not be found.", resource));
    |                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
161 -         error.user_message = Some(format!("The requested {} could not be found.", resource));
161 +         error.user_message = Some(format!("The requested {resource} could not be found."));
    |
--
   --> src/api/errors.rs:277:42
    |
277 |                     ApiError::BadRequest(format!("Unsupported language: {}", language)),
    |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
277 -                     ApiError::BadRequest(format!("Unsupported language: {}", language)),
277 +                     ApiError::BadRequest(format!("Unsupported language: {language}")),
    |
--
   --> src/api/errors.rs:279:42
    |
279 |                     ApiError::BadRequest(format!("Failed to parse {}: {}", file, reason)),
    |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
279 -                     ApiError::BadRequest(format!("Failed to parse {}: {}", file, reason)),
279 +                     ApiError::BadRequest(format!("Failed to parse {file}: {reason}")),
    |
--
   --> src/api/errors.rs:281:42
    |
281 |                     ApiError::BadRequest(format!("Syntax error in {} at line {}: {}", file, line, details)),
    |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
281 -                     ApiError::BadRequest(format!("Syntax error in {} at line {}: {}", file, line, details)),
281 +                     ApiError::BadRequest(format!("Syntax error in {file} at line {line}: {details}")),
    |
--
   --> src/api/errors.rs:283:45
    |
283 |                     ApiError::InternalError(format!("Parser error: {}", msg)),
    |                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
283 -                     ApiError::InternalError(format!("Parser error: {}", msg)),
283 +                     ApiError::InternalError(format!("Parser error: {msg}")),
    |
--
   --> src/api/errors.rs:285:45
    |
285 |                     ApiError::InternalError(format!("Regex compilation failed for '{}': {}", pattern, reason)),
    |                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
285 -                     ApiError::InternalError(format!("Regex compilation failed for '{}': {}", pattern, reason)),
285 +                     ApiError::InternalError(format!("Regex compilation failed for '{pattern}': {reason}")),
    |
--
   --> src/api/errors.rs:287:42
    |
287 |                     ApiError::BadRequest(format!("File too large: {} bytes (limit: {} bytes)", size, limit)),
    |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
287 -                     ApiError::BadRequest(format!("File too large: {} bytes (limit: {} bytes)", size, limit)),
287 +                     ApiError::BadRequest(format!("File too large: {size} bytes (limit: {limit} bytes)")),
    |
--
   --> src/api/errors.rs:289:50
    |
289 |                     ApiError::ServiceUnavailable(format!("Parse timeout after {} seconds", timeout_seconds)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
289 -                     ApiError::ServiceUnavailable(format!("Parse timeout after {} seconds", timeout_seconds)),
289 +                     ApiError::ServiceUnavailable(format!("Parse timeout after {timeout_seconds} seconds")),
    |
--
   --> src/api/errors.rs:291:42
    |
291 |                     ApiError::BadRequest(format!("Invalid encoding: {}", msg)),
    |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
291 -                     ApiError::BadRequest(format!("Invalid encoding: {}", msg)),
291 +                     ApiError::BadRequest(format!("Invalid encoding: {msg}")),
    |
--
   --> src/api/errors.rs:293:42
    |
293 |                     ApiError::BadRequest(format!("Dependency parsing error: {}", msg)),
    |                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
293 -                     ApiError::BadRequest(format!("Dependency parsing error: {}", msg)),
293 +                     ApiError::BadRequest(format!("Dependency parsing error: {msg}")),
    |
--
   --> src/api/errors.rs:295:45
    |
295 |                     ApiError::InternalError(format!("AST traversal error: {}", msg)),
    |                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
295 -                     ApiError::InternalError(format!("AST traversal error: {}", msg)),
295 +                     ApiError::InternalError(format!("AST traversal error: {msg}")),
    |
--
   --> src/api/errors.rs:297:50
    |
297 |                     ApiError::ServiceUnavailable(format!("Parser pool error: {}", msg)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
297 -                     ApiError::ServiceUnavailable(format!("Parser pool error: {}", msg)),
297 +                     ApiError::ServiceUnavailable(format!("Parser pool error: {msg}")),
    |
--
   --> src/api/errors.rs:302:50
    |
302 |                     ApiError::ServiceUnavailable(format!("Database connection failed: {}", msg)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
302 -                     ApiError::ServiceUnavailable(format!("Database connection failed: {}", msg)),
302 +                     ApiError::ServiceUnavailable(format!("Database connection failed: {msg}")),
    |
--
   --> src/api/errors.rs:304:50
    |
304 |                     ApiError::ServiceUnavailable(format!("Query failed: {} - {}", query, reason)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
304 -                     ApiError::ServiceUnavailable(format!("Query failed: {} - {}", query, reason)),
304 +                     ApiError::ServiceUnavailable(format!("Query failed: {query} - {reason}")),
    |
--
   --> src/api/errors.rs:306:50
    |
306 |                     ApiError::ServiceUnavailable(format!("Transaction failed: {}", msg)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
306 -                     ApiError::ServiceUnavailable(format!("Transaction failed: {}", msg)),
306 +                     ApiError::ServiceUnavailable(format!("Transaction failed: {msg}")),
    |
--
   --> src/api/errors.rs:308:50
    |
308 |                     ApiError::ServiceUnavailable(format!("Cache error: {}", msg)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
308 -                     ApiError::ServiceUnavailable(format!("Cache error: {}", msg)),
308 +                     ApiError::ServiceUnavailable(format!("Cache error: {msg}")),
    |
--
   --> src/api/errors.rs:310:45
    |
310 |                     ApiError::InternalError(format!("Serialization error: {}", msg)),
    |                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
310 -                     ApiError::InternalError(format!("Serialization error: {}", msg)),
310 +                     ApiError::InternalError(format!("Serialization error: {msg}")),
    |
--
   --> src/api/errors.rs:312:45
    |
312 |                     ApiError::InternalError(format!("Deserialization error: {}", msg)),
    |                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
312 -                     ApiError::InternalError(format!("Deserialization error: {}", msg)),
312 +                     ApiError::InternalError(format!("Deserialization error: {msg}")),
    |
--
   --> src/api/errors.rs:316:40
    |
316 |                     ApiError::Conflict(format!("Constraint violation: {}", msg)),
    |                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
316 -                     ApiError::Conflict(format!("Constraint violation: {}", msg)),
316 +                     ApiError::Conflict(format!("Constraint violation: {msg}")),
    |
--
   --> src/api/errors.rs:320:50
    |
320 |                     ApiError::ServiceUnavailable(format!("Storage quota exceeded: {} / {}", current, limit)),
    |                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
320 -                     ApiError::ServiceUnavailable(format!("Storage quota exceeded: {} / {}", current, limit)),
320 +                     ApiError::ServiceUnavailable(format!("Storage quota exceeded: {current} / {limit}")),
    |
--
  --> src/api/handlers/analysis.rs:87:21
   |
87 |                     format!("Service {} temporarily unavailable", service),
   |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
87 -                     format!("Service {} temporarily unavailable", service),
87 +                     format!("Service {service} temporarily unavailable"),
   |
--
   --> src/api/handlers/analysis.rs:144:28
    |
144 |         progress_url: Some(format!("/ws/analysis/{}", analysis_id)),
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
144 -         progress_url: Some(format!("/ws/analysis/{}", analysis_id)),
144 +         progress_url: Some(format!("/ws/analysis/{analysis_id}")),
    |
--
188 | |                         "Failed to get Spanner connection: {:?}",
189 | |                         e
190 | |                     )))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/analysis.rs:235:59
    |
235 |                       return (ErrorResponse::internal_error(format!(
--
236 | |                         "Failed to get Spanner connection: {:?}",
237 | |                         e
238 | |                     )))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/analysis.rs:277:58
    |
277 |                       return ErrorResponse::internal_error(format!(
--
278 | |                         "Failed to get Spanner connection: {:?}",
279 | |                         e
280 | |                     ))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/analysis.rs:338:58
    |
338 |                       return ErrorResponse::internal_error(format!(
--
339 | |                         "Failed to get Spanner connection: {:?}",
340 | |                         e
341 | |                     ))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/analysis.rs:430:58
    |
430 |                       return ErrorResponse::internal_error(format!(
--
431 | |                         "Failed to get Spanner connection: {:?}",
432 | |                         e
433 | |                     ))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: this `map_or` can be simplified
   --> src/api/handlers/analysis.rs:452:29
    |
452 | / ...                   w.file_path
--
516 | |                         "Failed to get Spanner connection: {:?}",
517 | |                         e
518 | |                     ))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/analysis.rs:557:59
    |
557 |                       return (ErrorResponse::internal_error(format!(
--
558 | |                         "Failed to get Spanner connection: {:?}",
559 | |                         e
560 | |                     )))
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
  --> src/api/handlers/security.rs:55:39
   |
55 |           return Err(ApiError::NotFound(format!(
--
56 | |             "Analysis {} not found",
57 | |             analysis_id
58 | |         )));
   | |_________^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/security.rs:180:40
    |
180 |           None => Err(ApiError::NotFound(format!(
--
181 | |             "Security analysis not found for analysis {}",
182 | |             analysis_id
183 | |         ))),
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/security.rs:302:40
    |
302 |           None => Err(ApiError::NotFound(format!(
--
303 | |             "Security assessment not found for analysis {}",
304 | |             analysis_id
305 | |         ))),
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/api/handlers/security.rs:328:40
    |
328 |           None => Err(ApiError::NotFound(format!(
--
329 | |             "Security metadata not found for analysis {}",
330 | |             analysis_id
331 | |         ))),
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: this `map_or` can be simplified
   --> src/api/middleware/auth_layer.rs:165:30
    |
165 |             .retain(|_, key| key.expires_at.map_or(true, |expires| expires > now));
--
   --> src/api/middleware/auth_layer.rs:475:22
    |
475 |         .map_err(|e| format!("Failed to get Spanner connection from pool: {:?}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
475 -         .map_err(|e| format!("Failed to get Spanner connection from pool: {:?}", e))?;
475 +         .map_err(|e| format!("Failed to get Spanner connection from pool: {e:?}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:497:22
    |
497 |         .map_err(|e| format!("Failed to create read transaction: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
497 -         .map_err(|e| format!("Failed to create read transaction: {}", e))?;
497 +         .map_err(|e| format!("Failed to create read transaction: {e}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:501:22
    |
501 |         .map_err(|e| format!("Failed to query API keys: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
501 -         .map_err(|e| format!("Failed to query API keys: {}", e))?;
501 +         .map_err(|e| format!("Failed to query API keys: {e}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:508:22
    |
508 |         .map_err(|e| format!("Failed to read row: {}", e))?
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
508 -         .map_err(|e| format!("Failed to read row: {}", e))?
508 +         .map_err(|e| format!("Failed to read row: {e}"))?
    |
--
   --> src/api/middleware/auth_layer.rs:512:26
    |
512 |             .map_err(|e| format!("Failed to read key_hash: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
512 -             .map_err(|e| format!("Failed to read key_hash: {}", e))?;
512 +             .map_err(|e| format!("Failed to read key_hash: {e}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:515:26
    |
515 |             .map_err(|e| format!("Failed to read salt: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
515 -             .map_err(|e| format!("Failed to read salt: {}", e))?;
515 +             .map_err(|e| format!("Failed to read salt: {e}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:524:38
    |
524 |                         .map_err(|e| format!("Invalid expiration time format: {}", e))?;
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
524 -                         .map_err(|e| format!("Invalid expiration time format: {}", e))?;
524 +                         .map_err(|e| format!("Invalid expiration time format: {e}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:532:34
    |
532 |                     .map_err(|e| format!("Failed to read user_id: {}", e))?;
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
532 -                     .map_err(|e| format!("Failed to read user_id: {}", e))?;
532 +                     .map_err(|e| format!("Failed to read user_id: {e}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:535:34
    |
535 |                     .map_err(|e| format!("Failed to read rate_limit: {}", e))?;
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
535 -                     .map_err(|e| format!("Failed to read rate_limit: {}", e))?;
535 +                     .map_err(|e| format!("Failed to read rate_limit: {e}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:567:22
    |
567 |         .map_err(|e| format!("Failed to decode salt: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
567 -         .map_err(|e| format!("Failed to decode salt: {}", e))?;
567 +         .map_err(|e| format!("Failed to decode salt: {e}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:622:51
    |
622 |     let header = decode_header(token).map_err(|e| format!("Invalid JWT header: {}", e))?;
    |                                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
622 -     let header = decode_header(token).map_err(|e| format!("Invalid JWT header: {}", e))?;
622 +     let header = decode_header(token).map_err(|e| format!("Invalid JWT header: {e}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:631:28
    |
631 |             .ok_or_else(|| format!("Unknown key ID: {}", kid))?
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
631 -             .ok_or_else(|| format!("Unknown key ID: {}", kid))?
631 +             .ok_or_else(|| format!("Unknown key ID: {kid}"))?
    |
--
   --> src/api/middleware/auth_layer.rs:667:18
    |
667 |             _ => format!("Token validation failed: {}", e),
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
667 -             _ => format!("Token validation failed: {}", e),
667 +             _ => format!("Token validation failed: {e}"),
    |
--
   --> src/api/middleware/auth_layer.rs:707:22
    |
707 |         .map_err(|e| format!("System time error: {}", e))?
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
707 -         .map_err(|e| format!("System time error: {}", e))?
707 +         .map_err(|e| format!("System time error: {e}"))?
    |
--
   --> src/api/middleware/auth_layer.rs:755:30
    |
755 |                 .map_err(|e| format!("Failed to get spanner connection: {:?}", e))?;
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
755 -                 .map_err(|e| format!("Failed to get spanner connection: {:?}", e))?;
755 +                 .map_err(|e| format!("Failed to get spanner connection: {e:?}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:760:30
    |
760 |                 .map_err(|e| format!("Failed to create read transaction: {}", e))?;
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
760 -                 .map_err(|e| format!("Failed to create read transaction: {}", e))?;
760 +                 .map_err(|e| format!("Failed to create read transaction: {e}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:764:30
    |
764 |                 .map_err(|e| format!("Failed to query user: {}", e))?;
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
764 -                 .map_err(|e| format!("Failed to query user: {}", e))?;
764 +                 .map_err(|e| format!("Failed to query user: {e}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:768:30
    |
768 |                 .map_err(|e| format!("Failed to read row: {}", e))?
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
768 -                 .map_err(|e| format!("Failed to read row: {}", e))?
768 +                 .map_err(|e| format!("Failed to read row: {e}"))?
    |
--
   --> src/api/middleware/auth_layer.rs:778:26
    |
778 |             .map_err(|e| format!("Failed to read rate_limit: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
778 -             .map_err(|e| format!("Failed to read rate_limit: {}", e))?;
778 +             .map_err(|e| format!("Failed to read rate_limit: {e}"))?;
    |
--
   --> src/api/middleware/auth_layer.rs:849:5
    |
849 |     format!("{:x}", result)
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
849 -     format!("{:x}", result)
849 +     format!("{result:x}")
    |
--
    --> src/api/middleware/auth_layer.rs:1091:22
     |
1091 |         description: format!("Wait {} seconds before making another request", retry_after),
     |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |
     = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
     |
1091 -         description: format!("Wait {} seconds before making another request", retry_after),
1091 +         description: format!("Wait {retry_after} seconds before making another request"),
     |
--
    --> src/api/middleware/auth_layer.rs:1139:15
     |
1139 |     let key = format!("rate_limit:{}", user_id);
     |               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |
     = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
     |
1139 -     let key = format!("rate_limit:{}", user_id);
1139 +     let key = format!("rate_limit:{user_id}");
     |
--
  --> src/api/rate_limit_extractor.rs:68:17
   |
68 |                 format!("Rate limit check failed: {}", e),
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
68 -                 format!("Rate limit check failed: {}", e),
68 +                 format!("Rate limit check failed: {e}"),
   |
--
   --> src/api/rate_limit_extractor.rs:173:15
    |
173 |     let key = format!("rate_limit:{}", user_id);
    |               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
173 -     let key = format!("rate_limit:{}", user_id);
173 +     let key = format!("rate_limit:{user_id}");
    |
--
   --> src/api/rate_limit_extractor.rs:190:63
    |
190 |     let new_count: i64 = conn.incr(&key, 1).await.map_err(|e| format!("Redis error: {}", e))?;
    |                                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
190 -     let new_count: i64 = conn.incr(&key, 1).await.map_err(|e| format!("Redis error: {}", e))?;
190 +     let new_count: i64 = conn.incr(&key, 1).await.map_err(|e| format!("Redis error: {e}"))?;
    |
--
   --> src/api/rate_limit_extractor.rs:194:65
    |
194 |         let _: () = conn.expire(&key, window).await.map_err(|e| format!("Redis error: {}", e))?;
    |                                                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
194 -         let _: () = conn.expire(&key, window).await.map_err(|e| format!("Redis error: {}", e))?;
194 +         let _: () = conn.expire(&key, window).await.map_err(|e| format!("Redis error: {e}"))?;
    |
--
   --> src/api/rate_limit_extractor.rs:248:9
    |
248 |         format!("{} seconds", seconds_until_reset)
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
248 -         format!("{} seconds", seconds_until_reset)
248 +         format!("{seconds_until_reset} seconds")
    |
--
   --> src/storage/spanner.rs:442:24
    |
442 |         let database = format!("projects/{}/instances/{}/databases/{}", project_id, instance_id, database_id);
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
442 -         let database = format!("projects/{}/instances/{}/databases/{}", project_id, instance_id, database_id);
442 +         let database = format!("projects/{project_id}/instances/{instance_id}/databases/{database_id}");
    |
--
    --> src/storage/spanner.rs:1344:29
     |
1344 |             query.push_str(&format!(" LIMIT {}", limit));
     |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |
     = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
     |
1344 -             query.push_str(&format!(" LIMIT {}", limit));
1344 +             query.push_str(&format!(" LIMIT {limit}"));
     |
--
  --> src/storage/storage.rs:21:33
   |
21 |             .unwrap_or_else(|_| format!("ccl-analysis-{}", project_id));
   |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
21 -             .unwrap_or_else(|_| format!("ccl-analysis-{}", project_id));
21 +             .unwrap_or_else(|_| format!("ccl-analysis-{project_id}"));
   |
--
  --> src/storage/storage.rs:50:27
   |
50 |         let object_name = format!("analysis_results/{}.json", analysis_id);
   |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
50 -         let object_name = format!("analysis_results/{}.json", analysis_id);
50 +         let object_name = format!("analysis_results/{analysis_id}.json");
   |
--
  --> src/storage/storage.rs:94:27
   |
94 |         let object_name = format!("analysis_results/{}.json", analysis_id);
   |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
94 -         let object_name = format!("analysis_results/{}.json", analysis_id);
94 +         let object_name = format!("analysis_results/{analysis_id}.json");
   |
--
  --> src/storage/pubsub.rs:35:30
   |
35 |             let topic_path = format!("projects/{}/topics/{}", project_id, topic_name);
   |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
35 -             let topic_path = format!("projects/{}/topics/{}", project_id, topic_name);
35 +             let topic_path = format!("projects/{project_id}/topics/{topic_name}");
   |
--
   --> src/storage/pubsub.rs:186:39
    |
186 |                         warnings.push(format!("Cannot verify topic {} due to missing pubsub.topics.get permission", name));
    |                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
186 -                         warnings.push(format!("Cannot verify topic {} due to missing pubsub.topics.get permission", name));
186 +                         warnings.push(format!("Cannot verify topic {name} due to missing pubsub.topics.get permission"));
    |
--
  --> src/storage/redis_client.rs:55:30
   |
55 |         let rate_limit_key = format!("rate_limit:{}", key);
   |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
55 -         let rate_limit_key = format!("rate_limit:{}", key);
55 +         let rate_limit_key = format!("rate_limit:{key}");
   |
--
   --> src/storage/redis_client.rs:152:31
    |
152 |         let _rate_limit_key = format!("rate_limit:{}", key);
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
152 -         let _rate_limit_key = format!("rate_limit:{}", key);
152 +         let _rate_limit_key = format!("rate_limit:{key}");
    |
--
   --> src/storage/redis_client.rs:167:29
    |
167 |             let burst_key = format!("burst:{}", key);
    |                             ^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
167 -             let burst_key = format!("burst:{}", key);
167 +             let burst_key = format!("burst:{key}");
    |
--
   --> src/storage/redis_client.rs:198:30
    |
198 |         let rate_limit_key = format!("rate_limit:{}", key);
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
198 -         let rate_limit_key = format!("rate_limit:{}", key);
198 +         let rate_limit_key = format!("rate_limit:{key}");
    |
--
   --> src/storage/redis_client.rs:230:30
    |
230 |         let rate_limit_key = format!("rate_limit:{}", key);
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
230 -         let rate_limit_key = format!("rate_limit:{}", key);
230 +         let rate_limit_key = format!("rate_limit:{key}");
    |
--
   --> src/storage/redis_client.rs:231:25
    |
231 |         let burst_key = format!("burst:{}", key);
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
231 -         let burst_key = format!("burst:{}", key);
231 +         let burst_key = format!("burst:{key}");
    |
--
   --> src/storage/cache.rs:366:29
    |
366 |             let cache_key = format!("{}{}:main", ANALYSIS_CACHE_PREFIX, repo_url);
    |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
366 -             let cache_key = format!("{}{}:main", ANALYSIS_CACHE_PREFIX, repo_url);
366 +             let cache_key = format!("{ANALYSIS_CACHE_PREFIX}{repo_url}:main");
    |
--
   --> src/storage/cache.rs:397:19
    |
397 |         let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
397 -         let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
397 +         let key = format!("{ANALYSIS_CACHE_PREFIX}{analysis_id}");
    |
--
   --> src/storage/cache.rs:504:23
    |
504 |             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
504 -             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
504 +             let key = format!("{ANALYSIS_CACHE_PREFIX}{analysis_id}");
    |
--
   --> src/storage/cache.rs:537:23
    |
537 |             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
537 -             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
537 +             let key = format!("{ANALYSIS_CACHE_PREFIX}{analysis_id}");
    |
--
   --> src/storage/cache.rs:556:23
    |
556 |             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
556 -             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
556 +             let key = format!("{ANALYSIS_CACHE_PREFIX}{analysis_id}");
    |
--
   --> src/storage/cache.rs:584:23
    |
584 |             let key = format!("{}{}", PATTERN_CACHE_PREFIX, file_hash);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
584 -             let key = format!("{}{}", PATTERN_CACHE_PREFIX, file_hash);
584 +             let key = format!("{PATTERN_CACHE_PREFIX}{file_hash}");
    |
--
   --> src/storage/cache.rs:605:23
    |
605 |             let key = format!("{}{}", PATTERN_CACHE_PREFIX, file_hash);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
605 -             let key = format!("{}{}", PATTERN_CACHE_PREFIX, file_hash);
605 +             let key = format!("{PATTERN_CACHE_PREFIX}{file_hash}");
    |
--
   --> src/storage/cache.rs:625:23
    |
625 |             let key = format!("{}{}", EMBEDDING_CACHE_PREFIX, chunk_id);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
625 -             let key = format!("{}{}", EMBEDDING_CACHE_PREFIX, chunk_id);
625 +             let key = format!("{EMBEDDING_CACHE_PREFIX}{chunk_id}");
    |
--
   --> src/storage/cache.rs:646:23
    |
646 |             let key = format!("{}{}", EMBEDDING_CACHE_PREFIX, chunk_id);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
646 -             let key = format!("{}{}", EMBEDDING_CACHE_PREFIX, chunk_id);
646 +             let key = format!("{EMBEDDING_CACHE_PREFIX}{chunk_id}");
    |
--
   --> src/storage/cache.rs:672:23
    |
672 |             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
672 -             let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
672 +             let key = format!("{ANALYSIS_CACHE_PREFIX}{analysis_id}");
    |
--
  --> src/git/mod.rs:21:40
   |
21 |         let clone_path = PathBuf::from(format!("/tmp/ccl-analysis/{}", analysis_id));
   |                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
21 -         let clone_path = PathBuf::from(format!("/tmp/ccl-analysis/{}", analysis_id));
21 +         let clone_path = PathBuf::from(format!("/tmp/ccl-analysis/{analysis_id}"));
   |
--
  --> src/git/mod.rs:32:32
   |
32 |                 let key_path = format!("{}/.ssh/id_rsa", home);
   |                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
32 -                 let key_path = format!("{}/.ssh/id_rsa", home);
32 +                 let key_path = format!("{home}/.ssh/id_rsa");
   |
--
  --> src/git/mod.rs:55:22
   |
55 |             .context(format!("Failed to clone repository: {}", url))?;
   |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
55 -             .context(format!("Failed to clone repository: {}", url))?;
55 +             .context(format!("Failed to clone repository: {url}"))?;
   |
--
  --> src/git/mod.rs:86:39
   |
86 |         let temp_path = PathBuf::from(format!("/tmp/ccl-hash-check/{}", temp_id));
   |                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
86 -         let temp_path = PathBuf::from(format!("/tmp/ccl-hash-check/{}", temp_id));
86 +         let temp_path = PathBuf::from(format!("/tmp/ccl-hash-check/{temp_id}"));
   |
--
   --> src/git/mod.rs:105:32
    |
105 |                 let key_path = format!("{}/.ssh/id_rsa", home);
    |                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
105 -                 let key_path = format!("{}/.ssh/id_rsa", home);
105 +                 let key_path = format!("{home}/.ssh/id_rsa");
    |
--
   --> src/git/mod.rs:131:22
    |
131 |             .context(format!("Failed to shallow clone repository for hash check: {}", url))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
131 -             .context(format!("Failed to shallow clone repository for hash check: {}", url))?;
131 +             .context(format!("Failed to shallow clone repository for hash check: {url}"))?;
    |
--
   --> src/parser/adapters.rs:183:39
    |
183 | ...                   name: format!("{}.{}", name, attr_name),
    |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
183 -                                 name: format!("{}.{}", name, attr_name),
183 +                                 name: format!("{name}.{attr_name}"),
    |
--
   --> src/parser/adapters.rs:209:34
    |
209 |                         message: format!("XML parse error: {}", e),
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
209 -                         message: format!("XML parse error: {}", e),
209 +                         message: format!("XML parse error: {e}"),
    |
--
   --> src/parser/adapters.rs:241:26
    |
241 |                 message: format!("TOML parse error: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
241 -                 message: format!("TOML parse error: {}", e),
241 +                 message: format!("TOML parse error: {e}"),
    |
--
   --> src/parser/adapters.rs:255:21
    |
255 |                     format!("{}.{}", prefix, key)
    |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
255 -                     format!("{}.{}", prefix, key)
255 +                     format!("{prefix}.{key}")
    |
--
   --> src/parser/adapters.rs:353:31
    |
353 |                         name: format!("code_block_{}", lang),
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
353 -                         name: format!("code_block_{}", lang),
353 +                         name: format!("code_block_{lang}"),
    |
--
   --> src/parser/adapters.rs:369:45
    |
369 |                         documentation: Some(format!("Code block in {}", lang)),
    |                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
369 -                         documentation: Some(format!("Code block in {}", lang)),
369 +                         documentation: Some(format!("Code block in {lang}")),
    |
--
   --> src/parser/adapters.rs:420:37
    |
420 |                     signature: Some(format!("H{} {}", level, title)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
420 -                     signature: Some(format!("H{} {}", level, title)),
420 +                     signature: Some(format!("H{level} {title}")),
    |
--
   --> src/parser/adapters.rs:421:41
    |
421 |                     documentation: Some(format!("Heading level {} in documentation", level)),
    |                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
421 -                     documentation: Some(format!("Heading level {} in documentation", level)),
421 +                     documentation: Some(format!("Heading level {level} in documentation")),
    |
--
   --> src/parser/adapters.rs:433:27
    |
433 |                     name: format!("task: {}", task_text),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
433 -                     name: format!("task: {}", task_text),
433 +                     name: format!("task: {task_text}"),
    |
--
   --> src/parser/adapters.rs:460:27
    |
460 |                     name: format!("list_item: {}", item_text),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
460 -                     name: format!("list_item: {}", item_text),
460 +                     name: format!("list_item: {item_text}"),
    |
--
   --> src/parser/adapters.rs:475:37
    |
475 |                     signature: Some(format!("List item: {}", item_text)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
475 -                     signature: Some(format!("List item: {}", item_text)),
475 +                     signature: Some(format!("List item: {item_text}")),
    |
--
   --> src/parser/adapters.rs:485:31
    |
485 |                         name: format!("table_row_{}", current_line),
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
485 -                         name: format!("table_row_{}", current_line),
485 +                         name: format!("table_row_{current_line}"),
    |
--
   --> src/parser/adapters.rs:512:27
    |
512 |                     name: format!("link: {}", link_text),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
512 -                     name: format!("link: {}", link_text),
512 +                     name: format!("link: {link_text}"),
    |
--
   --> src/parser/adapters.rs:527:37
    |
527 |                     signature: Some(format!("Link: {} -> {}", link_text, link_url)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
527 -                     signature: Some(format!("Link: {} -> {}", link_text, link_url)),
527 +                     signature: Some(format!("Link: {link_text} -> {link_url}")),
    |
--
   --> src/parser/adapters.rs:538:27
    |
538 |                     name: format!("image: {}", alt_text),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
538 -                     name: format!("image: {}", alt_text),
538 +                     name: format!("image: {alt_text}"),
    |
--
   --> src/parser/adapters.rs:553:37
    |
553 |                     signature: Some(format!("Image: {} -> {}", alt_text, image_url)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
553 -                     signature: Some(format!("Image: {} -> {}", alt_text, image_url)),
553 +                     signature: Some(format!("Image: {alt_text} -> {image_url}")),
    |
--
   --> src/parser/adapters.rs:564:27
    |
564 |                     name: format!("reference: {}", ref_name),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
564 -                     name: format!("reference: {}", ref_name),
564 +                     name: format!("reference: {ref_name}"),
    |
--
   --> src/parser/adapters.rs:579:37
    |
579 |                     signature: Some(format!("Reference: {} -> {}", ref_name, ref_url)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
579 -                     signature: Some(format!("Reference: {} -> {}", ref_name, ref_url)),
579 +                     signature: Some(format!("Reference: {ref_name} -> {ref_url}")),
    |
--
   --> src/parser/adapters.rs:589:27
    |
589 |                     name: format!("inline_code: {}", code),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
589 -                     name: format!("inline_code: {}", code),
589 +                     name: format!("inline_code: {code}"),
    |
--
   --> src/parser/adapters.rs:604:37
    |
604 |                     signature: Some(format!("Inline code: {}", code)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
604 -                     signature: Some(format!("Inline code: {}", code)),
604 +                     signature: Some(format!("Inline code: {code}")),
    |
--
  --> src/parser/validation_demo.rs:13:13
   |
13 |             println!("❌ Failed to create parser: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
13 -             println!("❌ Failed to create parser: {}", e);
13 +             println!("❌ Failed to create parser: {e}");
   |
--
  --> src/parser/validation_demo.rs:64:21
   |
64 |                     println!("✅ {} → {}", filename, detected_lang);
   |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
64 -                     println!("✅ {} → {}", filename, detected_lang);
64 +                     println!("✅ {filename} → {detected_lang}");
   |
--
68 | |                         "⚠️  {} → {} (expected {})",
69 | |                         filename, detected_lang, expected_lang
70 | |                     );
   | |_____________________^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
  --> src/parser/validation_demo.rs:81:5
   |
81 |     println!("Successfully detected: {}", detected_count);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
81 -     println!("Successfully detected: {}", detected_count);
81 +     println!("Successfully detected: {detected_count}");
   |
--
  --> src/parser/validation_demo.rs:93:9
   |
93 |         println!("• {}", lang);
   |         ^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
93 -         println!("• {}", lang);
93 +         println!("• {lang}");
   |
--
   --> src/parser/validation_demo.rs:110:18
    |
110 |             _ => format!("test.{}", lang),
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
110 -             _ => format!("test.{}", lang),
110 +             _ => format!("test.{lang}"),
    |
--
  --> src/parser/ast/chunk_extractor.rs:94:24
   |
94 |         let chunk_id = format!("chunk_{:016x}", counter);
   |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
94 -         let chunk_id = format!("chunk_{:016x}", counter);
94 +         let chunk_id = format!("chunk_{counter:016x}");
   |
--
  --> src/parser/streaming/hasher.rs:24:9
   |
24 |         format!("{:x}", result)
   |         ^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
24 -         format!("{:x}", result)
24 +         format!("{result:x}")
   |
--
  --> src/parser/mod.rs:98:26
   |
98 |                 message: format!("Failed to read file metadata: {}", e),
   |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
98 -                 message: format!("Failed to read file metadata: {}", e),
98 +                 message: format!("Failed to read file metadata: {e}"),
   |
--
   --> src/parser/mod.rs:153:30
    |
153 |                     message: format!("Failed to detect language: {}", e),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
153 -                     message: format!("Failed to detect language: {}", e),
153 +                     message: format!("Failed to detect language: {e}"),
    |
--
   --> src/parser/mod.rs:159:30
    |
159 |                     message: format!("Could not detect language for file: {}", file_name),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
159 -                     message: format!("Could not detect language for file: {}", file_name),
159 +                     message: format!("Could not detect language for file: {file_name}"),
    |
--
   --> src/parser/mod.rs:177:26
    |
177 |                 message: format!("Failed to get parser from pool: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
177 -                 message: format!("Failed to get parser from pool: {}", e),
177 +                 message: format!("Failed to get parser from pool: {e}"),
    |
--
   --> src/parser/mod.rs:230:26
    |
230 |                 message: format!("Failed to read file: {}", e),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
230 -                 message: format!("Failed to read file: {}", e),
230 +                 message: format!("Failed to read file: {e}"),
    |
--
   --> src/parser/mod.rs:268:26
    |
268 |                 message: format!("No custom parser for language: {}", language),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
268 -                 message: format!("No custom parser for language: {}", language),
268 +                 message: format!("No custom parser for language: {language}"),
    |
--
   --> src/parser/mod.rs:459:26
    |
459 |                 message: format!("Language not supported: {}", language_name),
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
459 -                 message: format!("Language not supported: {}", language_name),
459 +                 message: format!("Language not supported: {language_name}"),
    |
--
   --> src/parser/mod.rs:469:30
    |
469 |                     message: format!("Failed to create parser pool: {}", e),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
469 -                     message: format!("Failed to create parser pool: {}", e),
469 +                     message: format!("Failed to create parser pool: {e}"),
    |
--
   --> src/parser/mod.rs:496:30
    |
496 |                     message: format!("Failed to preload parsers: {}", e),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
496 -                     message: format!("Failed to preload parsers: {}", e),
496 +                     message: format!("Failed to preload parsers: {e}"),
    |
--
   --> src/contracts.rs:442:56
    |
442 |         visibility: symbol.visibility.as_ref().map(|v| format!("{:?}", v)),
    |                                                        ^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
442 -         visibility: symbol.visibility.as_ref().map(|v| format!("{:?}", v)),
442 +         visibility: symbol.visibility.as_ref().map(|v| format!("{v:?}")),
    |
--
   --> src/errors.rs:193:33
    |
193 |         AnalysisError::Internal(format!("IO error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
193 -         AnalysisError::Internal(format!("IO error: {}", err))
193 +         AnalysisError::Internal(format!("IO error: {err}"))
    |
--
   --> src/errors.rs:199:33
    |
199 |         ParserError::TreeSitter(format!("IO error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
199 -         ParserError::TreeSitter(format!("IO error: {}", err))
199 +         ParserError::TreeSitter(format!("IO error: {err}"))
    |
--
   --> src/errors.rs:205:40
    |
205 |         StorageError::ConnectionFailed(format!("IO error: {}", err))
    |                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
205 -         StorageError::ConnectionFailed(format!("IO error: {}", err))
205 +         StorageError::ConnectionFailed(format!("IO error: {err}"))
    |
--
   --> src/errors.rs:217:33
    |
217 |         AnalysisError::Internal(format!("JSON parsing error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
217 -         AnalysisError::Internal(format!("JSON parsing error: {}", err))
217 +         AnalysisError::Internal(format!("JSON parsing error: {err}"))
    |
--
   --> src/errors.rs:235:33
    |
235 |         AnalysisError::Internal(format!("TOML parsing error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
235 -         AnalysisError::Internal(format!("TOML parsing error: {}", err))
235 +         AnalysisError::Internal(format!("TOML parsing error: {err}"))
    |
--
   --> src/errors.rs:292:33
    |
292 |         AnalysisError::Internal(format!("XML parsing error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
292 -         AnalysisError::Internal(format!("XML parsing error: {}", err))
292 +         AnalysisError::Internal(format!("XML parsing error: {err}"))
    |
--
   --> src/errors.rs:298:33
    |
298 |         AnalysisError::Internal(format!("Regex error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
298 -         AnalysisError::Internal(format!("Regex error: {}", err))
298 +         AnalysisError::Internal(format!("Regex error: {err}"))
    |
--
   --> src/errors.rs:304:33
    |
304 |         AnalysisError::Internal(format!("UTF-8 error: {}", err))
    |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
304 -         AnalysisError::Internal(format!("UTF-8 error: {}", err))
304 +         AnalysisError::Internal(format!("UTF-8 error: {err}"))
    |
--
   --> src/errors.rs:412:37
    |
412 |             AnalysisError::Internal(format!("{}: {}", context, base_error))
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
412 -             AnalysisError::Internal(format!("{}: {}", context, base_error))
412 +             AnalysisError::Internal(format!("{context}: {base_error}"))
    |
--
  --> src/migrations/mod.rs:88:38
   |
88 |                     .with_context(|| format!("Failed to read migration file: {:?}", path))?;
   |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
88 -                     .with_context(|| format!("Failed to read migration file: {:?}", path))?;
88 +                     .with_context(|| format!("Failed to read migration file: {path:?}"))?;
   |
--
  --> src/bin/test_parsers.rs:14:13
   |
14 |             println!("Failed to create parser: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
14 -             println!("Failed to create parser: {}", e);
14 +             println!("Failed to create parser: {e}");
   |
--
   --> src/bin/test_parsers.rs:123:9
    |
123 |         print!("Testing {} parser... ", lang);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
123 -         print!("Testing {} parser... ", lang);
123 +         print!("Testing {lang} parser... ");
    |
--
   --> src/bin/test_parsers.rs:148:13
    |
148 |             println!("✗ FAILED - couldn't write temp file: {}", e);
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
148 -             println!("✗ FAILED - couldn't write temp file: {}", e);
148 +             println!("✗ FAILED - couldn't write temp file: {e}");
    |
--
   --> src/bin/test_parsers.rs:162:17
    |
162 |                 println!("✗ FAILED - {}", e);
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
162 -                 println!("✗ FAILED - {}", e);
162 +                 println!("✗ FAILED - {e}");
    |
--
  --> src/bin/test_ai_services.rs:40:13
   |
40 |             eprintln!("✗ Failed to initialize AI services: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
40 -             eprintln!("✗ Failed to initialize AI services: {}", e);
40 +             eprintln!("✗ Failed to initialize AI services: {e}");
   |
--
  --> src/bin/test_ai_services.rs:51:13
   |
51 |             println!("✗ AI Pattern Detection test failed: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
51 -             println!("✗ AI Pattern Detection test failed: {}", e);
51 +             println!("✗ AI Pattern Detection test failed: {e}");
   |
--
  --> src/bin/test_ai_services.rs:64:13
   |
64 |             println!("✗ Code Quality Assessment test failed: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
64 -             println!("✗ Code Quality Assessment test failed: {}", e);
64 +             println!("✗ Code Quality Assessment test failed: {e}");
   |
--
  --> src/bin/test_ai_services.rs:76:13
   |
76 |             println!("✗ Semantic Search test failed: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
76 -             println!("✗ Semantic Search test failed: {}", e);
76 +             println!("✗ Semantic Search test failed: {e}");
   |
--
  --> src/bin/test_ai_services.rs:88:13
   |
88 |             println!("✗ Repository Insights test failed: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
88 -             println!("✗ Repository Insights test failed: {}", e);
88 +             println!("✗ Repository Insights test failed: {e}");
   |
--
  --> src/bin/test_unsafe_bindings.rs:33:13
   |
33 |             eprintln!("   ✗ Failed to load Rust: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
33 -             eprintln!("   ✗ Failed to load Rust: {}", e);
33 +             eprintln!("   ✗ Failed to load Rust: {e}");
   |
--
  --> src/bin/test_unsafe_bindings.rs:45:13
   |
45 |             eprintln!("   ✗ Failed to load Python: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
45 -             eprintln!("   ✗ Failed to load Python: {}", e);
45 +             eprintln!("   ✗ Failed to load Python: {e}");
   |
--
  --> src/bin/test_unsafe_bindings.rs:57:13
   |
57 |             eprintln!("   ✗ Failed to load JavaScript: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
57 -             eprintln!("   ✗ Failed to load JavaScript: {}", e);
57 +             eprintln!("   ✗ Failed to load JavaScript: {e}");
   |
--
  --> src/bin/test_unsafe_bindings.rs:70:13
   |
70 |             println!("   ✓ Correctly rejected unsupported language: {}", lang);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
70 -             println!("   ✓ Correctly rejected unsupported language: {}", lang);
70 +             println!("   ✓ Correctly rejected unsupported language: {lang}");
   |
--
  --> src/bin/test_unsafe_bindings.rs:73:13
   |
73 |             eprintln!("   ✗ Unexpected error type: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
73 -             eprintln!("   ✗ Unexpected error type: {}", e);
73 +             eprintln!("   ✗ Unexpected error type: {e}");
   |
--
  --> src/bin/test_unsafe_bindings.rs:92:17
   |
92 |                 eprintln!("\n   Failed to load {}: {}", lang_name, e);
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
92 -                 eprintln!("\n   Failed to load {}: {}", lang_name, e);
92 +                 eprintln!("\n   Failed to load {lang_name}: {e}");
   |
--
  --> src/bin/test_tree_sitter_apis.rs:24:5
   |
24 |     println!("Testing tree-sitter-{}:", name);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
24 -     println!("Testing tree-sitter-{}:", name);
24 +     println!("Testing tree-sitter-{name}:");
   |
--
  --> src/bin/test_tree_sitter_apis.rs:28:13
   |
28 |             println!("  - Language name: {:?}", language);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
28 -             println!("  - Language name: {:?}", language);
28 +             println!("  - Language name: {language:?}");
   |
--
  --> src/bin/test_language_api.rs:19:13
   |
19 |             println!("✓ {}: get_language(\"{}\") works", name, name);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
19 -             println!("✓ {}: get_language(\"{}\") works", name, name);
19 +             println!("✓ {name}: get_language(\"{name}\") works");
   |
--
  --> src/bin/test_language_api.rs:23:13
   |
23 |             println!("✗ {}: get_language(\"{}\") failed", name, name);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
23 -             println!("✗ {}: get_language(\"{}\") failed", name, name);
23 +             println!("✗ {name}: get_language(\"{name}\") failed");
   |
--
  --> src/main.rs:48:69
   |
48 |         .map_err(|e| analysis_engine::errors::AnalysisError::Config(format!("Invalid PORT: {}", e)))?;
   |                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
48 -         .map_err(|e| analysis_engine::errors::AnalysisError::Config(format!("Invalid PORT: {}", e)))?;
48 +         .map_err(|e| analysis_engine::errors::AnalysisError::Config(format!("Invalid PORT: {e}")))?;
   |
--
 --> src/bin/test_language_registry.rs:9:5
  |
9 |     println!("Languages: {:?}\n", languages);
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  |
  = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
  = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
  |
9 -     println!("Languages: {:?}\n", languages);
9 +     println!("Languages: {languages:?}\n");
  |
--
  --> src/bin/test_language_registry.rs:18:17
   |
18 |                 println!("✓ {} - loaded successfully", lang_name);
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
18 -                 println!("✓ {} - loaded successfully", lang_name);
18 +                 println!("✓ {lang_name} - loaded successfully");
   |
--
  --> src/bin/test_language_registry.rs:22:17
   |
22 |                 println!("✗ {} - FAILED to load", lang_name);
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
22 -                 println!("✗ {} - FAILED to load", lang_name);
22 +                 println!("✗ {lang_name} - FAILED to load");
   |
--
  --> src/bin/test_language_registry.rs:31:9
   |
31 |         println!("Failed: {:?}", failed_languages);
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
31 -         println!("Failed: {:?}", failed_languages);
31 +         println!("Failed: {failed_languages:?}");
   |
--
  --> src/bin/test_language_registry.rs:40:13
   |
40 |             println!("{}: Available ✓", lang);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
40 -             println!("{}: Available ✓", lang);
40 +             println!("{lang}: Available ✓");
   |
--
  --> src/bin/test_language_registry.rs:42:13
   |
42 |             println!("{}: Not available ✗", lang);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
42 -             println!("{}: Not available ✗", lang);
42 +             println!("{lang}: Not available ✗");
   |
--
   --> src/bin/../../tests/load_test.rs:187:42
    |
187 |                         let error_type = format!("{:?}", e);
    |                                          ^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
    = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
    |
187 -                         let error_type = format!("{:?}", e);
187 +                         let error_type = format!("{e:?}");
    |
--
   --> src/bin/../../tests/load_test.rs:422:35
    |
422 |             .map(|(error, count)| format!("- {}: {}", error, count))
    |                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
422 -             .map(|(error, count)| format!("- {}: {}", error, count))
422 +             .map(|(error, count)| format!("- {error}: {count}"))
    |
