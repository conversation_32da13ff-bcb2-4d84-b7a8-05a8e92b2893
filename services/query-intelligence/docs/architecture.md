# Query Intelligence Service - Architecture Documentation

## Overview

The Query Intelligence Service is a microservice designed for natural language query processing in the Episteme platform. It leverages Google's Gemini 2.5 models to understand developer queries and provide intelligent, context-aware responses about codebases.

## System Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────────┐
│                           API Gateway                                │
│                    (Authentication & Routing)                        │
└─────────────────────┬───────────────────────┬──────────────────────┘
                      │                       │
                      ▼                       ▼
         ┌────────────────────┐    ┌──────────────────┐
         │   REST API         │    │  WebSocket API   │
         │   (FastAPI)        │    │  (Streaming)     │
         └────────┬───────────┘    └────────┬─────────┘
                  │                          │
                  ▼                          ▼
         ┌────────────────────────────────────────────┐
         │          Query Processing Engine           │
         │  (NLP, Routing, Caching, Orchestration)   │
         └────────────────────┬───────────────────────┘
                              │
     ┌────────────────────────┴───────────────────────────┐
     │                                                    │
     ▼                                                    ▼
┌──────────────┐  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐
│   Gemini     │  │    Redis     │  │  PostgreSQL  │  │   External   │
│   Models     │  │    Cache     │  │   Database   │  │   Services   │
└──────────────┘  └──────────────┘  └──────────────┘  └──────────────┘
```

### Component Architecture

```
query-intelligence/
├── API Layer
│   ├── REST Controllers
│   ├── WebSocket Handlers
│   ├── Request Validation
│   └── Response Formatting
│
├── Business Logic Layer
│   ├── Query Processing
│   ├── Model Selection
│   ├── Result Ranking
│   └── Summary Generation
│
├── Integration Layer
│   ├── Gemini Client
│   ├── Analysis Engine Client
│   ├── Pattern Mining Client
│   └── Cache Manager
│
├── Data Layer
│   ├── Query Repository
│   ├── Feedback Store
│   ├── Metrics Storage
│   └── Configuration
│
└── Infrastructure Layer
    ├── Resilience Patterns
    ├── Monitoring & Metrics
    ├── Security Framework
    └── Configuration Management
```

## Core Components

### 1. API Layer

#### REST API (FastAPI)
- **Framework**: FastAPI 0.104+
- **Features**:
  - Automatic OpenAPI documentation
  - Request/response validation with Pydantic
  - Async request handling
  - Dependency injection

#### WebSocket API
- **Protocol**: WebSocket over TLS
- **Features**:
  - Real-time streaming responses
  - Bidirectional communication
  - Connection pooling
  - Automatic reconnection

### 2. Query Processing Engine

#### NLP Pipeline
```python
Query → Preprocessing → Intent Recognition → Entity Extraction → Model Selection → Result Generation
```

**Components**:
- **Preprocessor**: Cleans and normalizes queries
- **Intent Classifier**: Determines query type (search, explain, analyze)
- **Entity Extractor**: Identifies code entities, languages, patterns
- **Model Router**: Selects optimal Gemini model based on query

#### Model Selection Logic
```yaml
Model Selection Rules:
- Simple queries → Gemini 2.5 Flash Lite (fastest)
- Standard queries → Gemini 2.5 Flash (balanced)
- Complex queries → Gemini 2.5 Pro (highest accuracy)

Factors:
- Query complexity score
- Response time requirements
- Cost optimization
- Current load
```

### 3. Caching Strategy

#### Multi-Level Cache Architecture
```
L1: In-Memory Cache (LRU, 1000 entries)
 ↓
L2: Redis Cache (Distributed, TTL-based)
 ↓
L3: Database Cache (Persistent, long-term)
```

**Cache Key Generation**:
```python
cache_key = hash(
    query_normalized + 
    repository_id + 
    options_hash + 
    model_version
)
```

**Cache Invalidation**:
- TTL-based expiration (default: 3600s)
- Repository update triggers
- Manual invalidation API
- LRU eviction for memory cache

### 4. External Service Integration

#### Analysis Engine Integration
```python
# Asynchronous pattern with circuit breaker
async def get_code_context(file_path: str) -> CodeContext:
    async with circuit_breaker("analysis_engine"):
        return await analysis_client.get_context(
            file_path,
            timeout=5.0,
            retry_count=3
        )
```

#### Pattern Mining Integration
```python
# Bulkhead pattern for resource isolation
@bulkhead(max_concurrent=10)
async def find_similar_patterns(pattern: str) -> List[Pattern]:
    return await pattern_client.search(
        pattern,
        limit=20,
        threshold=0.8
    )
```

### 5. Data Models

#### Core Entities

**Query Model**:
```python
class Query(BaseModel):
    query_id: str
    text: str
    repository_id: str
    user_id: str
    timestamp: datetime
    options: QueryOptions
    results: List[QueryResult]
    metadata: QueryMetadata
```

**Query Result**:
```python
class QueryResult(BaseModel):
    file_path: str
    line_start: int
    line_end: int
    content: str
    relevance_score: float
    explanation: str
    language: str
    context: Optional[CodeContext]
```

## Resilience Architecture

### 1. Circuit Breaker Pattern
```python
Circuit States:
CLOSED → OPEN → HALF_OPEN → CLOSED

Thresholds:
- Failure threshold: 5 failures
- Success threshold: 2 successes
- Timeout: 60 seconds
- Min calls: 10
```

### 2. Rate Limiting
```python
Algorithms:
- Token Bucket (primary)
- Sliding Window (fallback)

Limits:
- Global: 60 req/min per IP
- Authenticated: 1000 req/hour per user
- Burst: 1.5x sustained rate
```

### 3. Retry Strategy
```python
Backoff Strategies:
- Exponential: 2^n * base_delay
- Linear: n * increment
- Fibonacci: fib(n) * base_delay

Configuration:
- Max attempts: 3
- Base delay: 1s
- Max delay: 60s
- Jitter: enabled
```

### 4. Bulkhead Isolation
```python
Resource Pools:
- Query Processing: 100 concurrent
- API Calls: 50 concurrent
- Database: 20 concurrent
- Cache: 200 concurrent
```

## Security Architecture

### 1. Authentication Flow
```
Client → API Gateway → JWT Validation → User Context → Authorized Request
```

### 2. Security Layers
- **Transport**: TLS 1.3 minimum
- **Authentication**: JWT tokens with RS256
- **Authorization**: RBAC with permissions
- **Input Validation**: Schema validation, sanitization
- **Output Filtering**: PII detection, data masking

### 3. Threat Mitigation
- **SQL Injection**: Parameterized queries only
- **XSS**: Output encoding, CSP headers
- **CSRF**: Token validation
- **DDoS**: Rate limiting, CloudFlare
- **Data Exposure**: Field-level encryption

## Performance Architecture

### 1. Optimization Strategies
- **Query Optimization**: Indexed searches, query planning
- **Caching**: Multi-level caching with warming
- **Connection Pooling**: Database, Redis, HTTP clients
- **Async Processing**: Non-blocking I/O throughout
- **Resource Management**: Memory limits, CPU throttling

### 2. Performance Targets
```yaml
Latency:
- p50: <100ms
- p95: <200ms
- p99: <500ms

Throughput:
- Sustained: 1000 QPS
- Peak: 2000 QPS
- Concurrent: 200 requests

Resource Usage:
- CPU: <70% at peak
- Memory: <2GB per instance
- Network: <100Mbps
```

## Monitoring Architecture

### 1. Metrics Collection
```
Service → MetricsCollector → Prometheus → Grafana
       ↓
   StatsD (optional)
```

### 2. Distributed Tracing
```
Request → OpenTelemetry → Trace Context → Jaeger/Zipkin
```

### 3. Key Metrics
- **Business**: Queries processed, user satisfaction
- **Performance**: Latency, throughput, errors
- **Resource**: CPU, memory, connections
- **Cache**: Hit rate, evictions, size
- **External**: Service health, latency

## Deployment Architecture

### 1. Container Strategy
```dockerfile
# Multi-stage build
Build Stage → Test Stage → Production Stage
    ↓             ↓              ↓
Dependencies   Security    Minimal Runtime
```

### 2. Kubernetes Deployment
```yaml
Deployment:
- Replicas: 3-10 (auto-scaling)
- Strategy: RollingUpdate
- Health checks: Liveness, Readiness
- Resource limits: CPU, Memory

Services:
- ClusterIP: Internal communication
- LoadBalancer: External access
- Horizontal Pod Autoscaler: CPU/Memory based
```

### 3. Environment Configuration
```
Development → Staging → Production
    ↓           ↓          ↓
 Local K8s   GKE Staging  GKE Production
```

## Data Flow

### 1. Query Processing Flow
```
1. Client sends query
2. API validates and authenticates
3. Check cache for existing results
4. If cache miss:
   a. Preprocess query
   b. Select Gemini model
   c. Generate embeddings
   d. Search code context
   e. Generate response
   f. Cache results
5. Return formatted response
6. Log metrics and analytics
```

### 2. Streaming Flow
```
1. WebSocket connection established
2. Client sends streaming query
3. Server processes in chunks:
   a. Stream code matches
   b. Stream explanations
   c. Generate summary
4. Send completion signal
5. Maintain connection for follow-ups
```

## Scalability Considerations

### 1. Horizontal Scaling
- **Stateless design**: No session affinity required
- **Shared cache**: Redis cluster for distributed caching
- **Load balancing**: Round-robin with health checks
- **Auto-scaling**: CPU and memory triggers

### 2. Vertical Scaling
- **Resource optimization**: Efficient memory usage
- **Connection pooling**: Reuse expensive connections
- **Batch processing**: Group similar operations
- **Lazy loading**: Load resources on demand

### 3. Data Partitioning
- **Cache sharding**: Consistent hashing
- **Query routing**: Repository-based routing
- **Read replicas**: For database scaling
- **CDN integration**: For static resources

## Future Architecture Considerations

### 1. Multi-Region Deployment
```
US-Central ←→ EU-West ←→ Asia-Pacific
    ↓            ↓           ↓
Regional Cache  Regional DB  Regional Services
```

### 2. Event-Driven Architecture
```
Query Events → Kafka/PubSub → Stream Processing → Real-time Analytics
```

### 3. ML Pipeline Integration
```
Feedback → Training Pipeline → Model Updates → A/B Testing → Production
```

---

**Version**: 1.0.0  
**Last Updated**: December 2024  
**Architecture Review**: Approved