# Query Intelligence Service - API Documentation

## Overview

The Query Intelligence Service provides REST and WebSocket APIs for natural language query processing. This documentation covers all endpoints, request/response formats, authentication, and usage examples.

## Base URL

- **Production**: `https://query-intelligence.episteme.ai`
- **Staging**: `https://query-intelligence-staging.episteme.ai`
- **Local**: `http://localhost:8001`

## Authentication

### JWT Authentication

Most endpoints require JWT authentication. Include the token in the Authorization header:

```http
Authorization: Bearer <jwt_token>
```

### API Key Authentication

For service-to-service communication:

```http
X-API-Key: <api_key>
```

## REST API Endpoints

### Health & Status

#### GET /health

Service health check endpoint.

**Response**:
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2024-12-01T10:00:00Z"
}
```

#### GET /health/ready

Readiness probe with dependency checks.

**Response**:
```json
{
  "status": "ready",
  "checks": {
    "database": "healthy",
    "redis": "healthy",
    "analysis_engine": "healthy",
    "pattern_mining": "healthy"
  },
  "timestamp": "2024-12-01T10:00:00Z"
}
```

#### GET /health/live

Liveness probe for Kubernetes.

**Response**:
```json
{
  "status": "alive",
  "uptime_seconds": 3600
}
```

### Query Processing

#### POST /api/v1/query

Process a natural language query about code.

**Request**:
```json
{
  "query": "How does authentication work in this service?",
  "repository_id": "episteme-core",
  "options": {
    "include_context": true,
    "max_results": 20,
    "search_depth": "deep",
    "language_filter": ["python", "typescript"]
  }
}
```

**Response**:
```json
{
  "query_id": "q_1234567890",
  "query": "How does authentication work in this service?",
  "results": [
    {
      "file_path": "src/auth/jwt_manager.py",
      "line_start": 45,
      "line_end": 89,
      "content": "class JWTManager:\n    def authenticate(self, token: str) -> User:...",
      "relevance_score": 0.95,
      "explanation": "This class handles JWT token validation and user authentication.",
      "language": "python"
    }
  ],
  "summary": "Authentication in this service uses JWT tokens with HS256 algorithm. The JWTManager class validates tokens and extracts user information.",
  "metadata": {
    "processing_time_ms": 187,
    "model_used": "gemini-2.5-flash",
    "cache_hit": false,
    "total_results": 15
  }
}
```

#### GET /api/v1/queries

Get query history with pagination.

**Query Parameters**:
- `limit` (integer, default: 20): Number of results per page
- `offset` (integer, default: 0): Pagination offset
- `repository_id` (string, optional): Filter by repository
- `start_date` (ISO 8601, optional): Filter by date range
- `end_date` (ISO 8601, optional): Filter by date range

**Response**:
```json
{
  "queries": [
    {
      "query_id": "q_1234567890",
      "query": "How does authentication work?",
      "repository_id": "episteme-core",
      "timestamp": "2024-12-01T10:00:00Z",
      "processing_time_ms": 187,
      "result_count": 15
    }
  ],
  "pagination": {
    "total": 150,
    "limit": 20,
    "offset": 0,
    "has_next": true
  }
}
```

#### GET /api/v1/query/{query_id}

Get detailed information about a specific query.

**Response**:
```json
{
  "query_id": "q_1234567890",
  "query": "How does authentication work?",
  "repository_id": "episteme-core",
  "timestamp": "2024-12-01T10:00:00Z",
  "results": [...],
  "summary": "...",
  "metadata": {
    "processing_time_ms": 187,
    "model_used": "gemini-2.5-flash",
    "cache_hit": false,
    "total_results": 15
  }
}
```

### Feedback & Improvement

#### POST /api/v1/query/{query_id}/feedback

Submit feedback for a query result.

**Request**:
```json
{
  "rating": 5,
  "helpful": true,
  "comment": "Very accurate results",
  "result_feedback": [
    {
      "result_index": 0,
      "relevant": true,
      "helpful": true
    }
  ]
}
```

**Response**:
```json
{
  "feedback_id": "f_1234567890",
  "status": "recorded",
  "message": "Thank you for your feedback"
}
```

### Monitoring & Metrics

#### GET /metrics

Prometheus metrics endpoint.

**Response** (Prometheus format):
```
# HELP query_intelligence_requests_total Total number of requests
# TYPE query_intelligence_requests_total counter
query_intelligence_requests_total{method="POST",endpoint="/api/v1/query",status="200"} 1234

# HELP query_intelligence_response_time_seconds Response time in seconds
# TYPE query_intelligence_response_time_seconds histogram
query_intelligence_response_time_seconds_bucket{le="0.1"} 456
query_intelligence_response_time_seconds_bucket{le="0.5"} 890
query_intelligence_response_time_seconds_bucket{le="1.0"} 950
```

#### GET /api/v1/sla/status

Get current SLA compliance status.

**Response**:
```json
{
  "sla_status": {
    "availability": {
      "current_value": 99.95,
      "target": 99.9,
      "status": "compliant",
      "unit": "percent"
    },
    "response_time_p95": {
      "current_value": 187,
      "target": 500,
      "status": "compliant",
      "unit": "ms"
    },
    "error_rate": {
      "current_value": 0.12,
      "target": 1.0,
      "status": "compliant",
      "unit": "percent"
    }
  },
  "overall_status": "compliant",
  "measurement_period": "last_24_hours"
}
```

## WebSocket API

### Connection

Connect to the WebSocket endpoint for real-time streaming:

```javascript
const ws = new WebSocket('wss://query-intelligence.episteme.ai/api/v1/ws/query');

// Send authentication
ws.onopen = () => {
  ws.send(JSON.stringify({
    type: 'auth',
    token: 'your-jwt-token'
  }));
};
```

### Query Streaming

Send a query and receive streaming results:

```javascript
// Send query
ws.send(JSON.stringify({
  type: 'query',
  data: {
    query: "Explain the database schema",
    repository_id: "episteme-core",
    stream: true
  }
}));

// Receive streaming results
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  
  switch(message.type) {
    case 'result':
      console.log('New result:', message.data);
      break;
    case 'summary':
      console.log('Summary:', message.data);
      break;
    case 'complete':
      console.log('Query complete');
      break;
    case 'error':
      console.error('Error:', message.error);
      break;
  }
};
```

### Message Types

#### Authentication
```json
{
  "type": "auth",
  "token": "jwt-token"
}
```

#### Query Request
```json
{
  "type": "query",
  "data": {
    "query": "string",
    "repository_id": "string",
    "stream": true,
    "options": {
      "include_context": true,
      "max_results": 20
    }
  }
}
```

#### Result Messages
```json
{
  "type": "result",
  "data": {
    "index": 0,
    "file_path": "src/file.py",
    "content": "...",
    "relevance_score": 0.95
  }
}
```

#### Summary Message
```json
{
  "type": "summary",
  "data": {
    "summary": "...",
    "total_results": 15,
    "processing_time_ms": 187
  }
}
```

#### Error Message
```json
{
  "type": "error",
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded",
    "retry_after": 60
  }
}
```

## Error Responses

### Error Format

All errors follow a consistent format:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      "field": "Additional context"
    },
    "request_id": "req_1234567890"
  }
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `UNAUTHORIZED` | 401 | Missing or invalid authentication |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `VALIDATION_ERROR` | 400 | Invalid request data |
| `RATE_LIMIT_EXCEEDED` | 429 | Rate limit exceeded |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |
| `INTERNAL_ERROR` | 500 | Internal server error |

### Rate Limiting

Rate limit information is included in response headers:

```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1701432000
Retry-After: 60
```

## Request Examples

### cURL Examples

#### Basic Query
```bash
curl -X POST https://query-intelligence.episteme.ai/api/v1/query \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "How does authentication work?",
    "repository_id": "episteme-core"
  }'
```

#### Query with Options
```bash
curl -X POST https://query-intelligence.episteme.ai/api/v1/query \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Find all database models",
    "repository_id": "episteme-core",
    "options": {
      "include_context": true,
      "max_results": 50,
      "language_filter": ["python"],
      "search_depth": "deep"
    }
  }'
```

### Python Examples

```python
import requests

# Setup
base_url = "https://query-intelligence.episteme.ai"
headers = {
    "Authorization": f"Bearer {jwt_token}",
    "Content-Type": "application/json"
}

# Simple query
response = requests.post(
    f"{base_url}/api/v1/query",
    headers=headers,
    json={
        "query": "How does caching work?",
        "repository_id": "episteme-core"
    }
)

result = response.json()
print(f"Found {len(result['results'])} results")
print(f"Summary: {result['summary']}")

# Query with options
response = requests.post(
    f"{base_url}/api/v1/query",
    headers=headers,
    json={
        "query": "Find performance bottlenecks",
        "repository_id": "episteme-core",
        "options": {
            "include_context": True,
            "max_results": 30,
            "search_depth": "deep"
        }
    }
)
```

### JavaScript/TypeScript Examples

```typescript
// Using fetch
const queryCode = async (query: string, repositoryId: string) => {
  const response = await fetch('https://query-intelligence.episteme.ai/api/v1/query', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${jwtToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      query,
      repository_id: repositoryId
    })
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return await response.json();
};

// WebSocket streaming
class QueryClient {
  private ws: WebSocket;
  
  constructor(token: string) {
    this.ws = new WebSocket('wss://query-intelligence.episteme.ai/api/v1/ws/query');
    
    this.ws.onopen = () => {
      this.authenticate(token);
    };
  }
  
  private authenticate(token: string) {
    this.ws.send(JSON.stringify({
      type: 'auth',
      token
    }));
  }
  
  query(query: string, repositoryId: string, onResult: (result: any) => void) {
    this.ws.send(JSON.stringify({
      type: 'query',
      data: {
        query,
        repository_id: repositoryId,
        stream: true
      }
    }));
    
    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      if (message.type === 'result') {
        onResult(message.data);
      }
    };
  }
}
```

## Best Practices

### 1. Authentication
- Store JWT tokens securely
- Refresh tokens before expiration
- Use API keys for service accounts only

### 2. Rate Limiting
- Implement exponential backoff on 429 errors
- Monitor rate limit headers
- Use caching to reduce API calls

### 3. Error Handling
- Always check response status
- Parse error responses for details
- Implement proper retry logic

### 4. Performance
- Use streaming for large result sets
- Enable result caching when appropriate
- Batch related queries when possible

### 5. Security
- Always use HTTPS in production
- Validate SSL certificates
- Never log authentication tokens

---

**API Version**: 1.0.0  
**Last Updated**: December 2024  
**Contact**: <EMAIL>