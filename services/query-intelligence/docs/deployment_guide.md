# Query Intelligence Service - Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Query Intelligence Service across different environments with best practices for security, performance, and reliability.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Local Development](#local-development)
4. [Staging Deployment](#staging-deployment)
5. [Production Deployment](#production-deployment)
6. [Post-Deployment](#post-deployment)
7. [Rollback Procedures](#rollback-procedures)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Tools

```bash
# Check required tools
docker --version           # Docker 20.10+
docker-compose --version   # Docker Compose 2.0+
kubectl version --client   # Kubernetes 1.24+
gcloud --version          # Google Cloud SDK 400+
poetry --version          # Poetry 1.5+
python --version          # Python 3.11+
```

### Access Requirements

- **GCP Project Access**: `vibe-match-463114`
- **Kubernetes Cluster**: `episteme-prod` 
- **Container Registry**: `gcr.io/vibe-match-463114`
- **Service Accounts**: Query Intelligence service account

### Environment Credentials

```bash
# Authenticate with GCP
gcloud auth login
gcloud config set project vibe-match-463114

# Configure Docker for GCR
gcloud auth configure-docker

# Get Kubernetes credentials
gcloud container clusters get-credentials episteme-prod --region us-central1
```

## Environment Setup

### Configuration Files

1. **Base Configuration** (`config/config.yaml`):
```yaml
service_name: query-intelligence
service_version: 1.0.0

# API Configuration
api_host: 0.0.0.0
api_port: 8001
api_workers: 4

# Performance
max_concurrent_queries: 100
query_timeout: 30.0
cache_ttl: 3600

# Security
jwt_algorithm: HS256
cors_allowed_origins:
  - https://ccl.episteme.ai
  - https://api.episteme.ai
```

2. **Environment-Specific Configs**:

**Staging** (`config/config.staging.yaml`):
```yaml
environment: staging
api_workers: 2

# External Services
database_url: ${DATABASE_URL}
redis_url: redis://redis-staging:6379
analysis_engine_url: http://analysis-engine-staging:8001
pattern_mining_url: http://pattern-mining-staging:8002

# Monitoring
tracing_sample_rate: 0.5
metrics_enabled: true
```

**Production** (`config/config.production.yaml`):
```yaml
environment: production
api_workers: 4

# External Services
database_url: ${DATABASE_URL}
redis_url: redis://redis-prod:6379
analysis_engine_url: http://analysis-engine:8001
pattern_mining_url: http://pattern-mining:8002

# Monitoring
tracing_sample_rate: 0.1
metrics_enabled: true

# Performance
rate_limit_requests_per_minute: 60
max_concurrent_queries: 200
```

### Secrets Management

1. **Create Secrets in Kubernetes**:
```bash
# Create namespace if needed
kubectl create namespace episteme

# Create secret for JWT
kubectl create secret generic query-intelligence-jwt \
  --from-literal=jwt-secret-key='your-super-secret-key' \
  -n episteme

# Create secret for database
kubectl create secret generic query-intelligence-db \
  --from-literal=database-url='********************************/db' \
  -n episteme

# Create secret for API keys
kubectl create secret generic query-intelligence-api \
  --from-literal=google-api-key='your-google-api-key' \
  --from-literal=pinecone-api-key='your-pinecone-api-key' \
  -n episteme
```

## Local Development

### Quick Start

```bash
# 1. Clone repository
git clone https://github.com/episteme/stockholm.git
cd stockholm/services/query-intelligence

# 2. Install dependencies
poetry install

# 3. Set up environment
cp .env.example .env
# Edit .env with local settings

# 4. Start dependencies
docker-compose -f docker-compose.dev.yml up -d

# 5. Run database migrations
poetry run alembic upgrade head

# 6. Start service
poetry run uvicorn src.query_intelligence.main:app --reload --port 8001
```

### Docker Development

```bash
# Build development image
docker build -f Dockerfile.dev -t query-intelligence:dev .

# Run with hot reload
docker run -it --rm \
  -p 8001:8001 \
  -v $(pwd):/app \
  -e ENVIRONMENT=development \
  query-intelligence:dev
```

## Staging Deployment

### 1. Build and Test

```bash
# Run tests
poetry run pytest --cov=src/query_intelligence

# Build production image
docker build -t query-intelligence:staging-$(git rev-parse --short HEAD) .

# Run security scan
docker scan query-intelligence:staging-$(git rev-parse --short HEAD)
```

### 2. Push to Registry

```bash
# Tag for staging
docker tag query-intelligence:staging-$(git rev-parse --short HEAD) \
  gcr.io/vibe-match-463114/query-intelligence:staging

# Push to GCR
docker push gcr.io/vibe-match-463114/query-intelligence:staging
```

### 3. Deploy to Staging

```bash
# Update staging deployment
kubectl set image deployment/query-intelligence \
  query-intelligence=gcr.io/vibe-match-463114/query-intelligence:staging \
  -n episteme-staging

# Wait for rollout
kubectl rollout status deployment/query-intelligence -n episteme-staging

# Verify pods
kubectl get pods -l app=query-intelligence -n episteme-staging
```

### 4. Staging Validation

```bash
# Check health
curl https://query-intelligence-staging.episteme.ai/health

# Run smoke tests
./scripts/run_smoke_tests.sh staging

# Check metrics
curl https://query-intelligence-staging.episteme.ai/metrics
```

## Production Deployment

### 1. Pre-Deployment Checklist

```bash
# Run pre-deployment script
./scripts/pre_deployment_check.sh

# Verify checklist
- [ ] All tests passing
- [ ] Security scan clean
- [ ] Performance benchmarks met
- [ ] Documentation updated
- [ ] Rollback plan ready
```

### 2. Build Production Image

```bash
# Get release version
VERSION=$(cat VERSION)

# Build with version tag
docker build \
  --build-arg VERSION=$VERSION \
  --build-arg BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ") \
  -t query-intelligence:$VERSION .

# Tag for production
docker tag query-intelligence:$VERSION \
  gcr.io/vibe-match-463114/query-intelligence:$VERSION

docker tag query-intelligence:$VERSION \
  gcr.io/vibe-match-463114/query-intelligence:latest
```

### 3. Push to Registry

```bash
# Push versioned image
docker push gcr.io/vibe-match-463114/query-intelligence:$VERSION

# Push latest
docker push gcr.io/vibe-match-463114/query-intelligence:latest
```

### 4. Deploy to Production

#### Option A: Automated Deployment

```bash
# Use deployment script
./scripts/deploy_production.py $VERSION

# The script will:
# 1. Validate prerequisites
# 2. Build and push image
# 3. Deploy with rolling update
# 4. Run health checks
# 5. Execute smoke tests
```

#### Option B: Manual Deployment

```bash
# 1. Update deployment
kubectl set image deployment/query-intelligence \
  query-intelligence=gcr.io/vibe-match-463114/query-intelligence:$VERSION \
  -n episteme

# 2. Monitor rollout
kubectl rollout status deployment/query-intelligence -n episteme --timeout=10m

# 3. Verify pods
kubectl get pods -l app=query-intelligence -n episteme -w
```

### 5. Production Validation

```bash
# Health checks
for i in {1..5}; do
  curl -f https://query-intelligence.episteme.ai/health || exit 1
  sleep 2
done

# Performance check
./scripts/performance_test.sh production

# SLA validation
curl https://query-intelligence.episteme.ai/api/v1/sla/status
```

## Post-Deployment

### 1. Monitor Deployment

```bash
# Watch metrics
watch -n 5 'kubectl top pods -l app=query-intelligence -n episteme'

# Check logs
kubectl logs -f deployment/query-intelligence -n episteme

# Monitor dashboard
open https://grafana.episteme.ai/d/query-intelligence
```

### 2. Verify SLAs

```yaml
# Expected SLAs
- Availability: >99.9%
- Response Time (p95): <500ms
- Error Rate: <1%
- Throughput: >1000 QPS
```

### 3. Update Documentation

```bash
# Update deployment log
echo "$(date): Deployed version $VERSION to production" >> deployments.log

# Update status page
./scripts/update_status_page.sh $VERSION
```

## Rollback Procedures

### Automatic Rollback

The deployment script includes automatic rollback on:
- Health check failures
- Error rate >5%
- Response time >2x baseline

### Manual Rollback

```bash
# 1. Quick rollback to previous version
kubectl rollout undo deployment/query-intelligence -n episteme

# 2. Rollback to specific revision
kubectl rollout history deployment/query-intelligence -n episteme
kubectl rollout undo deployment/query-intelligence --to-revision=42 -n episteme

# 3. Emergency rollback with image update
PREVIOUS_VERSION=$(cat VERSION.previous)
kubectl set image deployment/query-intelligence \
  query-intelligence=gcr.io/vibe-match-463114/query-intelligence:$PREVIOUS_VERSION \
  -n episteme
```

### Rollback Validation

```bash
# Verify rollback
kubectl rollout status deployment/query-intelligence -n episteme

# Check health
curl https://query-intelligence.episteme.ai/health

# Verify version
curl https://query-intelligence.episteme.ai/api/v1/version
```

## Troubleshooting

### Common Issues

#### 1. Deployment Stuck

```bash
# Check events
kubectl describe deployment query-intelligence -n episteme

# Check pod issues
kubectl describe pods -l app=query-intelligence -n episteme

# Force pod recreation
kubectl delete pods -l app=query-intelligence -n episteme
```

#### 2. Health Check Failures

```bash
# Check pod logs
kubectl logs -l app=query-intelligence -n episteme --tail=100

# Test internal health
kubectl exec -it deployment/query-intelligence -n episteme -- curl localhost:8001/health

# Check dependencies
kubectl exec -it deployment/query-intelligence -n episteme -- curl analysis-engine:8001/health
```

#### 3. Performance Issues

```bash
# Check resource usage
kubectl top pods -l app=query-intelligence -n episteme

# Scale horizontally
kubectl scale deployment/query-intelligence --replicas=5 -n episteme

# Check cache
kubectl exec -it deployment/query-intelligence -n episteme -- redis-cli ping
```

### Emergency Procedures

#### Circuit Breaker Override

```bash
# Temporarily disable circuit breaker
kubectl set env deployment/query-intelligence \
  CIRCUIT_BREAKER_ENABLED=false -n episteme
```

#### Rate Limit Adjustment

```bash
# Increase rate limits temporarily
kubectl set env deployment/query-intelligence \
  RATE_LIMIT_REQUESTS_PER_MINUTE=120 -n episteme
```

#### Force Scale

```bash
# Emergency scaling
kubectl autoscale deployment/query-intelligence \
  --min=10 --max=50 --cpu-percent=50 -n episteme
```

## Best Practices

### 1. Deployment Windows
- **Staging**: Anytime, automated
- **Production**: Tuesday-Thursday, 10 AM - 4 PM PST
- **Emergency**: Follow incident response procedure

### 2. Communication
- Announce deployments in #deployments Slack channel
- Update status page before and after deployment
- Document any issues in deployment log

### 3. Monitoring
- Watch dashboards for 30 minutes post-deployment
- Monitor error rates and performance
- Check customer feedback channels

### 4. Documentation
- Update version in README.md
- Add deployment notes to CHANGELOG.md
- Update any changed configuration

---

**Last Updated**: December 2024  
**Version**: 1.0.0  
**Contact**: <EMAIL>