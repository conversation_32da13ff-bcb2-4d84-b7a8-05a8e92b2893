"""
Global test configuration and fixtures for query-intelligence service
"""

import pytest
import asyncio
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import os

# Configure pytest-asyncio for async tests
pytest_plugins = ["pytest_asyncio"]

# Set test environment variables
os.environ.update(
    {
        "ENVIRONMENT": "test",
        "LOG_LEVEL": "DEBUG",
        "REDIS_URL": "redis://localhost:6379",
        "ANALYSIS_ENGINE_URL": "http://localhost:8001",
        "USE_VERTEX_AI": "false",
        "GOOGLE_API_KEY": "test-api-key",
        "GEMINI_MODEL_NAME": "gemini-2.5-flash",
        "JWT_SECRET_KEY": "test-secret-key",
        "ENABLE_METRICS": "true",
        "SEMANTIC_CACHE_ENABLED": "false",
        "RATE_LIMIT_REQUESTS": "10000",  # Very high limit for tests
        "RATE_LIMIT_WINDOW_SECONDS": "3600",  # 1 hour window
        "PINECONE_API_KEY": "test-pinecone-key",
        "PINECONE_INDEX_NAME": "test-index",
    }
)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(autouse=True)
async def mock_external_services():
    """Mock all external service dependencies"""

    # Mock Redis client
    mock_redis = AsyncMock()
    mock_redis.ping = AsyncMock(return_value=True)
    mock_redis.get = AsyncMock(return_value=None)
    mock_redis.set = AsyncMock(return_value=True)
    mock_redis.aclose = AsyncMock()

    # Mock Analysis Engine client
    mock_analysis_client = AsyncMock()
    mock_analysis_client.health_check = AsyncMock(return_value=True)
    mock_analysis_client.search_embeddings = AsyncMock(
        return_value={"chunks": [], "total_count": 0}
    )
    mock_analysis_client.close = AsyncMock()

    # Mock Pinecone client
    mock_pinecone = MagicMock()
    mock_index = MagicMock()
    mock_index.query = MagicMock(return_value={"matches": [], "namespace": ""})
    mock_pinecone.Index = MagicMock(return_value=mock_index)

    # Mock Google GenAI Client
    mock_genai_client = MagicMock()
    mock_response = MagicMock()
    mock_response.text = "Test response from Gemini"
    mock_response.usage_metadata = MagicMock()
    mock_response.usage_metadata.prompt_token_count = 100
    mock_response.usage_metadata.candidates_token_count = 50
    mock_genai_client.models.generate_content = MagicMock(return_value=mock_response)

    # Mock sentence transformers
    mock_sentence_transformer = MagicMock()
    mock_sentence_transformer.encode = MagicMock(return_value=[[0.1] * 768])

    # Mock rate limiting middleware
    async def mock_rate_limit_middleware(request, call_next):
        # Always allow requests in tests
        return await call_next(request)

    with patch(
        "query_intelligence.clients.redis.get_redis_client", return_value=mock_redis
    ), patch(
        "query_intelligence.clients.analysis_engine.get_analysis_engine_client",
        return_value=mock_analysis_client,
    ), patch(
        "pinecone.Pinecone", return_value=mock_pinecone
    ), patch(
        "google.genai.Client", return_value=mock_genai_client
    ), patch(
        "sentence_transformers.SentenceTransformer",
        return_value=mock_sentence_transformer,
    ), patch(
        "google.auth.default", return_value=(None, "test-project")
    ), patch(
        "query_intelligence.middleware.rate_limit.rate_limit_middleware",
        mock_rate_limit_middleware,
    ):
        yield {
            "redis": mock_redis,
            "analysis_engine": mock_analysis_client,
            "pinecone": mock_pinecone,
            "genai_client": mock_genai_client,
            "sentence_transformer": mock_sentence_transformer,
        }


@pytest.fixture
def mock_query_result():
    """Mock query result for testing"""
    from query_intelligence.models import QueryResult, QueryIntent, CodeReference

    return QueryResult(
        answer="This is a test response about authentication middleware",
        intent=QueryIntent.EXPLAIN,
        confidence=0.92,
        references=[
            CodeReference(
                file_path="src/middleware/auth.py",
                start_line=10,
                end_line=30,
                snippet="def authenticate(request):\n    # JWT validation logic",
                relevance_score=0.95,
                language="python",
            )
        ],
        execution_time_ms=85.5,
        follow_up_questions=[
            "How are tokens generated?",
            "What happens on token expiry?",
        ],
        metadata={
            "model_used": "gemini-2.5-flash",
            "chunks_retrieved": 5,
            "chunks_used": 2,
            "cache_hit": False,
        },
    )


@pytest.fixture
def mock_authentication():
    """Mock authentication for testing"""

    def create_mock_auth(user_id="test-user", roles=None):
        if roles is None:
            roles = ["user"]

        mock_auth = MagicMock()
        mock_auth.user_id = user_id
        mock_auth.roles = roles
        mock_auth.is_authenticated = True
        return mock_auth

    return create_mock_auth


@pytest.fixture
def auth_headers():
    """Create valid auth headers for testing"""
    from query_intelligence.middleware.auth import jwt_auth

    # Create a test JWT token
    test_token = jwt_auth.create_access_token(
        data={"sub": "test-user-123", "email": "<EMAIL>", "roles": ["user"]}
    )

    return {"Authorization": f"Bearer {test_token}", "Content-Type": "application/json"}


@pytest.fixture
def sample_query_request():
    """Sample query request for testing"""
    return {
        "query": "How does the authentication middleware work?",
        "repository_id": "test-repo-123",
        "session_id": "test-session-456",
        "filters": {"file_pattern": "*.py", "exclude_tests": True},
    }


@pytest.fixture(autouse=True)
async def reset_metrics():
    """Reset metrics between tests"""
    # This would reset any global metrics state
    yield
    # Cleanup after test
