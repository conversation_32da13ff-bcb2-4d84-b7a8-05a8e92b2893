[{"endpoint": "/health", "status": 200, "response_time_ms": 0, "success": true, "error": null}, {"endpoint": "/metrics", "status": 200, "response_time_ms": 2, "success": true, "error": null}, {"endpoint": "/api/v1/languages", "status": 200, "response_time_ms": 0, "success": false, "error": "Invalid languages response"}, {"endpoint": "/api/v1/analyze", "status": 404, "response_time_ms": 1, "success": false, "error": "Invalid analysis response"}, {"endpoint": "/api/v1/analyze/repository", "status": 404, "response_time_ms": 0, "success": false, "error": "Repository analysis failed"}, {"endpoint": "/api/v1/security/scan", "status": 404, "response_time_ms": 0, "success": false, "error": "Security scan failed"}, {"endpoint": "/ws", "status": 200, "response_time_ms": 0, "success": true, "error": "WebSocket test not implemented"}]