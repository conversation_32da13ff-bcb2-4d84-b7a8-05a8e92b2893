{"analysis_summary": {"claim_validation": {"can_process_1m_loc_in_5min": true, "performance_margin": 52.2356145415271, "projected_1m_loc_time_seconds": 5.74378239126061}, "performance_target_met": true, "supported_languages": ["javascript", "json", "markdown", "typescript"], "total_languages": 4}, "basic_metrics": {"duration_seconds": 18.385594708, "files_per_second": 2152.337230776729, "language_metrics": {"javascript": {"ast_nodes_total": 0, "average_file_size": 128.76972947486297, "failed_files": 0, "files_count": 16967, "lines_count": 2184836, "lines_per_second": 277155.8829457951, "patterns_total": 0, "processing_time_seconds": 7.883058359714848, "success_rate": 1.0, "successful_files": 16967, "symbols_total": 0}, "json": {"ast_nodes_total": 0, "average_file_size": 64.6305385139741, "failed_files": 0, "files_count": 1467, "lines_count": 94813, "lines_per_second": 139106.71428877572, "patterns_total": 0, "processing_time_seconds": 0.6815846415808148, "success_rate": 1.0, "successful_files": 1467, "symbols_total": 0}, "markdown": {"ast_nodes_total": 0, "average_file_size": 1639.4766355140187, "failed_files": 0, "files_count": 107, "lines_count": 175424, "lines_per_second": 3528706.6016053916, "patterns_total": 0, "processing_time_seconds": 0.04971339921550591, "success_rate": 1.0, "successful_files": 107, "symbols_total": 0}, "typescript": {"ast_nodes_total": 0, "average_file_size": 35.465883695497126, "failed_files": 9, "files_count": 21031, "lines_count": 745883, "lines_per_second": 76334.54190021582, "patterns_total": 0, "processing_time_seconds": 9.771238307488831, "success_rate": 0.99957206029195, "successful_files": 21022, "symbols_total": 0}}, "lines_per_second": 174101.30326690982, "memory_usage_mb": 0.0, "success_rate": 0.9997725664611341, "total_files": 39572, "total_lines": 3200956}, "comprehensive_metrics": {"duration_seconds": 67.695501125, "files_per_second": 584.5587866604333, "language_metrics": {}, "lines_per_second": 65371.522870161774, "memory_usage_mb": 0.0, "success_rate": 0.9997725664611341, "total_files": 39572, "total_lines": 4425358}, "repository": "/Users/<USER>/conductor/repo/episteme/brasilia/services/analysis-engine/test-data/repositories/typescript", "timestamp": "2025-07-18T15:56:25.920192+00:00"}