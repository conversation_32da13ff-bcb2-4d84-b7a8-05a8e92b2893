#!/bin/bash
# Phase 1.3: Dependency Audit Script
# Validates security vulnerabilities and license compliance

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
EVIDENCE_DIR="${SCRIPT_DIR}/../evidence/phase1-dependency-audit"
SERVICE_DIR="${SCRIPT_DIR}/../../../services/analysis-engine"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Create evidence directory
mkdir -p "${EVIDENCE_DIR}"

# Start validation report
REPORT_FILE="${EVIDENCE_DIR}/dependency-audit-${TIMESTAMP}.txt"
echo "=== Analysis Engine Dependency Audit ===" > "${REPORT_FILE}"
echo "Timestamp: $(date)" >> "${REPORT_FILE}"
echo "" >> "${REPORT_FILE}"

# Function to log results
log_result() {
    local test_name=$1
    local status=$2
    local details=$3
    echo "[${status}] ${test_name}" | tee -a "${REPORT_FILE}"
    echo "  Details: ${details}" | tee -a "${REPORT_FILE}"
    echo "" | tee -a "${REPORT_FILE}"
}

cd "${SERVICE_DIR}"

echo "Starting dependency audit..." | tee -a "${REPORT_FILE}"

# Test 1: Security vulnerability scan with cargo-audit
echo "=== Test 1: Security vulnerability scan ===" | tee -a "${REPORT_FILE}"
AUDIT_OUTPUT="${EVIDENCE_DIR}/cargo-audit-${TIMESTAMP}.txt"

if command -v cargo-audit &> /dev/null; then
    if cargo audit 2>&1 | tee "${AUDIT_OUTPUT}"; then
        # Check if there are any vulnerabilities
        if grep -q "0 vulnerabilities found" "${AUDIT_OUTPUT}"; then
            log_result "Security audit" "PASS" "No known vulnerabilities found"
        else
            # Count critical and high vulnerabilities
            CRITICAL_COUNT=$(grep -c "Critical" "${AUDIT_OUTPUT}" || echo "0")
            HIGH_COUNT=$(grep -c "High" "${AUDIT_OUTPUT}" || echo "0")
            
            if [ "$CRITICAL_COUNT" -gt 0 ] || [ "$HIGH_COUNT" -gt 0 ]; then
                log_result "Security audit" "FAIL" "Found ${CRITICAL_COUNT} critical and ${HIGH_COUNT} high vulnerabilities"
            else
                log_result "Security audit" "WARN" "Found vulnerabilities (no critical/high)"
            fi
        fi
    else
        log_result "Security audit" "FAIL" "cargo audit failed to run"
    fi
else
    # Fallback: Install and run cargo-audit
    echo "Installing cargo-audit..." | tee -a "${REPORT_FILE}"
    if cargo install cargo-audit --quiet; then
        # Re-run the audit
        cargo audit 2>&1 | tee "${AUDIT_OUTPUT}"
        if grep -q "0 vulnerabilities found" "${AUDIT_OUTPUT}"; then
            log_result "Security audit" "PASS" "No known vulnerabilities found"
        else
            log_result "Security audit" "FAIL" "Vulnerabilities found - see ${AUDIT_OUTPUT}"
        fi
    else
        log_result "Security audit" "SKIP" "cargo-audit not available and could not be installed"
    fi
fi

# Test 2: Check for outdated dependencies
echo "=== Test 2: Checking for outdated dependencies ===" | tee -a "${REPORT_FILE}"
OUTDATED_OUTPUT="${EVIDENCE_DIR}/cargo-outdated-${TIMESTAMP}.txt"

if command -v cargo-outdated &> /dev/null; then
    cargo outdated --workspace --depth 1 2>&1 | tee "${OUTDATED_OUTPUT}"
    
    # Count outdated dependencies
    OUTDATED_COUNT=$(grep -E "^\w+" "${OUTDATED_OUTPUT}" | wc -l || echo "0")
    OUTDATED_COUNT=$(echo "$OUTDATED_COUNT" | tr -d '[:space:]')
    
    if [ "$OUTDATED_COUNT" -eq 0 ]; then
        log_result "Dependency freshness" "PASS" "All dependencies are up to date"
    else
        log_result "Dependency freshness" "INFO" "Found ${OUTDATED_COUNT} outdated dependencies"
    fi
else
    # Try alternative: parse Cargo.lock for basic info
    log_result "Dependency freshness" "SKIP" "cargo-outdated not available"
fi

# Test 3: License compliance check
echo "=== Test 3: License compliance check ===" | tee -a "${REPORT_FILE}"
LICENSE_OUTPUT="${EVIDENCE_DIR}/licenses-${TIMESTAMP}.txt"

# Check if cargo-license is available
if command -v cargo-license &> /dev/null; then
    cargo license 2>&1 | tee "${LICENSE_OUTPUT}"
    
    # Check for problematic licenses
    PROBLEMATIC_LICENSES=""
    if grep -qE "GPL|LGPL|AGPL|CC-BY-NC" "${LICENSE_OUTPUT}"; then
        PROBLEMATIC_LICENSES="Found potentially problematic licenses (GPL/LGPL/AGPL)"
        log_result "License compliance" "WARN" "${PROBLEMATIC_LICENSES}"
    else
        log_result "License compliance" "PASS" "No problematic licenses found"
    fi
elif command -v cargo-lichking &> /dev/null; then
    # Alternative: cargo-lichking
    if cargo lichking check 2>&1 | tee "${LICENSE_OUTPUT}"; then
        log_result "License compliance" "PASS" "License check passed"
    else
        log_result "License compliance" "FAIL" "License compatibility issues found"
    fi
else
    # Fallback: Basic Cargo.toml analysis
    echo "Analyzing Cargo.toml for license info..." >> "${LICENSE_OUTPUT}"
    grep -h "license" Cargo.toml >> "${LICENSE_OUTPUT}" || true
    log_result "License compliance" "INFO" "Manual license review required - see ${LICENSE_OUTPUT}"
fi

# Test 4: Dependency tree analysis
echo "=== Test 4: Dependency tree analysis ===" | tee -a "${REPORT_FILE}"
TREE_OUTPUT="${EVIDENCE_DIR}/dependency-tree-${TIMESTAMP}.txt"

cargo tree --workspace 2>&1 | head -100 > "${TREE_OUTPUT}"

# Count total dependencies
TOTAL_DEPS=$(cargo tree --workspace --no-dedupe 2>/dev/null | grep -v "^analysis-engine" | wc -l || echo "0")
UNIQUE_DEPS=$(cargo tree --workspace 2>/dev/null | grep -v "^analysis-engine" | wc -l || echo "0")

TOTAL_DEPS=$(echo "$TOTAL_DEPS" | tr -d '[:space:]')
UNIQUE_DEPS=$(echo "$UNIQUE_DEPS" | tr -d '[:space:]')

log_result "Dependency count" "INFO" "Total: ${TOTAL_DEPS}, Unique: ${UNIQUE_DEPS}"

# Test 5: Check for duplicate dependencies
echo "=== Test 5: Checking for duplicate dependencies ===" | tee -a "${REPORT_FILE}"
DUPES_OUTPUT="${EVIDENCE_DIR}/duplicate-deps-${TIMESTAMP}.txt"

cargo tree --workspace --duplicates 2>&1 | tee "${DUPES_OUTPUT}"

DUPE_COUNT=$(grep -c "^[a-zA-Z]" "${DUPES_OUTPUT}" || echo "0")
DUPE_COUNT=$(echo "$DUPE_COUNT" | tr -d '[:space:]')

if [ "$DUPE_COUNT" -eq 0 ]; then
    log_result "Duplicate dependencies" "PASS" "No duplicate dependencies found"
else
    log_result "Duplicate dependencies" "WARN" "Found ${DUPE_COUNT} duplicate dependencies"
fi

# Test 6: Check for yanked crates
echo "=== Test 6: Checking for yanked crates ===" | tee -a "${REPORT_FILE}"
if cargo update --dry-run 2>&1 | grep -q "yanked"; then
    log_result "Yanked crates" "FAIL" "Found yanked crates in dependencies"
else
    log_result "Yanked crates" "PASS" "No yanked crates found"
fi

# Test 7: Supply chain security - check for known issues
echo "=== Test 7: Supply chain security check ===" | tee -a "${REPORT_FILE}"
CRITICAL_DEPS=(
    "tokio"
    "axum"
    "tree-sitter"
    "serde"
    "redis"
)

SUPPLY_CHAIN_ISSUES=0
for dep in "${CRITICAL_DEPS[@]}"; do
    if cargo tree --workspace | grep -q "^${dep} "; then
        echo "Critical dependency '${dep}' found in tree" >> "${REPORT_FILE}"
    else
        echo "WARNING: Critical dependency '${dep}' not found!" >> "${REPORT_FILE}"
        SUPPLY_CHAIN_ISSUES=$((SUPPLY_CHAIN_ISSUES + 1))
    fi
done

if [ "$SUPPLY_CHAIN_ISSUES" -eq 0 ]; then
    log_result "Supply chain check" "PASS" "All critical dependencies verified"
else
    log_result "Supply chain check" "WARN" "${SUPPLY_CHAIN_ISSUES} critical dependencies missing"
fi

# Test 8: Check Cargo.lock is committed
echo "=== Test 8: Checking Cargo.lock status ===" | tee -a "${REPORT_FILE}"
if [ -f "Cargo.lock" ]; then
    if git ls-files --error-unmatch Cargo.lock &> /dev/null; then
        log_result "Cargo.lock status" "PASS" "Cargo.lock is tracked in git"
    else
        log_result "Cargo.lock status" "FAIL" "Cargo.lock exists but not tracked in git"
    fi
else
    log_result "Cargo.lock status" "FAIL" "Cargo.lock not found"
fi

# Generate summary
echo "" | tee -a "${REPORT_FILE}"
echo "=== DEPENDENCY AUDIT SUMMARY ===" | tee -a "${REPORT_FILE}"
echo "Report saved to: ${REPORT_FILE}" | tee -a "${REPORT_FILE}"

# Count results
PASS_COUNT=$(grep -c "\[PASS\]" "${REPORT_FILE}" || echo "0")
FAIL_COUNT=$(grep -c "\[FAIL\]" "${REPORT_FILE}" || echo "0")
WARN_COUNT=$(grep -c "\[WARN\]" "${REPORT_FILE}" || echo "0")
INFO_COUNT=$(grep -c "\[INFO\]" "${REPORT_FILE}" || echo "0")

echo "Results: ${PASS_COUNT} PASS, ${FAIL_COUNT} FAIL, ${WARN_COUNT} WARN, ${INFO_COUNT} INFO" | tee -a "${REPORT_FILE}"

# Create recommendations file
RECOMMENDATIONS="${EVIDENCE_DIR}/recommendations-${TIMESTAMP}.txt"
echo "=== Dependency Audit Recommendations ===" > "${RECOMMENDATIONS}"
echo "" >> "${RECOMMENDATIONS}"

if [ "$FAIL_COUNT" -gt 0 ]; then
    echo "CRITICAL ACTIONS REQUIRED:" >> "${RECOMMENDATIONS}"
    echo "1. Run 'cargo update' to update dependencies" >> "${RECOMMENDATIONS}"
    echo "2. Review and fix any security vulnerabilities" >> "${RECOMMENDATIONS}"
    echo "3. Ensure Cargo.lock is committed to git" >> "${RECOMMENDATIONS}"
    echo "" >> "${RECOMMENDATIONS}"
fi

if [ "$WARN_COUNT" -gt 0 ]; then
    echo "RECOMMENDED ACTIONS:" >> "${RECOMMENDATIONS}"
    echo "1. Review duplicate dependencies and consolidate versions" >> "${RECOMMENDATIONS}"
    echo "2. Check license compliance for production use" >> "${RECOMMENDATIONS}"
    echo "3. Consider updating outdated dependencies" >> "${RECOMMENDATIONS}"
fi

echo "Recommendations saved to: ${RECOMMENDATIONS}"

# Exit with error if any critical failures
if [ "$FAIL_COUNT" -gt 0 ]; then
    echo "Dependency audit failed with ${FAIL_COUNT} critical issues"
    exit 1
else
    echo "Dependency audit completed successfully"
    exit 0
fi