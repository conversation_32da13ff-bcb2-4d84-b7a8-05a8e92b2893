 150 variables can be used directly in the `format!` string
   7 this expression creates a reference which is immediately dereferenced by the compiler
   6 `debug_assert!(true)` will be optimized out by the compiler
   5 using `clone` on type `SecuritySeverity` which implements the `Copy` trait
   5 called `Iterator::last` on a `DoubleEndedIterator`; this will needlessly iterate the entire iterator
   4 this `map_or` can be simplified
   3 use of `or_insert_with` to construct default value
   3 unnecessary `if let` since only the `Ok` variant of the iterator element is used
   3 this `impl` can be derived
   3 redundant closure
   2 this `if` statement can be collapsed
   2 parameter is only used in recursion
   2 casting to the same type is unnecessary (`u32` -> `u32`)
   2 calling `push_str()` using a single-character string literal
   2 accessing first element with `arr.get(0)`
   1 you should consider adding a `Default` implementation for `SystemResourceMonitor`
   1 you should consider adding a `Default` implementation for `SecurityAnalyzer`
   1 you should consider adding a `Default` implementation for `PatternDetector`
   1 you should consider adding a `Default` implementation for `MetricsService`
   1 you should consider adding a `Default` implementation for `MetricsCollector`
   1 you should consider adding a `Default` implementation for `LanguageMetricsCalculator`
   1 you should consider adding a `Default` implementation for `LanguageDetector`
   1 you should consider adding a `Default` implementation for `GranularMetricsCollector`
   1 you should consider adding a `Default` implementation for `GitService`
   1 you seem to use `.enumerate()` and immediately discard the index
   1 writing `&mut Vec` instead of `&mut [_]` involves a new object where a slice will do
   1 using `clone` on type `WarningType` which implements the `Copy` trait
   1 using `clone` on type `AnalysisStatus` which implements the `Copy` trait
   1 useless use of `format!`
   1 unused variable: `service`
   1 unused variable: `request`
   1 unused variable: `config`
   1 unused imports: `api::AppState`, `config::ServiceConfig`, and `parser::TreeSitterParser`
   1 unused import: `tree_sitter::Language`
   1 unused import: `std::sync::Arc`
   1 unused import: `error`
   1 unused import: `analysis_engine::config::ServiceConfig`
   1 unneeded `return` statement
   1 this match could be written as a `let` statement
   1 this `if` has identical blocks
   1 this `if let` can be collapsed into the outer `match`
   1 this `else { if .. }` block can be collapsed
   1 the loop variable `i` is used to index `vector`
   1 redundant pattern matching, consider using `is_some()`
   1 module has the same name as its containing module
   1 manual implementation of `Option::map`
   1 length comparison to zero
   1 iterating on a map's values
   1 fields `name`, `expected_loc`, and `expected_files` are never read
   1 `to_string` applied to a type that implements `Display` in `format!` args
   1 `format!` in `format!` args
   1 `analysis-engine` (lib) generated 198 warnings (run `cargo clippy --fix --lib -p analysis-engine` to apply 180 suggestions)
   1 `analysis-engine` (bin "test_unsafe_bindings") generated 6 warnings (run `cargo clippy --fix --bin "test_unsafe_bindings"` to apply 6 suggestions)
   1 `analysis-engine` (bin "test_tree_sitter_apis") generated 2 warnings (run `cargo clippy --fix --bin "test_tree_sitter_apis"` to apply 2 suggestions)
   1 `analysis-engine` (bin "test_parsers") generated 4 warnings (run `cargo clippy --fix --bin "test_parsers"` to apply 4 suggestions)
   1 `analysis-engine` (bin "test_language_registry") generated 7 warnings (run `cargo clippy --fix --bin "test_language_registry"` to apply 6 suggestions)
   1 `analysis-engine` (bin "test_language_api") generated 3 warnings (run `cargo clippy --fix --bin "test_language_api"` to apply 3 suggestions)
   1 `analysis-engine` (bin "test_ai_services") generated 5 warnings (run `cargo clippy --fix --bin "test_ai_services"` to apply 5 suggestions)
   1 `analysis-engine` (bin "load_test") generated 10 warnings (run `cargo clippy --fix --bin "load_test"` to apply 6 suggestions)
