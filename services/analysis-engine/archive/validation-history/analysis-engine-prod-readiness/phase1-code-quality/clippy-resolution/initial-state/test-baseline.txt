warning: unused import: `super::*`
 --> src/services/analyzer/pattern_optimization_tests.rs:6:9
  |
6 |     use super::*;
  |         ^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: function `create_test_analysis_result` is never used
   --> src/services/analyzer/storage.rs:210:8
    |
210 |     fn create_test_analysis_result() -> AnalysisResult {
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = note: `#[warn(dead_code)]` on by default

warning: function `create_test_config` is never used
   --> src/services/analyzer/mod.rs:543:8
    |
543 |     fn create_test_config() -> ServiceConfig {
    |        ^^^^^^^^^^^^^^^^^^

warning: function `create_test_repo` is never used
   --> src/services/analyzer/mod.rs:592:8
    |
592 |     fn create_test_repo(temp_dir: &TempDir) -> PathBuf {
    |        ^^^^^^^^^^^^^^^^

warning: `analysis-engine` (lib test) generated 4 warnings (run `cargo fix --lib -p analysis-engine --tests` to apply 1 suggestion)
    Finished `test` profile [unoptimized + debuginfo] target(s) in 0.40s
     Running unittests src/lib.rs (target/debug/deps/analysis_engine-5d0862ebf1e26c80)

running 120 tests
test errors::tests::test_analysis_error_helpers ... ok
test errors::tests::test_git_error_conversion ... ok
test errors::tests::test_error_chain ... ok
test errors::tests::test_error_context_with_parser_error ... ok
test errors::tests::test_error_context ... ok
test errors::tests::test_error_debugging ... ok
test errors::tests::test_http_error_conversion ... ok
test errors::tests::test_error_display ... ok
test errors::tests::test_parser_error_conversion ... ok
test errors::tests::test_io_error_conversion ... ok
test errors::tests::test_parser_error_helpers ... ok
test errors::tests::test_serialization_error_conversion ... ok
test errors::tests::test_storage_error_conversion ... ok
Validating AI Services Implementation...
1. Testing EnhancedEmbeddingsService creation...
test errors::tests::test_storage_error_helpers ... ok
test errors::tests::test_type_aliases ... ok
test monitoring::resource_monitor::tests::test_memory_estimation ... ok
test metrics::tests::test_repository_metrics_calculation ... ok
test monitoring::resource_monitor::tests::test_memory_limit_check ... ok
test metrics::granular::tests::test_language_metrics ... ok
test metrics::granular::tests::test_operation_metrics_recording ... ok
test metrics::granular::tests::test_error_categorization ... ok
test parser::adapters::tests::test_xml_parsing ... ok
test parser::language_metrics::tests::test_unsupported_language ... ok
test parser::language_validation_test::test_parser_pool_creation ... ignored, Language version mismatch between tree-sitter 0.22.6 and language parsers
test api::handlers::websocket::tests::test_progress_update_serialization ... ok
All 33 language detection tests passed!
test parser::tests::test_language_detection ... ok
test parser::language_validation_test::test_language_detection_accuracy ... ok
test parser::tests::test_parser_creation ... ok
test parser::unsafe_bindings::tests::test_load_supported_language ... ok
test parser::unsafe_bindings::tests::test_load_unsupported_language ... ok
test parser::unsafe_bindings::tests::test_supported_language_check ... ok
⚠ Failed to create parser pool for rust: ParseError { file_path: "", error_type: Other, message: "Failed to create parser pool: Failed to set language for parser during warm-up", position: None }
⚠ rust parsing failed: Failed to create parser pool: Failed to set language for parser during warm-up (this might be expected)
✓ Successfully created parser pool for javascript
✓ Successfully created parser pool for typescript
✓ Successfully created parser pool for python
✓ Successfully created parser pool for go
test parser::unsafe_bindings::tests::test_supported_languages_list ... ok
✓ Successfully created parser pool for java
test parser::adapters::tests::test_toml_parsing ... ok
⚠ Failed to create parser pool for c: ParseError { file_path: "", error_type: Other, message: "Failed to create parser pool: Failed to set language for parser during warm-up", position: None }
✓ Successfully created parser pool for cpp
✓ Successfully created parser pool for html
✓ Successfully created parser pool for css
test parser::adapters::tests::test_sql_parsing ... ok
✓ Successfully created parser pool for json
✓ Successfully created parser pool for yaml
✓ Successfully created parser pool for ruby
✓ Successfully created parser pool for bash
⚠ Failed to create parser pool for markdown: ParseError { file_path: "", error_type: UnsupportedLanguage, message: "Language not supported: markdown", position: None }
✓ Successfully created parser pool for swift
✓ Successfully created parser pool for kotlin
✓ Successfully created parser pool for objc
✓ Successfully created parser pool for r
✓ Successfully created parser pool for julia
✓ Successfully created parser pool for haskell
✓ Successfully created parser pool for scala
✓ Successfully created parser pool for erlang
✓ Successfully created parser pool for elixir
✓ Successfully created parser pool for zig
✓ Successfully created parser pool for d
✓ Successfully created parser pool for lua
⚠ Failed to create parser pool for dart: ParseError { file_path: "", error_type: UnsupportedLanguage, message: "Language not supported: dart", position: None }
✓ Successfully created parser pool for nix
Created 25 parser pools out of 29 languages
All 29 tree-sitter languages and 3 custom parsers validated successfully!
test parser::language_validation_test::test_supported_languages_compilation ... ok
   ✓ EnhancedEmbeddingsService created successfully
2. Testing AIPatternDetector creation...

thread 'services::ai_pattern_detector::tests::test_convert_ai_patterns' panicked at src/services/ai_pattern_detector.rs:787:85:
called `Result::unwrap()` on an `Err` value: GCP_PROJECT_ID environment variable not set

Caused by:
    environment variable not found
note: run with `RUST_BACKTRACE=1` environment variable to display a backtrace

thread 'services::ai_test::tests::test_ai_services_initialization' panicked at src/services/ai_test.rs:274:55:
called `Result::unwrap()` on an `Err` value: GCP_PROJECT_ID environment variable not set

Caused by:
    environment variable not found

thread 'ai_services_validation::tests::test_ai_services_validation' panicked at src/ai_services_validation.rs:73:60:
called `Result::unwrap()` on an `Err` value: GCP_PROJECT_ID environment variable not set

Caused by:
    environment variable not found

thread 'services::ai_pattern_detector::tests::test_build_analysis_prompt' panicked at src/services/ai_pattern_detector.rs:667:73:
called `Result::unwrap()` on an `Err` value: GCP_PROJECT_ID environment variable not set

Caused by:
    environment variable not found
test services::ai_pattern_detector::tests::test_convert_ai_patterns ... FAILED
test services::ai_test::tests::test_ai_services_initialization ... FAILED
test services::ai_pattern_detector::tests::test_build_analysis_prompt ... FAILED
test ai_services_validation::tests::test_ai_services_validation ... FAILED
test services::analyzer::file_processor::tests::test_calculate_optimal_concurrency ... ok
test services::analyzer::file_processor::tests::test_get_optimal_batch_size ... ok
test services::analyzer::pattern_optimization_tests::pattern_optimization_tests::test_clamp_edge_cases ... ok
test services::analyzer::pattern_optimization_tests::pattern_optimization_tests::test_concurrency_bounds_validation ... ok
test services::analyzer::pattern_optimization_tests::pattern_optimization_tests::test_original_vs_optimized_behavior ... ok
test services::analyzer::pattern_optimization_tests::pattern_optimization_tests::test_specific_clamp_bounds ... ok
test services::analyzer::file_collector::tests::test_default_excludes ... ok
test services::analyzer::performance::tests::test_memory_warnings ... ok
test services::analyzer::file_collector::tests::test_file_collection_with_patterns ... ok
test services::analyzer::file_collector::tests::test_file_size_limit ... ok
test services::analyzer::progress::tests::test_create_progress_updates ... ok
test services::analyzer::progress::tests::test_send_progress ... ok
test services::analyzer::progress::tests::test_send_progress_with_details ... ok
test services::analyzer::performance::tests::test_get_available_memory ... ok
test services::analyzer::repository::tests::test_check_repository_size_warnings ... ok
test services::analyzer::results::tests::test_aggregate_metrics ... ok
test services::analyzer::results::tests::test_high_failure_rate_warning ... ok
test services::analyzer::results::tests::test_partition_results ... ok
test services::analyzer::storage::tests::test_storage_manager_creation ... ignored, Requires real GCS credentials
test services::analyzer::storage::tests::test_store_analysis_result_without_spanner ... ignored, Requires real GCS credentials
test services::analyzer::repository::tests::test_calculate_directory_size ... ok
test services::analyzer::performance::tests::test_resource_availability ... ok
test services::analyzer::tests::test_send_progress_channel ... ok
test services::analyzer::streaming_processor::tests::test_streaming_config ... ok

thread 'services::code_quality_assessor::tests::test_fallback_assessment_creation' panicked at src/services/code_quality_assessor.rs:833:75:
called `Result::unwrap()` on an `Err` value: GCP_PROJECT_ID environment variable not set

Caused by:
    environment variable not found
test services::code_quality_assessor::tests::test_fallback_assessment_creation ... FAILED
test services::code_quality_assessor::tests::test_quality_assessment_serialization ... ok
test services::embeddings::tests::test_empty_embedding_dimension ... ok
test services::embeddings::tests::test_retryable_error_detection ... ok
test services::embeddings_enhancement::tests::test_cosine_similarity ... ok
test services::embeddings_enhancement::tests::test_feature_toggles_default ... ok
test services::intelligent_documentation::tests::test_fallback_documentation_creation ... ok
test services::pattern_detector::tests::test_singleton_detection ... ok
test services::repository_insights::tests::test_fallback_insights_creation ... ok
test services::intelligent_documentation::tests::test_documentation_serialization ... ok
test parser::adapters::tests::test_markdown_parsing ... ok
test services::repository_insights::tests::test_repository_insights_serialization ... ok
test services::analyzer::streaming_processor::tests::test_large_file_detection ... ok
test services::analyzer::performance::tests::test_optimal_batch_size ... ok
test services::security::dependency::parsers::cargo::tests::test_clean_cargo_version ... ok
test services::security::dependency::parsers::dotnet::tests::test_clean_dotnet_version ... ok
test services::security::dependency::parsers::go::tests::test_go_mod_parsing ... ok
test services::security::dependency::parsers::gradle::tests::test_clean_gradle_version ... ok
test services::security::dependency::parsers::maven::tests::test_clean_maven_version ... ok
test services::security::dependency::parsers::npm::tests::test_clean_version ... ok
test services::security::dependency::parsers::php::tests::test_clean_composer_version ... ok
test services::security::dependency::parsers::python::tests::test_clean_python_version ... ok
test services::security::dependency::parsers::python::tests::test_parse_pep508_dependency ... ok
test services::security::dependency::parsers::ruby::tests::test_clean_ruby_version ... ok
test services::security::dependency::scanner::tests::test_version_vulnerability_check ... ok
test services::security::dependency::vulnerability_check::tests::test_version_matching ... ok
test services::security::dependency::vulnerability_check::tests::test_vulnerability_db_loading ... ok
test services::security::risk::assessor::tests::test_risk_assessment_calculation ... ok
test services::security::secrets::detector::tests::test_entropy_calculation ... ok
test services::security::secrets::detector::tests::test_false_positive_detection ... ok
test services::security::secrets::detector::tests::test_secret_masking ... ok
test services::security::secrets::patterns::tests::test_pattern_loading ... ok
test services::security::compliance::rules::tests::test_violation_detection ... ok
test services::security::threat::modeler::tests::test_risk_score_calculation ... ok
test services::security::types::tests::test_looks_like_secret ... ok
test services::security::vulnerability::detector::tests::test_dangerous_function_detection ... ok
test services::security::compliance::checker::tests::test_compliance_checker_creation ... ok
test services::security::compliance::checker::tests::test_risk_rating_calculation ... ok
test services::security::compliance::rules::tests::test_rule_loading ... ok
test services::security::tests::test_security_analyzer_creation ... ok
test services::security::vulnerability::patterns::tests::test_pattern_loading ... ok
test services::semantic_search::tests::test_search_query_defaults ... ok
test services::semantic_search::tests::test_cosine_similarity_calculation ... ok
test services::semantic_search::tests::test_cache_key_generation ... ok
test services::semantic_search::tests::test_search_result_serialization ... ok
test storage::redis_client::tests::test_rate_limiting ... ignored
test services::semantic_search::tests::test_semantic_search_service_creation ... ok
test storage::cache::tests::test_cache_manager_without_redis ... ok
test services::security::vulnerability::patterns::tests::test_pattern_matching ... ok
test services::security::vulnerability::ml_classifier::tests::test_ml_classifier_creation ... ok
✓ javascript parsing successful
Rust Metrics: function_count=2, class_count=1, complexity=1, comment_ratio=0.05263157894736842
test parser::language_metrics::tests::test_rust_metrics ... ok
Python Metrics: function_count=3, class_count=1, complexity=1, comment_ratio=0.13333333333333333
test parser::language_metrics::tests::test_python_metrics ... ok
✓ python parsing successful
✓ go parsing successful
JavaScript Metrics: function_count=5, class_count=1, complexity=1, comment_ratio=0.043478260869565216
test parser::language_metrics::tests::test_javascript_metrics ... ok
✓ java parsing successful
⚠ c parsing failed: Failed to create parser pool: Failed to set language for parser during warm-up (this might be expected)
✓ cpp parsing successful
✓ json parsing successful
✓ yaml parsing successful
✓ html parsing successful
✓ css parsing successful
✓ bash parsing successful
✓ markdown parsing successful
✓ sql parsing successful
✓ toml parsing successful
✓ xml parsing successful
test parser::language_validation_test::test_language_parsing_samples ... ok

thread 'services::analyzer::events::tests::test_event_manager_clone' panicked at src/services/analyzer/events.rs:316:14:
called `Result::unwrap()` on an `Err` value: GCP_PROJECT_ID environment variable not set

Caused by:
    environment variable not found
test services::analyzer::events::tests::test_event_manager_clone ... FAILED

thread 'services::analyzer::events::tests::test_webhook_notification' panicked at src/services/analyzer/events.rs:256:14:
called `Result::unwrap()` on an `Err` value: GCP_PROJECT_ID environment variable not set

Caused by:
    environment variable not found
test services::analyzer::events::tests::test_webhook_notification ... FAILED

thread 'services::analyzer::events::tests::test_webhook_notification_failure' panicked at src/services/analyzer/events.rs:289:14:
called `Result::unwrap()` on an `Err` value: GCP_PROJECT_ID environment variable not set

Caused by:
    environment variable not found
test services::analyzer::events::tests::test_webhook_notification_failure ... FAILED

failures:

failures:
    ai_services_validation::tests::test_ai_services_validation
    services::ai_pattern_detector::tests::test_build_analysis_prompt
    services::ai_pattern_detector::tests::test_convert_ai_patterns
    services::ai_test::tests::test_ai_services_initialization
    services::analyzer::events::tests::test_event_manager_clone
    services::analyzer::events::tests::test_webhook_notification
    services::analyzer::events::tests::test_webhook_notification_failure
    services::code_quality_assessor::tests::test_fallback_assessment_creation

test result: FAILED. 108 passed; 8 failed; 4 ignored; 0 measured; 0 filtered out; finished in 2.86s

error: test failed, to rerun pass `--lib`
