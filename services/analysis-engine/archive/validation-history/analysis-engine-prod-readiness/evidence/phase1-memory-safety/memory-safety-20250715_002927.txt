=== Analysis Engine Memory Safety Validation ===
Timestamp: Tue Jul 15 00:29:27 EEST 2025

Starting memory safety validation...
=== Test 1: Analyzing unsafe code blocks ===
[INFO] Unsafe block count
  Details: Found unsafe blocks in 4 files

Unsafe blocks saved to: /Users/<USER>/Documents/GitHub/episteme/validation-results/analysis-engine-prod-readiness/phase1-code-quality/../evidence/phase1-memory-safety/unsafe-blocks-20250715_002927.txt
=== Test 2: Checking unsafe block documentation ===
Checking src/bin/check_api.rs...
  WARNING: Undocumented unsafe block at src/bin/check_api.rs:17
  WARNING: Undocumented unsafe block at src/bin/check_api.rs:30
Checking src/bin/test_each_language.rs...
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:45
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:46
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:47
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:48
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:49
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:50
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:51
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:52
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:53
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:54
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:55
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:56
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:57
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:58
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:59
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:60
  WARNING: Undocumented unsafe block at src/bin/test_each_language.rs:61
Checking src/bin/inspect_types.rs...
  WARNING: Undocumented unsafe block at src/bin/inspect_types.rs:15
  WARNING: Undocumented unsafe block at src/bin/inspect_types.rs:18
Checking src/parser/unsafe_bindings.rs...
  WARNING: Undocumented unsafe block at src/parser/unsafe_bindings.rs:133
[FAIL] Unsafe documentation
  Details: Found 22 undocumented unsafe blocks

=== Test 3: Checking for common memory safety patterns ===
[INFO] Memory safety patterns
  Details: Raw pointers: 0 files, Transmute: 0 files, Uninitialized: 0 files

=== Test 4: Checking Drop implementations ===
[INFO] Drop implementations
  Details: Found 1 custom Drop implementations

=== Test 5: Checking Send/Sync implementations ===
[PASS] Send/Sync implementations
  Details: No unsafe Send/Sync implementations found

=== Test 6: Tree-sitter integration safety ===
[WARN] Tree-sitter cleanup
  Details: Found language creation but no cleanup code

=== Test 7: Running Miri memory safety checker ===
[SKIP] Miri safety check
  Details: Miri not available - install with: rustup +nightly component add miri


=== MEMORY SAFETY SUMMARY ===
Report saved to: /Users/<USER>/Documents/GitHub/episteme/validation-results/analysis-engine-prod-readiness/phase1-code-quality/../evidence/phase1-memory-safety/memory-safety-20250715_002927.txt
Results: 1 PASS, 1 FAIL, 1 WARN, 3 INFO
