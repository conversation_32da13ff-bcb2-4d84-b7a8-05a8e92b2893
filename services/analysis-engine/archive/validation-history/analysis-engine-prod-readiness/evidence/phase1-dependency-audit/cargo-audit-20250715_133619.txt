[0m[0m[1m[32m    Fetching[0m advisory database from `https://github.com/RustSec/advisory-db.git`
[0m[0m[1m[32m      Loaded[0m 790 security advisories (from /Users/<USER>/.cargo/advisory-db)
[0m[0m[1m[32m    Updating[0m crates.io index
[0m[0m[1m[32m    Scanning[0m Cargo.lock for vulnerabilities (597 crate dependencies)
[0m[0m[1m[33mCrate:    [0m ansi_term
[0m[0m[1m[33mVersion:  [0m 0.12.1
[0m[0m[1m[33mWarning:  [0m unmaintained
[0m[0m[1m[33mTitle:    [0m ansi_term is Unmaintained
[0m[0m[1m[33mDate:     [0m 2021-08-18
[0m[0m[1m[33mID:       [0m RUSTSEC-2021-0139
[0m[0m[1m[33mURL:      [0m https://rustsec.org/advisories/RUSTSEC-2021-0139
[0m[0m[1m[33mDependency tree:
[0mansi_term 0.12.1
└── clap 2.34.0
    └── tokei 12.1.2
        └── analysis-engine 0.1.0

[0m[0m[1m[33mCrate:    [0m atty
[0m[0m[1m[33mVersion:  [0m 0.2.14
[0m[0m[1m[33mWarning:  [0m unmaintained
[0m[0m[1m[33mTitle:    [0m `atty` is unmaintained
[0m[0m[1m[33mDate:     [0m 2024-09-25
[0m[0m[1m[33mID:       [0m RUSTSEC-2024-0375
[0m[0m[1m[33mURL:      [0m https://rustsec.org/advisories/RUSTSEC-2024-0375
[0m[0m[1m[33mDependency tree:
[0matty 0.2.14
├── env_logger 0.8.4
│   └── tokei 12.1.2
│       └── analysis-engine 0.1.0
└── clap 2.34.0
    └── tokei 12.1.2

[0m[0m[1m[33mCrate:    [0m dotenv
[0m[0m[1m[33mVersion:  [0m 0.15.0
[0m[0m[1m[33mWarning:  [0m unmaintained
[0m[0m[1m[33mTitle:    [0m dotenv is Unmaintained
[0m[0m[1m[33mDate:     [0m 2021-12-24
[0m[0m[1m[33mID:       [0m RUSTSEC-2021-0141
[0m[0m[1m[33mURL:      [0m https://rustsec.org/advisories/RUSTSEC-2021-0141
[0m[0m[1m[33mDependency tree:
[0mdotenv 0.15.0
└── analysis-engine 0.1.0

[0m[0m[1m[33mCrate:    [0m instant
[0m[0m[1m[33mVersion:  [0m 0.1.13
[0m[0m[1m[33mWarning:  [0m unmaintained
[0m[0m[1m[33mTitle:    [0m `instant` is unmaintained
[0m[0m[1m[33mDate:     [0m 2024-09-01
[0m[0m[1m[33mID:       [0m RUSTSEC-2024-0384
[0m[0m[1m[33mURL:      [0m https://rustsec.org/advisories/RUSTSEC-2024-0384
[0m[0m[1m[33mDependency tree:
[0minstant 0.1.13
├── parking_lot_core 0.8.6
│   └── parking_lot 0.11.2
│       └── tokei 12.1.2
│           └── analysis-engine 0.1.0
├── parking_lot 0.11.2
└── fastrand 1.9.0
    └── futures-lite 1.13.0
        └── http-types 2.12.0
            └── wiremock 0.5.22
                └── analysis-engine 0.1.0

[0m[0m[1m[33mCrate:    [0m term_size
[0m[0m[1m[33mVersion:  [0m 0.3.2
[0m[0m[1m[33mWarning:  [0m unmaintained
[0m[0m[1m[33mTitle:    [0m `term_size` is unmaintained; use `terminal_size` instead
[0m[0m[1m[33mDate:     [0m 2020-11-03
[0m[0m[1m[33mID:       [0m RUSTSEC-2020-0163
[0m[0m[1m[33mURL:      [0m https://rustsec.org/advisories/RUSTSEC-2020-0163
[0m[0m[1m[33mDependency tree:
[0mterm_size 0.3.2
└── tokei 12.1.2
    └── analysis-engine 0.1.0

[0m[0m[1m[33mCrate:    [0m yaml-rust
[0m[0m[1m[33mVersion:  [0m 0.4.5
[0m[0m[1m[33mWarning:  [0m unmaintained
[0m[0m[1m[33mTitle:    [0m yaml-rust is unmaintained.
[0m[0m[1m[33mDate:     [0m 2024-03-20
[0m[0m[1m[33mID:       [0m RUSTSEC-2024-0320
[0m[0m[1m[33mURL:      [0m https://rustsec.org/advisories/RUSTSEC-2024-0320
[0m[0m[1m[33mDependency tree:
[0myaml-rust 0.4.5
└── config 0.13.4
    └── analysis-engine 0.1.0

[0m[0m[1m[33mCrate:    [0m atty
[0m[0m[1m[33mVersion:  [0m 0.2.14
[0m[0m[1m[33mWarning:  [0m unsound
[0m[0m[1m[33mTitle:    [0m Potential unaligned read
[0m[0m[1m[33mDate:     [0m 2021-07-04
[0m[0m[1m[33mID:       [0m RUSTSEC-2021-0145
[0m[0m[1m[33mURL:      [0m https://rustsec.org/advisories/RUSTSEC-2021-0145

[0m[0m[1m[33mwarning:[0m 7 allowed warnings found
