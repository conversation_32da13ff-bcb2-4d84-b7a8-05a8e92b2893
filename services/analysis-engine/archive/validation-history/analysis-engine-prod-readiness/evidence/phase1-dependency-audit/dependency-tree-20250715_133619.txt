analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
├── anyhow v1.0.98
├── async-trait v0.1.88 (proc-macro)
│   ├── proc-macro2 v1.0.95
│   │   └── unicode-ident v1.0.18
│   ├── quote v1.0.40
│   │   └── proc-macro2 v1.0.95 (*)
│   └── syn v2.0.104
│       ├── proc-macro2 v1.0.95 (*)
│       ├── quote v1.0.40 (*)
│       └── unicode-ident v1.0.18
├── axum v0.8.4
│   ├── axum-core v0.5.2
│   │   ├── bytes v1.10.1
│   │   ├── futures-core v0.3.31
│   │   ├── http v1.3.1
│   │   │   ├── bytes v1.10.1
│   │   │   ├── fnv v1.0.7
│   │   │   └── itoa v1.0.15
│   │   ├── http-body v1.0.1
│   │   │   ├── bytes v1.10.1
│   │   │   └── http v1.3.1 (*)
│   │   ├── http-body-util v0.1.3
│   │   │   ├── bytes v1.10.1
│   │   │   ├── futures-core v0.3.31
│   │   │   ├── http v1.3.1 (*)
│   │   │   ├── http-body v1.0.1 (*)
│   │   │   └── pin-project-lite v0.2.16
│   │   ├── mime v0.3.17
│   │   ├── pin-project-lite v0.2.16
│   │   ├── rustversion v1.0.21 (proc-macro)
│   │   ├── sync_wrapper v1.0.2
│   │   │   └── futures-core v0.3.31
│   │   ├── tower-layer v0.3.3
│   │   ├── tower-service v0.3.3
│   │   └── tracing v0.1.41
│   │       ├── log v0.4.27
│   │       ├── pin-project-lite v0.2.16
│   │       ├── tracing-attributes v0.1.30 (proc-macro)
│   │       │   ├── proc-macro2 v1.0.95 (*)
│   │       │   ├── quote v1.0.40 (*)
│   │       │   └── syn v2.0.104 (*)
│   │       └── tracing-core v0.1.34
│   │           └── once_cell v1.21.3
│   ├── axum-macros v0.5.0 (proc-macro)
│   │   ├── proc-macro2 v1.0.95 (*)
│   │   ├── quote v1.0.40 (*)
│   │   └── syn v2.0.104 (*)
│   ├── base64 v0.22.1
│   ├── bytes v1.10.1
│   ├── form_urlencoded v1.2.1
│   │   └── percent-encoding v2.3.1
│   ├── futures-util v0.3.31
│   │   ├── futures-channel v0.3.31
│   │   │   ├── futures-core v0.3.31
│   │   │   └── futures-sink v0.3.31
│   │   ├── futures-core v0.3.31
│   │   ├── futures-io v0.3.31
│   │   ├── futures-macro v0.3.31 (proc-macro)
│   │   │   ├── proc-macro2 v1.0.95 (*)
│   │   │   ├── quote v1.0.40 (*)
│   │   │   └── syn v2.0.104 (*)
│   │   ├── futures-sink v0.3.31
│   │   ├── futures-task v0.3.31
│   │   ├── memchr v2.7.5
│   │   ├── pin-project-lite v0.2.16
│   │   ├── pin-utils v0.1.0
│   │   └── slab v0.4.10
│   ├── http v1.3.1 (*)
│   ├── http-body v1.0.1 (*)
│   ├── http-body-util v0.1.3 (*)
│   ├── hyper v1.6.0
│   │   ├── bytes v1.10.1
│   │   ├── futures-channel v0.3.31 (*)
│   │   ├── futures-util v0.3.31 (*)
│   │   ├── h2 v0.4.11
│   │   │   ├── atomic-waker v1.1.2
│   │   │   ├── bytes v1.10.1
│   │   │   ├── fnv v1.0.7
│   │   │   ├── futures-core v0.3.31
│   │   │   ├── futures-sink v0.3.31
│   │   │   ├── http v1.3.1 (*)
│   │   │   ├── indexmap v2.10.0
│   │   │   │   ├── equivalent v1.0.2
│   │   │   │   └── hashbrown v0.15.4
│   │   │   ├── slab v0.4.10
│   │   │   ├── tokio v1.46.1
│   │   │   │   ├── bytes v1.10.1
│   │   │   │   ├── libc v0.2.174
│   │   │   │   ├── mio v1.0.4
│   │   │   │   │   └── libc v0.2.174
│   │   │   │   ├── parking_lot v0.12.4
│   │   │   │   │   ├── lock_api v0.4.13
│   │   │   │   │   │   └── scopeguard v1.2.0
│   │   │   │   │   │   [build-dependencies]
│   │   │   │   │   │   └── autocfg v1.5.0
│   │   │   │   │   └── parking_lot_core v0.9.11
│   │   │   │   │       ├── cfg-if v1.0.1
│   │   │   │   │       ├── libc v0.2.174
│   │   │   │   │       └── smallvec v1.15.1
