=== Analysis Engine Dependency Audit ===
Timestamp: Tue Jul 15 13:36:19 EEST 2025

Starting dependency audit...
=== Test 1: Security vulnerability scan ===
[WARN] Security audit
  Details: Found vulnerabilities (no critical/high)

=== Test 2: Checking for outdated dependencies ===
[SKIP] Dependency freshness
  Details: cargo-outdated not available

=== Test 3: License compliance check ===
[INFO] License compliance
  Details: Manual license review required - see /Users/<USER>/Documents/GitHub/episteme/validation-results/analysis-engine-prod-readiness/phase1-code-quality/../evidence/phase1-dependency-audit/licenses-20250715_133619.txt

=== Test 4: Dependency tree analysis ===
[INFO] Dependency count
  Details: Total: 97284, Unique: 1597

=== Test 5: Checking for duplicate dependencies ===
[WARN] Duplicate dependencies
  Details: Found 117 duplicate dependencies

=== Test 6: Checking for yanked crates ===
[PASS] Yanked crates
  Details: No yanked crates found

=== Test 7: Supply chain security check ===
WARNING: Critical dependency 'tokio' not found!
WARNING: Critical dependency 'axum' not found!
WARNING: Critical dependency 'tree-sitter' not found!
WARNING: Critical dependency 'serde' not found!
WARNING: Critical dependency 'redis' not found!
[WARN] Supply chain check
  Details: 5 critical dependencies missing

=== Test 8: Checking Cargo.lock status ===
[FAIL] Cargo.lock status
  Details: Cargo.lock exists but not tracked in git


=== DEPENDENCY AUDIT SUMMARY ===
Report saved to: /Users/<USER>/Documents/GitHub/episteme/validation-results/analysis-engine-prod-readiness/phase1-code-quality/../evidence/phase1-dependency-audit/dependency-audit-20250715_133619.txt
Results: 1 PASS, 1 FAIL, 3 WARN, 2 INFO
