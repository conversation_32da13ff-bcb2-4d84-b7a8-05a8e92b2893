ahash v0.7.8
└── hashbrown v0.12.3
    ├── indexmap v1.9.3
    │   └── tower v0.4.13
    │       ├── google-cloud-gax v0.19.2
    │       │   ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    │       │   ├── google-cloud-longrunning v0.21.0
    │       │   │   └── google-cloud-spanner v0.33.0
    │       │   │       └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    │       │   ├── google-cloud-pubsub v0.30.0
    │       │   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    │       │   └── google-cloud-spanner v0.33.0 (*)
    │       └── tonic v0.12.3
    │           ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    │           ├── google-cloud-gax v0.19.2 (*)
    │           ├── google-cloud-googleapis v0.16.1
    │           │   ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    │           │   ├── google-cloud-longrunning v0.21.0 (*)
    │           │   ├── google-cloud-pubsub v0.30.0 (*)
    │           │   └── google-cloud-spanner v0.33.0 (*)
    │           └── google-cloud-longrunning v0.21.0 (*)
    └── ordered-multimap v0.4.3
        └── rust-ini v0.18.0
            └── config v0.13.4
                └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)

ahash v0.8.12
└── jsonschema v0.18.3
    [dev-dependencies]
    └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)

aho-corasick v0.7.20
└── tokei v12.1.2
    └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)

aho-corasick v1.1.3
├── globset v0.4.16
│   └── ignore v0.4.23
│       ├── globwalk v0.9.1
│       │   └── tera v1.20.0
│       │       [build-dependencies]
│       │       └── tokei v12.1.2 (*)
│       └── tokei v12.1.2 (*)
│       [build-dependencies]
│       └── tokei v12.1.2 (*)
├── regex v1.11.1
│   ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   ├── criterion v0.5.1
│   │   [dev-dependencies]
│   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   ├── env_logger v0.8.4
│   │   └── tokei v12.1.2 (*)
│   ├── google-cloud-storage v0.24.0
│   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   ├── jsonschema v0.18.3 (*)
│   ├── mockito v1.7.0
│   │   [dev-dependencies]
│   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   ├── parse-zoneinfo v0.3.1
│   │   └── chrono-tz-build v0.3.0
│   │       [build-dependencies]
│   │       └── chrono-tz v0.9.0
│   │           └── tera v1.20.0 (*)
│   ├── rstest_macros v0.21.0 (proc-macro)
│   │   └── rstest v0.21.0
│   │       [dev-dependencies]
│   │       └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   ├── tera v1.20.0 (*)
│   ├── tokei v12.1.2 (*)
│   ├── tracing-subscriber v0.3.19
│   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   ├── tree-sitter v0.20.10
│   │   └── tree-sitter-haskell v0.15.0
│   │       └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   ├── tree-sitter v0.22.6
│   │   ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   │   ├── tree-sitter-css v0.21.1
│   │   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   │   ├── tree-sitter-d v0.6.1
│   │   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   │   ├── tree-sitter-erlang v0.7.0
│   │   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   │   ├── tree-sitter-html v0.20.4
│   │   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   │   ├── tree-sitter-json v0.21.0
│   │   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   │   ├── tree-sitter-kotlin v0.3.8
│   │   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   │   ├── tree-sitter-lua v0.1.0
│   │   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   │   └── tree-sitter-yaml v0.6.1
│   │       └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   ├── validator v0.20.0
│   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   └── wiremock v0.5.22
│       [dev-dependencies]
│       └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
└── regex-automata v0.4.9
    ├── fancy-regex v0.13.0
    │   └── jsonschema v0.18.3 (*)
    ├── globset v0.4.16 (*)
    ├── ignore v0.4.23 (*)
    └── regex v1.11.1 (*)

axum v0.7.9
└── tonic v0.12.3 (*)

axum v0.8.4
└── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)

axum-core v0.4.5
└── axum v0.7.9 (*)

axum-core v0.5.2
└── axum v0.8.4 (*)

base64 v0.13.1
├── http-types v2.12.0
│   └── wiremock v0.5.22 (*)
└── ron v0.7.1
    └── config v0.13.4 (*)

base64 v0.21.7
├── google-cloud-auth v0.16.0
│   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
├── google-cloud-auth v0.17.2
│   ├── google-cloud-pubsub v0.30.0 (*)
│   ├── google-cloud-spanner v0.33.0 (*)
│   └── google-cloud-storage v0.24.0 (*)
├── google-cloud-spanner v0.33.0 (*)
├── google-cloud-storage v0.24.0 (*)
├── reqwest v0.11.27
│   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
├── rustls-pemfile v1.0.4
│   └── reqwest v0.11.27 (*)
└── wiremock v0.5.22 (*)

base64 v0.22.1
├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
├── axum v0.8.4 (*)
├── hyper-util v0.1.15
│   ├── axum v0.8.4 (*)
│   ├── hyper-timeout v0.5.2
│   │   └── tonic v0.12.3 (*)
│   ├── hyper-tls v0.6.0
│   │   └── reqwest v0.12.22
│   │       ├── google-cloud-auth v0.16.0 (*)
│   │       ├── google-cloud-auth v0.17.2 (*)
│   │       ├── google-cloud-metadata v0.5.1
│   │       │   ├── google-cloud-auth v0.16.0 (*)
│   │       │   ├── google-cloud-auth v0.17.2 (*)
│   │       │   └── google-cloud-storage v0.24.0 (*)
│   │       ├── google-cloud-storage v0.24.0 (*)
│   │       ├── jsonschema v0.18.3 (*)
│   │       └── reqwest-middleware v0.4.2
│   │           └── google-cloud-storage v0.24.0 (*)
│   ├── mockito v1.7.0 (*)
│   ├── reqwest v0.12.22 (*)
│   └── tonic v0.12.3 (*)
├── jsonschema v0.18.3 (*)
├── jsonwebtoken v9.3.1
│   ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   ├── google-cloud-auth v0.16.0 (*)
│   └── google-cloud-auth v0.17.2 (*)
├── pem v3.0.5
│   └── jsonwebtoken v9.3.1 (*)
├── reqwest v0.12.22 (*)
└── tonic v0.12.3 (*)

bit-set v0.5.3
└── fancy-regex v0.13.0 (*)

bit-set v0.8.0
└── proptest v1.7.0
    [dev-dependencies]
    └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)

bit-vec v0.6.3
└── bit-set v0.5.3 (*)

bit-vec v0.8.0
├── bit-set v0.8.0 (*)
└── proptest v1.7.0 (*)

bitflags v1.3.2
├── clap v2.34.0
│   └── tokei v12.1.2 (*)
├── ron v0.7.1 (*)
└── system-configuration v0.5.1
    └── reqwest v0.11.27 (*)

bitflags v2.9.1
└── globwalk v0.9.1 (*)

bitflags v2.9.1
├── git2 v0.19.0
│   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
├── proptest v1.7.0 (*)
├── rustix v1.0.7
│   └── tempfile v3.20.0
│       ├── native-tls v0.2.14
│       │   ├── hyper-tls v0.5.0
│       │   │   └── reqwest v0.11.27 (*)
│       │   ├── hyper-tls v0.6.0 (*)
│       │   ├── reqwest v0.11.27 (*)
│       │   ├── reqwest v0.12.22 (*)
│       │   └── tokio-native-tls v0.3.1
│       │       ├── hyper-tls v0.5.0 (*)
│       │       ├── hyper-tls v0.6.0 (*)
│       │       ├── reqwest v0.11.27 (*)
│       │       └── reqwest v0.12.22 (*)
│       ├── proptest v1.7.0 (*)
│       └── rusty-fork v0.3.0
│           └── proptest v1.7.0 (*)
│       [dev-dependencies]
│       └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
├── security-framework v2.11.1
│   └── native-tls v0.2.14 (*)
└── tower-http v0.6.6
    ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    └── reqwest v0.12.22 (*)

chrono v0.4.41
├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
└── fake v2.10.0
    [dev-dependencies]
    └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)

chrono v0.4.41
├── chrono-tz v0.9.0 (*)
└── tera v1.20.0 (*)

clap v2.34.0 (*)

clap v4.5.41
├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
├── criterion v0.5.1 (*)
└── jsonschema v0.18.3 (*)

crypto-common v0.1.6
└── digest v0.10.7
    └── sha2 v0.10.9
        [build-dependencies]
        └── pest_meta v2.8.1
            └── pest_generator v2.8.1
                └── pest_derive v2.8.1 (proc-macro)
                    ├── json5 v0.4.1
                    │   └── config v0.13.4 (*)
                    └── tera v1.20.0 (*)

crypto-common v0.1.6
└── digest v0.10.7
    ├── sha1 v0.10.6
    │   ├── axum v0.8.4 (*)
    │   ├── tungstenite v0.26.2
    │   │   └── tokio-tungstenite v0.26.2
    │   │       └── axum v0.8.4 (*)
    │   └── tungstenite v0.27.0
    │       └── tokio-tungstenite v0.27.0
    │           └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    └── sha2 v0.10.9
        ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
        └── google-cloud-storage v0.24.0 (*)

dashmap v4.0.2
└── tokei v12.1.2 (*)

dashmap v5.5.3
└── governor v0.6.3
    └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)

dashmap v6.1.0
└── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)

digest v0.10.7 (*)

digest v0.10.7 (*)

fastrand v1.9.0
└── futures-lite v1.13.0
    └── http-types v2.12.0 (*)

fastrand v2.3.0
└── tempfile v3.20.0 (*)

getrandom v0.1.16
├── rand v0.7.3
│   └── http-types v2.12.0 (*)
└── rand_core v0.5.1
    ├── rand v0.7.3 (*)
    └── rand_chacha v0.2.2
        └── rand v0.7.3 (*)

getrandom v0.2.16
├── ahash v0.7.8 (*)
├── rand_core v0.6.4
│   ├── rand v0.8.5
│   │   ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   │   ├── fake v2.10.0 (*)
│   │   ├── governor v0.6.3 (*)
│   │   ├── phf_generator v0.11.3
│   │   │   └── phf_codegen v0.11.3
│   │   │       └── chrono-tz-build v0.3.0 (*)
│   │   ├── rstest_reuse v0.7.0 (proc-macro)
│   │   │   [dev-dependencies]
│   │   │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   │   ├── tera v1.20.0 (*)
│   │   ├── tokio-retry v0.3.0
│   │   │   └── redis v0.25.4
│   │   │       └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   │   └── tower v0.4.13 (*)
│   └── rand_chacha v0.3.1
│       └── rand v0.8.5 (*)
└── ring v0.17.14
    ├── google-cloud-storage v0.24.0 (*)
    ├── jsonwebtoken v9.3.1 (*)
    ├── rustls v0.23.29
    │   └── tokio-rustls v0.26.2
    │       └── tonic v0.12.3 (*)
    └── rustls-webpki v0.103.4
        └── rustls v0.23.29 (*)

getrandom v0.3.3
├── ahash v0.8.12 (*)
├── rand_core v0.9.3
│   ├── rand v0.9.1
│   │   ├── mockito v1.7.0 (*)
│   │   ├── proptest v1.7.0 (*)
│   │   ├── tungstenite v0.26.2 (*)
│   │   └── tungstenite v0.27.0 (*)
│   ├── rand_chacha v0.9.0
│   │   ├── proptest v1.7.0 (*)
│   │   └── rand v0.9.1 (*)
│   └── rand_xorshift v0.4.0
│       └── proptest v1.7.0 (*)
├── tempfile v3.20.0 (*)
└── uuid v1.17.0
    ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    └── jsonschema v0.18.3 (*)

google-cloud-auth v0.16.0 (*)

google-cloud-auth v0.17.2 (*)

h2 v0.3.27
├── hyper v0.14.32
│   ├── hyper-tls v0.5.0 (*)
│   ├── reqwest v0.11.27 (*)
│   └── wiremock v0.5.22 (*)
└── reqwest v0.11.27 (*)

h2 v0.4.11
├── hyper v1.6.0
│   ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   ├── axum v0.8.4 (*)
│   ├── hyper-timeout v0.5.2 (*)
│   ├── hyper-tls v0.6.0 (*)
│   ├── hyper-util v0.1.15 (*)
│   ├── mockito v1.7.0 (*)
│   ├── reqwest v0.12.22 (*)
│   └── tonic v0.12.3 (*)
└── tonic v0.12.3 (*)

hashbrown v0.12.3 (*)

hashbrown v0.14.5
├── dashmap v5.5.3 (*)
└── dashmap v6.1.0 (*)

hashbrown v0.15.4
└── indexmap v2.10.0
    ├── h2 v0.3.27 (*)
    ├── h2 v0.4.11 (*)
    ├── serde_yaml v0.9.34+deprecated
    │   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    ├── toml_edit v0.22.27
    │   └── toml v0.8.23
    │       └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    └── toml_edit v0.22.27
        └── proc-macro-crate v3.3.0
            └── rstest_macros v0.21.0 (proc-macro) (*)

http v0.2.12
├── h2 v0.3.27 (*)
├── http-body v0.4.6
│   ├── hyper v0.14.32 (*)
│   └── reqwest v0.11.27 (*)
├── http-types v2.12.0 (*)
├── hyper v0.14.32 (*)
└── reqwest v0.11.27 (*)

http v1.3.1
├── axum v0.7.9 (*)
├── axum v0.8.4 (*)
├── axum-core v0.4.5 (*)
├── axum-core v0.5.2 (*)
├── google-cloud-gax v0.19.2 (*)
├── h2 v0.4.11 (*)
├── http-body v1.0.1
│   ├── axum v0.7.9 (*)
│   ├── axum v0.8.4 (*)
│   ├── axum-core v0.4.5 (*)
│   ├── axum-core v0.5.2 (*)
│   ├── http-body-util v0.1.3
│   │   ├── axum v0.7.9 (*)
│   │   ├── axum v0.8.4 (*)
│   │   ├── axum-core v0.4.5 (*)
│   │   ├── axum-core v0.5.2 (*)
│   │   ├── hyper-tls v0.6.0 (*)
│   │   ├── mockito v1.7.0 (*)
│   │   ├── reqwest v0.12.22 (*)
│   │   └── tonic v0.12.3 (*)
│   ├── hyper v1.6.0 (*)
│   ├── hyper-util v0.1.15 (*)
│   ├── mockito v1.7.0 (*)
│   ├── reqwest v0.12.22 (*)
│   ├── tonic v0.12.3 (*)
│   └── tower-http v0.6.6 (*)
├── http-body-util v0.1.3 (*)
├── hyper v1.6.0 (*)
├── hyper-util v0.1.15 (*)
├── mockito v1.7.0 (*)
├── reqwest v0.12.22 (*)
├── reqwest-middleware v0.4.2 (*)
├── tonic v0.12.3 (*)
├── tower-http v0.6.6 (*)
├── tungstenite v0.26.2 (*)
└── tungstenite v0.27.0 (*)

http-body v0.4.6 (*)

http-body v1.0.1 (*)

hyper v0.14.32 (*)

hyper v1.6.0 (*)

hyper-tls v0.5.0 (*)

hyper-tls v0.6.0 (*)

indexmap v1.9.3 (*)

indexmap v2.10.0 (*)

itertools v0.10.5
├── criterion v0.5.1 (*)
└── criterion-plot v0.5.0
    └── criterion v0.5.1 (*)

itertools v0.12.1
└── prost-derive v0.12.6 (proc-macro)
    └── prost v0.12.6
        └── prost-types v0.12.6
            └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)

itertools v0.14.0
└── prost-derive v0.13.5 (proc-macro)
    └── prost v0.13.5
        ├── google-cloud-googleapis v0.16.1 (*)
        ├── google-cloud-longrunning v0.21.0 (*)
        ├── prost-types v0.13.5
        │   ├── google-cloud-googleapis v0.16.1 (*)
        │   ├── google-cloud-pubsub v0.30.0 (*)
        │   └── google-cloud-spanner v0.33.0 (*)
        └── tonic v0.12.3 (*)

matchit v0.7.3
└── axum v0.7.9 (*)

matchit v0.8.4
└── axum v0.8.4 (*)

nom v7.1.3
└── config v0.13.4 (*)

nom v8.0.0
└── iso8601 v0.6.3
    └── jsonschema v0.18.3 (*)

num-traits v0.2.19
└── chrono v0.4.41 (*)

num-traits v0.2.19
├── bigdecimal v0.4.8
│   └── google-cloud-spanner v0.33.0 (*)
├── chrono v0.4.41 (*)
├── criterion v0.5.1 (*)
├── num v0.4.3
│   └── fraction v0.15.3
│       └── jsonschema v0.18.3 (*)
├── num-bigint v0.4.6
│   ├── bigdecimal v0.4.8 (*)
│   ├── num v0.4.3 (*)
│   ├── num-rational v0.4.2
│   │   └── num v0.4.3 (*)
│   └── simple_asn1 v0.6.3
│       └── jsonwebtoken v9.3.1 (*)
├── num-complex v0.4.6
│   └── num v0.4.3 (*)
├── num-integer v0.1.46
│   ├── bigdecimal v0.4.8 (*)
│   ├── num v0.4.3 (*)
│   ├── num-bigint v0.4.6 (*)
│   ├── num-iter v0.1.45
│   │   └── num v0.4.3 (*)
│   └── num-rational v0.4.2 (*)
├── num-iter v0.1.45 (*)
├── num-rational v0.4.2 (*)
├── plotters v0.3.7
│   └── criterion v0.5.1 (*)
├── proptest v1.7.0 (*)
└── simple_asn1 v0.6.3 (*)

parking_lot v0.11.2
└── tokei v12.1.2 (*)

parking_lot v0.12.4
├── bb8 v0.8.6
│   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
├── google-cloud-spanner v0.33.0 (*)
├── governor v0.6.3 (*)
├── jsonschema v0.18.3 (*)
├── prometheus v0.14.0
│   └── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
└── tokio v1.46.1
    ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    ├── async-compression v0.4.27
    │   └── tower-http v0.6.6 (*)
    ├── axum v0.8.4 (*)
    ├── bb8 v0.8.6 (*)
    ├── combine v4.6.7
    │   └── redis v0.25.4 (*)
    ├── deadpool v0.9.5
    │   └── wiremock v0.5.22 (*)
    ├── google-cloud-auth v0.16.0 (*)
    ├── google-cloud-auth v0.17.2 (*)
    ├── google-cloud-gax v0.19.2 (*)
    ├── google-cloud-metadata v0.5.1 (*)
    ├── google-cloud-pubsub v0.30.0 (*)
    ├── google-cloud-spanner v0.33.0 (*)
    ├── google-cloud-storage v0.24.0 (*)
    ├── h2 v0.3.27 (*)
    ├── h2 v0.4.11 (*)
    ├── hyper v0.14.32 (*)
    ├── hyper v1.6.0 (*)
    ├── hyper-timeout v0.5.2 (*)
    ├── hyper-tls v0.5.0 (*)
    ├── hyper-tls v0.6.0 (*)
    ├── hyper-util v0.1.15 (*)
    ├── mockito v1.7.0 (*)
    ├── redis v0.25.4 (*)
    ├── reqwest v0.11.27 (*)
    ├── reqwest v0.12.22 (*)
    ├── tokio-native-tls v0.3.1 (*)
    ├── tokio-retry v0.3.0 (*)
    ├── tokio-retry2 v0.5.7
    │   └── google-cloud-gax v0.19.2 (*)
    ├── tokio-rustls v0.26.2 (*)
    ├── tokio-stream v0.1.17
    │   └── tonic v0.12.3 (*)
    ├── tokio-tungstenite v0.26.2 (*)
    ├── tokio-tungstenite v0.27.0 (*)
    ├── tokio-util v0.7.15
    │   ├── combine v4.6.7 (*)
    │   ├── google-cloud-pubsub v0.30.0 (*)
    │   ├── google-cloud-spanner v0.33.0 (*)
    │   ├── h2 v0.3.27 (*)
    │   ├── h2 v0.4.11 (*)
    │   ├── redis v0.25.4 (*)
    │   ├── reqwest v0.12.22 (*)
    │   ├── tower v0.4.13 (*)
    │   └── tower-http v0.6.6 (*)
    ├── tonic v0.12.3 (*)
    ├── tower v0.4.13 (*)
    ├── tower v0.5.2
    │   ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    │   ├── axum v0.7.9 (*)
    │   ├── axum v0.8.4 (*)
    │   ├── reqwest v0.12.22 (*)
    │   └── tower-http v0.6.6 (*)
    ├── tower-http v0.6.6 (*)
    └── wiremock v0.5.22 (*)

parking_lot_core v0.8.6
└── parking_lot v0.11.2 (*)

parking_lot_core v0.9.11
├── dashmap v5.5.3 (*)
├── dashmap v6.1.0 (*)
└── parking_lot v0.12.4 (*)

prost v0.12.6 (*)

prost v0.13.5 (*)

prost-derive v0.12.6 (proc-macro) (*)

prost-derive v0.13.5 (proc-macro) (*)

prost-types v0.12.6 (*)

prost-types v0.13.5 (*)

rand v0.7.3 (*)

rand v0.8.5 (*)

rand v0.9.1 (*)

rand_chacha v0.2.2 (*)

rand_chacha v0.3.1 (*)

rand_chacha v0.9.0 (*)

rand_core v0.5.1 (*)

rand_core v0.6.4 (*)

rand_core v0.9.3 (*)

regex-automata v0.1.10
└── matchers v0.1.0
    └── tracing-subscriber v0.3.19 (*)

regex-automata v0.4.9 (*)

regex-syntax v0.6.29
└── regex-automata v0.1.10 (*)

regex-syntax v0.8.5
├── fancy-regex v0.13.0 (*)
├── globset v0.4.16 (*)
├── proptest v1.7.0 (*)
├── regex v1.11.1 (*)
└── regex-automata v0.4.9 (*)

reqwest v0.11.27 (*)

reqwest v0.12.22 (*)

rustls-pemfile v1.0.4 (*)

rustls-pemfile v2.2.0
└── tonic v0.12.3 (*)

serde v1.0.219
├── ahash v0.8.12 (*)
├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
├── assert-json-diff v2.0.2
│   ├── mockito v1.7.0 (*)
│   └── wiremock v0.5.22 (*)
├── axum v0.7.9 (*)
├── axum v0.8.4 (*)
├── bigdecimal v0.4.8 (*)
├── chrono v0.4.41 (*)
├── ciborium v0.2.2
│   └── criterion v0.5.1 (*)
├── config v0.13.4 (*)
├── criterion v0.5.1 (*)
├── dashmap v4.0.2 (*)
├── deranged v0.4.0
│   └── time v0.3.41
│       ├── google-cloud-auth v0.16.0 (*)
│       ├── google-cloud-auth v0.17.2 (*)
│       ├── google-cloud-spanner v0.33.0 (*)
│       ├── google-cloud-storage v0.24.0 (*)
│       ├── jsonschema v0.18.3 (*)
│       └── simple_asn1 v0.6.3 (*)
├── google-cloud-auth v0.16.0 (*)
├── google-cloud-auth v0.17.2 (*)
├── google-cloud-spanner v0.33.0 (*)
├── google-cloud-storage v0.24.0 (*)
├── http-types v2.12.0 (*)
├── json5 v0.4.1 (*)
├── jsonschema v0.18.3 (*)
├── jsonwebtoken v9.3.1 (*)
├── reqwest v0.11.27 (*)
├── reqwest v0.12.22 (*)
├── reqwest-middleware v0.4.2 (*)
├── ron v0.7.1 (*)
├── serde_json v1.0.140
│   ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   ├── assert-json-diff v2.0.2 (*)
│   ├── axum v0.8.4 (*)
│   ├── config v0.13.4 (*)
│   ├── criterion v0.5.1 (*)
│   ├── google-cloud-auth v0.16.0 (*)
│   ├── google-cloud-auth v0.17.2 (*)
│   ├── google-cloud-storage v0.24.0 (*)
│   ├── http-types v2.12.0 (*)
│   ├── jsonschema v0.18.3 (*)
│   ├── jsonwebtoken v9.3.1 (*)
│   ├── mockito v1.7.0 (*)
│   ├── reqwest v0.11.27 (*)
│   ├── reqwest v0.12.22 (*)
│   ├── tinytemplate v1.2.1
│   │   └── criterion v0.5.1 (*)
│   ├── tokei v12.1.2 (*)
│   ├── validator v0.20.0 (*)
│   └── wiremock v0.5.22 (*)
├── serde_path_to_error v0.1.17
│   └── axum v0.8.4 (*)
├── serde_qs v0.8.5
│   └── http-types v2.12.0 (*)
├── serde_spanned v0.6.9
│   ├── toml v0.8.23 (*)
│   └── toml_edit v0.22.27 (*)
├── serde_urlencoded v0.7.1
│   ├── axum v0.8.4 (*)
│   ├── http-types v2.12.0 (*)
│   ├── mockito v1.7.0 (*)
│   ├── reqwest v0.11.27 (*)
│   └── reqwest v0.12.22 (*)
├── serde_yaml v0.9.34+deprecated (*)
├── time v0.3.41 (*)
├── tinytemplate v1.2.1 (*)
├── tokei v12.1.2 (*)
├── toml v0.5.11
│   ├── config v0.13.4 (*)
│   └── tokei v12.1.2 (*)
├── toml v0.8.23 (*)
├── toml_datetime v0.6.11
│   ├── toml v0.8.23 (*)
│   └── toml_edit v0.22.27 (*)
├── toml_edit v0.22.27 (*)
├── url v2.5.4
│   ├── git2 v0.19.0 (*)
│   ├── google-cloud-storage v0.24.0 (*)
│   ├── http-types v2.12.0 (*)
│   ├── jsonschema v0.18.3 (*)
│   ├── redis v0.25.4 (*)
│   ├── reqwest v0.11.27 (*)
│   ├── reqwest v0.12.22 (*)
│   └── validator v0.20.0 (*)
├── uuid v1.17.0 (*)
├── validator v0.20.0 (*)
└── wiremock v0.5.22 (*)

serde v1.0.219
├── serde_json v1.0.140
│   └── tera v1.20.0 (*)
│   [build-dependencies]
│   ├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
│   └── tokei v12.1.2 (*)
└── tera v1.20.0 (*)

serde_json v1.0.140 (*)

serde_json v1.0.140 (*)

sha2 v0.10.9 (*)

sha2 v0.10.9 (*)

strsim v0.8.0
└── clap v2.34.0 (*)

strsim v0.11.1
├── clap_builder v4.5.41
│   └── clap v4.5.41 (*)
└── darling_core v0.20.11
    ├── darling v0.20.11
    │   ├── dummy v0.8.0 (proc-macro)
    │   │   └── fake v2.10.0 (*)
    │   └── validator_derive v0.20.0 (proc-macro)
    │       └── validator v0.20.0 (*)
    └── darling_macro v0.20.11 (proc-macro)
        └── darling v0.20.11 (*)

sync_wrapper v0.1.2
└── reqwest v0.11.27 (*)

sync_wrapper v1.0.2
├── axum v0.7.9 (*)
├── axum v0.8.4 (*)
├── axum-core v0.4.5 (*)
├── axum-core v0.5.2 (*)
├── reqwest v0.12.22 (*)
└── tower v0.5.2 (*)

thiserror v1.0.69
├── analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
├── google-cloud-auth v0.16.0 (*)
├── google-cloud-auth v0.17.2 (*)
├── google-cloud-gax v0.19.2 (*)
├── google-cloud-metadata v0.5.1 (*)
├── google-cloud-pubsub v0.30.0 (*)
├── google-cloud-spanner v0.33.0 (*)
├── google-cloud-storage v0.24.0 (*)
├── protobuf v3.7.2
│   └── prometheus v0.14.0 (*)
├── protobuf-support v3.7.2
│   └── protobuf v3.7.2 (*)
├── reqwest-middleware v0.4.2 (*)
└── serde_qs v0.8.5 (*)

thiserror v2.0.12
├── pest v2.8.1
│   ├── json5 v0.4.1 (*)
│   ├── pest_derive v2.8.1 (proc-macro) (*)
│   ├── pest_generator v2.8.1 (*)
│   ├── pest_meta v2.8.1 (*)
│   └── tera v1.20.0 (*)
├── prometheus v0.14.0 (*)
├── simple_asn1 v0.6.3 (*)
├── tungstenite v0.26.2 (*)
└── tungstenite v0.27.0 (*)

thiserror-impl v1.0.69 (proc-macro)
└── thiserror v1.0.69 (*)

thiserror-impl v2.0.12 (proc-macro)
└── thiserror v2.0.12 (*)

tokio-tungstenite v0.26.2 (*)

tokio-tungstenite v0.27.0 (*)

toml v0.5.11 (*)

toml v0.8.23 (*)

toml_datetime v0.6.11
└── toml_edit v0.22.27 (*)

toml_datetime v0.6.11 (*)

toml_edit v0.22.27 (*)

toml_edit v0.22.27 (*)

tower v0.4.13 (*)

tower v0.5.2 (*)

tree-sitter v0.20.10 (*)

tree-sitter v0.22.6 (*)

tungstenite v0.26.2 (*)

tungstenite v0.27.0 (*)

webpki-roots v0.26.11
└── tonic v0.12.3 (*)

webpki-roots v1.0.1
└── webpki-roots v0.26.11 (*)
