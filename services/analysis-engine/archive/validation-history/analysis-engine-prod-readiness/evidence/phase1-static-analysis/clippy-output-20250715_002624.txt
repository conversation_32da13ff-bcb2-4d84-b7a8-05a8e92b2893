    Checking futures-io v0.3.31
    Checking regex-automata v0.4.9
   Compiling num-traits v0.2.19
    Checking url v2.5.4
    Checking either v1.15.0
   Compiling analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    Checking futures-util v0.3.31
    Checking rayon v1.10.0
error: used `unwrap()` on a `Result` value
  --> build.rs:26:33
   |
26 |     let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());
   |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: if this value is an `Err`, it will panic
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unwrap_used
   = note: `-D clippy::unwrap-used` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::unwrap_used)]`

error: used `expect()` on a `Result` value
  --> build.rs:79:20
   |
79 |       let metadata = Command::new("cargo")
   |  ____________________^
80 | |         .arg("metadata")
81 | |         .arg("--format-version=1")
82 | |         .output()
83 | |         .expect("Failed to run cargo metadata");
   | |_______________________________________________^
   |
   = note: if this value is an `Err`, it will panic
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#expect_used
   = note: `-D clippy::expect-used` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::expect_used)]`

error: used `expect()` on a `Result` value
  --> build.rs:86:9
   |
86 |         serde_json::from_slice(&metadata.stdout).expect("Failed to parse cargo metadata");
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: if this value is an `Err`, it will panic
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#expect_used

error: used `unwrap()` on an `Option` value
  --> build.rs:88:20
   |
88 |     let packages = metadata["packages"].as_array().unwrap();
   |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: if this value is `None`, it will panic
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unwrap_used

error: used `unwrap()` on an `Option` value
  --> build.rs:92:20
   |
92 |         let name = package["name"].as_str().unwrap();
   |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: if this value is `None`, it will panic
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unwrap_used

error: used `unwrap()` on an `Option` value
  --> build.rs:94:47
   |
94 |             let manifest_path = PathBuf::from(package["manifest_path"].as_str().unwrap());
   |                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: if this value is `None`, it will panic
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unwrap_used

error: used `unwrap()` on an `Option` value
  --> build.rs:97:17
   |
97 |                 manifest_path.parent().unwrap().to_path_buf(),
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: if this value is `None`, it will panic
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unwrap_used

error: variables can be used directly in the `format!` string
   --> build.rs:122:28
    |
122 |           bindings.push_str(&format!(
    |  ____________________________^
123 | |             "        \"{}\" => crate::parser::unsafe_bindings::load_language_unsafe(\"{}\").ok(),\n",
124 | |             grammar_name, grammar_name
125 | |         ));
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
    = note: `-D clippy::uninlined-format-args` implied by `-D warnings`
    = help: to override `-D warnings` add `#[allow(clippy::uninlined_format_args)]`

error: variables can be used directly in the `format!` string
   --> build.rs:134:28
    |
134 |         bindings.push_str(&format!("    \"{}\",\n", grammar_name));
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
134 -         bindings.push_str(&format!("    \"{}\",\n", grammar_name));
134 +         bindings.push_str(&format!("    \"{grammar_name}\",\n"));
    |

error: used `unwrap()` on a `Result` value
   --> build.rs:139:5
    |
139 |     fs::write(&dest_path, bindings).unwrap();
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = note: if this value is an `Err`, it will panic
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unwrap_used

error: could not compile `analysis-engine` (build script) due to 10 previous errors
warning: build failed, waiting for other jobs to finish...
