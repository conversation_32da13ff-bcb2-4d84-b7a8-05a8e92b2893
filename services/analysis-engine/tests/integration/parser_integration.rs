//! # Parser and Analyzer Integration Tests
//!
//! ## Purpose
//! This module tests the critical integration point between the `parser` and `analyzer`
//! components. The goal is to ensure that the data structures produced by the parser
//! (e.g., an Abstract Syntax Tree or a Concrete Syntax Tree) can be correctly consumed
//! and interpreted by the analyzer to produce meaningful code intelligence.
//!
//! ## Methodology
//! - These tests operate on small, well-defined snippets of real code.
//! - They call the actual parser to generate a parse tree.
//! - They then pass this parse tree to the actual analyzer.
//! - Finally, they assert that the analyzer's output (e.g., list of functions,
//!   structs, dependencies) is correct for the given code snippet.
//!
//! ## Execution
//! While faster than E2E tests, these can be slower than unit tests. They are part
//! of the default test suite but can be run specifically.
//!
//! ```bash
//! cargo test --test parser_integration
//! ```

use analysis_engine::config::ServiceConfig;
use analysis_engine::parser::TreeSitterParser;
use std::fs::File;
use std::io::Write;
use std::sync::Arc;
use tempfile::NamedTempFile;

/// # Test: `test_parser_identifies_symbols_correctly`
///
/// ## Description
/// This test uses the real `TreeSitterParser` to parse a temporary file containing a
/// simple Rust function. It then verifies that the resulting `FileAnalysis` object
/// contains the correct symbol information.
///
/// ## Success Criteria
/// - The parser must successfully parse the file without errors.
/// - The `symbols` field of the `FileAnalysis` result must contain one symbol.
/// - The found symbol must have the correct name ("hello_world") and kind ("function").
#[tokio::test]
async fn test_parser_identifies_symbols_correctly() {
    // --- Arrange ---

    // 1. Create the test source code.
    let source_code = r#"
        fn hello_world() {
            println!("Hello, parser!");
        }
    "#;

    // 2. Write the code to a temporary file with a `.rs` extension so the parser
    // can identify the language.
    let mut temp_file = NamedTempFile::new().unwrap();
    temp_file.write_all(source_code.as_bytes()).unwrap();
    let temp_path = temp_file.into_temp_path();
    // Rename the file to have the correct extension.
    let rs_path = temp_path.parent().unwrap().join("test_file.rs");
    std::fs::rename(&temp_path, &rs_path).unwrap();

    // 3. Instantiate the real `TreeSitterParser`.
    let config = Arc::new(ServiceConfig::default());
    let parser = TreeSitterParser::new(config).expect("Failed to create TreeSitterParser.");

    // --- Act ---
    // 4. Parse the file.
    let analysis_result = parser
        .parse_file(&rs_path)
        .await
        .expect("The file should parse successfully.");

    // --- Assert ---
    // 5. Verify the results.
    assert_eq!(
        analysis_result.language, "rust",
        "The language should be detected as Rust."
    );

    let symbols = analysis_result
        .symbols
        .expect("Symbols should have been extracted.");
    assert_eq!(symbols.len(), 1, "Expected to find exactly one symbol.");

    let found_symbol = &symbols[0];
    assert_eq!(found_symbol.name, "hello_world");
    assert_eq!(found_symbol.kind, "function");

    println!(
        "Successfully parsed file and found symbol: '{}' of kind '{}'",
        found_symbol.name, found_symbol.kind
    );

    // The temporary file is automatically cleaned up when `rs_path` goes out of scope.
}
