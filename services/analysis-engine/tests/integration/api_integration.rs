//! Integration tests for the API endpoints of the Analysis Engine.
//!
//! These tests are designed to verify the behavior of the API layer when integrated
//! with other components of the application, such as the service layer.
//! They use a real router instance but may mock or use test instances of deeper
//! dependencies like databases.

use analysis_engine::api::handlers;
use axum::{
    body::Body,
    http::{Request, StatusCode},
    routing::get,
    Router,
};
use serde::Deserialize;
use tower::ServiceExt; // Required for `oneshot`.

/// A minimal router for testing the languages endpoint.
async fn setup_test_app() -> Router {
    Router::new().route("/api/v1/languages", get(handlers::supported_languages))
}

/// Represents the structure of the JSON response from the `/api/v1/languages` endpoint.
/// Only fields relevant for this test are deserialized.
#[derive(Deserialize, Debug)]
struct LanguagesResponse {
    languages: Vec<String>,
    capabilities: Capabilities,
}

#[derive(Deserialize, Debug)]
struct Capabilities {
    language_detection: usize,
}

/// # Test: `test_get_languages_endpoint`
///
/// ## Description
/// This test verifies that the `/api/v1/languages` endpoint is functioning correctly. It checks for the correct
/// status code, response format, and validates key figures in the response.
///
/// ## Success Criteria
/// - The endpoint must return a `200 OK` HTTP status.
/// - The response body must be a valid JSON object matching the expected structure.
/// - The `capabilities.language_detection` field must report the correct number of detectable languages.
/// - The `languages` array must contain the list of fully supported languages, including Rust.
#[tokio::test]
async fn test_get_languages_endpoint_returns_correct_format_and_count() {
    // --- Arrange ---
    // Create a minimal app containing only the route we want to test.
    let app = setup_test_app().await;

    // --- Act ---
    let response = app
        .oneshot(
            Request::builder()
                .uri("/api/v1/languages")
                .body(Body::empty())
                .expect("Failed to build request"),
        )
        .await
        .expect("Failed to execute request");

    // --- Assert ---
    // 1. Verify that the HTTP status code is 200 OK.
    assert_eq!(response.status(), StatusCode::OK);

    // 2. Extract the response body and parse it as JSON.
    let body_bytes = hyper::body::to_bytes(response.into_body())
        .await
        .expect("Failed to read response body");
    let body_str =
        String::from_utf8(body_bytes.to_vec()).expect("Response body was not valid UTF-8");

    let parsed_response: Result<LanguagesResponse, _> = serde_json::from_str(&body_str);

    // Ensure JSON parsing was successful. If not, the test fails with a clear message.
    assert!(
        parsed_response.is_ok(),
        "Failed to parse JSON response. Body: {}",
        body_str
    );
    let languages_response = parsed_response.unwrap();

    // 3. The primary validation: check if the number of detectable languages is correct.
    // As per the implementation, this is 15 (tree-sitter) + 3 (adapters) + 12 (future) = 30.
    // The original mission spec mentioned 31, which might be a future target.
    const EXPECTED_DETECTABLE_COUNT: usize = 30;
    assert_eq!(
        languages_response.capabilities.language_detection, EXPECTED_DETECTABLE_COUNT,
        "Expected {} detectable languages, but found {}. Response body: {}",
        EXPECTED_DETECTABLE_COUNT, languages_response.capabilities.language_detection, body_str
    );

    // 4. Spot-check: Verify that a known supported language ('rust') is present in the main list.
    assert!(
        languages_response
            .languages
            .iter()
            .any(|lang| lang == "rust"),
        "The language list should include 'rust'"
    );
}
