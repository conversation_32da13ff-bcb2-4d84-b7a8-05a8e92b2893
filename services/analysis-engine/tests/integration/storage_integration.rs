//! # Storage Integration Tests
//!
//! ## Purpose
//! This module contains integration tests for the storage layer. The primary goal is to
//! verify that the service can correctly interact with a real database instance. These
//! tests ensure that data serialization, deserialization, and querying logic work as
//! expected against an actual database schema.
//!
//! ## Methodology
//! - These tests connect to a dedicated, ephemeral test database (e.g., a temporary
//!   Postgres or Spanner instance spun up for the test run).
//! - They test the full lifecycle of data: creating, reading, updating, and deleting (CRUD).
//! - They operate on the service layer's public interface, not by calling storage
//!   functions directly. This ensures we are testing the integration point correctly.
//!
//! ## Execution
//! These tests require a running database and are therefore marked `#[ignore]`.
//! They should be run as part of a dedicated integration test suite.
//!
//! ```bash
//! cargo test --test storage_integration -- --ignored --nocapture
//! ```

use analysis_engine::models::AnalysisResult;
use analysis_engine::services::analyzer::StorageManager;
use analysis_engine::storage::{StorageOperations, connection_pool::SpannerPool};
use analysis_engine::config::ServiceConfig;
use std::sync::Arc;
use chrono::Utc;

// Real storage service wrapper for testing
struct TestStorageService {
    storage_manager: StorageManager,
    config: Arc<ServiceConfig>,
}

impl TestStorageService {
    /// Connects to the test database using real storage components.
    async fn new_for_test() -> Result<Self, String> {
        println!("Connecting to the test database...");
        
        // Create test configuration
        let config = Arc::new(ServiceConfig {
            service: analysis_engine::config::ServiceSettings {
                name: "test-analysis-engine".to_string(),
                version: "0.1.0".to_string(),
                port: 8001,
                host: "127.0.0.1".to_string(),
                environment: analysis_engine::config::Environment::Development,
            },
            gcp: analysis_engine::config::GcpSettings {
                project_id: "test-project".to_string(),
                spanner_instance: "test-instance".to_string(),
                spanner_database: "test-database".to_string(),
                storage_bucket: "test-bucket".to_string(),
                storage_bucket_name: "test-bucket-name".to_string(),
                pubsub_topic: "test-topic".to_string(),
                region: "us-central1".to_string(),
            },
            analysis: analysis_engine::config::AnalysisSettings {
                max_concurrent_analyses: 10,
                max_repository_size_gb: 1,
                analysis_timeout_seconds: 300,
                max_file_size_mb: 10,
                temp_dir: "/tmp/test".to_string(),
                supported_languages: vec!["rust".to_string()],
            },
            security: analysis_engine::config::SecuritySettings {
                enable_auth: false,
                api_key_header: "x-api-key".to_string(),
                jwt_secret: Some("test-secret".to_string()),
                cors_origins: vec!["*".to_string()],
            },
            observability: analysis_engine::config::ObservabilitySettings {
                enable_tracing: false,
                enable_metrics: false,
                log_level: "info".to_string(),
                otel_endpoint: None,
            },
            circuit_breaker: Default::default(),
            redis: Default::default(),
            resource_limits: Default::default(),
        });
        
        // Create storage operations for testing
        let storage_client = Arc::new(
            StorageOperations::new_for_testing()
                .await
                .map_err(|e| format!("Failed to create storage client: {}", e))?,
        );
        
        // Create storage manager with test configuration
        let storage_manager = StorageManager::new(
            None, // No Spanner pool for basic tests
            storage_client,
            None, // No backpressure manager
        );
        
        Ok(Self {
            storage_manager,
            config,
        })
    }

    /// Saves an analysis result to the database using real storage logic.
    async fn save_result(&self, result: &AnalysisResult) -> Result<(), String> {
        self.storage_manager
            .store_analysis_result(result)
            .await
            .map_err(|e| format!("Failed to save result: {}", e))
    }

    /// Retrieves an analysis result by its ID using real storage logic.
    async fn get_result(&self, id: &str) -> Result<Option<AnalysisResult>, String> {
        // This will fail until actual storage retrieval is implemented
        // This is intentional - the test should fail until real functionality exists
        Err(format!("Storage retrieval not yet implemented for ID: {}", id))
    }
}

/// # Test: `test_save_and_retrieve_analysis_result`
///
/// ## Description
/// This test verifies the basic CRUD (Create, Read) functionality of the storage service.
/// It ensures that an analysis result can be saved to the database and then retrieved
/// accurately.
///
/// ## Success Criteria
/// - The `save_result` method must execute without errors.
/// - The `get_result` method must retrieve a result that is identical to the one saved.
#[tokio::test]
#[ignore] // Ignored by default as it requires a live test database.
async fn test_save_and_retrieve_analysis_result() {
    // --- Arrange ---
    // 1. Set up the connection to the test database.
    let storage_service = TestStorageService::new_for_test().await
        .expect("Failed to create test storage service");

    // 2. Create a sample analysis result object to be saved.
    let original_result = AnalysisResult {
        id: "test-id-123".to_string(),
        repository_url: "https://github.com/example/repo".to_string(),
        branch: "main".to_string(),
        commit_hash: Some("abc123".to_string()),
        repository_size_bytes: Some(1024 * 1024), // 1MB
        clone_time_ms: Some(5000),
        status: analysis_engine::models::AnalysisStatus::Completed,
        started_at: Utc::now(),
        completed_at: Some(Utc::now()),
        duration_seconds: Some(30),
        progress: Some(100.0),
        current_stage: Some("Completed".to_string()),
        estimated_completion: None,
        metrics: None,
        patterns: vec![],
        languages: vec![],
        embeddings: None,
        error_message: None,
        failed_files: vec![],
        successful_analyses: None,
        user_id: "test-user".to_string(),
        webhook_url: None,
        file_count: 10,
        success_rate: 100.0,
        performance_metrics: None,
        warnings: vec![],
    };

    // --- Act ---
    // 3. Save the result to the database.
    let save_operation = storage_service.save_result(&original_result).await;
    assert!(save_operation.is_ok(), "Failed to save the result.");

    // 4. Attempt to retrieve the same result by its ID.
    let retrieved_result = storage_service
        .get_result("test-id-123")
        .await
        .expect("Database query for get_result failed.")
        .expect("No result found for the given ID, but one was expected.");

    // --- Assert ---
    // 5. Verify that the retrieved data matches the original data.
    // This test will fail until actual storage retrieval is implemented
    assert_eq!(retrieved_result.id, original_result.id);
    assert_eq!(retrieved_result.repository_url, original_result.repository_url);
    assert_eq!(retrieved_result.branch, original_result.branch);
    assert_eq!(retrieved_result.status, original_result.status);

    println!("Successfully saved and retrieved analysis result.");
}
