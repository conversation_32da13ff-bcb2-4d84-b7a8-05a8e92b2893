# Analysis-Engine Testing Framework: A Guide to Evidence-Based Validation

## 1. Overview & Philosophy

This document outlines the testing framework for the `analysis-engine` service. This framework was redesigned from the ground up to address critical flaws in the previous system, which led to inflated and unreliable performance metrics.

Our testing philosophy is rooted in **truth, transparency, and evidence**. We prioritize honest, reproducible validation over synthetic, inflated claims. Every test in this framework is designed to serve a clear purpose and produce verifiable evidence of the engine's real-world capabilities.

The core goal is to build and maintain trust in our software through a rigorous, honest, and transparent validation process.

## 2. Core Principles

1.  **Truth Over Hype**: We report honest metrics, even if they are lower than aspirational goals. The focus is on accuracy, not vanity.
2.  **Evidence Over Claims**: Every performance or capability claim must be backed by a verifiable, raw evidence file stored in the `validation-evidence/` directory.
3.  **Real Over Synthetic**: We exclusively use code from real-world open-source repositories for performance, E2E, and validation testing. Synthetic data is forbidden for any performance measurement.
4.  **Simple Over Complex**: Tests should be clear, maintainable, and easy to understand. Complexity is only introduced when absolutely necessary and must be thoroughly documented.
5.  **Automated Over Manual**: Our validation is designed to be run continuously and automatically to detect regressions and ensure ongoing quality.

## 3. Directory Structure

The `tests/` directory is organized by test type and purpose. This structure ensures a clear separation of concerns and makes it easy to locate and execute specific kinds of tests.

-   `README.md`: (This file) The comprehensive guide to our testing strategy.
-   `unit/`: Fast, isolated tests for individual components (functions, structs, modules). They must not have external dependencies like databases or networks.
    -   `parser/`: Unit tests for `tree-sitter` parsing logic.
    -   `analyzer/`: Unit tests for the code analysis logic.
    -   `storage/`: Unit tests for the storage layer, using mocks.
    -   `api/`: Unit tests for individual API handlers.
-   `integration/`: Tests for interactions between different modules of the `analysis-engine`. These tests verify the "plumbing" between components.
    -   `api_integration.rs`: Tests API endpoints with a real (but test-only) service layer.
    -   `storage_integration.rs`: Tests the service layer with a real test database.
    -   `parser_integration.rs`: Tests the interaction between the parser and the analyzer.
-   `e2e/`: End-to-end tests that simulate full user workflows from the API request to the final result.
    -   `analysis_workflow.rs`: Tests the complete analysis pipeline for a small repository.
    -   `real_repository_test.rs`: Runs the pipeline against larger, real repositories.
    -   `concurrent_analysis.rs`: Tests the system's ability to handle multiple analyses simultaneously.
-   `performance/`: Performance validation and benchmarking tests. These are ignored by default and must be run explicitly.
    -   `throughput_validation.rs`: Measures and validates LOC/second processing speed.
    -   `memory_profiling.rs`: Tracks memory usage during analysis.
    -   `scale_testing.rs`: Tests performance on very large repositories.
    -   `regression_prevention.rs`: Compares performance against established baselines.
-   `security/`: Tests focused on the security of the application.
    -   `vulnerability_scan.rs`: Placeholder for integration with dependency vulnerability scanners.
    -   `injection_tests.rs`: Tests API inputs for common injection vulnerabilities.
    -   `auth_tests.rs`: Validates authentication and authorization middleware and logic.
-   `validation/`: Tests that provide an "evidence-based" check on our own metrics using trusted, external tools.
    -   `loc_verification.rs`: Cross-validates our internal LOC count against `cloc` and `tokei`.
    -   `parse_success_rate.rs`: Measures the percentage of files that parse successfully for each language.
    -   `evidence_collector.rs`: Helper module for running external tools and gathering evidence.
    -   `truth_validator.rs`: A suite of tests to verify specific, documented claims about the system.
-   `fixtures/`: Shared test code, utilities, and helpers.
    -   `real_repos.rs`: Helpers for cloning and managing real git repositories for testing.
    -   `test_helpers.rs`: Common setup/teardown functions and other utilities.
    -   `mock_builder.rs`: Builders for creating mock objects. Mocking is discouraged and should be used sparingly.
-   `real-data/`: Contains git submodules or scripts to fetch real open-source repositories used for testing.
    -   `README.md`: Explains how to manage the real data samples.
    -   `small/`, `medium/`, `large/`: Subdirectories for organizing repositories by size.
-   `validation-evidence/`: **(Git-ignored)** The output directory for all raw evidence generated by performance and validation tests.
    -   `performance/`: Raw timing data, `perf` outputs, and memory profiles.
    -   `accuracy/`: Reports on parse success rates and LOC discrepancies.
    -   `verification/`: Raw outputs from `cloc`, `tokei`, and other external tools.

## 4. How to Run Tests

### Check Compilation
To ensure all tests, including ignored ones, compile without errors:
```bash
cargo test --no-run
```

### Run Unit Tests
Unit tests are tagged as part of the library and are designed to be fast.
```bash
cargo test --lib
```

### Run Integration Tests
Run a specific category of tests by name.
```bash
# Run all integration tests
cargo test --test '*integration*'

# Run a specific integration test file
cargo test --test api_integration
```

### Run Performance Validation Tests
Performance tests are marked with `#[ignore]` to prevent them from running on every `cargo test` command. They must be run explicitly. The `--nocapture` flag is essential to see the evidence output.

```bash
# Run the primary throughput validation test and see its output
cargo test --test throughput_validation -- --ignored --nocapture
```

### Run All Tests
To run every test (unit, integration, e2e, etc.), use the following command. Be aware this will be slow and may require network access.
```bash
cargo test -- --include-ignored
```

## 5. Evidence-Based Framework

No performance claim is valid without proof.
1.  **Evidence Generation**: Performance and validation tests MUST use helper functions from `fixtures/` and `validation/` to execute external commands (`cloc`, `/usr/bin/time -v`, etc.).
2.  **Evidence Storage**: All raw output from these commands must be saved to a timestamped file in the appropriate subdirectory of `validation-evidence/`.
3.  **Evidence Manifest**: An `EVIDENCE-MANIFEST.md` file will be maintained at the root of `tests/`, linking specific performance claims to the exact evidence files that prove them.
4.  **No Magic Numbers**: Any performance metric cited in a pull request or report must reference an evidence file.

## 6. Anti-Patterns to Avoid

To prevent repeating past mistakes, the following practices are strictly forbidden:
-   **NEVER** create synthetic test data by duplicating code or directories.
-   **NEVER** make a performance claim without a corresponding, verifiable evidence file.
-   **NEVER** use magic numbers in performance assertions; compare against a documented, evidence-based baseline.
-   **NEVER** create monolithic test files. Keep tests organized in their respective modules.
-   **NEVER** commit a test that is marked `#[ignore]` without a clear and documented reason.

This framework is designed to be a living document. As we evolve, we will update our testing strategies, but always in adherence to the core principles of truth and evidence.