// episteme/services/analysis-engine/tests/fixtures/mock_builder.rs

//! # Mock Object Builder
//!
//! ## Purpose
//! This module provides builder patterns for creating mock objects to be used in unit tests.
//! While the testing philosophy of this project strongly favors using real components
//! wherever possible (see `real_repos.rs` and integration tests), there are isolated
//! scenarios where mocking a complex dependency is necessary for a fast, focused unit test.
//!
//! ## Philosophy on Mocking
//! **Use mocks sparingly.** Over-mocking can lead to brittle tests that are tightly coupled
//! to the implementation details rather than the behavior. Before using a mock from this
//! module, consider if an integration test with a real component would be more valuable.
//!
//! The builders provided here should be used to create test-specific, predictable objects
//! to isolate the component under test.
//!
//! ## Usage
//! ```rust,ignore
//! use crate::fixtures::mock_builder::MockAnalysisResultBuilder;
//!
//! #[test]
//! fn my_unit_test() {
//!     let mock_result = MockAnalysisResultBuilder::new()
//!         .with_id("test-123")
//!         .with_loc_count(5000)
//!         .build();
//!
//!     // ... use mock_result in the unit test ...
//! }
//! ```

// A placeholder for a real data model that might need mocking.
// This should mirror a real struct in the application.
#[derive(Debug, Clone)]
pub struct MockAnalysisResult {
    pub id: String,
    pub repo_url: String,
    pub loc_count: u64,
    pub language_breakdown: std::collections::HashMap<String, u64>,
    pub status: String,
}

/// A builder for creating instances of `MockAnalysisResult` for tests.
///
/// This allows for easy construction of test-specific data without needing to
/// manually fill in every field.
#[derive(Debug, Default)]
pub struct MockAnalysisResultBuilder {
    id: Option<String>,
    repo_url: Option<String>,
    loc_count: Option<u64>,
    language_breakdown: Option<std::collections::HashMap<String, u64>>,
    status: Option<String>,
}

impl MockAnalysisResultBuilder {
    /// Creates a new builder with default values.
    pub fn new() -> Self {
        Self::default()
    }

    /// Sets the ID for the mock result.
    pub fn with_id(mut self, id: &str) -> Self {
        self.id = Some(id.to_string());
        self
    }

    /// Sets the repository URL for the mock result.
    pub fn with_repo_url(mut self, url: &str) -> Self {
        self.repo_url = Some(url.to_string());
        self
    }

    /// Sets the line-of-code count for the mock result.
    pub fn with_loc_count(mut self, count: u64) -> Self {
        self.loc_count = Some(count);
        self
    }

    /// Sets the language breakdown for the mock result.
    pub fn with_languages(mut self, breakdown: std::collections::HashMap<String, u64>) -> Self {
        self.language_breakdown = Some(breakdown);
        self
    }

    /// Sets the status for the mock result.
    pub fn with_status(mut self, status: &str) -> Self {
        self.status = Some(status.to_string());
        self
    }

    /// Constructs the final `MockAnalysisResult` object.
    ///
    /// Any fields not explicitly set will be given sensible defaults.
    pub fn build(self) -> MockAnalysisResult {
        MockAnalysisResult {
            id: self.id.unwrap_or_else(|| "default-id".to_string()),
            repo_url: self
                .repo_url
                .unwrap_or_else(|| "https://github.com/example/default".to_string()),
            loc_count: self.loc_count.unwrap_or(1000),
            language_breakdown: self.language_breakdown.unwrap_or_default(),
            status: self.status.unwrap_or_else(|| "completed".to_string()),
        }
    }
}

// An example test demonstrating the use of the builder.
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_mock_builder_creates_correct_object() {
        // Arrange
        let mut languages = std::collections::HashMap::new();
        languages.insert("Rust".to_string(), 750);
        languages.insert("TOML".to_string(), 50);

        // Act
        let mock_result = MockAnalysisResultBuilder::new()
            .with_id("unique-test-id")
            .with_repo_url("https://example.com/test-repo")
            .with_loc_count(800)
            .with_languages(languages.clone())
            .with_status("success")
            .build();

        // Assert
        assert_eq!(mock_result.id, "unique-test-id");
        assert_eq!(mock_result.repo_url, "https://example.com/test-repo");
        assert_eq!(mock_result.loc_count, 800);
        assert_eq!(mock_result.language_breakdown, languages);
        assert_eq!(mock_result.status, "success");
    }

    #[test]
    fn test_mock_builder_uses_defaults() {
        // Arrange & Act
        let mock_result = MockAnalysisResultBuilder::new().build();

        // Assert
        assert_eq!(mock_result.id, "default-id");
        assert_eq!(mock_result.loc_count, 1000);
        assert!(mock_result.language_breakdown.is_empty());
    }
}
