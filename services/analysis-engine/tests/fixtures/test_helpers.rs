// episteme/services/analysis-engine/tests/fixtures/test_helpers.rs

//! # Common Test Helpers and Fixtures
//!
//! ## Purpose
//! This module provides shared utilities and setup/teardown logic that can be reused across
//! different types of tests, particularly integration and end-to-end tests. The goal is to
//! reduce boilerplate code and ensure a consistent testing environment.
//!
//! ## Key Components
//! - `TestContext`: A struct to hold common test resources like an HTTP client and a
//!   database connection.
//! - `setup_test_environment`: A high-level function that prepares everything needed for
//!   a full E2E test, including starting a server and initializing a database.
//! - Functions for creating specific test data or configurations.

use analysis_engine::create_app;
use std::net::SocketAddr;
use tokio::net::TcpListener;
use tokio::task::Join<PERSON><PERSON>le;

/// Represents a running instance of the application server for testing.
/// The server is shut down automatically when this struct is dropped.
pub struct TestServer {
    pub addr: SocketAddr,
    server_handle: <PERSON><PERSON><PERSON><PERSON><PERSON><()>,
}

impl TestServer {
    /// Starts the server on a random available port.
    pub async fn start() -> Self {
        // Create the application router by calling the function from main.rs.
        // Note: `create_app` from `main.rs` must be made public for this to work.
        let app = create_app().await.expect("Failed to create test app.");

        // Bind to a random available port by using port 0.
        let listener = TcpListener::bind("127.0.0.1:0")
            .await
            .expect("Failed to bind to random port.");
        let addr = listener.local_addr().expect("Failed to get local address.");

        println!("[Test Helper] Live test server listening on {}", addr);

        // Spawn the server in a background Tokio task.
        let server_handle = tokio::spawn(async move {
            axum::serve(listener, app)
                .await
                .expect("Test server failed to run.");
        });

        Self {
            addr,
            server_handle,
        }
    }

    /// Returns the base URL of the running test server.
    pub fn base_url(&self) -> String {
        format!("http://{}", self.addr)
    }
}

impl Drop for TestServer {
    fn drop(&mut self) {
        // Abort the server task when the TestServer goes out of scope.
        // This ensures the port is freed and the test run can exit cleanly.
        println!("[Test Helper] Shutting down test server at {}", self.addr);
        self.server_handle.abort();
    }
}

mod mock_db {
    pub struct TestDbConnection;
    impl TestDbConnection {
        pub async fn new() -> Self {
            println!("[Test Helper] Mock database connection created and migrations run.");
            Self
        }
    }
}

/// A context object to hold all the necessary clients and handles for a test.
///
/// This simplifies passing around common resources like an HTTP client or a database pool
/// to test functions. It also helps manage teardown logic via its `Drop` implementation.
pub struct TestContext {
    pub http_client: reqwest::Client,
    pub server: TestServer,
    pub db_conn: mock_db::TestDbConnection,
}

/// Sets up a complete test environment for E2E or integration tests.
///
/// This is the primary entry point for tests that need a live server and database.
/// It starts the server, connects to the database, and returns a `TestContext`
/// containing all the necessary handles.
///
/// # Returns
/// A `TestContext` ready for use in a test.
pub async fn setup_full_test_environment() -> TestContext {
    println!("\n--- Setting up full test environment ---");

    // In a real test suite, you'd initialize logging, load config from env vars, etc.
    // std::env::set_var("RUST_LOG", "info");
    // let _ = env_logger::builder().is_test(true).try_init();

    let server = TestServer::start().await;
    let db_conn = mock_db::TestDbConnection::new().await;
    let http_client = reqwest::Client::new();

    println!("--- Test environment setup complete ---\n");

    TestContext {
        http_client,
        server,
        db_conn,
    }
}

/// A simple helper to generate a unique ID for test entities.
pub fn generate_unique_id() -> String {
    format!("test-id-{}", uuid::Uuid::new_v4())
}

// Example of how to use the setup function in a test.
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    #[ignore] // Ignored as it's a meta-test for the helper itself.
    async fn test_setup_environment_works() {
        // Arrange
        let context = setup_full_test_environment().await;

        // Act & Assert
        assert!(!context.server.base_url().is_empty());

        // Check if the server is "reachable" by making a real request to its health endpoint.
        let response = context
            .http_client
            .get(format!("{}/health", context.server.base_url()))
            .send()
            .await
            .expect("Failed to send request to test server's /health endpoint.");

        // A real test against a real server can now check for a success status.
        assert!(response.status().is_success());
        println!("Test context created and health check successful.");
        // `context` is dropped here, and in a real implementation, its Drop trait
        // would handle cleanup (e.g., shutting down the server, clearing the db).
    }
}
