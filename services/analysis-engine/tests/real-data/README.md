# Real-Data Directory for Testing

## 1. Purpose and Guiding Principle

This directory contains the source code of real-world, open-source projects that are used for testing the `analysis-engine`. It serves as the single source of truth for all integration, end-to-end (E2E), performance, and validation tests.

Our guiding principle is **Real Over Synthetic**. We exclusively use real code to validate our engine's capabilities. This practice is a direct response to past failures where duplicated, synthetic data produced misleading and inflated performance metrics. The use of real data ensures that our tests accurately reflect the performance and correctness of the engine under realistic conditions.

**Under no circumstances should synthetic or duplicated data be added to this directory.**

## 2. Directory Structure

The repositories are organized by their approximate size to facilitate different types of testing:

-   `small/`: Contains repositories with **< 10,000 Lines of Code (LOC)**. These are ideal for quick, smoke, integration, and basic E2E tests that run frequently.
-   `medium/`: Contains repositories with **10,000 to 100,000 LOC**. These are used for more comprehensive workflow validation and baseline performance tests.
-   `large/`: Contains repositories with **100,000+ LOC**. These are reserved for scale testing, memory profiling, and throughput validation. These tests are typically marked `#[ignore]` and run explicitly as part of a dedicated performance suite.

## 3. Data Management

To avoid bloating the main `episteme` Git repository, **we do not use Git submodules**.

Instead, the repositories are managed by a script. A manifest file, for example `repositories.json`, will define the list of repositories to be used for testing. A script (e.g., `fetch_test_data.sh` or a Rust binary) will read this manifest and clone the repositories into the appropriate subdirectories.

The actual code within `small/`, `medium/`, and `large/` is **ignored by Git** via the `.gitignore` file. Only the management script and its manifest are version-controlled.

## 4. How to Add a New Repository

1.  **Select a Repository**: Choose a public, open-source repository that fits a testing need.
2.  **Verify License**: **Crucially, you must check the repository's license** to ensure we can use it for testing purposes without violating its terms. Permissive licenses like MIT, Apache 2.0, or BSD are ideal.
3.  **Update the Manifest**: Add the repository's URL, a short description, and target directory (`small`, `medium`, `large`) to the data management manifest file.
4.  **Fetch the Data**: Run the data management script to clone the new repository into the correct location.
5.  **Commit**: Commit the updated manifest file.

## 5. Usage in Tests

-   **Do not hardcode paths**. Test code should not contain direct paths to this directory (e.g., `tests/real-data/small/some-repo`).
-   **Use Test Fixtures**: All access to this test data should go through helper functions defined in `tests/fixtures/real_repos.rs`. These helpers provide a safe and consistent way to locate and use the test repositories.
-   **Read-Only**: Tests must treat the data in this directory as strictly read-only.

## 6. CI/CD Integration

The CI/CD pipeline must be configured to run the data management script during the setup phase of the test job. This ensures that the test runner has access to all necessary repository data before executing the test suite.