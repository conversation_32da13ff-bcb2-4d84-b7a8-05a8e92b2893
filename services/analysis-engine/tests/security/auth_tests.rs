// episteme/services/analysis-engine/tests/security/auth_tests.rs

//! # Security Test: Authentication and Authorization
//!
//! ## Purpose
//! This test module validates the authentication and authorization middleware and logic
//! of the `analysis-engine`. It ensures that protected API endpoints are correctly
//! secured and that users can only access resources they are permitted to see.
//!
//! ## Methodology
//! - A test server instance is started with authentication middleware enabled.
//! - The tests simulate various scenarios:
//!   1. Making requests without any authentication token.
//!   2. Making requests with an invalid, malformed, or expired token.
//!   3. Making requests with a valid token for a standard user.
//!   4. Making requests to admin-only endpoints with both standard and admin tokens.
//! - Assertions are made based on the HTTP status codes received (e.g., 200 OK,
//!   401 Unauthorized, 403 Forbidden).
//!
//! ## Execution
//! These tests require a running server and a known JWT secret for generating test tokens.
//! ```bash
//! JWT_SECRET=test_secret cargo test --test auth_tests -- --ignored --nocapture
//! ```

// Placeholders for real modules.
use analysis_engine::create_app;
use analysis_engine::api::AppState;
use jsonwebtoken::{encode, decode, Header, Algorithm, Validation, EncodingKey, DecodingKey};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::net::TcpListener;
use std::net::SocketAddr;

/// Real JWT token generator for testing authentication.
mod test_token_generator {
    use super::*;
    use chrono::{Duration, Utc};
    
    const JWT_SECRET: &str = "test-secret-key-for-testing-only";
    
    #[derive(Debug, Serialize, Deserialize)]
    struct Claims {
        sub: String,
        role: String,
        exp: usize,
        iat: usize,
    }
    
    pub fn generate_valid_user_token() -> String {
        let claims = Claims {
            sub: "test-user-id".to_string(),
            role: "user".to_string(),
            exp: (Utc::now() + Duration::hours(1)).timestamp() as usize,
            iat: Utc::now().timestamp() as usize,
        };
        
        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(JWT_SECRET.as_ref()),
        )
        .expect("Failed to generate valid user token")
    }
    
    pub fn generate_valid_admin_token() -> String {
        let claims = Claims {
            sub: "test-admin-id".to_string(),
            role: "admin".to_string(),
            exp: (Utc::now() + Duration::hours(1)).timestamp() as usize,
            iat: Utc::now().timestamp() as usize,
        };
        
        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(JWT_SECRET.as_ref()),
        )
        .expect("Failed to generate valid admin token")
    }
    
    pub fn generate_expired_token() -> String {
        let claims = Claims {
            sub: "test-expired-user".to_string(),
            role: "user".to_string(),
            exp: (Utc::now() - Duration::hours(1)).timestamp() as usize, // Already expired
            iat: (Utc::now() - Duration::hours(2)).timestamp() as usize,
        };
        
        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(JWT_SECRET.as_ref()),
        )
        .expect("Failed to generate expired token")
    }
    
    pub fn generate_invalid_signature_token() -> String {
        let claims = Claims {
            sub: "test-invalid-user".to_string(),
            role: "user".to_string(),
            exp: (Utc::now() + Duration::hours(1)).timestamp() as usize,
            iat: Utc::now().timestamp() as usize,
        };
        
        // Use wrong secret to create invalid signature
        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret("wrong-secret-key".as_ref()),
        )
        .expect("Failed to generate invalid signature token")
    }
}

/// # Test: `test_access_to_protected_endpoint`
///
/// ## Description
/// This test checks the fundamental behavior of a protected endpoint. It verifies that
/// access is denied without a token or with an invalid token, and granted with a valid one.
///
/// ## Success Criteria
/// - Request with no token -> 401 Unauthorized.
/// - Request with an expired token -> 401 Unauthorized.
/// - Request with an invalid signature -> 401 Unauthorized.
/// - Request with a valid token -> 200 OK.
#[tokio::test]
#[ignore] // Ignored by default.
async fn test_access_to_protected_endpoint() {
    // --- Arrange ---
    // Start actual test server
    let app = create_app().await.expect("Failed to create test app");
    let listener = TcpListener::bind("127.0.0.1:0")
        .await
        .expect("Failed to bind to random port");
    let addr = listener.local_addr().expect("Failed to get local address");
    let server_base_url = format!("http://{}", addr);
    let protected_endpoint = format!("{}/api/v1/analysis", server_base_url);
    
    // Spawn server in background
    tokio::spawn(async move {
        axum::serve(listener, app)
            .await
            .expect("Test server failed to run");
    });
    
    // Wait for server to start
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    let client = reqwest::Client::new();

    println!("Testing access control for: {}", protected_endpoint);

    // --- Act & Assert ---

    // 1. No token provided -> should be rejected.
    println!("  - Testing no token...");
    let res_no_token = client.get(&protected_endpoint).send().await.unwrap();
    assert_eq!(res_no_token.status(), reqwest::StatusCode::UNAUTHORIZED);
    println!("    -> Rejected with 401 (Correct!)");

    // 2. Expired token -> should be rejected.
    println!("  - Testing expired token...");
    let expired_token = test_token_generator::generate_expired_token();
    let res_expired = client
        .get(&protected_endpoint)
        .bearer_auth(expired_token)
        .send()
        .await
        .unwrap();
    assert_eq!(res_expired.status(), reqwest::StatusCode::UNAUTHORIZED);
    println!("    -> Rejected with 401 (Correct!)");

    // 3. Invalid signature token -> should be rejected.
    println!("  - Testing invalid signature...");
    let invalid_sig_token = test_token_generator::generate_invalid_signature_token();
    let res_invalid_sig = client
        .get(&protected_endpoint)
        .bearer_auth(invalid_sig_token)
        .send()
        .await
        .unwrap();
    assert_eq!(res_invalid_sig.status(), reqwest::StatusCode::UNAUTHORIZED);
    println!("    -> Rejected with 401 (Correct!)");

    // 4. Valid token -> should be accepted.
    println!("  - Testing valid token...");
    let valid_token = test_token_generator::generate_valid_user_token();
    let res_valid = client
        .get(&protected_endpoint)
        .bearer_auth(valid_token)
        .send()
        .await
        .unwrap();
    assert_eq!(res_valid.status(), reqwest::StatusCode::OK);
    println!("    -> Accepted with 200 (Correct!)");

    println!("✅ PASS: Protected endpoint behaved as expected.");
}

/// # Test: `test_access_to_admin_only_endpoint`
///
/// ## Description
/// This test verifies role-based access control. It ensures that an admin-only endpoint
/// is accessible to users with the 'admin' role but forbidden to standard users.
///
/// ## Success Criteria
/// - Request with a standard user token -> 403 Forbidden.
/// - Request with an admin token -> 200 OK.
#[tokio::test]
#[ignore] // Ignored by default.
async fn test_access_to_admin_only_endpoint() {
    // --- Arrange ---
    // Start actual test server
    let app = create_app().await.expect("Failed to create test app");
    let listener = TcpListener::bind("127.0.0.1:0")
        .await
        .expect("Failed to bind to random port");
    let addr = listener.local_addr().expect("Failed to get local address");
    let server_base_url = format!("http://{}", addr);
    let admin_endpoint = format!("{}/api/v1/analysis", server_base_url); // Use actual endpoint
    
    // Spawn server in background
    tokio::spawn(async move {
        axum::serve(listener, app)
            .await
            .expect("Test server failed to run");
    });
    
    // Wait for server to start
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    let client = reqwest::Client::new();

    println!("\nTesting role-based access for: {}", admin_endpoint);

    // --- Act & Assert ---

    // 1. Standard user token -> should be forbidden.
    println!("  - Testing with standard user token...");
    let user_token = test_token_generator::generate_valid_user_token();
    let res_user = client
        .get(&admin_endpoint)
        .bearer_auth(user_token)
        .send()
        .await
        .unwrap();
    assert_eq!(res_user.status(), reqwest::StatusCode::FORBIDDEN);
    println!("    -> Rejected with 403 (Correct!)");

    // 2. Admin user token -> should be allowed.
    println!("  - Testing with admin token...");
    let admin_token = test_token_generator::generate_valid_admin_token();
    let res_admin = client
        .get(&admin_endpoint)
        .bearer_auth(admin_token)
        .send()
        .await
        .unwrap();
    assert_eq!(res_admin.status(), reqwest::StatusCode::OK);
    println!("    -> Accepted with 200 (Correct!)");

    println!("✅ PASS: Admin endpoint behaved as expected.");
}
