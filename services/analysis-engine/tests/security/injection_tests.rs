// episteme/services/analysis-engine/tests/security/injection_tests.rs

//! # Security Test: Input Injection Vulnerabilities
//!
//! ## Purpose
//! This test module is focused on preventing input injection vulnerabilities, a critical
//! category of security flaws. The goal is to ensure that user-provided input sent to
//! our API endpoints is properly sanitized and validated, and cannot be used to execute
//! unintended actions on the server.
//!
//! ## Methodology
//! - The tests craft a series of malicious payloads designed to exploit common injection
//!   vectors, such as Path Traversal and Command Injection.
//! - These payloads are sent to relevant API endpoints (e.g., any endpoint that accepts
//!   a URL, file path, or other string that might be processed by the system).
//! - The test asserts that the application *rejects* the malicious input gracefully with
//!   an appropriate client-side error code (e.g., 400 Bad Request, 422 Unprocessable Entity).
//! - A test failure occurs if the server responds with a 5xx error (indicating a crash or
//!   unhandled exception) or, in a more advanced test, if the malicious action was
//!   actually successful (e.g., a file was created where it shouldn't have been).
//!
//! ## Execution
//! These tests require a running server instance and are part of the security suite.
//! ```bash
//! cargo test --test injection_tests -- --ignored --nocapture
//! ```

use analysis_engine::create_app;
use tokio::net::TcpListener;
use std::net::SocketAddr;

/// # Test: `test_api_resists_path_traversal_injection`
///
/// ## Description
/// This test sends path traversal payloads to API endpoints that might interact with
/// the filesystem. It verifies that the application does not allow an attacker to
/// access unintended files or directories (e.g., `../../etc/passwd`).
///
/// ## Success Criteria
/// - For every malicious payload, the API must respond with a `4xx` status code.
/// - The API must never respond with a `5xx` status code, as this could indicate an
///   unhandled error that might leak information.
#[tokio::test]
#[ignore] // Ignored by default as it requires a running test server.
async fn test_api_resists_path_traversal_injection() {
    // --- Arrange ---
    // 1. A list of common path traversal payloads.
    let malicious_payloads = vec![
        "../../../../etc/passwd",
        "https://example.com/../../etc/hosts",
        "file:///..%2F..%2F/boot.ini",
        "....//....//....//etc/shadow",
    ];

    // 2. Set up the test server.
    // Start actual test server
    let app = create_app().await.expect("Failed to create test app");
    let listener = TcpListener::bind("127.0.0.1:0")
        .await
        .expect("Failed to bind to random port");
    let addr = listener.local_addr().expect("Failed to get local address");
    let server_base_url = format!("http://{}", addr);
    let analysis_endpoint = format!("{}/api/v1/analysis", server_base_url);
    
    // Spawn server in background
    tokio::spawn(async move {
        axum::serve(listener, app)
            .await
            .expect("Test server failed to run");
    });
    
    // Wait for server to start
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    let client = reqwest::Client::new();

    println!(
        "Testing for path traversal injection on endpoint: {}",
        analysis_endpoint
    );

    // --- Act & Assert ---
    // 3. Send each payload to the endpoint and check for the response.
    for payload in malicious_payloads {
        println!("  - Sending payload: {}", payload);
        let response = client
            .post(&analysis_endpoint)
            .json(&serde_json::json!({ "repository_url": payload, "branch": "main" }))
            .send()
            .await
            .expect("Request failed to send");

        // The application MUST identify this as a client error and reject it.
        // It should be a 4xx error (e.g., 400 Bad Request). A 5xx error is a failure.
        let status = response.status();
        assert!(
            status.is_client_error(),
            "Payload '{}' was not rejected with a 4xx error. Received status: {}",
            payload,
            status
        );
        println!("    -> Rejected with status: {} (Correct!)", status);
    }
    println!("✅ PASS: All path traversal payloads were correctly rejected.");
}

/// # Test: `test_api_resists_os_command_injection`
///
/// ## Description
/// This test sends payloads designed to execute arbitrary OS commands. It targets any
/// endpoint that might (even indirectly) pass user input to a shell command (e.g., `git clone <URL>`).
///
/// ## Success Criteria
/// - The API must reject all payloads with a `4xx` status code.
/// - The API must not hang, crash, or show any signs of command execution.
#[tokio::test]
#[ignore] // Ignored by default.
async fn test_api_resists_os_command_injection() {
    // --- Arrange ---
    let malicious_payloads = vec![
        "https://example.com; ls -la",
        "$(whoami)",
        "| cat /etc/passwd",
        "&& rm -rf /", // The classic scary one
        "`reboot`",
    ];

    // Start actual test server
    let app = create_app().await.expect("Failed to create test app");
    let listener = TcpListener::bind("127.0.0.1:0")
        .await
        .expect("Failed to bind to random port");
    let addr = listener.local_addr().expect("Failed to get local address");
    let server_base_url = format!("http://{}", addr);
    let analysis_endpoint = format!("{}/api/v1/analysis", server_base_url);
    let client = reqwest::Client::new();
    
    // Spawn server in background
    tokio::spawn(async move {
        axum::serve(listener, app)
            .await
            .expect("Test server failed to run");
    });
    
    // Wait for server to start
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    println!(
        "\nTesting for OS command injection on endpoint: {}",
        analysis_endpoint
    );

    // --- Act & Assert ---
    for payload in malicious_payloads {
        println!("  - Sending payload: {}", payload);
        let response = client
            .post(&analysis_endpoint)
            .json(&serde_json::json!({ "repository_url": payload, "branch": "main" }))
            .send()
            .await
            .expect("Request failed to send");

        let status = response.status();
        assert!(
            status.is_client_error(),
            "Payload '{}' was not rejected with a 4xx error. Received status: {}",
            payload,
            status
        );
        println!("    -> Rejected with status: {} (Correct!)", status);
    }
    println!("✅ PASS: All OS command injection payloads were correctly rejected.");
}
