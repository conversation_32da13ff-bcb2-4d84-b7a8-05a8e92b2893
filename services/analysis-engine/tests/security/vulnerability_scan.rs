// episteme/services/analysis-engine/tests/security/vulnerability_scan.rs

//! # Security Test: Known Vulnerability Scanning
//!
//! ## Purpose
//! This test module integrates with external security scanning tools to check for known
//! vulnerabilities in the project's dependencies. Its primary goal is to provide an
//! automated, evidence-based check to ensure we are not shipping code with publicly
//! disclosed security flaws (CVEs).
//!
//! ## Methodology
//! - The test invokes a command-line security scanner, specifically `cargo audit`, which
//!   checks the `Cargo.lock` file against the official RustSec advisory database.
//! - The test is designed to be simple: it passes if `cargo audit` finds no vulnerabilities
//!   and fails if it finds one or more.
//! - This provides a critical quality gate in the CI/CD pipeline, preventing vulnerable
//!   builds from being deployed.
//!
//! ## Execution
//! This test requires the `cargo-audit` tool to be installed and requires network access
//! to fetch the latest advisory database. It is marked `#[ignore]` to be run as part of
//! a dedicated security suite.
//!
//! ```bash
//! # Install the tool first if you haven't already
//! cargo install cargo-audit
//! # Run the test
//! cargo test --test vulnerability_scan -- --ignored --nocapture
//! ```

use std::process::Command;

/// # Test: `test_for_known_vulnerabilities_in_dependencies`
///
/// ## Description
/// This test executes `cargo audit` to scan for dependencies with known security
/// vulnerabilities. It serves as an automated safeguard against deploying vulnerable code.
///
/// ## Success Criteria
/// - `cargo audit` must execute successfully.
/// - The command must exit with a status code of 0, which indicates that no
///   vulnerabilities were found.
/// - If vulnerabilities are found, the test must fail and print the detailed report
///   from `cargo audit` to standard output.
#[tokio::test]
#[ignore] // Ignored by default due to external tool and network dependencies.
async fn test_for_known_vulnerabilities_in_dependencies() {
    // --- Arrange ---
    println!("========================================================================");
    println!("  SECURITY SCAN: Checking for known vulnerabilities with `cargo audit`");
    println!("========================================================================");

    // The command to execute. We add `--json` to potentially parse the output in the
    // future, but for now, we just check the exit code.
    let mut cmd = Command::new("cargo");
    cmd.arg("audit");

    // --- Act ---
    // Execute the command and capture its output.
    let output = cmd
        .output()
        .expect("Failed to execute `cargo audit`. Is it installed and in your PATH?");

    // --- Assert ---
    let stdout = String::from_utf8_lossy(&output.stdout);
    let stderr = String::from_utf8_lossy(&output.stderr);

    // `cargo audit` exits with a non-zero status code if vulnerabilities are found.
    // Therefore, a successful status code means the test passes.
    if output.status.success() {
        println!("✅ PASS: No known vulnerabilities found in dependencies.");
        println!("{}", stdout); // Print the success message from the tool.
    } else {
        // If the command failed, it means vulnerabilities were found.
        // We print the full output and fail the test.
        eprintln!("========================= VULNERABILITIES DETECTED =========================");
        eprintln!("{}", stdout);
        if !stderr.is_empty() {
            eprintln!("\n--- Stderr ---\n{}", stderr);
        }
        eprintln!("============================================================================");
        panic!("❌ FAIL: `cargo audit` found security vulnerabilities. See the report above.");
    }

    // The final assertion is based on the exit code.
    assert!(
        output.status.success(),
        "Security vulnerabilities detected. Please review the `cargo audit` report."
    );
}
