// episteme/services/analysis-engine/tests/e2e/concurrent_analysis.rs

//! # End-to-End (E2E) Test: Concurrent Analysis Handling
//!
//! ## Purpose
//! This test module is designed to verify the `analysis-engine`'s stability and
//! correctness under a concurrent workload. It simulates multiple, simultaneous
//! analysis requests to ensure the system can process them in parallel without
//! race conditions, deadlocks, data corruption, or crashes.
//!
//! ## Methodology
//! - A full application instance is started, including the API server and database connection.
//! - A predefined number of analysis requests for different small repositories are fired
//!   off concurrently using asynchronous tasks.
//! - The test first asserts that all requests were successfully accepted by the API.
//! - It then polls the database to verify that *all* submitted analyses are eventually
//!   marked as "completed" and that their data is stored correctly.
//!
//! ## Execution
//! This is a complex, resource-intensive test. It is marked `#[ignore]` and should only
//! be run as part of a dedicated E2E or stress testing suite.
//!
//! ```bash
//! cargo test --test concurrent_analysis -- --ignored --nocapture
//! ```

use crate::fixtures::real_repos;
use crate::fixtures::test_helpers::TestServer;
use futures::future::join_all;

/// # Test: `test_system_handles_multiple_analyses_concurrently`
///
/// ## Description
/// This test simulates a high-traffic scenario by sending multiple analysis requests
/// to the server at the same time. It ensures that the system remains stable and
/// correctly processes every request to completion.
///
/// ## Success Criteria
/// - The API must successfully accept all concurrent requests.
/// - All analyses initiated must eventually be completed and have their results stored
///   in the database.
/// - The system must not crash or enter a deadlocked state.
#[tokio::test]
#[ignore] // Ignored by default due to high resource usage and complexity.
async fn test_system_handles_multiple_analyses_concurrently() {
    // --- Arrange ---
    const CONCURRENT_REQUESTS: usize = 3; // Keep it low to avoid hitting API rate limits on GitHub

    // 1. Set up the full test environment.
    let test_server = TestServer::start().await;
    let server_base_url = test_server.base_url();

    // 2. Prepare a list of different repositories to analyze. Using different ones
    // makes the test more realistic than hitting the same one repeatedly.
    let repo_identifiers = vec![
        "small/example-repo-1",
        "small/example-repo-2",
        "medium/standard-benchmark-repo",
    ];
    assert_eq!(repo_identifiers.len(), CONCURRENT_REQUESTS);
    let mut repo_urls = Vec::new();
    for id in &repo_identifiers {
        let path = real_repos::get_repo_path(id)
            .await
            .expect("Failed to get repo path");
        repo_urls.push(format!("file://{}", path.display()));
    }

    // --- Act ---

    // 3. Spawn concurrent tasks to send all analysis requests at once.
    let client = reqwest::Client::new();
    let mut request_futures = Vec::new();

    println!(
        "Firing {} concurrent analysis requests...",
        CONCURRENT_REQUESTS
    );
    for repo_url in &repo_urls {
        let client = client.clone();
        let endpoint = format!("{}/api/v1/analysis", server_base_url);
        let payload = serde_json::json!({ "repository_url": repo_url });

        let future =
            tokio::spawn(async move { client.post(&endpoint).json(&payload).send().await });
        request_futures.push(future);
    }

    // Wait for all the requests to be sent and the initial responses to be received.
    let responses = join_all(request_futures).await;

    // --- Assert (Phase 1: All requests accepted) ---

    // 4. Check that all requests were accepted and collect the analysis IDs.
    let mut analysis_ids = Vec::new();
    for (i, response_result) in responses.into_iter().enumerate() {
        let response = response_result
            .expect("Task panicked")
            .expect("Request failed");
        assert_eq!(
            response.status(),
            reqwest::StatusCode::CREATED,
            "Request #{} for repo '{}' was not accepted with 201 Created. Status: {}. Body: {:?}",
            i,
            repo_urls[i],
            response.status(),
            response.text().await
        );
        let body: serde_json::Value = response.json().await.unwrap();
        let id = body["analysis_id"].as_str().unwrap().to_string();
        analysis_ids.push(id);
    }
    println!(
        "All {} requests were accepted by the API. Analysis IDs: {:?}",
        CONCURRENT_REQUESTS, analysis_ids
    );
    assert_eq!(analysis_ids.len(), CONCURRENT_REQUESTS);

    // --- Assert (Phase 2: All analyses completed) ---

    // 5. Poll all status endpoints until every analysis is complete.
    println!("Polling all status endpoints for completion...");
    let mut completed_ids = std::collections::HashSet::new();
    for _ in 0..120 {
        // More generous timeout for multiple analyses.
        let check_futures = analysis_ids.iter().map(|id| {
            let client = client.clone();
            let endpoint = format!("{}/api/v1/analysis/{}/status", server_base_url, id);
            async move { (id.clone(), client.get(&endpoint).send().await) }
        });

        let results = join_all(check_futures).await;

        for (id, res) in results {
            if let Ok(response) = res {
                if let Ok(body) = response.json::<serde_json::Value>().await {
                    if let Some(status) = body["status"].as_str() {
                        if status == "Completed" && !completed_ids.contains(&id) {
                            println!("  - Analysis {} completed.", id);
                            completed_ids.insert(id);
                        }
                    }
                }
            }
        }

        if completed_ids.len() == CONCURRENT_REQUESTS {
            break;
        }
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
    }

    // 6. Final assertion: check if all submitted jobs were completed.
    assert_eq!(
        completed_ids.len(),
        CONCURRENT_REQUESTS,
        "Test timed out. Only {}/{} analyses completed. Missing: {:?}",
        completed_ids.len(),
        CONCURRENT_REQUESTS,
        analysis_ids
            .iter()
            .filter(|id| !completed_ids.contains(*id))
            .collect::<Vec<_>>()
    );

    println!(
        "✅ Successfully verified that all {} concurrent analyses completed.",
        CONCURRENT_REQUESTS
    );
}
