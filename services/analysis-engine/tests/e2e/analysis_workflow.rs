//! # End-to-End (E2E) Test: Full Analysis Workflow
//!
//! ## Purpose
//! This test module simulates a complete user journey through the `analysis-engine`.
//! It verifies that all major components of the system (API, parsing, analysis, storage)
//! work together correctly to fulfill a user request from start to finish.
//!
//! ## Methodology
//! - This test runs against a full instance of the application, including a live (but test)
//!   API server and a connection to a test database.
//! - It uses a small, real-world repository from `tests/real-data/small/` to ensure
//!   the workflow is tested under realistic conditions without being excessively slow.
//! - The workflow is typically initiated via an HTTP request to the main analysis endpoint,
//!   mimicking a real client.
//! - Assertions are made on both the API response and the state of the database after
//!   the analysis is complete.
//!
//! ## Execution
//! E2E tests are the slowest and most complex tests in the suite. They require a fully
//! configured test environment and are marked `#[ignore]`.
//!
//! ```bash
//! cargo test --test analysis_workflow -- --ignored --nocapture
//! ```

use crate::fixtures::test_helpers::TestServer;

/// # Test: `test_e2e_analysis_of_small_repository`
///
/// ## Description
/// This test simulates a client requesting an analysis of a small Git repository. It checks
/// that the API accepts the request, the analysis completes successfully, and the correct
//  results are persisted in the database.
///
/// ## Success Criteria
/// - The `/api/v1/analyze` endpoint must return a success status code (e.g., 202 Accepted).
/// - After a short delay, the analysis results corresponding to the request must be present
///   in the test database.
/// - The data in the database (e.g., LOC count, language breakdown) must be accurate for
///   the tested repository.
#[tokio::test]
#[ignore] // Ignored by default due to its complexity and dependencies.
async fn test_e2e_analysis_of_small_repository() {
    // --- Arrange ---
    // 1. Start a real test server. The server will be automatically shut down
    // when `_test_server` goes out of scope at the end of the test.
    let _test_server = TestServer::start().await;
    let server_base_url = _test_server.base_url();

    // 2. Define the repository to be analyzed.
    // In a real test, this might come from the `real_repos` fixture.
    let repo_url = "https://github.com/stedolan/jq"; // A real, small repository.
    println!("Target repository for analysis: {}", repo_url);

    // --- Act ---
    // 3. Initiate the analysis via an API call to the live test server.
    let client = reqwest::Client::new();
    let analysis_endpoint = format!("{}/api/v1/analysis", server_base_url);
    let response = client
        .post(&analysis_endpoint)
        // Note: The analysis endpoint in main.rs expects an `AnalysisRequest` struct,
        // which has `repository_url` as its key field.
        .json(&serde_json::json!({ "repository_url": repo_url }))
        .send()
        .await
        .expect("Failed to send request to the analysis endpoint.");

    // --- Assert (Phase 1: API Response) ---
    // 4. Check that the server accepted our request.
    // The handler returns a 201 Created status upon successfully creating the analysis job.
    assert_eq!(
        response.status(),
        reqwest::StatusCode::CREATED,
        "API did not return 201 Created for the analysis request."
    );
    let response_body: serde_json::Value = response
        .json()
        .await
        .expect("Failed to parse response body as JSON.");
    let analysis_id = response_body["analysis_id"]
        .as_str()
        .expect("Response body did not contain an 'analysis_id' string.");
    println!("Analysis job created with ID: {}", analysis_id);

    // --- Assert (Phase 2: Status Polling) ---
    // 5. Poll the status endpoint until the analysis is complete.
    let status_endpoint = format!("{}/api/v1/analysis/{}/status", server_base_url, analysis_id);
    let mut final_status = serde_json::Value::Null;
    println!("Polling for analysis completion...");
    for _ in 0..60 {
        // Poll for up to 5 minutes.
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
        let status_response = client
            .get(&status_endpoint)
            .send()
            .await
            .expect("Failed to poll status endpoint.");

        if status_response.status().is_success() {
            let status_body: serde_json::Value = status_response.json().await.unwrap();
            println!(
                "  - Current status: {}",
                status_body["status"].as_str().unwrap_or("unknown")
            );
            if status_body["status"] == "Completed" || status_body["status"] == "Failed" {
                final_status = status_body;
                break;
            }
        }
    }

    // 6. Verify that the analysis completed successfully.
    assert_ne!(final_status, serde_json::Value::Null, "Analysis timed out.");
    assert_eq!(
        final_status["status"], "Completed",
        "Analysis did not complete successfully. Final status: {}",
        final_status
    );

    // --- Assert (Phase 3: Final Results) ---
    // 7. Fetch the final results and perform a basic plausibility check.
    let results_endpoint = format!("{}/api/v1/analysis/{}", server_base_url, analysis_id);
    let results_response = client.get(&results_endpoint).send().await.unwrap();
    assert!(results_response.status().is_success());

    let final_results: serde_json::Value = results_response.json().await.unwrap();
    let loc_count = final_results["metrics"]["lines_of_code"]
        .as_u64()
        .unwrap_or(0);
    assert!(
        loc_count > 1000,
        "Expected a plausible LOC count for jq repo, but got {}",
        loc_count
    );

    println!("E2E analysis workflow completed and verified successfully.");
}
