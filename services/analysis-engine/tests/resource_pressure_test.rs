//! Resource pressure tests for production validation
//! Tests system behavior under various resource constraints and pressure scenarios

use analysis_engine::{
    backpressure::{BackpressureConfig, BackpressureManager, BackpressureDecision, RejectReason},
    config::{ResourceOptimizationConfig, ServiceConfig, SystemMonitorConfig},
    monitoring::{ResourceMonitor, SystemMonitor},
    parser::{parallel::ParallelProcessor, TreeSitterParser},
};
use std::sync::Arc;
use std::time::Duration;
use tempfile::TempDir;
use tokio::time::sleep;

/// Helper to create test files
fn create_test_files(dir: &TempDir, count: usize, lines_per_file: usize) -> Vec<std::path::PathBuf> {
    use std::fs;
    
    (0..count)
        .map(|i| {
            let content = (0..lines_per_file)
                .map(|j| format!("fn test_{}_{i}() {{ println!(\"{}\"); }}\n", j, j))
                .collect::<String>();
            let path = dir.path().join(format!("test_{}.rs", i));
            fs::write(&path, content).unwrap();
            path
        })
        .collect()
}

#[tokio::test]
async fn test_memory_exhaustion_handling() {
    let config = Arc::new(ServiceConfig::from_env().unwrap());
    let parser = Arc::new(TreeSitterParser::new(config).unwrap());
    
    // Create very low memory limit to trigger exhaustion
    let monitor = ResourceMonitor::new(50, 60); // 50MB limit
    monitor.start_monitoring().await;
    
    // Generate files that will exceed memory when processed
    let temp_dir = TempDir::new().unwrap();
    let files = create_test_files(&temp_dir, 20, 5000); // 100K lines total
    
    let processor = ParallelProcessor::new(parser);
    
    // Simulate memory growth during processing
    for i in 0..10 {
        monitor.add_memory_usage(10 * 1024 * 1024); // Add 10MB each iteration
        
        match monitor.check_limits().await {
            Ok(_) => {
                // Continue processing
                let chunk = files.iter().skip(i * 2).take(2).cloned().collect::<Vec<_>>();
                let _results = processor.process_files_parallel(&chunk, 1);
            }
            Err(e) => {
                // Expected memory exhaustion
                assert!(e.to_string().contains("Memory limit exceeded"));
                assert!(i >= 4); // Should fail after ~50MB
                return;
            }
        }
    }
    
    panic!("Expected memory exhaustion but didn't occur");
}

#[tokio::test]
async fn test_cpu_saturation_behavior() {
    // Create system monitor with very low CPU limit
    let system_config = SystemMonitorConfig {
        cpu_limit_percent: 10.0, // Very low to trigger saturation
        memory_limit_mb: 4096,
        cpu_warning_threshold: 5.0,
        memory_warning_threshold_percent: 80.0,
        monitoring_interval: Duration::from_millis(100),
        enable_process_monitoring: true,
        enable_disk_monitoring: false,
        enable_network_monitoring: false,
    };
    
    let system_monitor = Arc::new(SystemMonitor::new(system_config));
    system_monitor.start().await.unwrap();
    
    // Give monitor time to collect initial metrics
    sleep(Duration::from_millis(500)).await;
    
    // Simulate CPU-intensive work
    let config = Arc::new(ServiceConfig::from_env().unwrap());
    let parser = Arc::new(TreeSitterParser::new(config).unwrap());
    
    let temp_dir = TempDir::new().unwrap();
    let files = create_test_files(&temp_dir, 50, 1000);
    
    let processor = ParallelProcessor::new(parser);
    
    // Process files with many threads to increase CPU usage
    let start = std::time::Instant::now();
    let _results = processor.process_files_parallel(&files, 8); // Use 8 threads
    let duration = start.elapsed();
    
    // Check if system detected high CPU
    let is_under_pressure = system_monitor.is_under_pressure().await;
    
    println!("Processing duration: {:?}", duration);
    println!("System under pressure: {}", is_under_pressure);
    
    // In CI environments, CPU usage might not spike enough
    // So we just verify the monitoring works
    let metrics = system_monitor.get_metrics().await;
    assert!(metrics.cpu_count > 0);
    assert!(metrics.cpu_usage_percent >= 0.0);
    
    system_monitor.stop().await.unwrap();
}

#[tokio::test]
async fn test_backpressure_activation_thresholds() {
    // Configure backpressure with low thresholds
    let bp_config = BackpressureConfig {
        max_concurrent_analyses: 5,
        max_analysis_memory_mb: 1024,
        cpu_threshold_percent: 50.0,
        memory_threshold_percent: 50.0,
        queue_size_threshold: 10,
        response_time_threshold_ms: 100,
        ..Default::default()
    };
    
    let backpressure = BackpressureManager::new(bp_config);
    
    // Simulate high load conditions
    for i in 0..20 {
        // Update metrics to simulate increasing load
        backpressure.update_metrics(analysis_engine::backpressure::BackpressureMetrics {
            memory_usage_mb: 500 + (i * 50), // Gradually increase memory
            cpu_usage_percent: 30.0 + (i as f32 * 2.0), // Gradually increase CPU
            active_analyses: i.min(10),
            active_requests: i,
            queued_requests: i * 2,
            rejected_requests: 0,
            avg_response_time_ms: 50 + (i * 10),
            last_updated: 0,
        }).await.unwrap();
        
        let decision = backpressure.check_analysis_request().await;
        
        match decision {
            BackpressureDecision::Allow => {
                assert!(i < 5, "Should start rejecting after threshold");
            }
            BackpressureDecision::Throttle(delay) => {
                assert!(i >= 5 && i < 10, "Should throttle in middle range");
                assert!(delay.as_millis() > 0);
            }
            BackpressureDecision::Reject(reason) => {
                assert!(i >= 10, "Should reject under extreme load");
                match reason {
                    RejectReason::ResourceExhaustion => {},
                    RejectReason::QueueFull => {},
                    RejectReason::CircuitBreakerOpen => {},
                }
            }
        }
    }
}

#[tokio::test]
async fn test_graceful_degradation() {
    let config = Arc::new(ServiceConfig::from_env().unwrap());
    let parser = Arc::new(TreeSitterParser::new(config).unwrap());
    
    // Create system monitor with production-like limits
    let system_monitor = Arc::new(SystemMonitor::new(SystemMonitorConfig {
        cpu_limit_percent: 100.0,
        memory_limit_mb: 1024,
        ..Default::default()
    }));
    
    // Create backpressure manager
    let bp_config = BackpressureConfig {
        max_concurrent_analyses: 10,
        max_analysis_memory_mb: 1024,
        enable_circuit_breaker: true,
        circuit_breaker_threshold: 5,
        circuit_breaker_timeout: Duration::from_secs(5),
        ..Default::default()
    };
    let backpressure = BackpressureManager::with_system_monitor(bp_config, system_monitor.clone());
    
    system_monitor.start().await.unwrap();
    
    let temp_dir = TempDir::new().unwrap();
    let files = create_test_files(&temp_dir, 100, 1000);
    
    let processor = ParallelProcessor::new(parser);
    
    let mut successful = 0;
    let mut throttled = 0;
    let mut rejected = 0;
    
    // Process files with backpressure checks
    for chunk in files.chunks(10) {
        match backpressure.check_analysis_request().await {
            BackpressureDecision::Allow => {
                let results = processor.process_files_parallel(&chunk.to_vec(), 2);
                successful += results.len();
            }
            BackpressureDecision::Throttle(delay) => {
                throttled += 1;
                sleep(delay).await;
                // Retry after throttle
                let results = processor.process_files_parallel(&chunk.to_vec(), 1);
                successful += results.len();
            }
            BackpressureDecision::Reject(_) => {
                rejected += 1;
                // Skip this chunk
            }
        }
        
        // Small delay between chunks
        sleep(Duration::from_millis(50)).await;
    }
    
    println!("Graceful degradation results:");
    println!("  Successful: {}", successful);
    println!("  Throttled: {}", throttled);
    println!("  Rejected: {}", rejected);
    
    // Verify some level of degradation occurred
    assert!(successful > 0, "Should process some files");
    assert!(successful + rejected * 10 >= files.len(), "Should account for all files");
    
    system_monitor.stop().await.unwrap();
}

#[tokio::test]
async fn test_recovery_after_pressure() {
    let bp_config = BackpressureConfig {
        max_concurrent_analyses: 5,
        recovery_threshold_percent: 70.0, // Recovery at 70% of limits
        ..Default::default()
    };
    
    let backpressure = BackpressureManager::new(bp_config);
    
    // Phase 1: Apply pressure
    backpressure.update_metrics(analysis_engine::backpressure::BackpressureMetrics {
        memory_usage_mb: 1800,
        cpu_usage_percent: 95.0,
        active_analyses: 5,
        active_requests: 10,
        queued_requests: 20,
        rejected_requests: 5,
        avg_response_time_ms: 500,
        last_updated: 0,
    }).await.unwrap();
    
    let decision1 = backpressure.check_analysis_request().await;
    assert!(matches!(decision1, BackpressureDecision::Reject(_) | BackpressureDecision::Throttle(_)));
    
    // Phase 2: Reduce pressure
    sleep(Duration::from_secs(1)).await;
    
    backpressure.update_metrics(analysis_engine::backpressure::BackpressureMetrics {
        memory_usage_mb: 800,
        cpu_usage_percent: 60.0,
        active_analyses: 2,
        active_requests: 3,
        queued_requests: 5,
        rejected_requests: 0,
        avg_response_time_ms: 100,
        last_updated: 0,
    }).await.unwrap();
    
    // Phase 3: Verify recovery
    let decision2 = backpressure.check_analysis_request().await;
    assert!(matches!(decision2, BackpressureDecision::Allow));
    
    // Phase 4: Verify sustained recovery
    for _ in 0..5 {
        let decision = backpressure.check_analysis_request().await;
        assert!(matches!(decision, BackpressureDecision::Allow));
        sleep(Duration::from_millis(100)).await;
    }
}

#[tokio::test]
async fn test_queue_management_under_load() {
    use std::sync::atomic::{AtomicUsize, Ordering};
    
    let bp_config = BackpressureConfig {
        max_concurrent_analyses: 3,
        queue_size_threshold: 10,
        max_queue_size: 20,
        ..Default::default()
    };
    
    let backpressure = Arc::new(BackpressureManager::new(bp_config));
    let queued = Arc::new(AtomicUsize::new(0));
    let processed = Arc::new(AtomicUsize::new(0));
    let rejected = Arc::new(AtomicUsize::new(0));
    
    // Simulate multiple concurrent requests
    let mut handles = vec![];
    
    for i in 0..30 {
        let bp_clone = backpressure.clone();
        let queued_clone = queued.clone();
        let processed_clone = processed.clone();
        let rejected_clone = rejected.clone();
        
        let handle = tokio::spawn(async move {
            // Simulate varying request arrival
            sleep(Duration::from_millis(i * 10)).await;
            
            match bp_clone.check_analysis_request().await {
                BackpressureDecision::Allow => {
                    processed_clone.fetch_add(1, Ordering::SeqCst);
                    // Simulate processing time
                    sleep(Duration::from_millis(100)).await;
                }
                BackpressureDecision::Throttle(_) => {
                    queued_clone.fetch_add(1, Ordering::SeqCst);
                }
                BackpressureDecision::Reject(_) => {
                    rejected_clone.fetch_add(1, Ordering::SeqCst);
                }
            }
        });
        
        handles.push(handle);
    }
    
    // Wait for all requests to complete
    for handle in handles {
        handle.await.unwrap();
    }
    
    let final_processed = processed.load(Ordering::SeqCst);
    let final_queued = queued.load(Ordering::SeqCst);
    let final_rejected = rejected.load(Ordering::SeqCst);
    
    println!("Queue management results:");
    println!("  Processed: {}", final_processed);
    println!("  Queued: {}", final_queued);
    println!("  Rejected: {}", final_rejected);
    
    // Verify queue management worked
    assert!(final_processed > 0, "Should process some requests");
    assert!(final_processed + final_queued + final_rejected == 30, "Should account for all requests");
    assert!(final_rejected > 0 || final_queued > 0, "Should apply backpressure");
}

#[tokio::test]
async fn test_resource_monitoring_accuracy() {
    let monitor = ResourceMonitor::new(2048, 60);
    monitor.start_monitoring().await;
    
    // Get baseline
    let baseline = monitor.get_system_resource_info().await;
    assert!(baseline.total_memory > 0);
    assert!(baseline.available_memory > 0);
    assert!(baseline.cpu_count > 0);
    
    // Simulate memory allocation
    let _large_vec: Vec<u8> = vec![0; 50 * 1024 * 1024]; // 50MB
    
    // Give time for monitoring to update
    sleep(Duration::from_millis(100)).await;
    
    // Check updated metrics
    let updated = monitor.get_system_resource_info().await;
    
    // Memory usage should have increased
    // Note: This might not be exactly 50MB due to OS memory management
    println!("Memory change: {} MB", 
        (baseline.available_memory as i64 - updated.available_memory as i64) / 1024 / 1024);
    
    // Verify monitoring is working
    assert!(updated.process_memory > 0);
    assert!(updated.cpu_usage >= 0.0 && updated.cpu_usage <= 100.0);
}