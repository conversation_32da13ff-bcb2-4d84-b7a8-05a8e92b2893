#!/bin/bash
# Deploy Analysis Engine with all new environment variables
set -euo pipefail

echo "🚀 Deploying Analysis Engine with Updated Environment Variables"
echo "=============================================================="

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
SERVICE_NAME="analysis-engine"
IMAGE_TAG="fixed-$(date +%Y%m%d-%H%M%S)"

# Check if we're in the right directory
if [ ! -f "Cargo.toml" ]; then
    echo "❌ Error: Must run from services/analysis-engine directory"
    exit 1
fi

echo "📦 Building Docker image with fixes..."
docker build \
    --platform linux/amd64 \
    -t gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG} \
    -f Dockerfile.simple \
    .

echo "📤 Pushing image to Container Registry..."
docker push gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG}

echo "🚀 Deploying with all environment variables..."
gcloud run deploy ${SERVICE_NAME} \
    --image gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG} \
    --platform managed \
    --region ${REGION} \
    --update-env-vars "GCP_PROJECT_ID=${PROJECT_ID}" \
    --update-env-vars "GOOGLE_CLOUD_PROJECT=${PROJECT_ID}" \
    --update-env-vars "SPANNER_PROJECT_ID=${PROJECT_ID}" \
    --update-env-vars "SPANNER_INSTANCE_ID=ccl-instance" \
    --update-env-vars "SPANNER_DATABASE_ID=ccl_main" \
    --update-env-vars "REDIS_URL=redis://10.76.85.67:6379" \
    --update-env-vars "STORAGE_BUCKET=ccl-analysis-artifacts" \
    --update-env-vars "STORAGE_BUCKET_NAME=ccl-analysis-artifacts" \
    --update-env-vars "PUBSUB_TOPIC=analysis-events" \
    --update-env-vars "PUBSUB_TOPIC_EVENTS=analysis-events" \
    --update-env-vars "PUBSUB_TOPIC_PROGRESS=analysis-progress" \
    --update-env-vars "PUBSUB_TOPIC_PATTERNS=pattern-detected" \
    --update-env-vars "JWT_SECRET=nkt5/5J4oCSHfFMccpQLSnpbv5FblEj4ewlxQPID3e5ID8AqGJMqXhJBOsx8/bBcRdp6aOJtCrMQqxjRaEgmQw==" \
    --update-env-vars "ENABLE_AUTH=true" \
    --update-env-vars "ENVIRONMENT=production" \
    --project ${PROJECT_ID}

echo -e "\n⏳ Waiting for deployment..."
sleep 20

# Test health
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format='value(status.url)')

echo -e "\n🧪 Testing health endpoints..."
echo "1. Basic health:"
curl -s "${SERVICE_URL}/health" | jq '.'

echo -e "\n2. Ready check:"
curl -s "${SERVICE_URL}/health/ready" | jq '.'

echo -e "\n✅ Deployment complete with fixed hardcoded values!"
echo "All Pub/Sub topics and project IDs now use environment variables."