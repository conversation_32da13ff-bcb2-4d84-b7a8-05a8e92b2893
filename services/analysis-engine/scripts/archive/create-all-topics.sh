#!/bin/bash
# Create all Pub/Sub topics expected by the Analysis Engine
set -euo pipefail

echo "🔧 Creating All Required Pub/Sub Topics"
echo "======================================"

PROJECT_ID="vibe-match-463114"
SERVICE_ACCOUNT="analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com"

# Topics expected by the application (found in the code)
TOPICS=(
    "pattern-detected"
    "analysis-events"
    "analysis-progress"
)

echo "Creating topics expected by the application..."
for TOPIC in "${TOPICS[@]}"; do
    echo -e "\n📨 Processing topic: $TOPIC"
    
    # Create topic if it doesn't exist
    if gcloud pubsub topics describe ${TOPIC} --project=${PROJECT_ID} >/dev/null 2>&1; then
        echo "✅ Topic already exists: $TOPIC"
    else
        gcloud pubsub topics create ${TOPIC} \
            --project=${PROJECT_ID} \
            --message-retention-duration=7d
        echo "✅ Created topic: $TOPIC"
    fi
    
    # Grant publisher permission
    gcloud pubsub topics add-iam-policy-binding ${TOPIC} \
        --member="serviceAccount:${SERVICE_ACCOUNT}" \
        --role="roles/pubsub.publisher" \
        --project=${PROJECT_ID} >/dev/null 2>&1
    echo "✅ Ensured publisher permissions for: $TOPIC"
done

echo -e "\n✅ All Pub/Sub topics created successfully!"