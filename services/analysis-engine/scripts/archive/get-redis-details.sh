#!/bin/bash
# Get Redis instance details for Analysis Engine
set -euo pipefail

echo "🔍 Getting Redis instance details..."

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
REDIS_INSTANCE_NAME="analysis-engine-cache"

# Get Redis details
echo "📊 Redis Instance Information:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Get the Redis host IP
REDIS_HOST=$(gcloud redis instances describe ${REDIS_INSTANCE_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format="value(host)")

REDIS_PORT=$(gcloud redis instances describe ${REDIS_INSTANCE_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format="value(port)")

echo "Redis Host: ${REDIS_HOST}"
echo "Redis Port: ${REDIS_PORT}"
echo "Redis URL: redis://${REDIS_HOST}:${REDIS_PORT}"
echo ""

# Get full instance details
echo "📋 Full Instance Details:"
gcloud redis instances describe ${REDIS_INSTANCE_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format="table(
        name.basename(),
        tier,
        memorySizeGb,
        redisVersion,
        state,
        host,
        port,
        network.basename()
    )"

echo ""
echo "🔗 Connection Configuration:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "REDIS_URL=redis://${REDIS_HOST}:${REDIS_PORT}"
echo ""
echo "⚠️  Important: Cloud Run needs a VPC connector to access Redis"
echo "   See docs/REDIS_DEPLOYMENT.md for VPC connector setup"