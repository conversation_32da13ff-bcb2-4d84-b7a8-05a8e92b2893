#!/bin/bash
# Fix health check issues for Analysis Engine
set -euo pipefail

echo "🔧 Fixing Health Check Issues"
echo "============================="

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
SERVICE_NAME="analysis-engine"
CORRECT_BUCKET="ccl-analysis-artifacts"
CORRECT_TOPIC="analysis-results"

# The logs show it's looking for 'pattern-detected' topic
echo -e "\n1️⃣  The service is looking for wrong Pub/Sub topic name"
echo "Expected: pattern-detected (hardcoded in app)"
echo "Configured: analysis-results"
echo ""
echo "Creating the expected topic..."
if gcloud pubsub topics describe pattern-detected --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo "✅ Topic 'pattern-detected' already exists"
else
    gcloud pubsub topics create pattern-detected \
        --project=${PROJECT_ID} \
        --message-retention-duration=7d
    echo "✅ Created topic 'pattern-detected'"
    
    # Grant publisher permission
    gcloud pubsub topics add-iam-policy-binding pattern-detected \
        --member="serviceAccount:analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com" \
        --role="roles/pubsub.publisher" \
        --project=${PROJECT_ID}
    echo "✅ Granted publisher permissions"
fi

# Also update the environment variable to use the correct topic
echo -e "\n2️⃣  Updating environment variable to correct topic..."
gcloud run services update ${SERVICE_NAME} \
    --region=${REGION} \
    --update-env-vars "PUBSUB_TOPIC=pattern-detected" \
    --project=${PROJECT_ID}

echo -e "\n⏳ Waiting for deployment..."
sleep 15

# Test health check again
echo -e "\n3️⃣  Testing health checks..."
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format='value(status.url)')

echo "Health check response:"
curl -s "${SERVICE_URL}/health/ready" | jq '.'

# Check if we need to fix storage bucket name too
echo -e "\n4️⃣  Checking storage configuration..."
echo "Current environment variables related to storage:"
gcloud run services describe ${SERVICE_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format=json | jq '.spec.template.spec.containers[0].env[] | select(.name | contains("STORAGE"))'

echo -e "\n📊 Summary:"
echo "==========="
echo "✅ Created 'pattern-detected' Pub/Sub topic (app expects this name)"
echo "✅ Updated PUBSUB_TOPIC environment variable"
echo "⚠️  Storage health check might be looking for specific bucket structure"
echo ""
echo "The application code appears to have hardcoded topic names."
echo "For a permanent fix, the application code should use the PUBSUB_TOPIC environment variable."