#!/bin/bash
# Complete infrastructure setup for Analysis Engine
set -euo pipefail

echo "🚀 Complete Analysis Engine Infrastructure Setup"
echo "=============================================="
echo ""
echo "This script will:"
echo "1. Verify current infrastructure status"
echo "2. Create Storage bucket and Pub/Sub topics"
echo "3. Deploy with JWT authentication"
echo "4. Run final verification"
echo ""
echo "Press Enter to continue..."
read -r

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Step 1: Verify current status
echo -e "\n📋 Step 1: Checking current infrastructure status..."
echo "=================================================="
${SCRIPT_DIR}/verify-infrastructure.sh

echo -e "\n\nPress Enter to continue with setup..."
read -r

# Step 2: Setup Storage and Pub/Sub
echo -e "\n📦 Step 2: Setting up Storage and Pub/Sub..."
echo "==========================================="
${SCRIPT_DIR}/setup-storage-pubsub.sh

echo -e "\n\nPress Enter to continue..."
read -r

# Step 3: Deploy with authentication
echo -e "\n🔐 Step 3: Deploying with authentication..."
echo "=========================================="
${SCRIPT_DIR}/deploy-with-auth.sh

echo -e "\n\nPress Enter to run final verification..."
read -r

# Step 4: Final verification
echo -e "\n✅ Step 4: Final infrastructure verification..."
echo "============================================="
${SCRIPT_DIR}/verify-infrastructure.sh

echo -e "\n\n🎉 Setup Complete!"
echo "=================="
echo ""
echo "Your Analysis Engine is now fully deployed with:"
echo "✅ Cloud Run service with auto-scaling"
echo "✅ Spanner database for persistence"
echo "✅ Redis cache for performance"
echo "✅ Storage bucket for artifacts"
echo "✅ Pub/Sub for async messaging"
echo "✅ JWT authentication enabled"
echo "✅ VPC connector for private resources"
echo ""
echo "Next steps:"
echo "1. Test with a real repository analysis"
echo "2. Set up monitoring dashboards"
echo "3. Configure alerts"
echo "4. Run load tests"
echo "5. Update CORS origins for your frontend"