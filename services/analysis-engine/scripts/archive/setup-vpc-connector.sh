#!/bin/bash
# Setup VPC connector for Cloud Run to access Redis
set -euo pipefail

echo "🔌 Setting up VPC Connector for Redis access..."

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
CONNECTOR_NAME="analysis-engine-connector"
SUBNET_NAME="analysis-engine-subnet"

# Enable VPC Access API if not already enabled
echo "🔌 Checking VPC Access API..."
if ! gcloud services list --enabled --filter="name:vpcaccess.googleapis.com" --format="value(name)" | grep -q vpcaccess; then
    echo "📡 Enabling VPC Access API..."
    gcloud services enable vpcaccess.googleapis.com --project=${PROJECT_ID}
    echo "✅ VPC Access API enabled"
    echo "⏳ Waiting for API to propagate..."
    sleep 30
else
    echo "✅ VPC Access API already enabled"
fi

# Check if connector already exists
echo "🔍 Checking for existing VPC connector..."
if gcloud compute networks vpc-access connectors describe ${CONNECTOR_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo "✅ VPC connector '${CONNECTOR_NAME}' already exists"
    
    # Get connector details
    echo -e "\n📊 Connector Details:"
    gcloud compute networks vpc-access connectors describe ${CONNECTOR_NAME} \
        --region=${REGION} \
        --project=${PROJECT_ID} \
        --format="table(name.basename(),state,network,ipCidrRange)"
else
    echo "📡 Creating VPC connector..."
    
    # Check if subnet exists
    echo "🔍 Checking for subnet..."
    if ! gcloud compute networks subnets describe ${SUBNET_NAME} \
        --region=${REGION} \
        --project=${PROJECT_ID} >/dev/null 2>&1; then
        
        echo "📍 Creating subnet for VPC connector..."
        gcloud compute networks subnets create ${SUBNET_NAME} \
            --network=default \
            --region=${REGION} \
            --range=********/28 \
            --project=${PROJECT_ID}
        echo "✅ Subnet created"
    else
        echo "✅ Subnet already exists"
    fi
    
    # Create the VPC connector
    echo "🚀 Creating VPC connector..."
    gcloud compute networks vpc-access connectors create ${CONNECTOR_NAME} \
        --region=${REGION} \
        --subnet=${SUBNET_NAME} \
        --subnet-project=${PROJECT_ID} \
        --min-instances=2 \
        --max-instances=10 \
        --machine-type=e2-micro \
        --project=${PROJECT_ID}
    
    echo "✅ VPC connector created successfully"
fi

# Update Cloud Run service with Redis URL and VPC connector
echo -e "\n🚀 Updating Cloud Run service with Redis configuration..."

REDIS_URL="redis://10.76.85.67:6379"

gcloud run services update analysis-engine \
    --region=${REGION} \
    --update-env-vars "REDIS_URL=${REDIS_URL}" \
    --vpc-connector=${CONNECTOR_NAME} \
    --project=${PROJECT_ID}

echo -e "\n✅ Cloud Run service updated with:"
echo "   - Redis URL: ${REDIS_URL}"
echo "   - VPC Connector: ${CONNECTOR_NAME}"

# Wait for deployment to complete
echo -e "\n⏳ Waiting for deployment to complete..."
sleep 10

# Test the service
SERVICE_URL=$(gcloud run services describe analysis-engine \
    --platform managed \
    --region ${REGION} \
    --project ${PROJECT_ID} \
    --format 'value(status.url)')

echo -e "\n🏥 Testing health endpoint..."
if curl -f "${SERVICE_URL}/health"; then
    echo -e "\n✅ Service is healthy!"
else
    echo -e "\n⚠️  Health check failed. Checking logs..."
fi

echo -e "\n📋 Checking Redis connection in logs..."
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=analysis-engine AND textPayload:Redis" \
    --limit=5 \
    --project=${PROJECT_ID} \
    --format="table(timestamp,textPayload)" \
    --freshness=1m

echo -e "\n🎉 Redis integration complete!"
echo "   The Analysis Engine now has caching enabled via Redis"