#!/bin/bash
# Deploy Analysis Engine with JWT authentication enabled
set -euo pipefail

echo "🔐 Deploying Analysis Engine with Authentication..."
echo "================================================="

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
SERVICE_NAME="analysis-engine"

# Generate a secure JWT secret if not provided
if [ -z "${JWT_SECRET:-}" ]; then
    echo "🔐 Generating secure JWT secret..."
    JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')
    echo ""
    echo "⚠️  IMPORTANT: Save this JWT secret securely!"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "JWT_SECRET: ${JWT_SECRET}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    echo "Press Enter to continue with deployment..."
    read -r
fi

# Option 1: Store JWT secret in Secret Manager (recommended for production)
echo "🔒 Storing JWT secret in Secret Manager..."
SECRET_NAME="analysis-engine-jwt-secret"

# Check if secret exists
if gcloud secrets describe ${SECRET_NAME} --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo "Updating existing secret..."
    echo -n "${JWT_SECRET}" | gcloud secrets versions add ${SECRET_NAME} \
        --data-file=- \
        --project=${PROJECT_ID}
else
    echo "Creating new secret..."
    echo -n "${JWT_SECRET}" | gcloud secrets create ${SECRET_NAME} \
        --data-file=- \
        --replication-policy="automatic" \
        --project=${PROJECT_ID}
    
    # Grant access to service account
    gcloud secrets add-iam-policy-binding ${SECRET_NAME} \
        --member="serviceAccount:analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com" \
        --role="roles/secretmanager.secretAccessor" \
        --project=${PROJECT_ID}
fi

echo "✅ JWT secret stored in Secret Manager"

# Update Cloud Run service with all production settings
echo -e "\n🚀 Updating Cloud Run service with full production configuration..."
gcloud run services update ${SERVICE_NAME} \
    --region=${REGION} \
    --update-env-vars "JWT_SECRET=${JWT_SECRET}" \
    --update-env-vars "ENABLE_AUTH=true" \
    --update-env-vars "ENVIRONMENT=production" \
    --update-env-vars "CORS_ORIGINS=https://your-frontend-domain.com,http://localhost:3000" \
    --update-env-vars "API_KEY_HEADER=x-api-key" \
    --update-env-vars "RATE_LIMIT_PER_HOUR=1000" \
    --update-env-vars "JWT_ROTATION_DAYS=7" \
    --update-env-vars "ENABLE_AUDIT_LOGGING=true" \
    --update-env-vars "OTEL_ENDPOINT=https://monitoring.googleapis.com/v3/projects/${PROJECT_ID}/traces" \
    --min-instances=1 \
    --max-instances=100 \
    --project=${PROJECT_ID}

echo -e "\n✅ Authentication configuration complete!"

# Get service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format='value(status.url)')

# Test endpoints
echo -e "\n🧪 Testing endpoints..."
echo "━━━━━━━━━━━━━━━━━━━━━"

# Test public endpoints
echo -e "\n1. Public endpoints (should work without auth):"
echo -n "   /health: "
curl -sf "${SERVICE_URL}/health" >/dev/null && echo "✅ OK" || echo "❌ Failed"

echo -n "   /health/ready: "
curl -sf "${SERVICE_URL}/health/ready" >/dev/null && echo "✅ OK" || echo "❌ Failed"

echo -n "   /api/v1/version: "
curl -sf "${SERVICE_URL}/api/v1/version" >/dev/null && echo "✅ OK" || echo "❌ Failed"

echo -n "   /api/v1/languages: "
curl -sf "${SERVICE_URL}/api/v1/languages" >/dev/null && echo "✅ OK" || echo "❌ Failed"

# Test protected endpoint
echo -e "\n2. Protected endpoints (should require auth):"
echo -n "   /api/v1/analyze (without auth): "
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "${SERVICE_URL}/api/v1/analyze")
if [ "$RESPONSE" = "401" ] || [ "$RESPONSE" = "403" ]; then
    echo "✅ Properly protected (${RESPONSE})"
else
    echo "❌ Not protected (${RESPONSE})"
fi

# Generate sample JWT token
echo -e "\n📝 Sample JWT token generation:"
echo "================================"
echo "To generate a JWT token for testing, use:"
echo ""
echo "jwt.io or a JWT library with:"
echo "- Algorithm: HS256"
echo "- Secret: [Your JWT_SECRET]"
echo "- Payload example:"
echo '  {
    "sub": "user123",
    "email": "<EMAIL>",
    "exp": <unix_timestamp>,
    "iat": <unix_timestamp>
  }'

echo -e "\n📊 Deployment Summary:"
echo "====================="
echo "Service URL: ${SERVICE_URL}"
echo "Authentication: Enabled"
echo "JWT Secret: Stored in Secret Manager (${SECRET_NAME})"
echo "CORS Origins: Configure as needed"
echo "Rate Limiting: 1000 requests/hour"
echo ""
echo "🎉 Production deployment with authentication complete!"