#!/bin/bash
# Setup Storage Bucket and Pub/Sub Topic for Analysis Engine
set -euo pipefail

echo "🚀 Setting up Storage and Pub/Sub for Analysis Engine..."
echo "======================================================"

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
BUCKET_NAME="ccl-analysis-artifacts"
PUBSUB_TOPIC="analysis-results"
SERVICE_ACCOUNT="analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 1. Create Storage Bucket
echo -e "\n📦 Setting up Storage Bucket..."
if gsutil ls -b gs://${BUCKET_NAME} >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Storage bucket already exists: gs://${BUCKET_NAME}${NC}"
else
    echo "Creating storage bucket..."
    gsutil mb -p ${PROJECT_ID} -c STANDARD -l ${REGION} -b on gs://${BUCKET_NAME}
    echo -e "${GREEN}✅ Storage bucket created: gs://${BUCKET_NAME}${NC}"
    
    # Set bucket permissions for service account
    echo "Setting bucket permissions..."
    gsutil iam ch serviceAccount:${SERVICE_ACCOUNT}:objectAdmin gs://${BUCKET_NAME}
    echo -e "${GREEN}✅ Permissions set for service account${NC}"
    
    # Set lifecycle policy (optional - delete old artifacts after 30 days)
    cat > /tmp/lifecycle.json << EOF
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "Delete"},
        "condition": {
          "age": 30,
          "matchesPrefix": ["temp/", "artifacts/"]
        }
      }
    ]
  }
}
EOF
    gsutil lifecycle set /tmp/lifecycle.json gs://${BUCKET_NAME}
    rm /tmp/lifecycle.json
    echo -e "${GREEN}✅ Lifecycle policy set (30-day retention)${NC}"
fi

# 2. Create Pub/Sub Topic
echo -e "\n📨 Setting up Pub/Sub Topic..."
if gcloud pubsub topics describe ${PUBSUB_TOPIC} --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Pub/Sub topic already exists: ${PUBSUB_TOPIC}${NC}"
else
    echo "Creating Pub/Sub topic..."
    gcloud pubsub topics create ${PUBSUB_TOPIC} \
        --project=${PROJECT_ID} \
        --message-retention-duration=7d
    echo -e "${GREEN}✅ Pub/Sub topic created: ${PUBSUB_TOPIC}${NC}"
    
    # Grant publisher permission to service account
    echo "Setting topic permissions..."
    gcloud pubsub topics add-iam-policy-binding ${PUBSUB_TOPIC} \
        --member="serviceAccount:${SERVICE_ACCOUNT}" \
        --role="roles/pubsub.publisher" \
        --project=${PROJECT_ID}
    echo -e "${GREEN}✅ Publisher permissions set for service account${NC}"
fi

# 3. Create Dead Letter Topic (for failed messages)
DLQ_TOPIC="${PUBSUB_TOPIC}-dlq"
echo -e "\n📨 Setting up Dead Letter Queue..."
if gcloud pubsub topics describe ${DLQ_TOPIC} --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo -e "${GREEN}✅ DLQ topic already exists: ${DLQ_TOPIC}${NC}"
else
    echo "Creating DLQ topic..."
    gcloud pubsub topics create ${DLQ_TOPIC} \
        --project=${PROJECT_ID} \
        --message-retention-duration=7d
    echo -e "${GREEN}✅ DLQ topic created: ${DLQ_TOPIC}${NC}"
fi

# 4. Create a default subscription (optional)
SUBSCRIPTION_NAME="${PUBSUB_TOPIC}-default-sub"
echo -e "\n📨 Creating default subscription..."
if gcloud pubsub subscriptions describe ${SUBSCRIPTION_NAME} --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Subscription already exists: ${SUBSCRIPTION_NAME}${NC}"
else
    echo "Creating subscription..."
    gcloud pubsub subscriptions create ${SUBSCRIPTION_NAME} \
        --topic=${PUBSUB_TOPIC} \
        --project=${PROJECT_ID} \
        --ack-deadline=60 \
        --message-retention-duration=7d \
        --dead-letter-topic=${DLQ_TOPIC} \
        --max-delivery-attempts=5
    echo -e "${GREEN}✅ Subscription created: ${SUBSCRIPTION_NAME}${NC}"
fi

# 5. Test Storage Access
echo -e "\n🧪 Testing storage access..."
TEST_FILE="/tmp/test-$(date +%s).txt"
echo "Analysis Engine storage test" > ${TEST_FILE}
if gsutil cp ${TEST_FILE} gs://${BUCKET_NAME}/test/ && \
   gsutil rm gs://${BUCKET_NAME}/test/$(basename ${TEST_FILE}); then
    echo -e "${GREEN}✅ Storage access test passed${NC}"
else
    echo -e "${YELLOW}⚠️  Storage access test failed${NC}"
fi
rm -f ${TEST_FILE}

# 6. Update Cloud Run service with bucket name
echo -e "\n🔄 Updating Cloud Run service with storage configuration..."
gcloud run services update analysis-engine \
    --region=${REGION} \
    --update-env-vars "STORAGE_BUCKET=${BUCKET_NAME}" \
    --update-env-vars "STORAGE_BUCKET_NAME=${BUCKET_NAME}" \
    --update-env-vars "PUBSUB_TOPIC=${PUBSUB_TOPIC}" \
    --project=${PROJECT_ID}

echo -e "\n✅ Storage and Pub/Sub setup complete!"
echo "======================================"
echo -e "📦 Storage Bucket: gs://${BUCKET_NAME}"
echo -e "📨 Pub/Sub Topic: ${PUBSUB_TOPIC}"
echo -e "📨 Dead Letter Queue: ${DLQ_TOPIC}"
echo -e "📨 Default Subscription: ${SUBSCRIPTION_NAME}"