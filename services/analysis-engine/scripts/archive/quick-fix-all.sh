#!/bin/bash
# Quick fix for all remaining issues
set -euo pipefail

echo "🚀 Quick Fix for Analysis Engine Issues"
echo "======================================"

PROJECT_ID="vibe-match-463114"
REGION="us-central1"

# 1. Create all required Pub/Sub topics
echo "1️⃣ Creating Pub/Sub topics..."
./services/analysis-engine/scripts/create-all-topics.sh

# 2. Update environment variable to match what the app expects
echo -e "\n2️⃣ Updating environment variables..."
gcloud run services update analysis-engine \
    --region=${REGION} \
    --update-env-vars "PUBSUB_TOPIC=analysis-events" \
    --update-env-vars "STORAGE_BUCKET_NAME=ccl-analysis-artifacts" \
    --project=${PROJECT_ID}

echo -e "\n⏳ Waiting for deployment..."
sleep 15

# 3. Test health again
echo -e "\n3️⃣ Testing health check..."
SERVICE_URL="https://analysis-engine-l3nxty7oka-uc.a.run.app"
curl -s "${SERVICE_URL}/health/ready" | jq '.'

echo -e "\n✅ Quick fixes applied!"
echo "If health checks still fail, we may need to:"
echo "1. Check bucket permissions more carefully"
echo "2. Review the application logs for specific errors"