#!/bin/bash
# Grant necessary IAM permissions for Analysis Engine setup
set -euo pipefail

echo "🔐 Setting up IAM Permissions for Analysis Engine"
echo "================================================"

# Configuration
PROJECT_ID="vibe-match-463114"
USER_EMAIL="${USER_EMAIL:-<EMAIL>}"
SERVICE_ACCOUNT="analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "\n👤 User Account: ${USER_EMAIL}"
echo -e "🤖 Service Account: ${SERVICE_ACCOUNT}"
echo ""

# Required roles for user account
echo "📋 Required roles for your user account:"
echo "========================================"
echo "1. roles/pubsub.admin - To manage Pub/Sub topics"
echo "2. roles/storage.admin - To manage Storage buckets"
echo "3. roles/secretmanager.admin - To manage secrets"
echo "4. roles/iam.serviceAccountAdmin - To manage service accounts"
echo "5. roles/run.admin - To manage Cloud Run services"
echo "6. roles/spanner.databaseAdmin - To manage Spanner"
echo "7. roles/redis.admin - To manage Redis instances"
echo "8. roles/compute.networkAdmin - For VPC connector"
echo ""

echo -e "${YELLOW}⚠️  You need project owner or IAM admin to run these commands.${NC}"
echo "If you don't have these permissions, ask your project admin to run:"
echo ""

# Generate commands for project admin
cat << EOF > grant-permissions-commands.txt
# Commands to grant permissions to ${USER_EMAIL}

# 1. Pub/Sub Admin
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="user:${USER_EMAIL}" \\
    --role="roles/pubsub.admin"

# 2. Storage Admin
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="user:${USER_EMAIL}" \\
    --role="roles/storage.admin"

# 3. Secret Manager Admin
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="user:${USER_EMAIL}" \\
    --role="roles/secretmanager.admin"

# 4. Service Account Admin
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="user:${USER_EMAIL}" \\
    --role="roles/iam.serviceAccountAdmin"

# 5. Cloud Run Admin
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="user:${USER_EMAIL}" \\
    --role="roles/run.admin"

# 6. Spanner Database Admin
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="user:${USER_EMAIL}" \\
    --role="roles/spanner.databaseAdmin"

# 7. Redis Admin
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="user:${USER_EMAIL}" \\
    --role="roles/redis.admin"

# 8. Compute Network Admin
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="user:${USER_EMAIL}" \\
    --role="roles/compute.networkAdmin"

# Service Account Permissions
# ===========================

# 1. Spanner Database User
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="serviceAccount:${SERVICE_ACCOUNT}" \\
    --role="roles/spanner.databaseUser"

# 2. Storage Object Admin
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="serviceAccount:${SERVICE_ACCOUNT}" \\
    --role="roles/storage.objectAdmin"

# 3. Pub/Sub Publisher
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="serviceAccount:${SERVICE_ACCOUNT}" \\
    --role="roles/pubsub.publisher"

# 4. Secret Manager Secret Accessor
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="serviceAccount:${SERVICE_ACCOUNT}" \\
    --role="roles/secretmanager.secretAccessor"

# 5. Redis Editor
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="serviceAccount:${SERVICE_ACCOUNT}" \\
    --role="roles/redis.editor"

# 6. Monitoring Metric Writer
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="serviceAccount:${SERVICE_ACCOUNT}" \\
    --role="roles/monitoring.metricWriter"

# 7. Cloud Trace Agent
gcloud projects add-iam-policy-binding ${PROJECT_ID} \\
    --member="serviceAccount:${SERVICE_ACCOUNT}" \\
    --role="roles/cloudtrace.agent"
EOF

echo "📝 Commands saved to: grant-permissions-commands.txt"
echo ""

# Check current permissions
echo "🔍 Checking your current permissions..."
echo "======================================"

# Check if user has owner role
if gcloud projects get-iam-policy ${PROJECT_ID} \
    --flatten="bindings[].members" \
    --filter="bindings.members:user:${USER_EMAIL} AND bindings.role:roles/owner" \
    --format="value(bindings.role)" | grep -q owner; then
    echo -e "${GREEN}✅ You have project owner role${NC}"
    echo ""
    echo "Would you like to run the permission grants now? (y/n)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "🚀 Granting permissions..."
        bash grant-permissions-commands.txt
        echo -e "\n${GREEN}✅ Permissions granted successfully!${NC}"
    else
        echo "Skipping automatic permission grant."
    fi
else
    echo -e "${YELLOW}⚠️  You don't have project owner role${NC}"
    echo ""
    echo "Please ask your project admin to run the commands in:"
    echo "👉 grant-permissions-commands.txt"
    echo ""
    echo "Or grant you these roles through the Cloud Console:"
    echo "https://console.cloud.google.com/iam-admin/iam?project=${PROJECT_ID}"
fi

# Alternative: Create a more limited script for essential operations
echo -e "\n📝 Alternative: Minimal permissions approach"
echo "==========================================="
echo "If you can't get all permissions, you can still proceed with:"
echo "1. Create resources manually in Cloud Console"
echo "2. Use the simplified setup scripts"
echo ""

# Create simplified setup script
cat << 'EOF' > setup-storage-pubsub-simple.sh
#!/bin/bash
# Simplified setup that avoids IAM operations
set -euo pipefail

PROJECT_ID="vibe-match-463114"
BUCKET_NAME="ccl-analysis-artifacts"
PUBSUB_TOPIC="analysis-results"

echo "📦 Creating Storage bucket (without IAM)..."
gsutil mb -p ${PROJECT_ID} -c STANDARD -l us-central1 gs://${BUCKET_NAME} 2>/dev/null || echo "Bucket might already exist"

echo "📨 Creating Pub/Sub topic (without IAM)..."
gcloud pubsub topics create ${PUBSUB_TOPIC} --project=${PROJECT_ID} 2>/dev/null || echo "Topic might already exist"

echo "✅ Basic resources created"
echo "⚠️  Note: IAM permissions must be set manually in Cloud Console"
EOF

chmod +x setup-storage-pubsub-simple.sh
echo "Created: setup-storage-pubsub-simple.sh (for limited permissions)"

echo -e "\n✅ Setup complete!"
echo "=================="
echo "Next steps:"
echo "1. Get the required permissions granted"
echo "2. Re-run the complete setup script"
echo "3. Or use the simplified scripts and configure IAM manually"