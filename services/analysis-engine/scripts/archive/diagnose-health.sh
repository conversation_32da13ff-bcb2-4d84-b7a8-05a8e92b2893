#!/bin/bash
# Diagnose health check issues for Analysis Engine
set -euo pipefail

echo "🔍 Diagnosing Health Check Issues"
echo "================================="

# Configuration
PROJECT_ID="vibe-match-463114"
SERVICE_URL="https://analysis-engine-l3nxty7oka-uc.a.run.app"
SERVICE_ACCOUNT="analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com"

# 1. Check detailed ready status
echo -e "\n1️⃣  Checking Detailed Ready Status..."
echo "Response from /health/ready:"
curl -s "${SERVICE_URL}/health/ready" | jq '.' || echo "Failed to get ready status"

# 2. Check service account IAM bindings
echo -e "\n2️⃣  Checking Service Account IAM Roles..."
echo "Current roles for ${SERVICE_ACCOUNT}:"
gcloud projects get-iam-policy ${PROJECT_ID} \
    --flatten="bindings[].members" \
    --filter="bindings.members:serviceAccount:${SERVICE_ACCOUNT}" \
    --format="table(bindings.role)" | grep -v ROLE || echo "No roles found"

# 3. Check if service account needs additional permissions
echo -e "\n3️⃣  Required Permissions Check..."
REQUIRED_ROLES=(
    "roles/storage.objectAdmin"
    "roles/pubsub.publisher"
    "roles/spanner.databaseUser"
)

for role in "${REQUIRED_ROLES[@]}"; do
    if gcloud projects get-iam-policy ${PROJECT_ID} \
        --flatten="bindings[].members" \
        --filter="bindings.members:serviceAccount:${SERVICE_ACCOUNT} AND bindings.role:${role}" \
        --format="value(bindings.role)" | grep -q "$role"; then
        echo "✅ Has $role"
    else
        echo "❌ Missing $role"
        echo "   Granting $role..."
        gcloud projects add-iam-policy-binding ${PROJECT_ID} \
            --member="serviceAccount:${SERVICE_ACCOUNT}" \
            --role="$role" \
            --condition=None 2>/dev/null || echo "   Failed to grant (may need admin permissions)"
    fi
done

# 4. Test actual storage access
echo -e "\n4️⃣  Testing Storage Access..."
TEST_FILE="/tmp/health-test-$(date +%s).txt"
echo "Health check test" > ${TEST_FILE}

echo "Testing write access to bucket..."
if gsutil -i ${SERVICE_ACCOUNT} cp ${TEST_FILE} gs://ccl-analysis-artifacts/health-test/ 2>/dev/null; then
    echo "✅ Storage write successful"
    gsutil -i ${SERVICE_ACCOUNT} rm gs://ccl-analysis-artifacts/health-test/$(basename ${TEST_FILE}) 2>/dev/null
else
    echo "❌ Storage write failed - checking bucket IAM..."
    gsutil iam get gs://ccl-analysis-artifacts | grep -A5 ${SERVICE_ACCOUNT} || echo "Service account not in bucket IAM"
fi
rm -f ${TEST_FILE}

# 5. Check Cloud Run service configuration
echo -e "\n5️⃣  Checking Cloud Run Configuration..."
echo "Service account attached to Cloud Run:"
gcloud run services describe analysis-engine \
    --region=us-central1 \
    --project=${PROJECT_ID} \
    --format="value(spec.template.spec.serviceAccountName)"

# 6. Check recent logs for storage/pubsub errors
echo -e "\n6️⃣  Checking Recent Logs for Storage/Pub/Sub Errors..."
gcloud logging read "resource.type=cloud_run_revision AND (textPayload:storage OR textPayload:pubsub OR textPayload:Storage OR textPayload:PubSub)" \
    --limit=10 \
    --project=${PROJECT_ID} \
    --format="table(timestamp,textPayload)" \
    --freshness=30m

# 7. Force update with explicit project ID
echo -e "\n7️⃣  Updating Service with Explicit GCP Configuration..."
gcloud run services update analysis-engine \
    --region=us-central1 \
    --update-env-vars "GCP_PROJECT_ID=${PROJECT_ID}" \
    --update-env-vars "GOOGLE_CLOUD_PROJECT=${PROJECT_ID}" \
    --project=${PROJECT_ID}

echo -e "\n⏳ Waiting for update to propagate..."
sleep 10

# 8. Final health check
echo -e "\n8️⃣  Final Health Check..."
curl -s "${SERVICE_URL}/health/ready" | jq '.' || echo "Failed to get ready status"

echo -e "\n📊 Diagnosis Complete!"
echo "===================="
echo "If health checks still fail, the service might need:"
echo "1. Explicit GCP credentials in the container"
echo "2. Additional IAM roles at the project level"
echo "3. Check if the service is using workload identity"