#!/bin/bash
# Deploy with JWT secret directly (without Secret Manager)
set -euo pipefail

echo "🔐 Deploying Analysis Engine with Direct JWT Configuration..."
echo "=========================================================="

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
SERVICE_NAME="analysis-engine"

# Use the JWT secret that was generated (update this with your actual secret)
JWT_SECRET="${JWT_SECRET:-nkt5/5J4oCSHfFMccpQLSnpbv5FblEj4ewlxQPID3e5ID8AqGJMqXhJBOsx8/bBcRdp6aOJtCrMQqxjRaEgmQw==}"

echo "⚠️  Using JWT Secret (keep this secure!):"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "${JWT_SECRET}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Update Cloud Run service with all production settings including missing env vars
echo "🚀 Updating Cloud Run service with full production configuration..."
gcloud run services update ${SERVICE_NAME} \
    --region=${REGION} \
    --update-env-vars "JWT_SECRET=${JWT_SECRET}" \
    --update-env-vars "ENABLE_AUTH=true" \
    --update-env-vars "ENVIRONMENT=production" \
    --update-env-vars "GCP_PROJECT_ID=${PROJECT_ID}" \
    --update-env-vars "SPANNER_PROJECT_ID=${PROJECT_ID}" \
    --update-env-vars "SPANNER_INSTANCE_ID=ccl-instance" \
    --update-env-vars "SPANNER_DATABASE_ID=ccl_main" \
    --update-env-vars "REDIS_URL=redis://10.76.85.67:6379" \
    --update-env-vars "STORAGE_BUCKET=ccl-analysis-artifacts" \
    --update-env-vars "STORAGE_BUCKET_NAME=ccl-analysis-artifacts" \
    --update-env-vars "PUBSUB_TOPIC=analysis-results" \
    --update-env-vars "CORS_ORIGINS=*" \
    --update-env-vars "API_KEY_HEADER=x-api-key" \
    --update-env-vars "RATE_LIMIT_PER_HOUR=1000" \
    --update-env-vars "JWT_ROTATION_DAYS=7" \
    --update-env-vars "ENABLE_AUDIT_LOGGING=true" \
    --update-env-vars "MAX_CONCURRENT_ANALYSES=50" \
    --update-env-vars "MAX_FILE_SIZE_BYTES=10485760" \
    --update-env-vars "PARSE_TIMEOUT_SECONDS=30" \
    --update-env-vars "MAX_ANALYSIS_MEMORY_MB=2048" \
    --update-env-vars "MAX_DEPENDENCY_COUNT=10000" \
    --min-instances=1 \
    --max-instances=100 \
    --project=${PROJECT_ID}

echo -e "\n✅ Deployment complete!"

# Get service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format='value(status.url)')

# Wait for deployment
echo -e "\n⏳ Waiting for deployment to stabilize..."
sleep 15

# Test endpoints
echo -e "\n🧪 Testing endpoints..."
echo "━━━━━━━━━━━━━━━━━━━━━"

# Test public endpoints
echo -e "\n1. Public endpoints (should work without auth):"
echo -n "   /health: "
curl -sf "${SERVICE_URL}/health" >/dev/null && echo "✅ OK" || echo "❌ Failed"

echo -n "   /health/ready: "
READY_RESPONSE=$(curl -s "${SERVICE_URL}/health/ready")
if echo "$READY_RESPONSE" | grep -q '"ready":true'; then
    echo "✅ OK - All services ready"
else
    echo "⚠️  Some services not ready: $READY_RESPONSE"
fi

echo -n "   /api/v1/version: "
curl -sf "${SERVICE_URL}/api/v1/version" >/dev/null && echo "✅ OK" || echo "❌ Failed"

echo -n "   /api/v1/languages: "
curl -sf "${SERVICE_URL}/api/v1/languages" >/dev/null && echo "✅ OK" || echo "❌ Failed"

# Test protected endpoint
echo -e "\n2. Protected endpoints (should require auth):"
echo -n "   /api/v1/analyze (without auth): "
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "${SERVICE_URL}/api/v1/analyze")
if [ "$RESPONSE" = "401" ] || [ "$RESPONSE" = "403" ]; then
    echo "✅ Properly protected (${RESPONSE})"
else
    echo "❌ Not protected (${RESPONSE})"
fi

# Show how to create JWT token
echo -e "\n📝 To test authenticated endpoints:"
echo "=================================="
echo "1. Go to https://jwt.io"
echo "2. Select Algorithm: HS256"
echo "3. Paste this secret in 'your-256-bit-secret':"
echo "   ${JWT_SECRET}"
echo "4. Set payload like:"
echo '   {
     "sub": "user123",
     "email": "<EMAIL>",
     "exp": 1999999999,
     "iat": 1700000000
   }'
echo "5. Use the generated token in Authorization header:"
echo "   curl -H \"Authorization: Bearer <YOUR_TOKEN>\" ${SERVICE_URL}/api/v1/analyze"

echo -e "\n📊 Deployment Summary:"
echo "====================="
echo "Service URL: ${SERVICE_URL}"
echo "Authentication: Enabled"
echo "All environment variables: Set"
echo "Infrastructure: Complete"
echo ""
echo "🎉 Analysis Engine is now fully deployed and production-ready!"
echo ""
echo "⚠️  Important: Store this JWT secret securely:"
echo "${JWT_SECRET}"