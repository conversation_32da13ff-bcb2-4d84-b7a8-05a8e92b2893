#!/bin/bash
set -euo pipefail

# ============================================
# Analysis Engine Optimized Deployment Script
# Supports blue-green deployment with rollback
# ============================================

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
export PROJECT_ID=${PROJECT_ID:-vibe-match-463114}
export REGION=${REGION:-us-central1}
export SERVICE_NAME=analysis-engine
export REPOSITORY=ccl-services
export IMAGE_REGISTRY="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}"
export IMAGE_NAME="${IMAGE_REGISTRY}/${SERVICE_NAME}"

# Deployment configuration
export DEPLOYMENT_MODE=${DEPLOYMENT_MODE:-canary} # canary, blue-green, or direct
export CANARY_PERCENTAGE=${CANARY_PERCENTAGE:-10}
export STABILITY_WAIT=${STABILITY_WAIT:-60}

# Function to print colored output
log() {
    local level=$1
    shift
    case $level in
        INFO) echo -e "${BLUE}[INFO]${NC} $*" ;;
        SUCCESS) echo -e "${GREEN}[SUCCESS]${NC} $*" ;;
        WARN) echo -e "${YELLOW}[WARN]${NC} $*" ;;
        ERROR) echo -e "${RED}[ERROR]${NC} $*" ;;
    esac
}

# Function to check prerequisites
check_prerequisites() {
    log INFO "Checking prerequisites..."
    
    # Check gcloud
    if ! command -v gcloud &> /dev/null; then
        log ERROR "gcloud CLI not found. Please install Google Cloud SDK."
        exit 1
    fi
    
    # Check docker
    if ! command -v docker &> /dev/null; then
        log ERROR "docker not found. Please install Docker."
        exit 1
    fi
    
    # Check jq
    if ! command -v jq &> /dev/null; then
        log ERROR "jq not found. Please install jq for JSON parsing."
        exit 1
    fi
    
    # Check authentication
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log ERROR "No active gcloud authentication found. Please run 'gcloud auth login'"
        exit 1
    fi
    
    log SUCCESS "All prerequisites met"
}

# Function to validate service configuration
validate_config() {
    log INFO "Validating configuration..."
    
    # Check if service account exists
    if ! gcloud iam service-accounts describe "analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
        log ERROR "Service account analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com not found"
        exit 1
    fi
    
    # Check if Artifact Registry repository exists
    if ! gcloud artifacts repositories describe ${REPOSITORY} --location=${REGION} &> /dev/null; then
        log WARN "Artifact Registry repository ${REPOSITORY} not found. Creating..."
        gcloud artifacts repositories create ${REPOSITORY} \
            --repository-format=docker \
            --location=${REGION} \
            --description="CCL Services Docker Repository"
    fi
    
    log SUCCESS "Configuration validated"
}

# Function to build and push image
build_and_push() {
    local version=$1
    log INFO "Building Docker image with version: ${version}"
    
    # Enable Docker BuildKit for better caching
    export DOCKER_BUILDKIT=1
    
    # Pull latest image for cache
    docker pull ${IMAGE_NAME}:latest || true
    
    # Build with cache
    docker build \
        --cache-from ${IMAGE_NAME}:latest \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        -t ${IMAGE_NAME}:${version} \
        -t ${IMAGE_NAME}:latest \
        -f Dockerfile \
        --build-arg RUNTIME_TARGET=distroless \
        .
    
    log INFO "Pushing image to Artifact Registry..."
    
    # Configure docker for Artifact Registry
    gcloud auth configure-docker ${REGION}-docker.pkg.dev --quiet
    
    # Push both tags
    docker push ${IMAGE_NAME}:${version}
    docker push ${IMAGE_NAME}:latest
    
    log SUCCESS "Image built and pushed successfully"
}

# Function to run security scan
security_scan() {
    local version=$1
    log INFO "Running security scan on image..."
    
    gcloud container images scan ${IMAGE_NAME}:${version} || {
        log WARN "Security scan failed or found vulnerabilities. Check the report."
        # Don't fail deployment, just warn
    }
}

# Function to deploy service
deploy_service() {
    local version=$1
    local tag=$2
    local traffic_percentage=${3:-0}
    
    log INFO "Deploying service with tag: ${tag}, traffic: ${traffic_percentage}%"
    
    # Deploy with no traffic initially
    gcloud run deploy ${SERVICE_NAME} \
        --image ${IMAGE_NAME}:${version} \
        --platform managed \
        --region ${REGION} \
        --memory 4Gi \
        --cpu 4 \
        --timeout 300s \
        --concurrency 1000 \
        --min-instances 1 \
        --max-instances 1000 \
        --cpu-boost \
        --port 8001 \
        --tag ${tag} \
        --no-traffic \
        --set-env-vars "ENVIRONMENT=production" \
        --set-env-vars "RUST_LOG=info" \
        --set-env-vars "GCP_PROJECT_ID=${PROJECT_ID}" \
        --set-env-vars "GCP_REGION=${REGION}" \
        --set-env-vars "SPANNER_INSTANCE=ccl-production" \
        --set-env-vars "SPANNER_DATABASE=ccl-main" \
        --set-env-vars "STORAGE_BUCKET=ccl-analysis-artifacts" \
        --set-env-vars "STORAGE_BUCKET_NAME=ccl-analysis-${PROJECT_ID}" \
        --set-env-vars "PUBSUB_TOPIC=analysis-events" \
        --set-env-vars "VERTEX_AI_LOCATION=${REGION}" \
        --set-env-vars "ENABLE_AUTH=true" \
        --set-env-vars "CORS_ORIGINS=*" \
        --set-env-vars "MAX_CONCURRENT_ANALYSES=50" \
        --set-env-vars "MAX_REPOSITORY_SIZE_GB=10" \
        --set-env-vars "ANALYSIS_TIMEOUT_SECONDS=300" \
        --set-env-vars "MAX_FILE_SIZE_MB=50" \
        --set-env-vars "TEMP_DIR=/tmp/ccl-analysis" \
        --set-env-vars "ENABLE_TRACING=true" \
        --set-env-vars "ENABLE_METRICS=true" \
        --set-secrets "JWT_SECRET=analysis-engine-jwt-secret:latest" \
        --set-secrets "GEMINI_API_KEY=gemini-api-key:latest" \
        --service-account "analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com" \
        --vpc-connector "projects/${PROJECT_ID}/locations/${REGION}/connectors/ccl-vpc-connector" \
        --vpc-egress all-traffic \
        --allow-unauthenticated
    
    if [ ${traffic_percentage} -gt 0 ]; then
        log INFO "Routing ${traffic_percentage}% traffic to ${tag}"
        gcloud run services update-traffic ${SERVICE_NAME} \
            --region ${REGION} \
            --to-tags ${tag}=${traffic_percentage}
    fi
}

# Function to check service health
check_health() {
    local url=$1
    local tag=${2:-}
    
    if [ -n "${tag}" ]; then
        url="${url/https:\/\//https://${tag}---}"
    fi
    
    log INFO "Checking health at: ${url}/health"
    
    local response=$(curl -s -w "\n%{http_code}" ${url}/health)
    local http_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | head -n-1)
    
    if [ "$http_code" -eq "200" ]; then
        log SUCCESS "Health check passed"
        echo "$body" | jq . || echo "$body"
        return 0
    else
        log ERROR "Health check failed with status: ${http_code}"
        echo "$body"
        return 1
    fi
}

# Function for canary deployment
canary_deployment() {
    local version=$1
    local tag=$2
    
    log INFO "Starting canary deployment"
    
    # Deploy with canary tag
    deploy_service ${version} ${tag} 0
    
    # Get service URL
    local service_url=$(gcloud run services describe ${SERVICE_NAME} \
        --platform managed \
        --region ${REGION} \
        --format 'value(status.url)')
    
    # Test the canary version
    if ! check_health ${service_url} ${tag}; then
        log ERROR "Canary health check failed"
        return 1
    fi
    
    # Route canary traffic
    log INFO "Routing ${CANARY_PERCENTAGE}% traffic to canary"
    gcloud run services update-traffic ${SERVICE_NAME} \
        --region ${REGION} \
        --to-tags ${tag}=${CANARY_PERCENTAGE}
    
    log INFO "Waiting ${STABILITY_WAIT} seconds for stability..."
    sleep ${STABILITY_WAIT}
    
    # Check metrics (simplified - in production, check actual metrics)
    if check_health ${service_url}; then
        log INFO "Canary stable, proceeding with full rollout"
        
        # Gradual rollout
        for percentage in 25 50 100; do
            if [ $percentage -eq 100 ]; then
                log INFO "Routing all traffic to new version"
                gcloud run services update-traffic ${SERVICE_NAME} \
                    --region ${REGION} \
                    --to-latest
            else
                log INFO "Increasing traffic to ${percentage}%"
                gcloud run services update-traffic ${SERVICE_NAME} \
                    --region ${REGION} \
                    --to-tags ${tag}=${percentage}
                sleep 30
            fi
        done
        
        log SUCCESS "Canary deployment completed successfully"
    else
        log ERROR "Canary deployment failed, rolling back"
        rollback ${tag}
        return 1
    fi
}

# Function for blue-green deployment
blue_green_deployment() {
    local version=$1
    local tag=$2
    
    log INFO "Starting blue-green deployment"
    
    # Deploy green version
    deploy_service ${version} ${tag} 0
    
    # Get service URL
    local service_url=$(gcloud run services describe ${SERVICE_NAME} \
        --platform managed \
        --region ${REGION} \
        --format 'value(status.url)')
    
    # Test green version
    if ! check_health ${service_url} ${tag}; then
        log ERROR "Green version health check failed"
        return 1
    fi
    
    log INFO "Green version healthy, switching traffic"
    
    # Switch all traffic to green
    gcloud run services update-traffic ${SERVICE_NAME} \
        --region ${REGION} \
        --to-tags ${tag}=100
    
    log SUCCESS "Blue-green deployment completed successfully"
}

# Function to rollback
rollback() {
    local failed_tag=$1
    
    log WARN "Rolling back deployment"
    
    # Remove traffic from failed version
    gcloud run services update-traffic ${SERVICE_NAME} \
        --region ${REGION} \
        --to-tags ${failed_tag}=0
    
    # Route all traffic back to stable version
    gcloud run services update-traffic ${SERVICE_NAME} \
        --region ${REGION} \
        --to-latest
    
    log SUCCESS "Rollback completed"
}

# Main deployment flow
main() {
    log INFO "🚀 Starting Analysis Engine deployment"
    log INFO "Project: ${PROJECT_ID}"
    log INFO "Region: ${REGION}"
    log INFO "Service: ${SERVICE_NAME}"
    log INFO "Deployment mode: ${DEPLOYMENT_MODE}"
    
    # Pre-deployment checks
    check_prerequisites
    validate_config
    
    # Generate version tag
    local version=$(git rev-parse --short HEAD 2>/dev/null || echo "latest")
    local timestamp=$(date +%Y%m%d%H%M%S)
    local tag="v${version}-${timestamp}"
    
    # Build and push image
    build_and_push ${version}
    
    # Security scan
    security_scan ${version}
    
    # Deploy based on mode
    case ${DEPLOYMENT_MODE} in
        canary)
            canary_deployment ${version} ${tag}
            ;;
        blue-green)
            blue_green_deployment ${version} ${tag}
            ;;
        direct)
            deploy_service ${version} ${tag} 100
            ;;
        *)
            log ERROR "Unknown deployment mode: ${DEPLOYMENT_MODE}"
            exit 1
            ;;
    esac
    
    # Final health check
    local service_url=$(gcloud run services describe ${SERVICE_NAME} \
        --platform managed \
        --region ${REGION} \
        --format 'value(status.url)')
    
    log INFO "Service URL: ${service_url}"
    
    if check_health ${service_url}; then
        log SUCCESS "✅ Deployment completed successfully!"
        
        # Run post-deployment tests
        log INFO "Running post-deployment validation..."
        curl -s ${service_url}/ready | jq .
        
        # Show service details
        gcloud run services describe ${SERVICE_NAME} \
            --platform managed \
            --region ${REGION} \
            --format="table(
                status.url,
                spec.template.spec.containers[0].image:label=IMAGE,
                status.traffic[].percent:label=TRAFFIC,
                status.traffic[].tag:label=TAG
            )"
    else
        log ERROR "Post-deployment health check failed"
        exit 1
    fi
}

# Run main function
main "$@"