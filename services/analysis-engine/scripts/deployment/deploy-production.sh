#!/bin/bash
# Production deployment script for analysis-engine on Cloud Run
# This script includes all required environment variables for full functionality
set -euo pipefail

echo "🚀 Deploying Analysis Engine to Cloud Run (Production)..."

# Configuration
PROJECT_ID="vibe-match-463114"
SERVICE_NAME="analysis-engine"
REGION="us-central1"
IMAGE_TAG="prod-$(date +%Y%m%d-%H%M%S)"

# Check if we're in the right directory
if [ ! -f "Cargo.toml" ]; then
    echo "❌ Error: Must run from services/analysis-engine directory"
    exit 1
fi

# Generate a secure JWT secret if not provided
if [ -z "${JWT_SECRET:-}" ]; then
    echo "🔐 Generating JWT secret..."
    JWT_SECRET=$(openssl rand -base64 32)
    echo "Generated JWT secret (save this securely): $JWT_SECRET"
fi

# Verify IAM permissions
echo "🔐 Verifying IAM permissions..."
SERVICE_ACCOUNT="analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com"

# Check if service account exists
if ! gcloud iam service-accounts describe ${SERVICE_ACCOUNT} --project=${PROJECT_ID} &>/dev/null; then
    echo "❌ Service account ${SERVICE_ACCOUNT} not found!"
    echo "   Create it with: gcloud iam service-accounts create analysis-engine --display-name='Analysis Engine Service Account'"
    exit 1
fi

# Check required roles
echo "Checking project-level IAM roles..."
REQUIRED_ROLES=(
    "roles/spanner.databaseUser"
    "roles/storage.objectAdmin"
    "roles/pubsub.publisher"
    "roles/pubsub.viewer"
)

CURRENT_ROLES=$(gcloud projects get-iam-policy ${PROJECT_ID} \
    --flatten="bindings[].members" \
    --filter="bindings.members:serviceAccount:${SERVICE_ACCOUNT}" \
    --format="value(bindings.role)" 2>/dev/null | sort)

for role in "${REQUIRED_ROLES[@]}"; do
    if ! echo "$CURRENT_ROLES" | grep -q "^${role}$"; then
        echo "⚠️  Warning: Missing required role ${role}"
    fi
done

# Check bucket permissions
BUCKET_NAME="ccl-analysis-artifacts"
echo "Checking bucket permissions..."
if ! gsutil iam get gs://${BUCKET_NAME} 2>/dev/null | grep -q "${SERVICE_ACCOUNT}"; then
    echo "⚠️  Warning: Service account may not have bucket permissions for health checks"
    echo "   Fix with: gsutil iam ch serviceAccount:${SERVICE_ACCOUNT}:legacyBucketReader gs://${BUCKET_NAME}"
fi

echo "✅ Permission check complete"

echo "📦 Building Docker image..."
docker build \
    --platform linux/amd64 \
    -t gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG} \
    -f Dockerfile.simple \
    .

echo "🔧 Configuring Docker for GCR..."
gcloud auth configure-docker --quiet

echo "📤 Pushing image to Container Registry..."
docker push gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG}

echo "🚀 Deploying to Cloud Run with full production configuration..."
gcloud run deploy ${SERVICE_NAME} \
    --image gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG} \
    --platform managed \
    --region ${REGION} \
    --memory 4Gi \
    --cpu 4 \
    --timeout 600 \
    --max-instances 100 \
    --min-instances 1 \
    --concurrency 50 \
    --set-env-vars "RUST_LOG=info" \
    --set-env-vars "RUST_BACKTRACE=1" \
    --set-env-vars "GCP_PROJECT_ID=${PROJECT_ID}" \
    --set-env-vars "SPANNER_PROJECT_ID=${PROJECT_ID}" \
    --set-env-vars "SPANNER_INSTANCE_ID=ccl-instance" \
    --set-env-vars "SPANNER_DATABASE_ID=ccl_main" \
    --set-env-vars "STORAGE_BUCKET=ccl-analysis-artifacts" \
    --set-env-vars "STORAGE_BUCKET_NAME=ccl-analysis-artifacts" \
    --set-env-vars "PUBSUB_TOPIC=analysis-results" \
    --set-env-vars "REDIS_URL=redis://10.0.0.100:6379" \
    --set-env-vars "JWT_SECRET=${JWT_SECRET}" \
    --set-env-vars "ENVIRONMENT=production" \
    --set-env-vars "MAX_CONCURRENT_ANALYSES=50" \
    --set-env-vars "MAX_FILE_SIZE_BYTES=********" \
    --set-env-vars "PARSE_TIMEOUT_SECONDS=30" \
    --set-env-vars "MAX_ANALYSIS_MEMORY_MB=2048" \
    --set-env-vars "MAX_DEPENDENCY_COUNT=10000" \
    --set-env-vars "ENABLE_AUTH=true" \
    --set-env-vars "CORS_ORIGINS=*" \
    --set-env-vars "OTEL_ENDPOINT=https://monitoring.googleapis.com/v3/projects/${PROJECT_ID}/traces" \
    --service-account analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com \
    --allow-unauthenticated \
    --project ${PROJECT_ID}

echo "🔍 Getting service URL..."
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
    --platform managed \
    --region ${REGION} \
    --project ${PROJECT_ID} \
    --format 'value(status.url)')

echo "Service URL: $SERVICE_URL"

echo "🏥 Running health checks..."
sleep 15  # Give Cloud Run time to fully start

echo "1. Basic health check..."
if curl -f "${SERVICE_URL}/health"; then
    echo "✅ Basic health check passed"
else
    echo "❌ Basic health check failed"
fi

echo -e "\n2. Readiness check..."
READY_RESPONSE=$(curl -s "${SERVICE_URL}/health/ready")
echo "Readiness response: $READY_RESPONSE"

echo -e "\n3. Version check..."
VERSION_RESPONSE=$(curl -s "${SERVICE_URL}/api/v1/version")
echo "Version response: $VERSION_RESPONSE"

echo -e "\n4. Languages check..."
LANGUAGES_RESPONSE=$(curl -s "${SERVICE_URL}/api/v1/languages" | head -c 200)
echo "Languages response (truncated): $LANGUAGES_RESPONSE..."

echo -e "\n📊 Deployment Summary:"
echo "========================"
echo "Service URL: ${SERVICE_URL}"
echo "Image: gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG}"
echo "Region: ${REGION}"
echo "Memory: 4Gi"
echo "CPU: 4"
echo "Max Instances: 100"
echo "Min Instances: 1"
echo ""
echo "⚠️  Important Notes:"
echo "1. JWT_SECRET has been set - save it securely for future deployments"
echo "2. Redis is not yet deployed - service running without cache"
echo "3. Verify Spanner database 'ccl_main' exists in 'ccl-instance'"
echo "4. Ensure storage bucket 'ccl-analysis-artifacts' exists"
echo ""
echo "🎉 Deployment complete!"