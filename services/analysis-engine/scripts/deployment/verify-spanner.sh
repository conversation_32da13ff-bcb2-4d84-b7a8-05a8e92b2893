#!/bin/bash
# Verify Spanner instance and database for Analysis Engine
set -euo pipefail

echo "🔍 Verifying Spanner configuration for Analysis Engine..."

# Configuration
PROJECT_ID="vibe-match-463114"
INSTANCE_ID="ccl-instance"
DATABASE_ID="ccl_main"
REGION="regional-us-central1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Checking prerequisites..."
if ! command_exists gcloud; then
    echo -e "${RED}❌ gcloud CLI not found. Please install Google Cloud SDK.${NC}"
    exit 1
fi

# Check authentication
echo "🔐 Checking authentication..."
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ No active gcloud authentication found. Please run: gcloud auth login${NC}"
    exit 1
fi

# Set project
echo "🏗️  Setting project to ${PROJECT_ID}..."
gcloud config set project ${PROJECT_ID} 2>/dev/null

# Check if Spanner API is enabled
echo "🔌 Checking if Spanner API is enabled..."
if ! gcloud services list --enabled --filter="name:spanner.googleapis.com" --format="value(name)" | grep -q spanner; then
    echo -e "${YELLOW}⚠️  Spanner API is not enabled. Enabling it now...${NC}"
    gcloud services enable spanner.googleapis.com
    echo "✅ Spanner API enabled"
    sleep 10  # Wait for API to be fully enabled
else
    echo "✅ Spanner API is enabled"
fi

# Check if instance exists
echo -e "\n🏢 Checking Spanner instance '${INSTANCE_ID}'..."
if gcloud spanner instances describe ${INSTANCE_ID} --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo "✅ Instance '${INSTANCE_ID}' exists"

    # Get instance details
    echo -e "\n📊 Instance details:"
    gcloud spanner instances describe ${INSTANCE_ID} \
        --project=${PROJECT_ID} \
        --format="table(name,config,nodeCount,state)"
else
    echo -e "${RED}❌ Instance '${INSTANCE_ID}' not found${NC}"
    echo -e "\n${YELLOW}Would you like to create it? (y/n)${NC}"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "Creating Spanner instance..."
        gcloud spanner instances create ${INSTANCE_ID} \
            --config=${REGION} \
            --description="Analysis Engine Spanner Instance" \
            --nodes=1 \
            --project=${PROJECT_ID}
        echo "✅ Instance created successfully"
    else
        echo "Skipping instance creation"
        exit 1
    fi
fi

# Check if database exists
echo -e "\n💾 Checking database '${DATABASE_ID}'..."
if gcloud spanner databases describe ${DATABASE_ID} \
    --instance=${INSTANCE_ID} \
    --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo "✅ Database '${DATABASE_ID}' exists"

    # Get database details
    echo -e "\n📊 Database details:"
    gcloud spanner databases describe ${DATABASE_ID} \
        --instance=${INSTANCE_ID} \
        --project=${PROJECT_ID} \
        --format="table(name,state)"
else
    echo -e "${RED}❌ Database '${DATABASE_ID}' not found${NC}"
    echo -e "\n${YELLOW}Would you like to create it? (y/n)${NC}"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "Creating database..."
        gcloud spanner databases create ${DATABASE_ID} \
            --instance=${INSTANCE_ID} \
            --project=${PROJECT_ID}
        echo "✅ Database created successfully"
    else
        echo "Skipping database creation"
        exit 1
    fi
fi

# List tables in the database
echo -e "\n📋 Checking tables in database..."
TABLES=$(gcloud spanner databases execute-sql ${DATABASE_ID} \
    --instance=${INSTANCE_ID} \
    --project=${PROJECT_ID} \
    --sql="SELECT table_name FROM information_schema.tables WHERE table_schema = ''" \
    --format="value(table_name)" 2>/dev/null || echo "")

if [ -z "$TABLES" ]; then
    echo -e "${YELLOW}⚠️  No tables found in database${NC}"
    echo "The Analysis Engine will create tables on first run"
else
    echo "✅ Found tables:"
    echo "$TABLES" | while read -r table; do
        echo "   - $table"
    done
fi

# Test connection with service account
echo -e "\n🔑 Checking service account permissions..."
SERVICE_ACCOUNT="analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com"

# Check if service account exists
if gcloud iam service-accounts describe ${SERVICE_ACCOUNT} --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo "✅ Service account exists: ${SERVICE_ACCOUNT}"

    # Check Spanner permissions
    echo "Checking Spanner permissions..."
    ROLES=$(gcloud projects get-iam-policy ${PROJECT_ID} \
        --flatten="bindings[].members" \
        --format="table(bindings.role)" \
        --filter="bindings.members:${SERVICE_ACCOUNT}" 2>/dev/null)

    if echo "$ROLES" | grep -q "spanner.databaseUser\|spanner.databaseAdmin\|spanner.admin"; then
        echo "✅ Service account has Spanner permissions"
    else
        echo -e "${YELLOW}⚠️  Service account may need Spanner permissions${NC}"
        echo "Run: gcloud projects add-iam-policy-binding ${PROJECT_ID} \\"
        echo "    --member=serviceAccount:${SERVICE_ACCOUNT} \\"
        echo "    --role=roles/spanner.databaseUser"
    fi
else
    echo -e "${RED}❌ Service account not found: ${SERVICE_ACCOUNT}${NC}"
fi

# Generate connection string
echo -e "\n🔗 Connection Details for Analysis Engine:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "SPANNER_PROJECT_ID=${PROJECT_ID}"
echo "SPANNER_INSTANCE_ID=${INSTANCE_ID}"
echo "SPANNER_DATABASE_ID=${DATABASE_ID}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Summary
echo -e "\n📊 Verification Summary:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━"
if gcloud spanner instances describe ${INSTANCE_ID} --project=${PROJECT_ID} >/dev/null 2>&1 && \
   gcloud spanner databases describe ${DATABASE_ID} --instance=${INSTANCE_ID} --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Spanner is ready for Analysis Engine${NC}"
    echo -e "\nNext steps:"
    echo "1. Deploy Analysis Engine with the connection details above"
    echo "2. The service will create tables automatically on first analysis"
    echo "3. Monitor Spanner metrics in Cloud Console"
else
    echo -e "${RED}❌ Spanner setup incomplete${NC}"
    echo -e "\nPlease resolve the issues above before deploying"
fi
