#!/bin/bash
set -euo pipefail

echo "=== Safe Deployment Process ==="
echo

# Step 1: Pre-deployment checks
echo "Step 1: Running pre-deployment checks..."
if ! ./pre-deploy-check.sh; then
    echo "❌ Pre-deployment checks failed. Aborting."
    exit 1
fi

# Step 2: Test local Docker build
echo -e "\nStep 2: Testing Docker build locally..."
echo "Building with simple Dockerfile..."
if docker build --platform linux/amd64 -t analysis-engine-local-test -f Dockerfile.simple . > /tmp/docker-build.log 2>&1; then
    echo "✅ Local Docker build successful"
else
    echo "❌ Docker build failed. Check /tmp/docker-build.log"
    tail -20 /tmp/docker-build.log
    exit 1
fi

# Step 3: Confirm deployment
echo -e "\nStep 3: Ready to deploy to Cloud Run"
echo "This will deploy the following changes:"
echo "- JWT authentication middleware is commented out (known issue)"
echo "- All compilation errors have been fixed"
echo "- 10 warnings remain (non-blocking)"
echo -e "\nDo you want to proceed with deployment? (yes/no)"
read -r response

if [[ "$response" != "yes" ]]; then
    echo "Deployment cancelled."
    exit 0
fi

# Step 4: Deploy using the simple deployment script
echo -e "\nStep 4: Deploying to Cloud Run..."
./deploy-simple.sh

echo -e "\n✅ Safe deployment process complete!"