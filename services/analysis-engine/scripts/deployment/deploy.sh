#!/bin/bash
# Production deployment script for analysis-engine
# 
# Usage:
#   ./scripts/deploy.sh staging
#   ./scripts/deploy.sh production --tag v1.0.0

set -euo pipefail

# Configuration
PROJECT_ID="${GCP_PROJECT_ID:-episteme}"
SERVICE_NAME="analysis-engine"
REGION="${GCP_REGION:-us-central1}"
REGISTRY="gcr.io"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse arguments
ENVIRONMENT="${1:-staging}"
TAG="${2:-latest}"

if [[ "$#" -gt 2 && "$2" == "--tag" ]]; then
    TAG="$3"
fi

# Validate environment
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
    log_error "Invalid environment: $ENVIRONMENT. Must be 'staging' or 'production'"
    exit 1
fi

log_info "Deploying $SERVICE_NAME to $ENVIRONMENT with tag $TAG"

# Set environment-specific variables
if [[ "$ENVIRONMENT" == "production" ]]; then
    INSTANCE_COUNT="3"
    MAX_INSTANCES="1000"
    MIN_INSTANCES="1"
    MEMORY="4Gi"
    CPU="4"
    CONCURRENCY="50"
    SPANNER_INSTANCE="ccl-production"
    SPANNER_DATABASE="ccl-main"
else
    INSTANCE_COUNT="1"
    MAX_INSTANCES="10"
    MIN_INSTANCES="0"
    MEMORY="2Gi"
    CPU="2"
    CONCURRENCY="10"
    SPANNER_INSTANCE="ccl-staging"
    SPANNER_DATABASE="ccl-staging"
fi

# Build the Docker image
log_info "Building Docker image..."
docker build \
    --platform linux/amd64 \
    --build-arg RUST_ENV=$ENVIRONMENT \
    -t "$REGISTRY/$PROJECT_ID/$SERVICE_NAME:$TAG" \
    -t "$REGISTRY/$PROJECT_ID/$SERVICE_NAME:$ENVIRONMENT-latest" \
    -f Dockerfile \
    .

# Push to registry
log_info "Pushing image to registry..."
docker push "$REGISTRY/$PROJECT_ID/$SERVICE_NAME:$TAG"
docker push "$REGISTRY/$PROJECT_ID/$SERVICE_NAME:$ENVIRONMENT-latest"

# Deploy to Cloud Run
log_info "Deploying to Cloud Run..."
gcloud run deploy "$SERVICE_NAME-$ENVIRONMENT" \
    --image "$REGISTRY/$PROJECT_ID/$SERVICE_NAME:$TAG" \
    --platform managed \
    --region "$REGION" \
    --memory "$MEMORY" \
    --cpu "$CPU" \
    --min-instances "$MIN_INSTANCES" \
    --max-instances "$MAX_INSTANCES" \
    --concurrency "$CONCURRENCY" \
    --timeout 600 \
    --set-env-vars "ENVIRONMENT=$ENVIRONMENT" \
    --set-env-vars "SPANNER_PROJECT_ID=$PROJECT_ID" \
    --set-env-vars "SPANNER_INSTANCE=$SPANNER_INSTANCE" \
    --set-env-vars "SPANNER_DATABASE=$SPANNER_DATABASE" \
    --set-env-vars "STORAGE_BUCKET=ccl-analysis-artifacts-$ENVIRONMENT" \
    --set-env-vars "PUBSUB_TOPIC=analysis-events-$ENVIRONMENT" \
    --set-env-vars "REDIS_URL=$REDIS_URL" \
    --set-env-vars "MAX_CONCURRENT_ANALYSES=50" \
    --set-env-vars "MAX_FILE_SIZE_BYTES=********" \
    --set-env-vars "PARSE_TIMEOUT_SECONDS=30" \
    --set-env-vars "MAX_ANALYSIS_MEMORY_MB=2048" \
    --set-env-vars "MAX_DEPENDENCY_COUNT=10000" \
    --set-env-vars "ENABLE_METRICS=true" \
    --set-env-vars "ENABLE_TRACING=true" \
    --set-env-vars "LOG_LEVEL=info" \
    --service-account "$SERVICE_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
    --allow-unauthenticated

# Get the service URL
SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME-$ENVIRONMENT" \
    --platform managed \
    --region "$REGION" \
    --format 'value(status.url)')

log_info "Service deployed to: $SERVICE_URL"

# Run health check
log_info "Running health check..."
HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/health")

if [[ "$HEALTH_STATUS" == "200" ]]; then
    log_info "Health check passed!"
else
    log_error "Health check failed with status: $HEALTH_STATUS"
    exit 1
fi

# Run smoke test if not production
if [[ "$ENVIRONMENT" != "production" ]]; then
    log_info "Running smoke test..."
    ./scripts/smoke_test.sh "$SERVICE_URL"
fi

# Update traffic allocation for production (canary deployment)
if [[ "$ENVIRONMENT" == "production" ]]; then
    log_warn "Production deployment complete. Manual traffic migration required."
    log_info "To migrate traffic, run:"
    echo "  gcloud run services update-traffic $SERVICE_NAME-$ENVIRONMENT \\"
    echo "    --region $REGION \\"
    echo "    --to-tags $TAG=10"
    echo ""
    echo "After validation, migrate full traffic:"
    echo "  gcloud run services update-traffic $SERVICE_NAME-$ENVIRONMENT \\"
    echo "    --region $REGION \\"
    echo "    --to-latest"
fi

log_info "Deployment complete!"