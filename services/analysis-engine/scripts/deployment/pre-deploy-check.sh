#!/bin/bash

echo "=== Pre-Deployment Safety Check ==="
echo

# Check 1: Library compilation
echo "1. Checking library compilation..."
if cargo check --lib 2>&1 | grep -q "error:"; then
    echo "❌ Library compilation has errors"
    cargo check --lib 2>&1 | grep "error:" | head -5
    exit 1
else
    echo "✅ Library compiles successfully"
fi

# Check 2: Binary compilation
echo -e "\n2. Checking binary compilation..."
if cargo check --bin analysis-engine 2>&1 | grep -q "error:"; then
    echo "❌ Binary compilation has errors"
    cargo check --bin analysis-engine 2>&1 | grep "error:" | head -5
    exit 1
else
    echo "✅ Binary compiles successfully"
fi

# Check 3: Count warnings
echo -e "\n3. Checking warnings..."
WARNINGS=$(cargo check --lib 2>&1 | grep -c "warning:")
echo "⚠️  Found $WARNINGS warnings"

# Check 4: Release build
echo -e "\n4. Testing release build..."
if cargo build --release --lib 2>&1 | grep -q "error:"; then
    echo "❌ Release build has errors"
    exit 1
else
    echo "✅ Release build successful"
fi

# Check 5: Environment
echo -e "\n5. Checking environment variables..."
if [ ! -f .env ]; then
    echo "⚠️  No .env file found (using defaults)"
else
    echo "✅ .env file exists"
fi

# Summary
echo -e "\n=== Summary ==="
echo "Library: ✅ Compiles"
echo "Binary: Will be checked during deployment"
echo "Warnings: $WARNINGS (non-blocking)"
echo "Release: ✅ Builds successfully"
echo -e "\n🚀 Ready for deployment with minor warnings"