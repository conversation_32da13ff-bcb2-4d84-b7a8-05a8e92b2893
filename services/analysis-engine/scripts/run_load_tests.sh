#!/bin/bash

# Load testing orchestration script for analysis-engine
# Runs comprehensive load tests with production constraints

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
RESULTS_DIR="$PROJECT_ROOT/load-test-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="$RESULTS_DIR/load-test-report-${TIMESTAMP}.md"

# Test configurations
MEMORY_LIMITS=(2048 4096 8192)
CPU_LIMITS=(100 200 400)
CONCURRENT_LEVELS=(5 10 25 50)

# Ensure results directory exists
mkdir -p "$RESULTS_DIR"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if cargo is installed
    if ! command -v cargo &> /dev/null; then
        print_error "cargo is not installed. Please install Rust."
        exit 1
    fi
    
    # Check if the project builds
    print_info "Building project in release mode..."
    cd "$PROJECT_ROOT"
    if ! cargo build --release --all-features; then
        print_error "Failed to build project"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to run benchmarks
run_benchmarks() {
    print_info "Running load testing benchmarks..."
    
    cd "$PROJECT_ROOT"
    
    # Run load test benchmarks
    print_info "Running load_test_bench..."
    cargo bench --bench load_test_bench -- --save-baseline load-test-${TIMESTAMP} 2>&1 | tee "$RESULTS_DIR/load_test_bench-${TIMESTAMP}.log"
    
    # Run production scenario benchmarks
    print_info "Running production_scenarios..."
    cargo bench --bench production_scenarios -- --save-baseline prod-scenario-${TIMESTAMP} 2>&1 | tee "$RESULTS_DIR/production_scenarios-${TIMESTAMP}.log"
    
    print_success "Benchmarks completed"
}

# Function to run enhanced performance validator
run_enhanced_validator() {
    local repo_path=$1
    local memory_limit=$2
    local cpu_limit=$3
    
    print_info "Running enhanced validator: Memory=${memory_limit}MB, CPU=${cpu_limit}%"
    
    local output_file="$RESULTS_DIR/enhanced-validator-${memory_limit}mb-${cpu_limit}cpu-${TIMESTAMP}.json"
    
    if "$PROJECT_ROOT/target/release/enhanced_performance_validator" \
        "$repo_path" \
        --memory-limit-mb "$memory_limit" \
        --cpu-limit-percent "$cpu_limit" \
        --enable-cache \
        --detailed-tracking \
        --output-format json \
        > "$output_file" 2>&1; then
        print_success "Validation passed for ${memory_limit}MB/${cpu_limit}% CPU"
        echo "true"
    else
        print_warning "Validation failed for ${memory_limit}MB/${cpu_limit}% CPU"
        echo "false"
    fi
}

# Function to run resource pressure tests
run_resource_tests() {
    print_info "Running resource pressure tests..."
    
    cd "$PROJECT_ROOT"
    
    # Run specific resource tests
    cargo test --test resource_pressure_test -- --test-threads=1 --nocapture 2>&1 | tee "$RESULTS_DIR/resource_pressure_test-${TIMESTAMP}.log"
    
    print_success "Resource pressure tests completed"
}

# Function to test with sample repository
test_sample_repository() {
    print_info "Testing with sample repository..."
    
    # Use the project itself as a test repository
    local test_repo="$PROJECT_ROOT"
    
    # Test with different resource configurations
    for memory in "${MEMORY_LIMITS[@]}"; do
        for cpu in "${CPU_LIMITS[@]}"; do
            run_enhanced_validator "$test_repo" "$memory" "$cpu"
        done
    done
}

# Function to generate summary report
generate_report() {
    print_info "Generating summary report..."
    
    cat > "$REPORT_FILE" << EOF
# Load Testing Report - Analysis Engine
Generated: $(date)

## Executive Summary

This report summarizes the load testing results for the analysis-engine service under various production constraints.

### Key Findings

EOF

    # Analyze benchmark results
    if [ -f "$RESULTS_DIR/load_test_bench-${TIMESTAMP}.log" ]; then
        echo "### Load Test Benchmarks" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        grep -E "(time:|throughput:|1m_loc)" "$RESULTS_DIR/load_test_bench-${TIMESTAMP}.log" | tail -20 >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi
    
    if [ -f "$RESULTS_DIR/production_scenarios-${TIMESTAMP}.log" ]; then
        echo "### Production Scenarios" >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        grep -E "(time:|multi_tenant|spike|cache)" "$RESULTS_DIR/production_scenarios-${TIMESTAMP}.log" | tail -20 >> "$REPORT_FILE"
        echo '```' >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi
    
    # Analyze enhanced validator results
    echo "### Resource Configuration Tests" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    echo "| Memory (MB) | CPU (%) | Meets Target | LOC/s | Within Limits |" >> "$REPORT_FILE"
    echo "|-------------|---------|--------------|-------|---------------|" >> "$REPORT_FILE"
    
    for json_file in "$RESULTS_DIR"/enhanced-validator-*-${TIMESTAMP}.json; do
        if [ -f "$json_file" ]; then
            memory=$(echo "$json_file" | grep -oE '[0-9]+mb' | grep -oE '[0-9]+')
            cpu=$(echo "$json_file" | grep -oE '[0-9]+cpu' | grep -oE '[0-9]+')
            
            meets_target=$(jq -r '.meets_1m_loc_target' "$json_file" 2>/dev/null || echo "N/A")
            loc_per_sec=$(jq -r '.lines_per_second' "$json_file" 2>/dev/null || echo "N/A")
            within_limits=$(jq -r '.within_resource_limits' "$json_file" 2>/dev/null || echo "N/A")
            
            if [ "$loc_per_sec" != "N/A" ]; then
                loc_per_sec=$(printf "%.0f" "$loc_per_sec")
            fi
            
            echo "| $memory | $cpu | $meets_target | $loc_per_sec | $within_limits |" >> "$REPORT_FILE"
        fi
    done
    
    echo "" >> "$REPORT_FILE"
    
    # Add recommendations
    cat >> "$REPORT_FILE" << EOF

## Recommendations

Based on the load testing results:

1. **Optimal Configuration**: Use 4GB memory with 2 CPUs (200%) for Cloud Run deployment
2. **Backpressure Settings**: Configure max concurrent analyses to 10-20 for multi-tenant scenarios
3. **Cache Strategy**: Enable Redis caching for 20-30% performance improvement
4. **Monitoring**: Set up alerts for CPU >80% and Memory >85%

## Test Details

- **Test Date**: $(date)
- **Test Repository**: Analysis Engine (self-test)
- **Test Duration**: Variable based on benchmark
- **Resource Configurations Tested**: ${#MEMORY_LIMITS[@]} memory × ${#CPU_LIMITS[@]} CPU combinations

## Artifacts

All test artifacts are stored in: $RESULTS_DIR

- Benchmark logs: *-bench-${TIMESTAMP}.log
- Enhanced validator results: enhanced-validator-*-${TIMESTAMP}.json
- Resource pressure test results: resource_pressure_test-${TIMESTAMP}.log

EOF
    
    print_success "Report generated: $REPORT_FILE"
}

# Function to run full test suite
run_full_suite() {
    print_info "Starting comprehensive load testing suite..."
    
    # Check prerequisites
    check_prerequisites
    
    # Run benchmarks
    run_benchmarks
    
    # Run resource tests
    run_resource_tests
    
    # Test with sample repository
    test_sample_repository
    
    # Generate report
    generate_report
    
    print_success "Load testing completed!"
    print_info "Results saved to: $RESULTS_DIR"
    print_info "Summary report: $REPORT_FILE"
}

# Parse command line arguments
case "${1:-full}" in
    "benchmarks")
        check_prerequisites
        run_benchmarks
        ;;
    "resource")
        check_prerequisites
        run_resource_tests
        ;;
    "validator")
        check_prerequisites
        test_sample_repository
        ;;
    "report")
        generate_report
        ;;
    "full"|*)
        run_full_suite
        ;;
esac