#!/bin/bash

# Service Integration Testing Script for Analysis Engine
# Tests the live service with large repositories and validates production readiness

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_DATA_DIR="$SCRIPT_DIR/../../test-data"
RESULTS_DIR="$TEST_DATA_DIR/integration_results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
INTEGRATION_LOG="$RESULTS_DIR/integration_log_${TIMESTAMP}.log"

# Service configuration
SERVICE_URL="http://localhost:8001"
SERVICE_STARTUP_TIMEOUT=30
SERVICE_HEALTH_CHECK_INTERVAL=2
API_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=10

# Test configuration
RUN_LOAD_TESTS=true
RUN_STRESS_TESTS=true
RUN_REPOSITORY_TESTS=true
TEST_REPOSITORIES_DIR="$TEST_DATA_DIR/repositories"

# Test counters
TOTAL_INTEGRATION_TESTS=0
SUCCESSFUL_INTEGRATION_TESTS=0
FAILED_INTEGRATION_TESTS=0

# Service process ID
SERVICE_PID=0

# Initialize integration testing
initialize_integration_testing() {
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}        Analysis Engine - Service Integration Testing               ${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    echo -e "Timestamp: $(date)"
    echo -e "Service URL: $SERVICE_URL"
    echo -e "Test Repositories: $TEST_REPOSITORIES_DIR"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}\n"
    
    # Create directories
    mkdir -p "$RESULTS_DIR"
    
    # Initialize integration log
    echo "Service Integration Testing Log - $(date)" > "$INTEGRATION_LOG"
    echo "=======================================" >> "$INTEGRATION_LOG"
    
    # Check prerequisites
    check_integration_prerequisites
}

# Check integration prerequisites
check_integration_prerequisites() {
    echo -e "${BLUE}📋 Checking integration prerequisites...${NC}"
    
    # Check required tools
    local required_tools=("curl" "jq" "cargo" "timeout")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            echo -e "${RED}❌ $tool not found. Please install $tool${NC}"
            exit 1
        else
            echo -e "${GREEN}✅ $tool found${NC}"
        fi
    done
    
    # Check if analysis engine is built
    if [ ! -f "$SCRIPT_DIR/../../target/release/analysis-engine" ]; then
        echo -e "${YELLOW}⚠️  Analysis engine not built, building...${NC}"
        cd "$SCRIPT_DIR/../../"
        if cargo build --release; then
            echo -e "${GREEN}✅ Analysis engine built successfully${NC}"
        else
            echo -e "${RED}❌ Failed to build analysis engine${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ Analysis engine binary found${NC}"
    fi
    
    # Check if test repositories exist
    if [ ! -d "$TEST_REPOSITORIES_DIR" ]; then
        echo -e "${YELLOW}⚠️  Test repositories directory not found${NC}"
        echo -e "${YELLOW}   Run collect_test_repositories.sh first${NC}"
        RUN_REPOSITORY_TESTS=false
    else
        local repo_count=$(ls "$TEST_REPOSITORIES_DIR" | wc -l)
        echo -e "${GREEN}✅ Found $repo_count test repositories${NC}"
    fi
    
    # Set environment variables
    export GCP_PROJECT_ID="${GCP_PROJECT_ID:-test-project}"
    export SPANNER_INSTANCE="${SPANNER_INSTANCE:-test-instance}"
    export SPANNER_DATABASE="${SPANNER_DATABASE:-test-database}"
    export REDIS_URL="${REDIS_URL:-redis://localhost:6379}"
    export ENVIRONMENT="${ENVIRONMENT:-development}"
    
    echo -e "${GREEN}✅ Integration prerequisites satisfied${NC}\n"
}

# Start analysis engine service
start_service() {
    echo -e "${BLUE}🚀 Starting analysis engine service...${NC}"
    
    # Check if service is already running
    if curl -s "$SERVICE_URL/health" > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Service appears to be already running${NC}"
        return 0
    fi
    
    # Start service in background
    cd "$SCRIPT_DIR/../../"
    
    echo "Starting service with environment:"
    echo "  GCP_PROJECT_ID: $GCP_PROJECT_ID"
    echo "  ENVIRONMENT: $ENVIRONMENT"
    
    # Start service and capture PID
    cargo run --release > "$RESULTS_DIR/service_output_${TIMESTAMP}.log" 2>&1 &
    SERVICE_PID=$!
    
    echo "Service started with PID: $SERVICE_PID"
    echo "Service PID: $SERVICE_PID" >> "$INTEGRATION_LOG"
    
    # Wait for service to be ready
    echo "Waiting for service to be ready..."
    local ready=false
    local attempts=0
    local max_attempts=$((SERVICE_STARTUP_TIMEOUT / SERVICE_HEALTH_CHECK_INTERVAL))
    
    while [ $attempts -lt $max_attempts ]; do
        if curl -s "$SERVICE_URL/health" > /dev/null 2>&1; then
            ready=true
            break
        fi
        
        # Check if service process is still running
        if ! kill -0 $SERVICE_PID 2>/dev/null; then
            echo -e "${RED}❌ Service process died during startup${NC}"
            echo "Service startup failed - process died" >> "$INTEGRATION_LOG"
            return 1
        fi
        
        sleep $SERVICE_HEALTH_CHECK_INTERVAL
        ((attempts++))
        
        if [ $((attempts % 5)) -eq 0 ]; then
            echo "  Still waiting... (${attempts}/${max_attempts})"
        fi
    done
    
    if [ "$ready" = false ]; then
        echo -e "${RED}❌ Service failed to start within ${SERVICE_STARTUP_TIMEOUT}s${NC}"
        echo "Service startup timeout" >> "$INTEGRATION_LOG"
        stop_service
        return 1
    fi
    
    echo -e "${GREEN}✅ Service started successfully${NC}"
    echo "Service started successfully" >> "$INTEGRATION_LOG"
    
    # Validate service health
    validate_service_health
    
    return 0
}

# Stop analysis engine service
stop_service() {
    echo -e "${BLUE}🛑 Stopping analysis engine service...${NC}"
    
    if [ $SERVICE_PID -ne 0 ]; then
        echo "Stopping service with PID: $SERVICE_PID"
        
        # Send SIGTERM first
        kill $SERVICE_PID 2>/dev/null || true
        
        # Wait for graceful shutdown
        local wait_time=0
        while [ $wait_time -lt 10 ]; do
            if ! kill -0 $SERVICE_PID 2>/dev/null; then
                echo -e "${GREEN}✅ Service stopped gracefully${NC}"
                echo "Service stopped gracefully" >> "$INTEGRATION_LOG"
                SERVICE_PID=0
                return 0
            fi
            sleep 1
            ((wait_time++))
        done
        
        # Force kill if still running
        echo "Force killing service..."
        kill -9 $SERVICE_PID 2>/dev/null || true
        wait $SERVICE_PID 2>/dev/null || true
        
        echo -e "${YELLOW}⚠️  Service force-stopped${NC}"
        echo "Service force-stopped" >> "$INTEGRATION_LOG"
        SERVICE_PID=0
    fi
}

# Validate service health
validate_service_health() {
    echo -e "${BLUE}🏥 Validating service health...${NC}"
    
    # Test basic health endpoint
    local health_response=$(curl -s "$SERVICE_URL/health" 2>/dev/null || echo "")
    if echo "$health_response" | grep -q "healthy"; then
        echo -e "${GREEN}✅ Basic health check passed${NC}"
    else
        echo -e "${RED}❌ Basic health check failed${NC}"
        echo "Health response: $health_response"
        return 1
    fi
    
    # Test detailed health endpoint
    local detailed_health=$(curl -s "$SERVICE_URL/health/detailed" 2>/dev/null || echo "")
    if echo "$detailed_health" | jq -e '.status' > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Detailed health check passed${NC}"
        
        # Extract health metrics
        local status=$(echo "$detailed_health" | jq -r '.status')
        local uptime=$(echo "$detailed_health" | jq -r '.uptime_seconds // 0')
        local memory_mb=$(echo "$detailed_health" | jq -r '.memory_usage_mb // 0')
        
        echo "  Status: $status"
        echo "  Uptime: ${uptime}s"
        echo "  Memory: ${memory_mb}MB"
    else
        echo -e "${RED}❌ Detailed health check failed${NC}"
        echo "Detailed health response: $detailed_health"
        return 1
    fi
    
    return 0
}

# Test service endpoints
test_service_endpoints() {
    echo -e "${BLUE}🔍 Testing service endpoints...${NC}"
    
    ((TOTAL_INTEGRATION_TESTS++))
    
    local endpoint_tests_passed=0
    local endpoint_tests_failed=0
    
    # Test 1: Languages endpoint
    echo -e "${CYAN}Testing languages endpoint...${NC}"
    local languages_response=$(curl -s "$SERVICE_URL/api/v1/languages" 2>/dev/null || echo "")
    
    if echo "$languages_response" | jq -e '.languages' > /dev/null 2>&1; then
        local language_count=$(echo "$languages_response" | jq '.languages | length')
        if [ "$language_count" -ge 21 ]; then
            echo -e "${GREEN}✅ Languages endpoint passed ($language_count languages)${NC}"
            ((endpoint_tests_passed++))
        else
            echo -e "${RED}❌ Languages endpoint failed ($language_count languages, expected ≥21)${NC}"
            ((endpoint_tests_failed++))
        fi
    else
        echo -e "${RED}❌ Languages endpoint failed (invalid response)${NC}"
        ((endpoint_tests_failed++))
    fi
    
    # Test 2: File analysis endpoint
    echo -e "${CYAN}Testing file analysis endpoint...${NC}"
    local test_content='fn main() { println!("Hello, world!"); }'
    local analysis_payload=$(jq -n --arg content "$test_content" '{
        content: $content,
        language: "rust",
        file_path: "test.rs"
    }')
    
    local analysis_response=$(curl -s -X POST "$SERVICE_URL/api/v1/analyze" \
        -H "Content-Type: application/json" \
        -d "$analysis_payload" 2>/dev/null || echo "")
    
    if echo "$analysis_response" | jq -e '.ast' > /dev/null 2>&1; then
        echo -e "${GREEN}✅ File analysis endpoint passed${NC}"
        ((endpoint_tests_passed++))
        
        # Validate analysis content
        local ast_node_type=$(echo "$analysis_response" | jq -r '.ast.node_type')
        local metrics_lines=$(echo "$analysis_response" | jq -r '.metrics.lines_of_code')
        
        echo "  AST node type: $ast_node_type"
        echo "  Lines of code: $metrics_lines"
    else
        echo -e "${RED}❌ File analysis endpoint failed${NC}"
        echo "Analysis response: $analysis_response"
        ((endpoint_tests_failed++))
    fi
    
    # Test 3: Metrics endpoint
    echo -e "${CYAN}Testing metrics endpoint...${NC}"
    local metrics_response=$(curl -s "$SERVICE_URL/metrics" 2>/dev/null || echo "")
    
    if echo "$metrics_response" | grep -q "analysis_engine"; then
        echo -e "${GREEN}✅ Metrics endpoint passed${NC}"
        ((endpoint_tests_passed++))
        
        # Count metrics
        local metrics_count=$(echo "$metrics_response" | grep -c "analysis_engine" || echo "0")
        echo "  Metrics found: $metrics_count"
    else
        echo -e "${RED}❌ Metrics endpoint failed${NC}"
        ((endpoint_tests_failed++))
    fi
    
    # Test 4: Authentication endpoint (if JWT is enabled)
    echo -e "${CYAN}Testing authentication endpoint...${NC}"
    local auth_response=$(curl -s "$SERVICE_URL/health/auth" 2>/dev/null || echo "")
    
    if echo "$auth_response" | jq -e '.auth_enabled' > /dev/null 2>&1; then
        local auth_enabled=$(echo "$auth_response" | jq -r '.auth_enabled')
        echo -e "${GREEN}✅ Authentication endpoint passed (auth_enabled: $auth_enabled)${NC}"
        ((endpoint_tests_passed++))
    else
        echo -e "${YELLOW}⚠️  Authentication endpoint not available or failed${NC}"
        # Not counting as failure since auth might be disabled in development
    fi
    
    # Summary
    echo -e "${CYAN}Endpoint testing summary:${NC}"
    echo -e "${GREEN}  Passed: $endpoint_tests_passed${NC}"
    echo -e "${RED}  Failed: $endpoint_tests_failed${NC}"
    
    if [ "$endpoint_tests_failed" -eq 0 ]; then
        echo -e "${GREEN}✅ All endpoint tests passed${NC}"
        ((SUCCESSFUL_INTEGRATION_TESTS++))
        return 0
    else
        echo -e "${RED}❌ Some endpoint tests failed${NC}"
        ((FAILED_INTEGRATION_TESTS++))
        return 1
    fi
}

# Test service with repository data
test_repository_integration() {
    echo -e "${BLUE}📁 Testing repository integration...${NC}"
    
    if [ "$RUN_REPOSITORY_TESTS" = false ]; then
        echo -e "${YELLOW}⚠️  Repository tests disabled${NC}"
        return 0
    fi
    
    if [ ! -d "$TEST_REPOSITORIES_DIR" ]; then
        echo -e "${YELLOW}⚠️  Test repositories directory not found${NC}"
        return 0
    fi
    
    ((TOTAL_INTEGRATION_TESTS++))
    
    local repositories=($(ls "$TEST_REPOSITORIES_DIR" | head -5))  # Test first 5 repositories
    
    if [ ${#repositories[@]} -eq 0 ]; then
        echo -e "${YELLOW}⚠️  No test repositories found${NC}"
        return 0
    fi
    
    echo -e "${CYAN}Testing ${#repositories[@]} repositories...${NC}"
    
    local repo_tests_passed=0
    local repo_tests_failed=0
    
    for repo_dir in "${repositories[@]}"; do
        local repo_path="$TEST_REPOSITORIES_DIR/$repo_dir"
        
        if [ ! -d "$repo_path" ]; then
            continue
        fi
        
        echo -e "${CYAN}Testing repository: $repo_dir${NC}"
        
        # Find a sample file to test
        local sample_file=$(find "$repo_path" -name "*.rs" -o -name "*.py" -o -name "*.js" -o -name "*.java" -o -name "*.go" | head -1)
        
        if [ -z "$sample_file" ]; then
            echo -e "${YELLOW}⚠️  No suitable test file found in $repo_dir${NC}"
            continue
        fi
        
        # Read file content
        local file_content=$(head -100 "$sample_file" | head -c 10000)  # Limit content size
        local file_ext="${sample_file##*.}"
        local language="unknown"
        
        # Map extension to language
        case "$file_ext" in
            "rs") language="rust" ;;
            "py") language="python" ;;
            "js") language="javascript" ;;
            "java") language="java" ;;
            "go") language="go" ;;
            "c") language="c" ;;
            "cpp"|"cc"|"cxx") language="cpp" ;;
            "rb") language="ruby" ;;
            "php") language="php" ;;
            *) language="unknown" ;;
        esac
        
        if [ "$language" = "unknown" ]; then
            echo -e "${YELLOW}⚠️  Unknown language for file: $sample_file${NC}"
            continue
        fi
        
        # Test analysis
        local analysis_payload=$(jq -n --arg content "$file_content" --arg language "$language" --arg file_path "$sample_file" '{
            content: $content,
            language: $language,
            file_path: $file_path
        }')
        
        local analysis_start=$(date +%s)
        local analysis_response=$(timeout $API_TIMEOUT curl -s -X POST "$SERVICE_URL/api/v1/analyze" \
            -H "Content-Type: application/json" \
            -d "$analysis_payload" 2>/dev/null || echo "")
        local analysis_end=$(date +%s)
        local analysis_duration=$((analysis_end - analysis_start))
        
        if echo "$analysis_response" | jq -e '.ast' > /dev/null 2>&1; then
            local lines_of_code=$(echo "$analysis_response" | jq -r '.metrics.lines_of_code // 0')
            echo -e "${GREEN}✅ $repo_dir analysis passed (${lines_of_code} LOC, ${analysis_duration}s)${NC}"
            ((repo_tests_passed++))
        else
            echo -e "${RED}❌ $repo_dir analysis failed (${analysis_duration}s)${NC}"
            ((repo_tests_failed++))
        fi
        
        # Add delay between tests
        sleep 1
    done
    
    echo -e "${CYAN}Repository integration testing summary:${NC}"
    echo -e "${GREEN}  Passed: $repo_tests_passed${NC}"
    echo -e "${RED}  Failed: $repo_tests_failed${NC}"
    
    if [ "$repo_tests_failed" -eq 0 ]; then
        echo -e "${GREEN}✅ All repository tests passed${NC}"
        ((SUCCESSFUL_INTEGRATION_TESTS++))
        return 0
    else
        echo -e "${RED}❌ Some repository tests failed${NC}"
        ((FAILED_INTEGRATION_TESTS++))
        return 1
    fi
}

# Run load tests
run_load_tests() {
    echo -e "${BLUE}⚡ Running load tests...${NC}"
    
    if [ "$RUN_LOAD_TESTS" = false ]; then
        echo -e "${YELLOW}⚠️  Load tests disabled${NC}"
        return 0
    fi
    
    ((TOTAL_INTEGRATION_TESTS++))
    
    # Simple load test with concurrent requests
    local test_content='fn main() { println!("Hello, world!"); }'
    local load_test_payload=$(jq -n --arg content "$test_content" '{
        content: $content,
        language: "rust",
        file_path: "load_test.rs"
    }')
    
    echo -e "${CYAN}Running $MAX_CONCURRENT_REQUESTS concurrent requests...${NC}"
    
    local load_test_start=$(date +%s)
    local load_test_pids=()
    local load_test_results=()
    
    # Start concurrent requests
    for i in $(seq 1 $MAX_CONCURRENT_REQUESTS); do
        (
            local response=$(curl -s -X POST "$SERVICE_URL/api/v1/analyze" \
                -H "Content-Type: application/json" \
                -d "$load_test_payload" 2>/dev/null || echo "")
            
            if echo "$response" | jq -e '.ast' > /dev/null 2>&1; then
                echo "success" > "$RESULTS_DIR/load_test_$i.result"
            else
                echo "failure" > "$RESULTS_DIR/load_test_$i.result"
            fi
        ) &
        load_test_pids+=($!)
    done
    
    # Wait for all requests to complete
    for pid in "${load_test_pids[@]}"; do
        wait $pid
    done
    
    local load_test_end=$(date +%s)
    local load_test_duration=$((load_test_end - load_test_start))
    
    # Count results
    local successful_requests=0
    local failed_requests=0
    
    for i in $(seq 1 $MAX_CONCURRENT_REQUESTS); do
        local result_file="$RESULTS_DIR/load_test_$i.result"
        if [ -f "$result_file" ]; then
            local result=$(cat "$result_file")
            if [ "$result" = "success" ]; then
                ((successful_requests++))
            else
                ((failed_requests++))
            fi
            rm -f "$result_file"
        else
            ((failed_requests++))
        fi
    done
    
    echo -e "${CYAN}Load test results:${NC}"
    echo -e "${GREEN}  Successful: $successful_requests${NC}"
    echo -e "${RED}  Failed: $failed_requests${NC}"
    echo -e "  Duration: ${load_test_duration}s"
    echo -e "  Requests/second: $(echo "scale=2; $successful_requests / $load_test_duration" | bc -l)"
    
    if [ "$failed_requests" -eq 0 ]; then
        echo -e "${GREEN}✅ Load test passed${NC}"
        ((SUCCESSFUL_INTEGRATION_TESTS++))
        return 0
    else
        echo -e "${RED}❌ Load test failed${NC}"
        ((FAILED_INTEGRATION_TESTS++))
        return 1
    fi
}

# Generate integration test report
generate_integration_report() {
    echo -e "${BLUE}📝 Generating integration test report...${NC}"
    
    local report_file="$RESULTS_DIR/integration_report_${TIMESTAMP}.md"
    
    cat > "$report_file" << EOF
# Service Integration Test Report

**Test Date:** $(date)  
**Service URL:** $SERVICE_URL  
**Test Duration:** $(date -d "$(head -1 "$INTEGRATION_LOG" | cut -d' ' -f5-)" +%s 2>/dev/null || echo "N/A")s  

## Executive Summary

- **Total Tests:** $TOTAL_INTEGRATION_TESTS
- **Successful:** $SUCCESSFUL_INTEGRATION_TESTS
- **Failed:** $FAILED_INTEGRATION_TESTS
- **Success Rate:** $([ "$TOTAL_INTEGRATION_TESTS" -gt 0 ] && echo "scale=1; $SUCCESSFUL_INTEGRATION_TESTS * 100 / $TOTAL_INTEGRATION_TESTS" | bc -l || echo "0")%

## Test Configuration

- **Service Startup Timeout:** ${SERVICE_STARTUP_TIMEOUT}s
- **API Timeout:** ${API_TIMEOUT}s
- **Max Concurrent Requests:** $MAX_CONCURRENT_REQUESTS
- **Load Tests:** $([ "$RUN_LOAD_TESTS" = true ] && echo "Enabled" || echo "Disabled")
- **Repository Tests:** $([ "$RUN_REPOSITORY_TESTS" = true ] && echo "Enabled" || echo "Disabled")

## Test Results

### Service Health
- **Basic Health Check:** $(curl -s "$SERVICE_URL/health" | grep -q "healthy" && echo "✅ Passed" || echo "❌ Failed")
- **Detailed Health Check:** $(curl -s "$SERVICE_URL/health/detailed" | jq -e '.status' > /dev/null 2>&1 && echo "✅ Passed" || echo "❌ Failed")

### API Endpoints
- **Languages Endpoint:** $(curl -s "$SERVICE_URL/api/v1/languages" | jq -e '.languages' > /dev/null 2>&1 && echo "✅ Passed" || echo "❌ Failed")
- **Analysis Endpoint:** $(echo '{"content":"fn main(){}","language":"rust","file_path":"test.rs"}' | curl -s -X POST "$SERVICE_URL/api/v1/analyze" -H "Content-Type: application/json" -d @- | jq -e '.ast' > /dev/null 2>&1 && echo "✅ Passed" || echo "❌ Failed")
- **Metrics Endpoint:** $(curl -s "$SERVICE_URL/metrics" | grep -q "analysis_engine" && echo "✅ Passed" || echo "❌ Failed")

### Performance
- **Load Test:** $([ "$RUN_LOAD_TESTS" = true ] && echo "$([ "$SUCCESSFUL_INTEGRATION_TESTS" -gt 0 ] && echo "✅ Passed" || echo "❌ Failed")" || echo "Skipped")
- **Repository Test:** $([ "$RUN_REPOSITORY_TESTS" = true ] && echo "$([ "$SUCCESSFUL_INTEGRATION_TESTS" -gt 0 ] && echo "✅ Passed" || echo "❌ Failed")" || echo "Skipped")

## Service Metrics

$(if curl -s "$SERVICE_URL/health/detailed" | jq -e '.status' > /dev/null 2>&1; then
    curl -s "$SERVICE_URL/health/detailed" | jq -r '
        "- **Status:** " + (.status // "unknown") + "\\n" +
        "- **Uptime:** " + (.uptime_seconds // 0 | tostring) + "s\\n" +
        "- **Memory Usage:** " + (.memory_usage_mb // 0 | tostring) + "MB\\n" +
        "- **Languages Supported:** " + (.languages_supported // 0 | tostring) + "\\n" +
        "- **Requests Processed:** " + (.requests_processed // 0 | tostring) + "\\n" +
        "- **Cache Hit Rate:** " + (.cache_hit_rate // 0 | tostring) + "%"
    '
else
    echo "Service metrics not available"
fi)

## Files Generated

- **Integration Log:** \`$(basename "$INTEGRATION_LOG")\`
- **Service Output:** \`service_output_${TIMESTAMP}.log\`
- **This Report:** \`$(basename "$report_file")\`

## Recommendations

$(if [ "$FAILED_INTEGRATION_TESTS" -eq 0 ]; then
    echo "✅ **All integration tests passed** - Service is production-ready"
    echo "✅ **API endpoints working** - All endpoints responding correctly"
    echo "✅ **Load handling validated** - Service handles concurrent requests"
elif [ "$SUCCESSFUL_INTEGRATION_TESTS" -gt "$FAILED_INTEGRATION_TESTS" ]; then
    echo "⚠️  **Most tests passed** - Address failures before production"
    echo "⚠️  **Check failed tests** - Review logs for failure details"
else
    echo "❌ **Significant failures** - Service needs attention"
    echo "❌ **Check service logs** - Review service output for errors"
fi)

## Next Steps

1. **Review Logs:** Check service output and integration logs for details
2. **Address Failures:** Fix any failing tests before production deployment
3. **Monitor Production:** Set up monitoring and alerting for production
4. **Load Testing:** Conduct more comprehensive load testing if needed

---

*Generated by Analysis Engine Service Integration Testing Framework*
EOF
    
    echo -e "${GREEN}✅ Integration test report generated: $report_file${NC}"
}

# Print final summary
print_final_summary() {
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}                  INTEGRATION TEST SUMMARY                          ${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    
    echo -e "Total Integration Tests: $TOTAL_INTEGRATION_TESTS"
    echo -e "${GREEN}Successful: $SUCCESSFUL_INTEGRATION_TESTS${NC}"
    echo -e "${RED}Failed: $FAILED_INTEGRATION_TESTS${NC}"
    
    if [ "$TOTAL_INTEGRATION_TESTS" -gt 0 ]; then
        local success_percentage=$(echo "scale=1; $SUCCESSFUL_INTEGRATION_TESTS * 100 / $TOTAL_INTEGRATION_TESTS" | bc -l)
        echo -e "Success Rate: ${success_percentage}%"
    fi
    
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    
    # Final recommendation
    if [ "$FAILED_INTEGRATION_TESTS" -eq 0 ]; then
        echo -e "${GREEN}🎉 All integration tests passed!${NC}"
        echo -e "${GREEN}   Service is ready for production deployment${NC}"
    elif [ "$SUCCESSFUL_INTEGRATION_TESTS" -gt "$FAILED_INTEGRATION_TESTS" ]; then
        echo -e "${YELLOW}⚠️  Most integration tests passed${NC}"
        echo -e "${YELLOW}   Address failures before production deployment${NC}"
    else
        echo -e "${RED}❌ Significant integration test failures${NC}"
        echo -e "${RED}   Service needs attention before deployment${NC}"
    fi
    
    echo -e "\n${BLUE}📁 Results Directory: $RESULTS_DIR${NC}"
    echo -e "${BLUE}📋 Integration Log: $INTEGRATION_LOG${NC}"
}

# Cleanup function
cleanup() {
    echo -e "${YELLOW}🧹 Cleaning up...${NC}"
    stop_service
    
    # Remove temporary files
    rm -f "$RESULTS_DIR"/load_test_*.result 2>/dev/null || true
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Show usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --service-url URL          Service URL (default: $SERVICE_URL)"
    echo "  --no-load-tests           Skip load testing"
    echo "  --no-repository-tests     Skip repository testing"
    echo "  --max-concurrent N        Max concurrent requests (default: $MAX_CONCURRENT_REQUESTS)"
    echo "  --timeout SECONDS         API timeout (default: $API_TIMEOUT)"
    echo "  -h, --help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                        # Run all integration tests"
    echo "  $0 --no-load-tests       # Skip load testing"
    echo "  $0 --max-concurrent 20   # Test with 20 concurrent requests"
    echo "  $0 --service-url http://localhost:8080  # Use different service URL"
}

# Main execution function
main() {
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --service-url)
                SERVICE_URL="$2"
                shift 2
                ;;
            --no-load-tests)
                RUN_LOAD_TESTS=false
                shift
                ;;
            --no-repository-tests)
                RUN_REPOSITORY_TESTS=false
                shift
                ;;
            --max-concurrent)
                MAX_CONCURRENT_REQUESTS="$2"
                shift 2
                ;;
            --timeout)
                API_TIMEOUT="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Set up cleanup on exit
    trap cleanup EXIT
    
    # Initialize integration testing
    initialize_integration_testing
    
    # Start service
    if ! start_service; then
        echo -e "${RED}❌ Failed to start service${NC}"
        exit 1
    fi
    
    # Run integration tests
    echo -e "${BLUE}🧪 Running integration tests...${NC}"
    
    # Test service endpoints
    test_service_endpoints
    
    # Test repository integration
    test_repository_integration
    
    # Run load tests
    run_load_tests
    
    # Generate integration report
    generate_integration_report
    
    # Print final summary
    print_final_summary
    
    # Exit with appropriate code
    if [ "$FAILED_INTEGRATION_TESTS" -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi