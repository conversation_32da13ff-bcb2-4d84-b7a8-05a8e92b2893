#!/bin/bash

# Multi-Language Testing Execution Script for Analysis Engine
# Coordinates comprehensive testing across collected repositories
# Orchestrates collection, testing, and reporting

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_DATA_DIR="$SCRIPT_DIR/../../test-data"
RESULTS_DIR="$TEST_DATA_DIR/results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
EXECUTION_LOG="$RESULTS_DIR/execution_log_${TIMESTAMP}.log"

# Test execution configuration
PARALLEL_TESTS=false
SKIP_COLLECTION=false
SKIP_PERFORMANCE=false
SKIP_INTEGRATION=false
TARGET_LANGUAGES=()
TEST_TIMEOUT=1800  # 30 minutes per test
MAX_CONCURRENT_TESTS=3

# Test counters
TOTAL_TESTS=0
SUCCESSFUL_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

# Initialize execution environment
initialize_execution() {
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}      Analysis Engine - Multi-Language Testing Execution            ${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    echo -e "Timestamp: $(date)"
    echo -e "Mode: $([ "$PARALLEL_TESTS" = true ] && echo "Parallel" || echo "Sequential")"
    echo -e "Target: $([ ${#TARGET_LANGUAGES[@]} -eq 0 ] && echo "All languages" || echo "${TARGET_LANGUAGES[*]}")"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}\n"
    
    # Create directories
    mkdir -p "$RESULTS_DIR"
    
    # Initialize execution log
    echo "Multi-Language Testing Execution Log - $(date)" > "$EXECUTION_LOG"
    echo "=============================================" >> "$EXECUTION_LOG"
    
    # Check prerequisites
    check_execution_prerequisites
}

# Check execution prerequisites
check_execution_prerequisites() {
    echo -e "${BLUE}📋 Checking execution prerequisites...${NC}"
    
    # Check if analysis engine is built
    if [ ! -f "$SCRIPT_DIR/../../target/release/analysis-engine" ]; then
        echo -e "${YELLOW}⚠️  Analysis engine not built, building...${NC}"
        cd "$SCRIPT_DIR/../../"
        if cargo build --release; then
            echo -e "${GREEN}✅ Analysis engine built successfully${NC}"
        else
            echo -e "${RED}❌ Failed to build analysis engine${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ Analysis engine binary found${NC}"
    fi
    
    # Check if performance validator is built
    if [ ! -f "$SCRIPT_DIR/../../target/release/performance_validator" ]; then
        echo -e "${YELLOW}⚠️  Performance validator not built, building...${NC}"
        cd "$SCRIPT_DIR/../../"
        if cargo build --release --bin performance_validator; then
            echo -e "${GREEN}✅ Performance validator built successfully${NC}"
        else
            echo -e "${RED}❌ Failed to build performance validator${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ Performance validator binary found${NC}"
    fi
    
    # Check if testing scripts exist
    local required_scripts=(
        "collect_test_repositories.sh"
        "test_large_repositories.sh"
    )
    
    for script in "${required_scripts[@]}"; do
        if [ ! -f "$SCRIPT_DIR/$script" ]; then
            echo -e "${RED}❌ Required script not found: $script${NC}"
            exit 1
        elif [ ! -x "$SCRIPT_DIR/$script" ]; then
            echo -e "${YELLOW}⚠️  Making $script executable...${NC}"
            chmod +x "$SCRIPT_DIR/$script"
        fi
        echo -e "${GREEN}✅ $script found and executable${NC}"
    done
    
    # Check for parallel execution tools if needed
    if [ "$PARALLEL_TESTS" = true ]; then
        if ! command -v parallel &> /dev/null; then
            echo -e "${YELLOW}⚠️  GNU parallel not found, falling back to sequential execution${NC}"
            PARALLEL_TESTS=false
        else
            echo -e "${GREEN}✅ GNU parallel found${NC}"
        fi
    fi
    
    echo -e "${GREEN}✅ Execution prerequisites satisfied${NC}\n"
}

# Collect test repositories
collect_test_repositories() {
    echo -e "${BLUE}📥 Collecting test repositories...${NC}"
    
    if [ "$SKIP_COLLECTION" = true ]; then
        echo -e "${YELLOW}⚠️  Skipping repository collection (--skip-collection)${NC}"
        return 0
    fi
    
    local collection_start=$(date +%s)
    
    # Determine collection arguments
    local collection_args=()
    if [ ${#TARGET_LANGUAGES[@]} -gt 0 ]; then
        # Collect repositories for specific languages
        for language in "${TARGET_LANGUAGES[@]}"; do
            echo -e "${CYAN}Collecting repositories for $language...${NC}"
            if ! "$SCRIPT_DIR/collect_test_repositories.sh" -l "$language" 2>&1 | tee -a "$EXECUTION_LOG"; then
                echo -e "${RED}❌ Failed to collect repositories for $language${NC}"
                return 1
            fi
        done
    else
        # Collect all repositories
        echo -e "${CYAN}Collecting all repositories...${NC}"
        if ! "$SCRIPT_DIR/collect_test_repositories.sh" --all 2>&1 | tee -a "$EXECUTION_LOG"; then
            echo -e "${RED}❌ Failed to collect repositories${NC}"
            return 1
        fi
    fi
    
    local collection_end=$(date +%s)
    local collection_duration=$((collection_end - collection_start))
    
    echo -e "${GREEN}✅ Repository collection completed in ${collection_duration}s${NC}"
    echo "Repository collection completed in ${collection_duration}s" >> "$EXECUTION_LOG"
    
    return 0
}

# Run performance tests
run_performance_tests() {
    echo -e "${BLUE}⏱️  Running performance tests...${NC}"
    
    if [ "$SKIP_PERFORMANCE" = true ]; then
        echo -e "${YELLOW}⚠️  Skipping performance tests (--skip-performance)${NC}"
        return 0
    fi
    
    local performance_start=$(date +%s)
    
    # Run large repository tests
    echo -e "${CYAN}Running large repository tests...${NC}"
    if ! timeout "$TEST_TIMEOUT" "$SCRIPT_DIR/test_large_repositories.sh" 2>&1 | tee -a "$EXECUTION_LOG"; then
        echo -e "${RED}❌ Large repository tests failed or timed out${NC}"
        ((FAILED_TESTS++))
        return 1
    fi
    
    local performance_end=$(date +%s)
    local performance_duration=$((performance_end - performance_start))
    
    echo -e "${GREEN}✅ Performance tests completed in ${performance_duration}s${NC}"
    echo "Performance tests completed in ${performance_duration}s" >> "$EXECUTION_LOG"
    
    ((SUCCESSFUL_TESTS++))
    return 0
}

# Run integration tests
run_integration_tests() {
    echo -e "${BLUE}🔗 Running integration tests...${NC}"
    
    if [ "$SKIP_INTEGRATION" = true ]; then
        echo -e "${YELLOW}⚠️  Skipping integration tests (--skip-integration)${NC}"
        return 0
    fi
    
    local integration_start=$(date +%s)
    
    # Start analysis engine service for integration testing
    echo -e "${CYAN}Starting analysis engine service...${NC}"
    
    # Set required environment variables
    export GCP_PROJECT_ID="${GCP_PROJECT_ID:-test-project}"
    export ENVIRONMENT="${ENVIRONMENT:-development}"
    
    # Start service in background
    cd "$SCRIPT_DIR/../../"
    cargo run --release &
    local service_pid=$!
    
    # Wait for service to be ready
    echo "Waiting for service to be ready..."
    local ready=false
    for i in {1..30}; do
        if curl -s http://localhost:8001/health > /dev/null 2>&1; then
            ready=true
            break
        fi
        sleep 2
    done
    
    if [ "$ready" = false ]; then
        echo -e "${RED}❌ Service failed to start${NC}"
        kill $service_pid 2>/dev/null || true
        return 1
    fi
    
    echo -e "${GREEN}✅ Service started successfully${NC}"
    
    # Run integration tests
    local integration_success=true
    
    # Test 1: Service health check
    echo -e "${CYAN}Testing service health...${NC}"
    if curl -s http://localhost:8001/health | grep -q "healthy"; then
        echo -e "${GREEN}✅ Health check passed${NC}"
    else
        echo -e "${RED}❌ Health check failed${NC}"
        integration_success=false
    fi
    
    # Test 2: Language support endpoint
    echo -e "${CYAN}Testing language support endpoint...${NC}"
    local language_count=$(curl -s http://localhost:8001/api/v1/languages | jq '.languages | length' 2>/dev/null || echo "0")
    if [ "$language_count" -ge 21 ]; then
        echo -e "${GREEN}✅ Language support test passed ($language_count languages)${NC}"
    else
        echo -e "${RED}❌ Language support test failed ($language_count languages, expected ≥21)${NC}"
        integration_success=false
    fi
    
    # Test 3: Simple file analysis
    echo -e "${CYAN}Testing file analysis endpoint...${NC}"
    local test_file_content='fn main() { println!("Hello, world!"); }'
    local analysis_response=$(curl -s -X POST http://localhost:8001/api/v1/analyze \
        -H "Content-Type: application/json" \
        -d "{\"content\": \"$test_file_content\", \"language\": \"rust\", \"file_path\": \"test.rs\"}" 2>/dev/null)
    
    if echo "$analysis_response" | jq -e '.ast' > /dev/null 2>&1; then
        echo -e "${GREEN}✅ File analysis test passed${NC}"
    else
        echo -e "${RED}❌ File analysis test failed${NC}"
        integration_success=false
    fi
    
    # Test 4: Performance metrics endpoint
    echo -e "${CYAN}Testing performance metrics endpoint...${NC}"
    if curl -s http://localhost:8001/metrics | grep -q "analysis_engine"; then
        echo -e "${GREEN}✅ Performance metrics test passed${NC}"
    else
        echo -e "${RED}❌ Performance metrics test failed${NC}"
        integration_success=false
    fi
    
    # Cleanup: Stop service
    echo -e "${CYAN}Stopping analysis engine service...${NC}"
    kill $service_pid 2>/dev/null || true
    wait $service_pid 2>/dev/null || true
    
    local integration_end=$(date +%s)
    local integration_duration=$((integration_end - integration_start))
    
    if [ "$integration_success" = true ]; then
        echo -e "${GREEN}✅ Integration tests completed successfully in ${integration_duration}s${NC}"
        echo "Integration tests completed successfully in ${integration_duration}s" >> "$EXECUTION_LOG"
        ((SUCCESSFUL_TESTS++))
        return 0
    else
        echo -e "${RED}❌ Integration tests failed in ${integration_duration}s${NC}"
        echo "Integration tests failed in ${integration_duration}s" >> "$EXECUTION_LOG"
        ((FAILED_TESTS++))
        return 1
    fi
}

# Run language-specific tests
run_language_specific_tests() {
    echo -e "${BLUE}🔍 Running language-specific tests...${NC}"
    
    local repositories_dir="$TEST_DATA_DIR/repositories"
    
    if [ ! -d "$repositories_dir" ]; then
        echo -e "${RED}❌ Repositories directory not found: $repositories_dir${NC}"
        return 1
    fi
    
    # Get list of collected repositories
    local repositories=($(ls "$repositories_dir" 2>/dev/null || true))
    
    if [ ${#repositories[@]} -eq 0 ]; then
        echo -e "${RED}❌ No repositories found in $repositories_dir${NC}"
        return 1
    fi
    
    echo -e "${CYAN}Found ${#repositories[@]} repositories for testing${NC}"
    
    # Test each repository
    local language_tests_successful=0
    local language_tests_failed=0
    
    for repo_dir in "${repositories[@]}"; do
        local repo_path="$repositories_dir/$repo_dir"
        
        if [ ! -d "$repo_path" ]; then
            continue
        fi
        
        echo -e "${CYAN}Testing repository: $repo_dir${NC}"
        
        # Read repository summary if available
        local summary_file="$repo_path/.analysis_summary.json"
        local language="unknown"
        
        if [ -f "$summary_file" ]; then
            language=$(jq -r '.repository.language // "unknown"' "$summary_file" 2>/dev/null || echo "unknown")
        fi
        
        # Skip if language filtering is enabled and this isn't a target language
        if [ ${#TARGET_LANGUAGES[@]} -gt 0 ]; then
            local skip_repo=true
            for target_lang in "${TARGET_LANGUAGES[@]}"; do
                if [ "$language" = "$target_lang" ]; then
                    skip_repo=false
                    break
                fi
            done
            
            if [ "$skip_repo" = true ]; then
                echo -e "${YELLOW}⚠️  Skipping $repo_dir (language: $language)${NC}"
                continue
            fi
        fi
        
        # Run performance validator on this repository
        local validator_start=$(date +%s)
        
        if timeout 300 "$SCRIPT_DIR/../../target/release/performance_validator" "$repo_path" > "$RESULTS_DIR/validator_${repo_dir}_${TIMESTAMP}.json" 2>&1; then
            local validator_end=$(date +%s)
            local validator_duration=$((validator_end - validator_start))
            
            echo -e "${GREEN}✅ $repo_dir tested successfully in ${validator_duration}s${NC}"
            ((language_tests_successful++))
        else
            local validator_end=$(date +%s)
            local validator_duration=$((validator_end - validator_start))
            
            echo -e "${RED}❌ $repo_dir test failed or timed out after ${validator_duration}s${NC}"
            ((language_tests_failed++))
        fi
        
        # Add delay between tests to avoid resource exhaustion
        sleep 1
    done
    
    echo -e "${CYAN}Language-specific tests completed:${NC}"
    echo -e "${GREEN}  Successful: $language_tests_successful${NC}"
    echo -e "${RED}  Failed: $language_tests_failed${NC}"
    
    if [ "$language_tests_failed" -eq 0 ]; then
        ((SUCCESSFUL_TESTS++))
        return 0
    else
        ((FAILED_TESTS++))
        return 1
    fi
}

# Run parallel tests
run_parallel_tests() {
    echo -e "${BLUE}⚡ Running tests in parallel...${NC}"
    
    if [ "$PARALLEL_TESTS" = false ]; then
        echo -e "${YELLOW}⚠️  Parallel execution disabled, running sequentially${NC}"
        run_sequential_tests
        return $?
    fi
    
    # Create test job file
    local job_file="$RESULTS_DIR/test_jobs_${TIMESTAMP}.txt"
    
    # Define test jobs
    cat > "$job_file" << EOF
collection:$SCRIPT_DIR/collect_test_repositories.sh --all
performance:$SCRIPT_DIR/test_large_repositories.sh
integration:run_integration_tests
language_specific:run_language_specific_tests
EOF
    
    # Run tests in parallel
    echo -e "${CYAN}Running $MAX_CONCURRENT_TESTS concurrent tests...${NC}"
    
    if parallel -j "$MAX_CONCURRENT_TESTS" --colsep ':' --results "$RESULTS_DIR/parallel_results_${TIMESTAMP}" \
        'echo "Starting {1}..."; eval {2} && echo "✅ {1} completed" || echo "❌ {1} failed"' < "$job_file"; then
        echo -e "${GREEN}✅ Parallel tests completed successfully${NC}"
        ((SUCCESSFUL_TESTS++))
        return 0
    else
        echo -e "${RED}❌ Some parallel tests failed${NC}"
        ((FAILED_TESTS++))
        return 1
    fi
}

# Run sequential tests
run_sequential_tests() {
    echo -e "${BLUE}🔄 Running tests sequentially...${NC}"
    
    local sequential_start=$(date +%s)
    
    # Test 1: Repository collection
    echo -e "${CYAN}Phase 1: Repository Collection${NC}"
    ((TOTAL_TESTS++))
    if collect_test_repositories; then
        echo -e "${GREEN}✅ Repository collection completed${NC}"
    else
        echo -e "${RED}❌ Repository collection failed${NC}"
        ((FAILED_TESTS++))
    fi
    
    # Test 2: Performance testing
    echo -e "${CYAN}Phase 2: Performance Testing${NC}"
    ((TOTAL_TESTS++))
    if run_performance_tests; then
        echo -e "${GREEN}✅ Performance testing completed${NC}"
    else
        echo -e "${RED}❌ Performance testing failed${NC}"
        ((FAILED_TESTS++))
    fi
    
    # Test 3: Integration testing
    echo -e "${CYAN}Phase 3: Integration Testing${NC}"
    ((TOTAL_TESTS++))
    if run_integration_tests; then
        echo -e "${GREEN}✅ Integration testing completed${NC}"
    else
        echo -e "${RED}❌ Integration testing failed${NC}"
        ((FAILED_TESTS++))
    fi
    
    # Test 4: Language-specific testing
    echo -e "${CYAN}Phase 4: Language-Specific Testing${NC}"
    ((TOTAL_TESTS++))
    if run_language_specific_tests; then
        echo -e "${GREEN}✅ Language-specific testing completed${NC}"
    else
        echo -e "${RED}❌ Language-specific testing failed${NC}"
        ((FAILED_TESTS++))
    fi
    
    local sequential_end=$(date +%s)
    local sequential_duration=$((sequential_end - sequential_start))
    
    echo -e "${CYAN}Sequential tests completed in ${sequential_duration}s${NC}"
    echo "Sequential tests completed in ${sequential_duration}s" >> "$EXECUTION_LOG"
    
    # Update successful tests counter
    SUCCESSFUL_TESTS=$((TOTAL_TESTS - FAILED_TESTS))
    
    return 0
}

# Generate comprehensive execution report
generate_execution_report() {
    echo -e "${BLUE}📝 Generating execution report...${NC}"
    
    local report_file="$RESULTS_DIR/execution_report_${TIMESTAMP}.md"
    local execution_duration=$(($(date +%s) - $(date -d "$(head -1 "$EXECUTION_LOG" | cut -d' ' -f6-)" +%s) ))
    
    cat > "$report_file" << EOF
# Multi-Language Testing Execution Report

**Execution Date:** $(date)  
**Duration:** ${execution_duration}s  
**Mode:** $([ "$PARALLEL_TESTS" = true ] && echo "Parallel" || echo "Sequential")  

## Executive Summary

- **Total Tests:** $TOTAL_TESTS
- **Successful:** $SUCCESSFUL_TESTS
- **Failed:** $FAILED_TESTS
- **Success Rate:** $([ "$TOTAL_TESTS" -gt 0 ] && echo "scale=1; $SUCCESSFUL_TESTS * 100 / $TOTAL_TESTS" | bc -l || echo "0")%

## Test Configuration

- **Repository Collection:** $([ "$SKIP_COLLECTION" = true ] && echo "Skipped" || echo "Enabled")
- **Performance Testing:** $([ "$SKIP_PERFORMANCE" = true ] && echo "Skipped" || echo "Enabled")
- **Integration Testing:** $([ "$SKIP_INTEGRATION" = true ] && echo "Skipped" || echo "Enabled")
- **Target Languages:** $([ ${#TARGET_LANGUAGES[@]} -eq 0 ] && echo "All" || echo "${TARGET_LANGUAGES[*]}")
- **Parallel Execution:** $([ "$PARALLEL_TESTS" = true ] && echo "Enabled" || echo "Disabled")

## Test Results

### Repository Collection
$([ "$SKIP_COLLECTION" = true ] && echo "- **Status:** Skipped" || echo "- **Status:** Executed")

### Performance Testing
$([ "$SKIP_PERFORMANCE" = true ] && echo "- **Status:** Skipped" || echo "- **Status:** Executed")

### Integration Testing
$([ "$SKIP_INTEGRATION" = true ] && echo "- **Status:** Skipped" || echo "- **Status:** Executed")

### Language-Specific Testing
- **Status:** Executed
- **Repositories Tested:** $(ls "$TEST_DATA_DIR/repositories" 2>/dev/null | wc -l || echo "0")

## Performance Validation

$(if [ -f "$RESULTS_DIR/performance_results.json" ]; then
    echo "### Performance Metrics"
    echo "\`\`\`json"
    jq '.analysis_summary' "$RESULTS_DIR/performance_results.json" 2>/dev/null || echo "No performance data available"
    echo "\`\`\`"
else
    echo "Performance validation results not available"
fi)

## Files Generated

- **Execution Log:** \`$(basename "$EXECUTION_LOG")\`
- **Results Directory:** \`$RESULTS_DIR\`
- **Performance Results:** \`performance_results.json\`
- **Individual Test Results:** \`validator_*_${TIMESTAMP}.json\`

## Next Steps

1. **Review Results:** Check individual test results for detailed metrics
2. **Address Failures:** Investigate and fix any failed tests
3. **Production Deployment:** If all tests pass, proceed with deployment
4. **Continuous Monitoring:** Set up monitoring for production performance

## Recommendations

$(if [ "$FAILED_TESTS" -eq 0 ]; then
    echo "✅ **All tests passed** - System is ready for production deployment"
    echo "✅ **Performance targets met** - 1M LOC in <5 minutes validated"
    echo "✅ **Language support complete** - All 21 languages working"
elif [ "$SUCCESSFUL_TESTS" -gt "$FAILED_TESTS" ]; then
    echo "⚠️  **Most tests passed** - Address failures before deployment"
    echo "⚠️  **Review failed tests** - Check logs for failure details"
else
    echo "❌ **Significant failures** - System needs attention before deployment"
    echo "❌ **Check prerequisites** - Verify build and environment setup"
fi)

---

*Generated by Analysis Engine Multi-Language Testing Execution Framework*
EOF
    
    echo -e "${GREEN}✅ Execution report generated: $report_file${NC}"
}

# Print final summary
print_final_summary() {
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}                    EXECUTION SUMMARY                               ${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    
    echo -e "Total Tests: $TOTAL_TESTS"
    echo -e "${GREEN}Successful: $SUCCESSFUL_TESTS${NC}"
    echo -e "${RED}Failed: $FAILED_TESTS${NC}"
    
    if [ "$TOTAL_TESTS" -gt 0 ]; then
        local success_percentage=$(echo "scale=1; $SUCCESSFUL_TESTS * 100 / $TOTAL_TESTS" | bc -l)
        echo -e "Success Rate: ${success_percentage}%"
    fi
    
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    
    # Final recommendation
    if [ "$FAILED_TESTS" -eq 0 ]; then
        echo -e "${GREEN}🎉 All tests completed successfully!${NC}"
        echo -e "${GREEN}   System is ready for production deployment${NC}"
    elif [ "$SUCCESSFUL_TESTS" -gt "$FAILED_TESTS" ]; then
        echo -e "${YELLOW}⚠️  Most tests completed successfully${NC}"
        echo -e "${YELLOW}   Address failures before production deployment${NC}"
    else
        echo -e "${RED}❌ Significant test failures occurred${NC}"
        echo -e "${RED}   System needs attention before deployment${NC}"
    fi
    
    echo -e "\n${BLUE}📁 Results Directory: $RESULTS_DIR${NC}"
    echo -e "${BLUE}📋 Execution Log: $EXECUTION_LOG${NC}"
}

# Show usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -p, --parallel              Run tests in parallel"
    echo "  -l, --language LANG[,LANG]  Target specific languages (comma-separated)"
    echo "  --skip-collection           Skip repository collection"
    echo "  --skip-performance          Skip performance testing"
    echo "  --skip-integration          Skip integration testing"
    echo "  --timeout SECONDS           Set test timeout (default: 1800)"
    echo "  --max-concurrent N          Max concurrent tests (default: 3)"
    echo "  -h, --help                  Show this help message"
    echo ""
    echo "Languages:"
    echo "  rust, python, javascript, typescript, java, go, c, cpp,"
    echo "  ruby, php, julia, scala, ocaml, bash, multi"
    echo ""
    echo "Examples:"
    echo "  $0                          # Run all tests sequentially"
    echo "  $0 -p                       # Run all tests in parallel"
    echo "  $0 -l rust,python          # Test only Rust and Python"
    echo "  $0 --skip-collection       # Skip repository collection"
    echo "  $0 --timeout 3600          # Set 1-hour timeout"
}

# Main execution function
main() {
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--parallel)
                PARALLEL_TESTS=true
                shift
                ;;
            -l|--language)
                IFS=',' read -ra TARGET_LANGUAGES <<< "$2"
                shift 2
                ;;
            --skip-collection)
                SKIP_COLLECTION=true
                shift
                ;;
            --skip-performance)
                SKIP_PERFORMANCE=true
                shift
                ;;
            --skip-integration)
                SKIP_INTEGRATION=true
                shift
                ;;
            --timeout)
                TEST_TIMEOUT="$2"
                shift 2
                ;;
            --max-concurrent)
                MAX_CONCURRENT_TESTS="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Initialize execution environment
    initialize_execution
    
    # Run tests (parallel or sequential)
    if [ "$PARALLEL_TESTS" = true ]; then
        run_parallel_tests
    else
        run_sequential_tests
    fi
    
    # Generate execution report
    generate_execution_report
    
    # Print final summary
    print_final_summary
    
    # Exit with appropriate code
    if [ "$FAILED_TESTS" -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi