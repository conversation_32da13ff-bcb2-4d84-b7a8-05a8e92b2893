#!/bin/bash

# Repository Collection Framework for Analysis Engine
# Collects large open source repositories across all 21 supported languages
# Optimized for performance testing and validation

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_DATA_DIR="$SCRIPT_DIR/../../test-data"
REPOSITORIES_DIR="$TEST_DATA_DIR/repositories"
CACHE_DIR="$TEST_DATA_DIR/cache"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$TEST_DATA_DIR/collection_log_${TIMESTAMP}.log"

# Repository size limits (in MB)
MAX_REPO_SIZE_MB=1000
MAX_DOWNLOAD_SIZE_MB=2000

# Repository collection statistics
TOTAL_REPOS=0
SUCCESSFUL_REPOS=0
FAILED_REPOS=0
SKIPPED_REPOS=0

# Repository definitions with comprehensive language coverage
declare -A REPOSITORY_CATALOG=(
    # Rust - Systems programming
    ["rust-actix-web"]="https://github.com/actix/actix-web:actix-web:rust:15000:web-framework"
    ["rust-tokio"]="https://github.com/tokio-rs/tokio:tokio:rust:100000:async-runtime"
    ["rust-serde"]="https://github.com/serde-rs/serde:serde:rust:50000:serialization"
    ["rust-rustc"]="https://github.com/rust-lang/rust:rust-lang:rust:2000000:compiler"
    ["rust-ripgrep"]="https://github.com/BurntSushi/ripgrep:ripgrep:rust:25000:search-tool"
    
    # Python - Data science and web
    ["python-requests"]="https://github.com/psf/requests:requests:python:10000:http-library"
    ["python-django"]="https://github.com/django/django:django:python:300000:web-framework"
    ["python-flask"]="https://github.com/pallets/flask:flask:python:20000:web-framework"
    ["python-cpython"]="https://github.com/python/cpython:cpython:python:1500000:interpreter"
    ["python-numpy"]="https://github.com/numpy/numpy:numpy:python:200000:scientific-computing"
    ["python-tensorflow"]="https://github.com/tensorflow/tensorflow:tensorflow:python:1000000:machine-learning"
    
    # JavaScript/TypeScript - Web development
    ["js-express"]="https://github.com/expressjs/express:express:javascript:8000:web-framework"
    ["js-react"]="https://github.com/facebook/react:react:javascript:500000:ui-library"
    ["js-vue"]="https://github.com/vuejs/vue:vue:javascript:30000:ui-framework"
    ["js-angular"]="https://github.com/angular/angular:angular:typescript:800000:web-framework"
    ["js-vscode"]="https://github.com/microsoft/vscode:vscode:typescript:1000000:editor"
    ["js-node"]="https://github.com/nodejs/node:nodejs:javascript:800000:runtime"
    
    # Java - Enterprise development
    ["java-spring-boot"]="https://github.com/spring-projects/spring-boot:spring-boot:java:50000:web-framework"
    ["java-kafka"]="https://github.com/apache/kafka:kafka:java:400000:streaming-platform"
    ["java-elasticsearch"]="https://github.com/elastic/elasticsearch:elasticsearch:java:800000:search-engine"
    ["java-openjdk"]="https://github.com/openjdk/jdk:openjdk:java:1500000:jvm"
    ["java-jenkins"]="https://github.com/jenkinsci/jenkins:jenkins:java:300000:ci-cd"
    
    # Go - Cloud and systems
    ["go-gin"]="https://github.com/gin-gonic/gin:gin:go:15000:web-framework"
    ["go-kubernetes"]="https://github.com/kubernetes/kubernetes:kubernetes:go:1200000:container-orchestration"
    ["go-golang"]="https://github.com/golang/go:golang:go:1000000:language"
    ["go-docker"]="https://github.com/docker/docker:docker:go:200000:containerization"
    ["go-etcd"]="https://github.com/etcd-io/etcd:etcd:go:150000:distributed-storage"
    
    # C/C++ - Systems programming
    ["c-redis"]="https://github.com/redis/redis:redis:c:80000:database"
    ["c-postgres"]="https://github.com/postgres/postgres:postgres:c:1300000:database"
    ["c-linux"]="https://github.com/torvalds/linux:linux:c:500000:kernel"
    ["cpp-folly"]="https://github.com/facebook/folly:folly:cpp:200000:library"
    ["cpp-protobuf"]="https://github.com/protocolbuffers/protobuf:protobuf:cpp:300000:serialization"
    
    # Ruby - Web development
    ["ruby-sinatra"]="https://github.com/sinatra/sinatra:sinatra:ruby:5000:web-framework"
    ["ruby-rails"]="https://github.com/rails/rails:rails:ruby:200000:web-framework"
    ["ruby-ruby"]="https://github.com/ruby/ruby:ruby:ruby:800000:language"
    ["ruby-jekyll"]="https://github.com/jekyll/jekyll:jekyll:ruby:30000:static-site"
    
    # PHP - Web development
    ["php-laravel"]="https://github.com/laravel/laravel:laravel:php:15000:web-framework"
    ["php-symfony"]="https://github.com/symfony/symfony:symfony:php:200000:web-framework"
    ["php-composer"]="https://github.com/composer/composer:composer:php:50000:package-manager"
    
    # Julia - Scientific computing
    ["julia-julia"]="https://github.com/JuliaLang/julia:julia:julia:500000:language"
    ["julia-plots"]="https://github.com/JuliaPlots/Plots.jl:plots:julia:50000:plotting"
    
    # Scala - Big data and functional programming
    ["scala-spark"]="https://github.com/apache/spark:spark:scala:300000:big-data"
    ["scala-scala"]="https://github.com/scala/scala:scala:scala:400000:language"
    ["scala-akka"]="https://github.com/akka/akka:akka:scala:200000:actor-framework"
    
    # OCaml - Functional programming
    ["ocaml-ocaml"]="https://github.com/ocaml/ocaml:ocaml:ocaml:200000:language"
    ["ocaml-dune"]="https://github.com/ocaml/dune:dune:ocaml:50000:build-system"
    
    # Shell/Bash - System administration
    ["bash-ohmyzsh"]="https://github.com/ohmyzsh/ohmyzsh:ohmyzsh:bash:50000:shell-framework"
    ["bash-docker-compose"]="https://github.com/docker/compose:docker-compose:bash:30000:container-orchestration"
    
    # Multi-language repositories for cross-language testing
    ["multi-vscode"]="https://github.com/microsoft/vscode:vscode-multi:multi:1000000:editor"
    ["multi-chromium"]="https://github.com/chromium/chromium:chromium:multi:500000:browser"
    ["multi-pytorch"]="https://github.com/pytorch/pytorch:pytorch:multi:800000:machine-learning"
)

# Language priority for collection (higher priority = collect first)
declare -A LANGUAGE_PRIORITY=(
    ["rust"]=10
    ["python"]=9
    ["javascript"]=8
    ["typescript"]=8
    ["java"]=7
    ["go"]=7
    ["c"]=6
    ["cpp"]=6
    ["ruby"]=5
    ["php"]=5
    ["julia"]=4
    ["scala"]=4
    ["ocaml"]=3
    ["bash"]=3
    ["multi"]=9
)

# Initialize collection environment
initialize_collection() {
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}         Analysis Engine - Repository Collection Framework           ${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    echo -e "Timestamp: $(date)"
    echo -e "Target: Collect repositories across 21 supported languages"
    echo -e "Output: $REPOSITORIES_DIR"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}\n"
    
    # Create directories
    mkdir -p "$REPOSITORIES_DIR" "$CACHE_DIR"
    
    # Initialize log file
    echo "Repository Collection Log - $(date)" > "$LOG_FILE"
    echo "=======================================" >> "$LOG_FILE"
    
    # Check prerequisites
    check_prerequisites
}

# Check prerequisites
check_prerequisites() {
    echo -e "${BLUE}📋 Checking prerequisites...${NC}"
    
    # Check for required tools
    local required_tools=("git" "curl" "jq" "du" "find")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            echo -e "${RED}❌ $tool not found. Please install $tool${NC}"
            exit 1
        else
            echo -e "${GREEN}✅ $tool found${NC}"
        fi
    done
    
    # Check for optional tools
    if command -v tokei &> /dev/null; then
        echo -e "${GREEN}✅ tokei found (will use for line counting)${NC}"
    else
        echo -e "${YELLOW}⚠️  tokei not found (will estimate line counts)${NC}"
    fi
    
    # Check disk space
    local available_space=$(df "$TEST_DATA_DIR" 2>/dev/null | tail -1 | awk '{print $4}' || echo "0")
    local required_space=10485760  # 10GB in KB
    
    if [ "$available_space" -lt "$required_space" ]; then
        echo -e "${RED}❌ Insufficient disk space. Need at least 10GB${NC}"
        exit 1
    else
        echo -e "${GREEN}✅ Sufficient disk space available${NC}"
    fi
    
    echo -e "${GREEN}✅ Prerequisites satisfied${NC}\n"
}

# Clone repository with size management
clone_repository() {
    local repo_key="$1"
    local repo_info="${REPOSITORY_CATALOG[$repo_key]}"
    
    IFS=':' read -r url name language expected_lines category <<< "$repo_info"
    
    echo -e "${BLUE}📥 Cloning $name ($language)...${NC}"
    echo "Repository: $url"
    echo "Category: $category"
    echo "Expected lines: ~$expected_lines"
    
    ((TOTAL_REPOS++))
    
    local repo_dir="$REPOSITORIES_DIR/$name"
    
    # Log start
    echo "[$TIMESTAMP] Starting clone: $name ($language)" >> "$LOG_FILE"
    
    # Check if repository already exists
    if [ -d "$repo_dir" ]; then
        echo -e "${YELLOW}⚠️  Repository already exists, checking...${NC}"
        
        if validate_repository "$repo_dir" "$language" "$expected_lines"; then
            echo -e "${GREEN}✅ Existing repository validated${NC}"
            ((SUCCESSFUL_REPOS++))
            echo "[$TIMESTAMP] Existing repository validated: $name" >> "$LOG_FILE"
            return 0
        else
            echo -e "${YELLOW}⚠️  Existing repository invalid, re-cloning...${NC}"
            rm -rf "$repo_dir"
        fi
    fi
    
    # Clone repository with timeout
    echo "Cloning repository (timeout: 300s)..."
    local clone_start=$(date +%s)
    
    if timeout 300 git clone --depth 1 "$url" "$repo_dir" 2>&1 | tee -a "$LOG_FILE"; then
        local clone_end=$(date +%s)
        local clone_duration=$((clone_end - clone_start))
        
        echo -e "${GREEN}✅ Repository cloned successfully in ${clone_duration}s${NC}"
        
        # Validate and optimize repository
        if validate_and_optimize_repository "$repo_dir" "$language" "$expected_lines"; then
            ((SUCCESSFUL_REPOS++))
            echo "[$TIMESTAMP] Successfully collected: $name ($language)" >> "$LOG_FILE"
        else
            echo -e "${RED}❌ Repository validation failed${NC}"
            ((FAILED_REPOS++))
            echo "[$TIMESTAMP] Validation failed: $name ($language)" >> "$LOG_FILE"
            return 1
        fi
    else
        echo -e "${RED}❌ Failed to clone repository${NC}"
        ((FAILED_REPOS++))
        echo "[$TIMESTAMP] Clone failed: $name ($language)" >> "$LOG_FILE"
        return 1
    fi
    
    echo ""
}

# Validate repository contents
validate_repository() {
    local repo_dir="$1"
    local language="$2"
    local expected_lines="$3"
    
    echo "Validating repository..."
    
    # Check if directory exists and is not empty
    if [ ! -d "$repo_dir" ] || [ ! "$(ls -A "$repo_dir")" ]; then
        echo "Repository directory is empty or missing"
        return 1
    fi
    
    # Check if it's a valid git repository
    if [ ! -d "$repo_dir/.git" ]; then
        echo "Not a valid git repository"
        return 1
    fi
    
    # Count relevant files
    local file_count=$(count_language_files "$repo_dir" "$language")
    
    if [ "$file_count" -eq 0 ]; then
        echo "No relevant files found for language: $language"
        return 1
    fi
    
    echo "Found $file_count relevant files"
    return 0
}

# Validate and optimize repository
validate_and_optimize_repository() {
    local repo_dir="$1"
    local language="$2"
    local expected_lines="$3"
    
    echo "Validating and optimizing repository..."
    
    # Basic validation
    if ! validate_repository "$repo_dir" "$language" "$expected_lines"; then
        return 1
    fi
    
    # Check repository size
    local repo_size_mb=$(du -sm "$repo_dir" 2>/dev/null | cut -f1)
    echo "Repository size: ${repo_size_mb}MB"
    
    # If repository is too large, optimize it
    if [ "$repo_size_mb" -gt "$MAX_REPO_SIZE_MB" ]; then
        echo -e "${YELLOW}⚠️  Repository too large (${repo_size_mb}MB), optimizing...${NC}"
        
        if optimize_repository_size "$repo_dir" "$language"; then
            local new_size_mb=$(du -sm "$repo_dir" 2>/dev/null | cut -f1)
            echo -e "${GREEN}✅ Repository optimized: ${repo_size_mb}MB → ${new_size_mb}MB${NC}"
        else
            echo -e "${RED}❌ Failed to optimize repository${NC}"
            return 1
        fi
    fi
    
    # Generate repository summary
    generate_repository_summary "$repo_dir" "$language"
    
    return 0
}

# Optimize repository size
optimize_repository_size() {
    local repo_dir="$1"
    local language="$2"
    
    echo "Optimizing repository size..."
    
    # Remove common large directories that aren't source code
    local dirs_to_remove=(
        "node_modules"
        "target"
        "build"
        "dist"
        "out"
        "bin"
        "obj"
        ".gradle"
        ".mvn"
        "__pycache__"
        ".pytest_cache"
        ".tox"
        "venv"
        ".venv"
        "env"
        ".env"
        "vendor"
        ".git/objects"
        ".git/logs"
        ".git/refs/remotes"
    )
    
    for dir in "${dirs_to_remove[@]}"; do
        if [ -d "$repo_dir/$dir" ]; then
            echo "Removing $dir..."
            rm -rf "$repo_dir/$dir"
        fi
    done
    
    # Remove large files (>10MB)
    echo "Removing large files..."
    find "$repo_dir" -type f -size +10M -delete 2>/dev/null || true
    
    # Remove binary files
    echo "Removing binary files..."
    find "$repo_dir" -type f \( -name "*.exe" -o -name "*.dll" -o -name "*.so" -o -name "*.dylib" -o -name "*.a" -o -name "*.lib" \) -delete 2>/dev/null || true
    
    # Remove media files
    echo "Removing media files..."
    find "$repo_dir" -type f \( -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" -o -name "*.gif" -o -name "*.svg" -o -name "*.ico" -o -name "*.mp4" -o -name "*.mp3" -o -name "*.wav" \) -delete 2>/dev/null || true
    
    return 0
}

# Count language-specific files
count_language_files() {
    local repo_dir="$1"
    local language="$2"
    
    local extensions=""
    case "$language" in
        "rust") extensions="-name '*.rs'" ;;
        "python") extensions="-name '*.py'" ;;
        "javascript") extensions="-name '*.js' -o -name '*.jsx'" ;;
        "typescript") extensions="-name '*.ts' -o -name '*.tsx'" ;;
        "java") extensions="-name '*.java'" ;;
        "go") extensions="-name '*.go'" ;;
        "c") extensions="-name '*.c' -o -name '*.h'" ;;
        "cpp") extensions="-name '*.cpp' -o -name '*.cc' -o -name '*.cxx' -o -name '*.hpp'" ;;
        "ruby") extensions="-name '*.rb'" ;;
        "php") extensions="-name '*.php'" ;;
        "julia") extensions="-name '*.jl'" ;;
        "scala") extensions="-name '*.scala'" ;;
        "ocaml") extensions="-name '*.ml' -o -name '*.mli'" ;;
        "bash") extensions="-name '*.sh' -o -name '*.bash'" ;;
        "multi") extensions="-name '*.rs' -o -name '*.py' -o -name '*.js' -o -name '*.ts' -o -name '*.tsx' -o -name '*.jsx' -o -name '*.java' -o -name '*.go' -o -name '*.c' -o -name '*.h' -o -name '*.cpp' -o -name '*.cc' -o -name '*.cxx' -o -name '*.hpp' -o -name '*.rb' -o -name '*.php' -o -name '*.jl' -o -name '*.scala' -o -name '*.ml' -o -name '*.mli' -o -name '*.sh' -o -name '*.bash' -o -name '*.html' -o -name '*.css' -o -name '*.json' -o -name '*.md'" ;;
        *) extensions="-name '*.txt'" ;;  # fallback
    esac
    
    eval "find '$repo_dir' -type f \\( $extensions \\) 2>/dev/null | wc -l"
}

# Generate repository summary
generate_repository_summary() {
    local repo_dir="$1"
    local language="$2"
    
    echo "Generating repository summary..."
    
    local summary_file="$repo_dir/.analysis_summary.json"
    local file_count=$(count_language_files "$repo_dir" "$language")
    local repo_size_mb=$(du -sm "$repo_dir" 2>/dev/null | cut -f1)
    
    # Count lines using tokei if available
    local line_count="estimated"
    if command -v tokei &> /dev/null; then
        line_count=$(tokei "$repo_dir" --output json 2>/dev/null | jq -r '.Total.code // "0"' || echo "0")
    fi
    
    # Generate summary JSON
    cat > "$summary_file" << EOF
{
    "repository": {
        "name": "$(basename "$repo_dir")",
        "language": "$language",
        "path": "$repo_dir",
        "size_mb": $repo_size_mb,
        "collected_at": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
    },
    "statistics": {
        "file_count": $file_count,
        "line_count": $line_count,
        "optimization_applied": true
    },
    "analysis_ready": true
}
EOF
    
    echo "Summary saved to: $summary_file"
}

# Collect repositories by language priority
collect_repositories_by_priority() {
    echo -e "${BLUE}🎯 Collecting repositories by language priority...${NC}"
    
    # Sort repositories by language priority
    local sorted_repos=()
    
    # Create array of repo_key:priority pairs
    local repo_priorities=()
    for repo_key in "${!REPOSITORY_CATALOG[@]}"; do
        local repo_info="${REPOSITORY_CATALOG[$repo_key]}"
        IFS=':' read -r url name language expected_lines category <<< "$repo_info"
        
        local priority=${LANGUAGE_PRIORITY[$language]:-1}
        repo_priorities+=("$repo_key:$priority")
    done
    
    # Sort by priority (descending)
    IFS=$'\n' sorted_repos=($(sort -t: -k2 -nr <<< "${repo_priorities[*]}" | cut -d: -f1))
    
    echo "Collection order (by priority):"
    for repo_key in "${sorted_repos[@]}"; do
        local repo_info="${REPOSITORY_CATALOG[$repo_key]}"
        IFS=':' read -r url name language expected_lines category <<< "$repo_info"
        local priority=${LANGUAGE_PRIORITY[$language]:-1}
        echo "  $name ($language) - Priority: $priority"
    done
    echo ""
    
    # Collect repositories in priority order
    for repo_key in "${sorted_repos[@]}"; do
        if ! clone_repository "$repo_key"; then
            echo -e "${YELLOW}⚠️  Failed to collect $repo_key, continuing...${NC}"
        fi
        
        # Add small delay to avoid overwhelming git servers
        sleep 2
    done
}

# Collect specific language repositories
collect_language_repositories() {
    local target_language="$1"
    
    echo -e "${BLUE}🎯 Collecting repositories for language: $target_language${NC}"
    
    local found_repos=0
    for repo_key in "${!REPOSITORY_CATALOG[@]}"; do
        local repo_info="${REPOSITORY_CATALOG[$repo_key]}"
        IFS=':' read -r url name language expected_lines category <<< "$repo_info"
        
        if [ "$language" = "$target_language" ]; then
            echo -e "${CYAN}Collecting $name...${NC}"
            if clone_repository "$repo_key"; then
                ((found_repos++))
            fi
        fi
    done
    
    if [ "$found_repos" -eq 0 ]; then
        echo -e "${RED}❌ No repositories found for language: $target_language${NC}"
        return 1
    else
        echo -e "${GREEN}✅ Collected $found_repos repositories for $target_language${NC}"
        return 0
    fi
}

# Generate collection report
generate_collection_report() {
    echo -e "${BLUE}📝 Generating collection report...${NC}"
    
    local report_file="$TEST_DATA_DIR/collection_report_${TIMESTAMP}.md"
    local total_size_mb=$(du -sm "$REPOSITORIES_DIR" 2>/dev/null | cut -f1 || echo "0")
    
    cat > "$report_file" << EOF
# Repository Collection Report

**Collection Date:** $(date)  
**Total Repositories:** $TOTAL_REPOSITORIES  
**Successful:** $SUCCESSFUL_REPOS  
**Failed:** $FAILED_REPOS  
**Skipped:** $SKIPPED_REPOS  

## Summary

- **Total Disk Usage:** ${total_size_mb}MB
- **Success Rate:** $(echo "scale=1; $SUCCESSFUL_REPOS * 100 / $TOTAL_REPOS" | bc -l)%
- **Collection Directory:** $REPOSITORIES_DIR

## Languages Collected

$(for language in "${!LANGUAGE_PRIORITY[@]}"; do
    local count=0
    for repo_key in "${!REPOSITORY_CATALOG[@]}"; do
        local repo_info="${REPOSITORY_CATALOG[$repo_key]}"
        IFS=':' read -r url name lang expected_lines category <<< "$repo_info"
        if [ "$lang" = "$language" ] && [ -d "$REPOSITORIES_DIR/$name" ]; then
            ((count++))
        fi
    done
    echo "- **$language:** $count repositories"
done)

## Repository Details

$(for repo_key in "${!REPOSITORY_CATALOG[@]}"; do
    local repo_info="${REPOSITORY_CATALOG[$repo_key]}"
    IFS=':' read -r url name language expected_lines category <<< "$repo_info"
    
    if [ -d "$REPOSITORIES_DIR/$name" ]; then
        local size_mb=$(du -sm "$REPOSITORIES_DIR/$name" 2>/dev/null | cut -f1)
        local file_count=$(count_language_files "$REPOSITORIES_DIR/$name" "$language")
        
        echo "### $name ($language)"
        echo "- **URL:** $url"
        echo "- **Category:** $category"
        echo "- **Size:** ${size_mb}MB"
        echo "- **Files:** $file_count"
        echo "- **Status:** ✅ Collected"
        echo ""
    else
        echo "### $name ($language)"
        echo "- **URL:** $url"
        echo "- **Category:** $category"
        echo "- **Status:** ❌ Failed"
        echo ""
    fi
done)

## Next Steps

1. **Run Performance Tests:** Use \`test_large_repositories.sh\` to test collected repositories
2. **Validate Results:** Check individual repository summaries in \`.analysis_summary.json\` files
3. **Monitor Usage:** Keep track of disk usage in \`$REPOSITORIES_DIR\`
4. **Update Collection:** Re-run collection periodically to get latest code

## Files Generated

- **Collection Log:** \`$(basename "$LOG_FILE")\`
- **Repository Directory:** \`$REPOSITORIES_DIR\`
- **This Report:** \`$(basename "$report_file")\`

---

*Generated by Analysis Engine Repository Collection Framework*
EOF
    
    echo -e "${GREEN}✅ Collection report generated: $report_file${NC}"
}

# Print final summary
print_final_summary() {
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}                    COLLECTION SUMMARY                              ${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    
    echo -e "Total Repositories: $TOTAL_REPOS"
    echo -e "${GREEN}Successful: $SUCCESSFUL_REPOS${NC}"
    echo -e "${RED}Failed: $FAILED_REPOS${NC}"
    echo -e "${YELLOW}Skipped: $SKIPPED_REPOS${NC}"
    
    if [ "$TOTAL_REPOS" -gt 0 ]; then
        local success_percentage=$(echo "scale=1; $SUCCESSFUL_REPOS * 100 / $TOTAL_REPOS" | bc -l)
        echo -e "Success Rate: ${success_percentage}%"
    fi
    
    local total_size_mb=$(du -sm "$REPOSITORIES_DIR" 2>/dev/null | cut -f1 || echo "0")
    echo -e "Total Size: ${total_size_mb}MB"
    
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    
    # Final recommendation
    if [ "$FAILED_REPOS" -eq 0 ]; then
        echo -e "${GREEN}🎉 All repositories collected successfully!${NC}"
        echo -e "${GREEN}   Ready for performance testing${NC}"
    elif [ "$SUCCESSFUL_REPOS" -gt "$FAILED_REPOS" ]; then
        echo -e "${YELLOW}⚠️  Most repositories collected successfully${NC}"
        echo -e "${YELLOW}   Some failures occurred - check log for details${NC}"
    else
        echo -e "${RED}❌ Significant collection failures occurred${NC}"
        echo -e "${RED}   Check prerequisites and network connectivity${NC}"
    fi
    
    echo -e "\n${BLUE}📁 Collection Directory: $REPOSITORIES_DIR${NC}"
    echo -e "${BLUE}📋 Collection Log: $LOG_FILE${NC}"
}

# Show usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -l, --language LANG     Collect repositories for specific language"
    echo "  -a, --all              Collect all repositories (default)"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Languages:"
    echo "  rust, python, javascript, typescript, java, go, c, cpp,"
    echo "  ruby, php, julia, scala, ocaml, bash, multi"
    echo ""
    echo "Examples:"
    echo "  $0                     # Collect all repositories"
    echo "  $0 -l rust            # Collect only Rust repositories"
    echo "  $0 -l multi           # Collect multi-language repositories"
}

# Main execution function
main() {
    local target_language=""
    local collect_all=true
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -l|--language)
                target_language="$2"
                collect_all=false
                shift 2
                ;;
            -a|--all)
                collect_all=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Initialize collection environment
    initialize_collection
    
    # Collect repositories
    if [ "$collect_all" = true ]; then
        collect_repositories_by_priority
    else
        if [ -z "$target_language" ]; then
            echo -e "${RED}❌ Language not specified${NC}"
            show_usage
            exit 1
        fi
        
        collect_language_repositories "$target_language"
    fi
    
    # Generate collection report
    generate_collection_report
    
    # Print final summary
    print_final_summary
    
    # Exit with appropriate code
    if [ "$FAILED_REPOS" -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi