#!/bin/bash

# Debug script to identify why files are failing to parse

echo "🔍 Debugging Parser Issues"
echo "========================="

# Run performance validator on a small test set and capture errors
echo "Testing with current directory..."
./target/release/performance_validator . 2>&1 | grep -E "(Failed to analyze|Error:|error:)" | head -20

echo ""
echo "Checking specific file types..."

# Test Rust files
echo "Testing Rust file..."
echo 'fn main() { println!("test"); }' > /tmp/test.rs
./target/release/performance_validator /tmp 2>&1 | grep -A2 "test.rs"

# Check if parser is initializing correctly
echo ""
echo "Checking language support..."
curl -s http://localhost:8001/api/v1/languages | jq '.languages[]' | head -10

# Clean up
rm -f /tmp/test.rs