//! Comprehensive load testing benchmarks for production validation
//! Tests the "1M LOC in <5 minutes" claim under production constraints

use analysis_engine::{
    backpressure::{BackpressureConfig, BackpressureManager},
    config::{ResourceOptimizationConfig, ServiceConfig, SystemMonitorConfig},
    monitoring::{ResourceMonitor, SystemMonitor},
    parser::{parallel::ParallelProcessor, TreeSitterParser},
};
use criterion::{black_box, criterion_group, criterion_main, BenchmarkId, Criterion, Throughput};
use std::fs;
use std::path::PathBuf;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tempfile::TempDir;
use tokio::runtime::Runtime;

/// Generate test files of various sizes
fn generate_test_files(dir: &TempDir, file_count: usize, lines_per_file: usize) -> Vec<PathBuf> {
    let mut files = Vec::new();
    
    for i in 0..file_count {
        let content = generate_rust_code(lines_per_file);
        let file_path = dir.path().join(format!("file_{}.rs", i));
        fs::write(&file_path, content).unwrap();
        files.push(file_path);
    }
    
    files
}

/// Generate realistic Rust code
fn generate_rust_code(lines: usize) -> String {
    let mut code = String::new();
    
    // Add imports
    code.push_str("use std::collections::HashMap;\n");
    code.push_str("use std::sync::Arc;\n\n");
    
    // Generate functions
    for i in 0..lines / 20 {
        code.push_str(&format!(
            r#"
/// Function documentation for function_{}
pub fn function_{}(param: i32) -> Result<String, Box<dyn std::error::Error>> {{
    // Complex logic to simulate real code
    let mut result = param;
    for j in 0..10 {{
        result = result.wrapping_mul(2).wrapping_add(j);
        if result > 1000000 {{
            return Err("Value too large".into());
        }}
    }}
    
    // More complex logic
    let data = vec![1, 2, 3, 4, 5];
    let sum: i32 = data.iter().sum();
    
    Ok(format!("Result: {{}}, Sum: {{}}", result, sum))
}}

#[test]
fn test_function_{}() {{
    assert!(function_{}(5).is_ok());
}}
"#,
            i, i, i, i
        ));
    }
    
    code
}

/// Benchmark load testing with resource constraints
fn bench_load_with_constraints(c: &mut Criterion) {
    let runtime = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("load_testing_with_constraints");
    group.sample_size(10); // Reduce sample size for longer benchmarks
    group.measurement_time(Duration::from_secs(60)); // Longer measurement time
    
    // Test configurations
    let test_configs = vec![
        ("2GB_limit", 2048, 1.0),  // 2GB memory, 1 CPU
        ("4GB_limit", 4096, 2.0),  // 4GB memory, 2 CPUs (Cloud Run typical)
        ("8GB_limit", 8192, 4.0),  // 8GB memory, 4 CPUs (higher tier)
    ];
    
    for (name, memory_mb, cpu_cores) in test_configs {
        let temp_dir = TempDir::new().unwrap();
        
        // Generate test files for ~100K LOC
        let files = generate_test_files(&temp_dir, 100, 1000);
        
        group.bench_function(name, |b| {
            b.iter(|| {
                runtime.block_on(async {
                    // Create resource-limited configuration
                    let config = Arc::new(ServiceConfig::from_env().unwrap());
                    let parser = Arc::new(TreeSitterParser::new(config.clone()).unwrap());
                    
                    // Create resource monitor with limits
                    let resource_monitor = Arc::new(ResourceMonitor::with_cpu_limit(
                        memory_mb,
                        300, // 5 minute timeout
                        cpu_cores * 100.0, // CPU percentage
                    ));
                    
                    // Start monitoring
                    resource_monitor.start_monitoring().await;
                    
                    // Create parallel processor
                    let processor = ParallelProcessor::new(parser);
                    
                    // Process files with resource monitoring
                    let start = Instant::now();
                    let results = processor.process_files_parallel(&files, cpu_cores as usize);
                    let duration = start.elapsed();
                    
                    // Check resource limits weren't exceeded
                    let resource_check = resource_monitor.check_limits().await;
                    
                    black_box((results, duration, resource_check))
                })
            });
        });
    }
    
    group.finish();
}

/// Benchmark sustained load for 1M LOC processing
fn bench_1m_loc_sustained(c: &mut Criterion) {
    let runtime = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("1m_loc_sustained_load");
    group.sample_size(5); // Very few samples for this long test
    group.measurement_time(Duration::from_secs(300)); // 5 minutes
    
    // Generate 1M LOC spread across files
    let temp_dir = TempDir::new().unwrap();
    let files = generate_test_files(&temp_dir, 1000, 1000); // 1000 files × 1000 lines = 1M LOC
    
    group.throughput(Throughput::Elements(1_000_000)); // 1M lines
    
    group.bench_function("1m_loc_production", |b| {
        b.iter(|| {
            runtime.block_on(async {
                let config = Arc::new(ServiceConfig::from_env().unwrap());
                let parser = Arc::new(TreeSitterParser::new(config.clone()).unwrap());
                
                // Create system monitor for production constraints
                let system_config = SystemMonitorConfig {
                    cpu_limit_percent: 200.0, // 2 CPUs
                    memory_limit_mb: 4096,    // 4GB (Cloud Run limit)
                    cpu_warning_threshold: 180.0,
                    memory_warning_threshold_percent: 85.0,
                    monitoring_interval: Duration::from_secs(1),
                    enable_process_monitoring: true,
                    enable_disk_monitoring: false,
                    enable_network_monitoring: false,
                };
                let system_monitor = Arc::new(SystemMonitor::new(system_config));
                
                // Create backpressure manager
                let bp_config = BackpressureConfig {
                    max_concurrent_analyses: 50,
                    max_analysis_memory_mb: 4096,
                    cpu_threshold_percent: 180.0,
                    memory_threshold_percent: 85.0,
                    ..Default::default()
                };
                let backpressure = BackpressureManager::with_system_monitor(
                    bp_config,
                    system_monitor.clone(),
                );
                
                // Start monitoring
                system_monitor.start().await.unwrap();
                
                // Process files with production constraints
                let processor = ParallelProcessor::new(parser);
                let start = Instant::now();
                
                let mut total_processed = 0;
                let mut chunks_processed = 0;
                
                // Process in chunks to simulate sustained load
                for chunk in files.chunks(100) {
                    // Check backpressure before processing
                    match backpressure.check_analysis_request().await {
                        analysis_engine::backpressure::BackpressureDecision::Allow => {
                            let results = processor.process_files_parallel(
                                &chunk.to_vec(),
                                4, // 4 threads max
                            );
                            total_processed += results.len();
                            chunks_processed += 1;
                        }
                        analysis_engine::backpressure::BackpressureDecision::Throttle(delay) => {
                            tokio::time::sleep(delay).await;
                        }
                        analysis_engine::backpressure::BackpressureDecision::Reject(_) => {
                            // Skip this chunk under extreme pressure
                            continue;
                        }
                    }
                }
                
                let duration = start.elapsed();
                let throughput = total_processed as f64 * 1000.0 / files.len() as f64;
                
                // Stop monitoring
                system_monitor.stop().await.unwrap();
                
                black_box((total_processed, duration, throughput, chunks_processed))
            })
        });
    });
    
    group.finish();
}

/// Benchmark concurrent load handling
fn bench_concurrent_load(c: &mut Criterion) {
    let runtime = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("concurrent_load_handling");
    group.sample_size(10);
    
    let concurrency_levels = vec![1, 5, 10, 25, 50];
    
    for concurrent_requests in concurrency_levels {
        let temp_dir = TempDir::new().unwrap();
        let files = generate_test_files(&temp_dir, 50, 1000); // 50K LOC per request
        
        group.bench_with_input(
            BenchmarkId::new("concurrent_requests", concurrent_requests),
            &concurrent_requests,
            |b, &concurrent_requests| {
                b.iter(|| {
                    runtime.block_on(async {
                        let config = Arc::new(ServiceConfig::from_env().unwrap());
                        let parser = Arc::new(TreeSitterParser::new(config).unwrap());
                        
                        // Simulate concurrent requests
                        let mut handles = vec![];
                        
                        for _ in 0..concurrent_requests {
                            let parser_clone = parser.clone();
                            let files_clone = files.clone();
                            
                            let handle = tokio::spawn(async move {
                                let processor = ParallelProcessor::new(parser_clone);
                                let start = Instant::now();
                                let results = processor.process_files_parallel(&files_clone, 2);
                                let duration = start.elapsed();
                                (results.len(), duration)
                            });
                            
                            handles.push(handle);
                        }
                        
                        // Wait for all requests to complete
                        let results = futures::future::join_all(handles).await;
                        
                        black_box(results)
                    })
                });
            },
        );
    }
    
    group.finish();
}

/// Benchmark memory pressure scenarios
fn bench_memory_pressure(c: &mut Criterion) {
    let runtime = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("memory_pressure_scenarios");
    group.sample_size(10);
    
    // Test with increasingly large files
    let file_sizes = vec![
        ("small_files", 100, 100),    // 10K LOC total
        ("medium_files", 100, 1000),  // 100K LOC total
        ("large_files", 10, 10000),   // 100K LOC total in fewer files
        ("huge_files", 1, 100000),    // 100K LOC in single file
    ];
    
    for (name, file_count, lines_per_file) in file_sizes {
        let temp_dir = TempDir::new().unwrap();
        let files = generate_test_files(&temp_dir, file_count, lines_per_file);
        
        group.bench_function(name, |b| {
            b.iter(|| {
                runtime.block_on(async {
                    let config = Arc::new(ServiceConfig::from_env().unwrap());
                    let parser = Arc::new(TreeSitterParser::new(config).unwrap());
                    
                    // Create memory-constrained monitor
                    let monitor = ResourceMonitor::new(1024, 60); // 1GB limit
                    monitor.start_monitoring().await;
                    
                    let processor = ParallelProcessor::new(parser);
                    let start = Instant::now();
                    
                    // Process with memory tracking
                    let results = processor.process_files_parallel(&files, 2);
                    
                    // Check memory usage
                    let memory_check = monitor.check_limits().await;
                    let duration = start.elapsed();
                    
                    black_box((results.len(), duration, memory_check))
                })
            });
        });
    }
    
    group.finish();
}

/// Benchmark CPU throttling scenarios
fn bench_cpu_throttling(c: &mut Criterion) {
    let runtime = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("cpu_throttling_scenarios");
    group.sample_size(10);
    
    // Test with different CPU limits
    let cpu_limits = vec![
        ("25_percent", 25.0),
        ("50_percent", 50.0),
        ("100_percent", 100.0),
        ("200_percent", 200.0), // 2 cores
    ];
    
    let temp_dir = TempDir::new().unwrap();
    let files = generate_test_files(&temp_dir, 100, 500); // 50K LOC
    
    for (name, cpu_limit) in cpu_limits {
        group.bench_function(name, |b| {
            b.iter(|| {
                runtime.block_on(async {
                    let config = Arc::new(ServiceConfig::from_env().unwrap());
                    let parser = Arc::new(TreeSitterParser::new(config).unwrap());
                    
                    // Create CPU-limited monitor
                    let monitor = ResourceMonitor::with_cpu_limit(4096, 60, cpu_limit);
                    monitor.start_monitoring().await;
                    
                    let processor = ParallelProcessor::new(parser);
                    
                    // Determine thread count based on CPU limit
                    let thread_count = ((cpu_limit / 100.0).ceil() as usize).max(1);
                    
                    let start = Instant::now();
                    let results = processor.process_files_parallel(&files, thread_count);
                    let duration = start.elapsed();
                    
                    // Check CPU usage
                    let cpu_check = monitor.check_limits().await;
                    
                    black_box((results.len(), duration, cpu_check))
                })
            });
        });
    }
    
    group.finish();
}

criterion_group!(
    benches,
    bench_load_with_constraints,
    bench_1m_loc_sustained,
    bench_concurrent_load,
    bench_memory_pressure,
    bench_cpu_throttling
);
criterion_main!(benches);