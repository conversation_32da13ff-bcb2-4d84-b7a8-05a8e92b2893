use std::sync::Arc;
use tempfile::TempDir;
use std::fs;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Testing Core Functionality: Can we analyze a repository?");
    
    // Create test config
    let config = Arc::new(create_test_config());
    
    // Create test services
    let cache_manager = Arc::new(analysis_engine::storage::CacheManager::new_for_testing().await?);
    
    // Create analysis service with minimal dependencies
    let analysis_service = analysis_engine::services::analyzer::AnalysisService::new_for_testing(
        None, // No Spanner
        cache_manager,
        config,
    ).await?;
    
    // Create a small test repository
    let test_repo = create_test_repository()?;
    
    println!("📂 Created test repository at: {}", test_repo.path().display());
    
    // Test the core functionality
    println!("🔍 Starting repository analysis...");
    let start_time = std::time::Instant::now();
    
    let result = analysis_service.analyze_repository_for_testing(
        test_repo.path().to_str().unwrap()
    ).await;
    
    let duration = start_time.elapsed();
    
    match result {
        Ok(analysis_result) => {
            println!("✅ SUCCESS! Repository analysis completed in {:?}", duration);
            println!("📊 Results:");
            println!("  - Files analyzed: {}", analysis_result.file_count);
            println!("  - Success rate: {:.1}%", analysis_result.success_rate);
            println!("  - Languages detected: {}", analysis_result.languages.len());
            
            if let Some(metrics) = &analysis_result.metrics {
                println!("  - Total lines: {}", metrics.total_lines);
                println!("  - Total files: {}", metrics.total_files);
            }
            
            if let Some(perf_metrics) = &analysis_result.performance_metrics {
                println!("  - Processing time: {}ms", perf_metrics.total_processing_ms);
                println!("  - Successful parses: {}", perf_metrics.successful_parses);
                println!("  - Failed parses: {}", perf_metrics.failed_parses);
            }
            
            println!("\n🎯 CORE FUNCTIONALITY: ✅ WORKING");
            println!("The analysis engine can successfully analyze repositories!");
        }
        Err(e) => {
            println!("❌ FAILED! Repository analysis failed: {}", e);
            println!("🎯 CORE FUNCTIONALITY: ❌ BROKEN");
            return Err(e.into());
        }
    }
    
    Ok(())
}

fn create_test_config() -> analysis_engine::config::ServiceConfig {
    analysis_engine::config::ServiceConfig {
        service: analysis_engine::config::ServiceSettings {
            name: "analysis-engine-test".to_string(),
            version: "0.1.0".to_string(),
            port: 8001,
            host: "127.0.0.1".to_string(),
            environment: analysis_engine::config::Environment::Development,
        },
        gcp: analysis_engine::config::GcpSettings {
            project_id: "test-project".to_string(),
            spanner_instance: "test-instance".to_string(),
            spanner_database: "test-database".to_string(),
            storage_bucket: "test-bucket".to_string(),
            storage_bucket_name: "test-bucket-name".to_string(),
            pubsub_topic: "test-topic".to_string(),
            region: "us-central1".to_string(),
        },
        analysis: analysis_engine::config::AnalysisSettings {
            max_concurrent_analyses: 10,
            max_repository_size_gb: 1,
            analysis_timeout_seconds: 300,
            max_file_size_mb: 10,
            temp_dir: "/tmp/test".to_string(),
            supported_languages: vec![
                "rust".to_string(),
                "python".to_string(),
                "javascript".to_string(),
                "typescript".to_string(),
            ],
        },
        security: analysis_engine::config::SecuritySettings {
            enable_auth: false,
            api_key_header: "x-api-key".to_string(),
            jwt_secret: Some("test-secret".to_string()),
            cors_origins: vec!["*".to_string()],
        },
        observability: analysis_engine::config::ObservabilitySettings {
            enable_tracing: false,
            enable_metrics: false,
            log_level: "info".to_string(),
            otel_endpoint: None,
        },
        circuit_breaker: Default::default(),
        redis: Default::default(),
        resource_limits: Default::default(),
    }
}

fn create_test_repository() -> Result<TempDir, Box<dyn std::error::Error>> {
    let temp_dir = TempDir::new()?;
    let repo_path = temp_dir.path();
    
    // Create some test files
    fs::create_dir_all(repo_path.join("src"))?;
    
    // Create a Rust file
    fs::write(
        repo_path.join("src/main.rs"),
        r#"
fn main() {
    println!("Hello, world!");
}

fn add(a: i32, b: i32) -> i32 {
    a + b
}

struct Config {
    name: String,
    value: i32,
}

impl Config {
    fn new(name: String, value: i32) -> Self {
        Self { name, value }
    }
}

#[test]
fn test_add() {
    assert_eq!(add(2, 2), 4);
}
"#,
    )?;
    
    // Create a Python file
    fs::write(
        repo_path.join("src/helper.py"),
        r#"
def calculate_sum(a, b):
    return a + b

class Calculator:
    def __init__(self):
        self.result = 0
    
    def add(self, value):
        self.result += value
        return self.result

if __name__ == "__main__":
    calc = Calculator()
    print(calc.add(5))
"#,
    )?;
    
    // Create a JavaScript file
    fs::write(
        repo_path.join("src/utils.js"),
        r#"
function processData(data) {
    return data.map(item => item.value * 2);
}

class DataProcessor {
    constructor() {
        this.cache = new Map();
    }
    
    process(key, data) {
        if (this.cache.has(key)) {
            return this.cache.get(key);
        }
        
        const result = processData(data);
        this.cache.set(key, result);
        return result;
    }
}

module.exports = { DataProcessor, processData };
"#,
    )?;
    
    // Create a README
    fs::write(
        repo_path.join("README.md"),
        r#"# Test Repository

This is a test repository for validating the analysis engine.

## Features

- Rust code analysis
- Python code analysis  
- JavaScript code analysis
- Multi-language support
"#,
    )?;
    
    Ok(temp_dir)
}