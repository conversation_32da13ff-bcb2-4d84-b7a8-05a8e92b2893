#!/bin/bash

# Performance Testing Script for Analysis Engine
# Tests the "1M LOC in <5 minutes" claim with real repositories

set -e

echo "🚀 Analysis Engine Performance Testing"
echo "====================================="

# Set required environment variables
export GCP_PROJECT_ID="test-project"
export SPANNER_INSTANCE="test-instance"
export SPANNER_DATABASE="test-database"
export REDIS_URL="redis://localhost:6379"

# Create test data directory
mkdir -p test-data/repositories

# Function to clone and test a repository
test_repository() {
    local repo_url=$1
    local repo_name=$2
    local expected_lines=$3
    
    echo "🔍 Testing $repo_name"
    echo "Repository: $repo_url"
    echo "Expected lines: ~$expected_lines"
    
    # Clone repository if it doesn't exist
    if [ ! -d "test-data/repositories/$repo_name" ]; then
        echo "📥 Cloning repository..."
        git clone --depth 1 "$repo_url" "test-data/repositories/$repo_name"
    fi
    
    # Build the performance validator
    echo "🔨 Building performance validator..."
    cargo build --release --bin performance_validator
    
    # Run performance test
    echo "⏱️  Running performance test..."
    ./target/release/performance_validator "test-data/repositories/$repo_name"
    
    # Verify with external tool
    echo "🔍 Verifying with tokei..."
    if command -v tokei &> /dev/null; then
        tokei "test-data/repositories/$repo_name"
    else
        echo "⚠️  tokei not found, skipping external verification"
    fi
    
    echo "✅ Completed testing $repo_name"
    echo "----------------------------------------"
}

# Test with progressively larger repositories
echo "📋 Testing with real repositories..."

# Small repository (~10K LOC)
test_repository "https://github.com/actix/actix-web" "actix-web" "10000"

# Medium repository (~100K LOC)
test_repository "https://github.com/tokio-rs/tokio" "tokio" "100000"

# Large repository (~500K LOC)
test_repository "https://github.com/rust-lang/rust" "rust" "500000"

# Analyze combined results
echo "📊 COMBINED PERFORMANCE ANALYSIS"
echo "================================"

# Calculate total lines across all repositories
total_lines=0
for repo_dir in test-data/repositories/*; do
    if [ -d "$repo_dir" ]; then
        lines=$(find "$repo_dir" -name "*.rs" -exec wc -l {} + | tail -1 | awk '{print $1}')
        total_lines=$((total_lines + lines))
        echo "📁 $(basename $repo_dir): $lines lines"
    fi
done

echo "📝 Total lines across all repositories: $total_lines"

# Test all repositories combined
echo "🎯 Testing combined repository performance..."
mkdir -p test-data/combined
for repo_dir in test-data/repositories/*; do
    if [ -d "$repo_dir" ]; then
        cp -r "$repo_dir"/* test-data/combined/ 2>/dev/null || true
    fi
done

./target/release/performance_validator "test-data/combined"

echo "✅ Performance testing completed!"
echo "📊 Check performance_results.json for detailed metrics"

# Generate summary report
echo "📋 PERFORMANCE VALIDATION SUMMARY"
echo "================================="
echo "Claim: Process 1M LOC in under 5 minutes (300 seconds)"
echo "Target throughput: 3,333 LOC/second minimum"
echo ""
echo "Results available in performance_results.json"
echo "Manual verification completed with tokei"
echo "Ready for production deployment assessment"