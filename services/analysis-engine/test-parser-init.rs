use tree_sitter::{Language, Parser};

// Import the language loading functions
include!(concat!(env!("OUT_DIR"), "/language_bindings.rs"));

fn test_language_initialization(name: &str) -> Result<(), String> {
    println!("Testing language: {}", name);
    
    // Try to get the language
    let language = match get_language(name) {
        Some(lang) => lang,
        None => return Err(format!("Failed to get language for {}", name)),
    };
    
    // Try to create a parser and set the language
    let mut parser = Parser::new();
    match parser.set_language(&language) {
        Ok(()) => {
            println!("✓ {} initialized successfully", name);
            Ok(())
        }
        Err(e) => {
            println!("✗ {} failed to initialize: {:?}", name, e);
            Err(format!("Failed to set language {}: {:?}", name, e))
        }
    }
}

fn main() {
    println!("Testing parser initialization for all languages...\n");
    
    let mut success_count = 0;
    let mut failure_count = 0;
    let mut failures = Vec::new();
    
    for language in SUPPORTED_LANGUAGES {
        match test_language_initialization(language) {
            Ok(()) => success_count += 1,
            Err(e) => {
                failure_count += 1;
                failures.push((language, e));
            }
        }
    }
    
    println!("\n=== Summary ===");
    println!("Total languages: {}", SUPPORTED_LANGUAGES.len());
    println!("Successful: {}", success_count);
    println!("Failed: {}", failure_count);
    
    if !failures.is_empty() {
        println!("\nFailures:");
        for (lang, err) in failures {
            println!("  - {}: {}", lang, err);
        }
    }
    
    // Test a simple parse
    if let Some(rust_lang) = get_language("rust") {
        let mut parser = Parser::new();
        if parser.set_language(&rust_lang).is_ok() {
            let code = "fn main() { println!(\"Hello\"); }";
            if let Some(tree) = parser.parse(code, None) {
                println!("\nTest parse successful! Root node: {:?}", tree.root_node().kind());
            }
        }
    }
}