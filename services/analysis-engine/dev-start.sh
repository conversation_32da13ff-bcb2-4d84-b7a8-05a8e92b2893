#!/bin/bash

# Development startup script for Analysis Engine
# This script sets all necessary environment variables for local development

echo "🚀 Starting Analysis Engine in Development Mode"
echo "============================================="

# Load environment variables from .env file
source .env

# Override specific settings for development
export ENVIRONMENT=development
export RUST_LOG=info
export PORT=8001
export ENABLE_AUTH=false

echo ""
echo "🎯 Environment configured for development"
echo "   - Service will run on port 8001"
echo "   - Environment variables loaded from .env"
echo "   - GCP services will show warnings but continue"
echo "   - Redis optional (service continues without it)"
echo "   - Authentication disabled"
echo ""

# Start the service
echo "🚀 Starting Analysis Engine..."
./target/release/analysis-engine