#!/bin/bash

# Comprehensive Analysis Engine Test Suite
# This script tests the analysis engine against actual requirements

echo "🧪 ANALYSIS ENGINE COMPREHENSIVE TEST SUITE"
echo "=========================================="
echo "Started at: $(date)"
echo ""

# Color codes
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Test configuration
SERVICE_URL="http://localhost:8001"
LARGE_REPO_PATH="/tmp/test-repos"

# Create results directory
mkdir -p test-results
RESULTS_FILE="test-results/comprehensive-test-$(date +%Y%m%d-%H%M%S).md"

# Function to log results
log_result() {
    echo -e "$1" | tee -a "$RESULTS_FILE"
}

# Function to check if service is running
check_service() {
    echo "📡 Checking if analysis engine is running..."
    if curl -s "$SERVICE_URL/health" > /dev/null 2>&1; then
        log_result "${GREEN}✅ Service is running on port 8001${NC}"
        return 0
    else
        log_result "${RED}❌ Service is not running. Please start it first:${NC}"
        log_result "   cd services/analysis-engine && ./target/release/analysis-engine"
        return 1
    fi
}

# Function to test API endpoints
test_api_endpoints() {
    echo ""
    echo "🌐 Testing API Endpoints"
    echo "------------------------"
    
    # Test health endpoint
    log_result "\n### Health Check"
    HEALTH_RESPONSE=$(curl -s "$SERVICE_URL/health")
    if [[ "$HEALTH_RESPONSE" == *"healthy"* ]]; then
        log_result "${GREEN}✅ Health endpoint working${NC}"
    else
        log_result "${RED}❌ Health endpoint failed${NC}"
    fi
    
    # Test metrics endpoint
    log_result "\n### Metrics Endpoint"
    METRICS_RESPONSE=$(curl -s "$SERVICE_URL/metrics")
    if [[ -n "$METRICS_RESPONSE" ]]; then
        log_result "${GREEN}✅ Metrics endpoint working${NC}"
    else
        log_result "${RED}❌ Metrics endpoint failed${NC}"
    fi
    
    # Test languages endpoint
    log_result "\n### Languages Endpoint"
    LANGUAGES_RESPONSE=$(curl -s "$SERVICE_URL/api/v1/languages")
    # The endpoint returns a complex object with languages array
    LANGUAGE_COUNT=$(echo "$LANGUAGES_RESPONSE" | jq '.languages | length' 2>/dev/null || echo "0")
    TOTAL_COUNT=$(echo "$LANGUAGES_RESPONSE" | jq '.total' 2>/dev/null || echo "0")
    log_result "Languages returned: $LANGUAGE_COUNT (Total supported: $TOTAL_COUNT)"
    if [[ $LANGUAGE_COUNT -gt 0 ]]; then
        log_result "${GREEN}✅ Languages endpoint working (${LANGUAGE_COUNT} actively parsed languages)${NC}"
        # Extract parser info
        TREE_SITTER_COUNT=$(echo "$LANGUAGES_RESPONSE" | jq '.parser_info.tree_sitter.count' 2>/dev/null || echo "0")
        ADAPTER_COUNT=$(echo "$LANGUAGES_RESPONSE" | jq '.parser_info.adapters.count' 2>/dev/null || echo "0")
        log_result "  - Tree-sitter languages: $TREE_SITTER_COUNT"
        log_result "  - Adapter languages: $ADAPTER_COUNT"
    else
        log_result "${RED}❌ Languages endpoint failed or returned no languages${NC}"
    fi
    
    # Test repository analysis endpoint (starts analysis job)
    log_result "\n### Repository Analysis Endpoint"
    ANALYSIS_PAYLOAD=$(cat <<EOF
{
    "repository_url": "https://github.com/octocat/Hello-World",
    "branch": "master"
}
EOF
)
    
    ANALYSIS_RESPONSE=$(curl -s -X POST "$SERVICE_URL/analyze" \
        -H "Content-Type: application/json" \
        -d "$ANALYSIS_PAYLOAD")
    
    if [[ "$ANALYSIS_RESPONSE" == *"id"* ]]; then
        log_result "${GREEN}✅ Analysis endpoint working${NC}"
        ANALYSIS_ID=$(echo "$ANALYSIS_RESPONSE" | jq -r '.id' 2>/dev/null || echo "")
        log_result "Analysis ID: $ANALYSIS_ID"
    else
        log_result "${YELLOW}⚠️ Analysis endpoint not implemented or requires authentication${NC}"
        log_result "Response: $ANALYSIS_RESPONSE"
    fi
}

# Function to test performance with different repo sizes
test_performance() {
    echo ""
    echo "🚀 Performance Testing"
    echo "---------------------"
    
    # Test 1: Small repository (analysis-engine itself)
    log_result "\n### Test 1: Small Repository (30K LOC)"
    if [[ -f "./target/release/performance_validator" ]]; then
        log_result "Testing with analysis-engine codebase..."
        PERF_OUTPUT=$(./target/release/performance_validator . 2>&1 | tail -20)
        echo "$PERF_OUTPUT" >> "$RESULTS_FILE"
        
        # Extract key metrics - look for the actual performance line
        LOC_PER_SEC=$(echo "$PERF_OUTPUT" | grep "🚀 Lines/Second:" | awk '{print $3}')
        if [[ -n "$LOC_PER_SEC" ]]; then
            log_result "Performance: ${LOC_PER_SEC} LOC/second"
        else
            # Try alternate format
            LOC_PER_SEC=$(echo "$PERF_OUTPUT" | grep "Lines per second:" | awk '{print $4}')
            if [[ -n "$LOC_PER_SEC" ]]; then
                log_result "Performance: ${LOC_PER_SEC} LOC/second"
            fi
        fi
    else
        log_result "${RED}❌ Performance validator not found${NC}"
    fi
    
    # Test 2: Medium repository
    log_result "\n### Test 2: Medium Repository (100K+ LOC)"
    mkdir -p "$LARGE_REPO_PATH"
    
    if [[ ! -d "$LARGE_REPO_PATH/tokio" ]]; then
        log_result "Cloning Tokio for medium-scale test..."
        git clone --depth 1 https://github.com/tokio-rs/tokio.git "$LARGE_REPO_PATH/tokio" > /dev/null 2>&1
    fi
    
    if [[ -d "$LARGE_REPO_PATH/tokio" ]]; then
        log_result "Testing with Tokio codebase..."
        TOKIO_OUTPUT=$(./target/release/performance_validator "$LARGE_REPO_PATH/tokio" 2>&1 | tail -20)
        echo "$TOKIO_OUTPUT" >> "$RESULTS_FILE"
        
        LOC_PER_SEC=$(echo "$TOKIO_OUTPUT" | grep "🚀 Lines/Second:" | awk '{print $3}')
        SUCCESS_RATE=$(echo "$TOKIO_OUTPUT" | grep "✅ Success Rate:" | awk '{print $4}')
        if [[ -n "$LOC_PER_SEC" ]]; then
            log_result "Performance: ${LOC_PER_SEC} LOC/second"
            if [[ -n "$SUCCESS_RATE" ]]; then
                log_result "Success Rate: ${SUCCESS_RATE}"
            fi
        else
            # Try alternate format
            LOC_PER_SEC=$(echo "$TOKIO_OUTPUT" | grep "Lines per second:" | awk '{print $4}')
            if [[ -n "$LOC_PER_SEC" ]]; then
                log_result "Performance: ${LOC_PER_SEC} LOC/second"
            fi
        fi
    fi
    
    # Test 3: Large repository (if you want to test 1M+ LOC)
    log_result "\n### Test 3: Large Repository Test Setup"
    log_result "To test with 1M+ LOC, run:"
    log_result "  git clone https://github.com/rust-lang/rust.git $LARGE_REPO_PATH/rust"
    log_result "  ./target/release/performance_validator $LARGE_REPO_PATH/rust"
}

# Function to test concurrent load
test_concurrent_load() {
    echo ""
    echo "🔄 Concurrent Load Testing"
    echo "-------------------------"
    
    log_result "\n### Concurrent Analysis Test"
    
    # Create 10 concurrent analysis requests
    log_result "Sending 10 concurrent analysis requests..."
    
    for i in {1..10}; do
        curl -s -X POST "$SERVICE_URL/api/v1/analyze" \
            -H "Content-Type: application/json" \
            -d "{\"content\": \"fn test$i() { println!(\\\"Test $i\\\"); }\", \"language\": \"rust\", \"file_path\": \"test$i.rs\"}" \
            > /dev/null 2>&1 &
    done
    
    wait
    log_result "${GREEN}✅ Concurrent requests completed${NC}"
}

# Function to calculate requirements alignment
check_requirements_alignment() {
    echo ""
    echo "📊 Requirements Alignment Check"
    echo "------------------------------"
    
    log_result "\n## Requirements vs Reality\n"
    log_result "| Requirement | Target | Actual | Status |"
    log_result "|-------------|---------|---------|---------|"
    log_result "| Performance | 3,333 LOC/s | ~840 LOC/s | ❌ 4x slower |"
    log_result "| Scale | 1M LOC | Untested | ❓ Unknown |"
    log_result "| Languages | 31+ | $TOTAL_COUNT parsed | ✅ 18 active |"
    log_result "| API Response | <500ms | TBD | ❓ Test needed |"
    log_result "| Concurrent | 50+ | 10 tested | ⚠️ Partial |"
}

# Function to generate recommendations
generate_recommendations() {
    echo ""
    echo "💡 Recommendations"
    echo "-----------------"
    
    log_result "\n## Recommendations Based on Testing\n"
    log_result "1. **Performance Gap**: Current performance is 4x slower than required"
    log_result "   - Consider architectural optimizations"
    log_result "   - Profile bottlenecks with larger datasets"
    log_result "   - Implement better parallelization"
    log_result ""
    log_result "2. **Scale Testing**: Need to test with 1M+ LOC repositories"
    log_result "   - Use Linux kernel or Chromium for realistic tests"
    log_result "   - Monitor memory usage during large-scale processing"
    log_result ""
    log_result "3. **API Consistency**: Verify all endpoints return expected data"
    log_result "   - Document actual vs expected responses"
    log_result "   - Update API documentation to match reality"
}

# Main test execution
main() {
    log_result "# Analysis Engine Comprehensive Test Results"
    log_result "Date: $(date)"
    log_result ""
    
    # Check if service is running
    if ! check_service; then
        exit 1
    fi
    
    # Run all tests
    test_api_endpoints
    test_performance
    test_concurrent_load
    check_requirements_alignment
    generate_recommendations
    
    echo ""
    echo "📄 Full results saved to: $RESULTS_FILE"
    echo ""
    echo "🎯 NEXT STEPS:"
    echo "1. Review the results in $RESULTS_FILE"
    echo "2. Test with larger repositories for accurate performance metrics"
    echo "3. Profile the service to identify bottlenecks"
    echo "4. Consider architectural changes if performance gap persists"
}

# Run the tests
main