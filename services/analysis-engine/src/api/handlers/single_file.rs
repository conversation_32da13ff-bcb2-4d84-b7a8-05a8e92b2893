use crate::api::{errors::ApiError, AppState};
use axum::{extract::State, response::IntoResponse, Json};
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::sync::Arc;
use tracing::{debug, error, instrument};

#[derive(Debug, Deserialize)]
pub struct SingleFileAnalysisRequest {
    pub content: String,
    pub language: String,
    pub file_path: String,
}

#[derive(Debug, Serialize)]
pub struct SingleFileAnalysisResponse {
    pub ast: serde_json::Value,
    pub metrics: FileMetrics,
    pub language: String,
    pub file_path: String,
    pub parsed_at: String,
}

#[derive(Debug, Serialize)]
pub struct FileMetrics {
    pub total_lines: usize,
    pub code_lines: usize,
    pub comment_lines: usize,
    pub blank_lines: usize,
    pub complexity: u32,
}

/// Analyze a single file and return AST and metrics
#[instrument(skip_all)]
pub async fn analyze_single_file(
    State(_state): State<Arc<AppState>>,
    Json(request): Json<SingleFileAnalysisRequest>,
) -> Result<impl IntoResponse, ApiError> {
    debug!(
        "Analyzing single file: {} (language: {})",
        request.file_path, request.language
    );

    // Get the parser for the specified language
    let language = crate::parser::get_language(&request.language)
        .ok_or_else(|| {
            ApiError::BadRequest(format!("Unsupported language: {}", request.language))
        })?;

    // Create a parser
    let mut parser = tree_sitter::Parser::new();
    parser.set_language(&language).map_err(|e| {
        error!("Failed to set parser language: {}", e);
        ApiError::InternalError("Failed to initialize parser".to_string())
    })?;

    // Parse the content
    let tree = parser.parse(&request.content, None).ok_or_else(|| {
        error!("Failed to parse file content");
        ApiError::InternalError("Failed to parse content".to_string())
    })?;

    // Extract metrics
    let lines = request.content.lines().count();
    let code_lines = request.content
        .lines()
        .filter(|line| {
            let trimmed = line.trim();
            !trimmed.is_empty() && !trimmed.starts_with("//") && !trimmed.starts_with("#")
        })
        .count();
    let blank_lines = request.content.lines().filter(|line| line.trim().is_empty()).count();
    let comment_lines = lines.saturating_sub(code_lines).saturating_sub(blank_lines);

    // Convert AST to simplified format
    let ast_json = ast_to_json(&tree.root_node(), &request.content);

    let metrics = FileMetrics {
        total_lines: lines,
        code_lines,
        comment_lines,
        blank_lines,
        complexity: estimate_complexity(&tree.root_node()),
    };

    let response = SingleFileAnalysisResponse {
        ast: ast_json,
        metrics,
        language: request.language,
        file_path: request.file_path,
        parsed_at: chrono::Utc::now().to_rfc3339(),
    };

    Ok(Json(response))
}

/// Convert tree-sitter AST node to JSON
fn ast_to_json(node: &tree_sitter::Node, source: &str) -> serde_json::Value {
    let mut obj = json!({
        "type": node.kind(),
        "start_position": {
            "row": node.start_position().row,
            "column": node.start_position().column,
        },
        "end_position": {
            "row": node.end_position().row,
            "column": node.end_position().column,
        },
    });

    // Add text for leaf nodes
    if node.child_count() == 0 {
        if let Ok(text) = node.utf8_text(source.as_bytes()) {
            obj["text"] = json!(text);
        }
    }

    // Add children
    if node.child_count() > 0 {
        let children: Vec<_> = (0..node.child_count())
            .filter_map(|i| node.child(i))
            .map(|child| ast_to_json(&child, source))
            .collect();
        obj["children"] = json!(children);
    }

    obj
}

/// Estimate complexity based on AST
fn estimate_complexity(node: &tree_sitter::Node) -> u32 {
    let mut complexity = 1;
    
    // Count control flow statements
    match node.kind() {
        "if_statement" | "if_expression" => complexity += 1,
        "else_clause" | "else_if_clause" => complexity += 1,
        "for_statement" | "for_expression" => complexity += 1,
        "while_statement" | "while_expression" => complexity += 1,
        "match_statement" | "match_expression" => complexity += 1,
        "match_arm" => complexity += 1,
        "logical_and" | "logical_or" => complexity += 1,
        "ternary_expression" => complexity += 1,
        _ => {}
    }
    
    // Recursively count for children
    for i in 0..node.child_count() {
        if let Some(child) = node.child(i) {
            complexity += estimate_complexity(&child);
        }
    }
    
    complexity
}