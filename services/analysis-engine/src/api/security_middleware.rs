//! Advanced security middleware for production hardening
//!
//! This module provides comprehensive security middleware including:
//! - Request/Response filtering and sanitization
//! - Advanced rate limiting with IP-based tracking
//! - Threat detection and blocking
//! - Security headers and CSRF protection
//! - Request fingerprinting and anomaly detection

use crate::api::{
    errors::{ErrorResponse, ErrorType},
    validation::InputValidator,
    AppState,
};
use crate::audit::{AuditAction, AuditEventBuilder, AuditLogger, AuditOutcome, AuditSeverity};
use axum::{
    extract::{Request, State},
    http::{HeaderMap, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
    Json,
};
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use std::net::IpAddr;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use uuid::Uuid;

lazy_static::lazy_static! {
    /// Global IP-based rate limiting cache
    static ref IP_RATE_LIMITS: DashMap<IpAddr, IpRateLimitEntry> = DashMap::new();
    
    /// Threat detection patterns
    static ref THREAT_PATTERNS: DashMap<String, ThreatPattern> = DashMap::new();
    
    /// Blocked IPs cache
    static ref BLOCKED_IPS: DashMap<IpAddr, BlockedIpEntry> = DashMap::new();
    
    /// Request fingerprinting cache
    static ref REQUEST_FINGERPRINTS: DashMap<String, RequestFingerprint> = DashMap::new();
}

/// IP-based rate limit entry
#[derive(Debug, Clone)]
struct IpRateLimitEntry {
    count: u64,
    window_start: Instant,
    violations: u32,
    last_violation: Option<Instant>,
}

/// Threat pattern for detection
#[derive(Debug, Clone)]
struct ThreatPattern {
    pattern: String,
    severity: ThreatSeverity,
    block_duration: Duration,
    description: String,
}

/// Blocked IP entry
#[derive(Debug, Clone)]
struct BlockedIpEntry {
    blocked_at: Instant,
    expires_at: Instant,
    reason: String,
    violation_count: u32,
}

/// Request fingerprint for anomaly detection
#[derive(Debug, Clone)]
struct RequestFingerprint {
    user_agent: String,
    headers_hash: String,
    request_count: u64,
    first_seen: Instant,
    last_seen: Instant,
    suspicious_patterns: Vec<String>,
}

/// Threat severity levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ThreatSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Security middleware configuration
#[derive(Debug, Clone)]
pub struct SecurityMiddlewareConfig {
    pub enable_ip_rate_limiting: bool,
    pub enable_threat_detection: bool,
    pub enable_request_fingerprinting: bool,
    pub enable_security_headers: bool,
    pub enable_csrf_protection: bool,
    pub max_requests_per_minute: u64,
    pub max_requests_per_hour: u64,
    pub threat_detection_threshold: u32,
    pub auto_block_duration: Duration,
    pub fingerprint_cache_size: usize,
}

impl Default for SecurityMiddlewareConfig {
    fn default() -> Self {
        Self {
            enable_ip_rate_limiting: true,
            enable_threat_detection: true,
            enable_request_fingerprinting: true,
            enable_security_headers: true,
            enable_csrf_protection: true,
            max_requests_per_minute: 1000,
            max_requests_per_hour: 10000,
            threat_detection_threshold: 5,
            auto_block_duration: Duration::from_secs(24 * 60 * 60),
            fingerprint_cache_size: 10000,
        }
    }
}

/// Security middleware for comprehensive protection
pub struct SecurityMiddleware {
    config: SecurityMiddlewareConfig,
    validator: InputValidator,
}

impl SecurityMiddleware {
    /// Create new security middleware with default configuration
    pub fn new() -> Self {
        Self {
            config: SecurityMiddlewareConfig::default(),
            validator: InputValidator::production_config(),
        }
    }

    /// Create security middleware with custom configuration
    pub fn with_config(config: SecurityMiddlewareConfig) -> Self {
        Self {
            config,
            validator: InputValidator::production_config(),
        }
    }

    /// Initialize threat detection patterns
    pub fn init_threat_patterns() {
        let patterns = vec![
            ThreatPattern {
                pattern: r"(?i)(union|select|insert|update|delete|drop|create|alter)".to_string(),
                severity: ThreatSeverity::High,
                block_duration: Duration::from_secs(2 * 60 * 60),
                description: "SQL injection attempt".to_string(),
            },
            ThreatPattern {
                pattern: r"(?i)(script|javascript|vbscript|onload|onerror)".to_string(),
                severity: ThreatSeverity::High,
                block_duration: Duration::from_secs(2 * 60 * 60),
                description: "XSS attempt".to_string(),
            },
            ThreatPattern {
                pattern: r"(?i)(\.\.\/|\.\.\\|\/etc\/|\/proc\/|\/sys\/)".to_string(),
                severity: ThreatSeverity::Critical,
                block_duration: Duration::from_secs(24 * 60 * 60),
                description: "Path traversal attempt".to_string(),
            },
            ThreatPattern {
                pattern: r"(?i)(cmd|powershell|bash|sh|exec|system|eval)".to_string(),
                severity: ThreatSeverity::Critical,
                block_duration: Duration::from_secs(24 * 60 * 60),
                description: "Command injection attempt".to_string(),
            },
            ThreatPattern {
                pattern: r"(?i)(null|nil|undefined|0x|%00|%2e%2e)".to_string(),
                severity: ThreatSeverity::Medium,
                block_duration: Duration::from_secs(60 * 60),
                description: "Null byte injection or encoding bypass".to_string(),
            },
        ];

        for pattern in patterns {
            THREAT_PATTERNS.insert(pattern.pattern.clone(), pattern);
        }
    }
}

impl Default for SecurityMiddleware {
    fn default() -> Self {
        Self::new()
    }
}

/// Main security middleware function
pub async fn security_middleware(
    State(state): State<Arc<AppState>>,
    mut request: Request,
    next: Next,
) -> Response {
    let start_time = Instant::now();
    let request_id = Uuid::new_v4().to_string();
    
    // Extract IP address from headers or use localhost fallback
    let client_ip = extract_client_ip(&request);
    
    // Initialize threat patterns on first request
    if THREAT_PATTERNS.is_empty() {
        SecurityMiddleware::init_threat_patterns();
    }

    // Check if IP is blocked
    if let Some(blocked_entry) = BLOCKED_IPS.get(&client_ip) {
        if Instant::now() < blocked_entry.expires_at {
            let error = ErrorResponse::new(
                ErrorType::Authorization,
                "IP address is temporarily blocked due to security violations".to_string(),
            );
            
            // Log security event
            log_security_event(
                &state,
                SecurityEventType::BlockedIpRequest,
                &client_ip,
                &request_id,
                format!("Blocked IP attempted request: {}", blocked_entry.reason),
            ).await;

            return (StatusCode::FORBIDDEN, Json(error)).into_response();
        } else {
            // Remove expired block
            BLOCKED_IPS.remove(&client_ip);
        }
    }

    // IP-based rate limiting
    if let Err(response) = check_ip_rate_limit(&client_ip, &state, &request_id).await {
        return response;
    }

    // Request fingerprinting and anomaly detection
    if let Err(response) = check_request_fingerprint(&request, &client_ip, &state, &request_id).await {
        return response;
    }

    // Threat detection
    if let Err(response) = check_threat_patterns(&request, &client_ip, &state, &request_id).await {
        return response;
    }

    // Add security headers to request
    add_security_headers(&mut request);

    // Process request
    let mut response = next.run(request).await;

    // Add security headers to response
    add_response_security_headers(&mut response);

    // Log successful request
    let processing_time = start_time.elapsed();
    tracing::debug!(
        "Security middleware processed request {} from {} in {:?}",
        request_id,
        client_ip,
        processing_time
    );

    response
}

/// Extract client IP address from request headers
fn extract_client_ip(request: &Request) -> IpAddr {
    // Check common headers for client IP
    if let Some(forwarded_for) = request.headers().get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded_for.to_str() {
            if let Some(ip_str) = forwarded_str.split(',').next() {
                if let Ok(ip) = ip_str.trim().parse::<IpAddr>() {
                    return ip;
                }
            }
        }
    }
    
    if let Some(real_ip) = request.headers().get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            if let Ok(ip) = ip_str.parse::<IpAddr>() {
                return ip;
            }
        }
    }
    
    // Fallback to localhost
    "127.0.0.1".parse().unwrap()
}

/// Check IP-based rate limiting
async fn check_ip_rate_limit(
    client_ip: &IpAddr,
    state: &Arc<AppState>,
    request_id: &str,
) -> Result<(), Response> {
    let now = Instant::now();
    let window_duration = Duration::from_secs(60); // 1 minute window

    let mut entry = IP_RATE_LIMITS
        .entry(*client_ip)
        .or_insert_with(|| IpRateLimitEntry {
            count: 0,
            window_start: now,
            violations: 0,
            last_violation: None,
        });

    // Reset window if needed
    if now.duration_since(entry.window_start) >= window_duration {
        entry.count = 0;
        entry.window_start = now;
    }

    entry.count += 1;

    // Check rate limit
    if entry.count > 1000 { // 1000 requests per minute
        entry.violations += 1;
        entry.last_violation = Some(now);

        // Auto-block after multiple violations
        if entry.violations >= 3 {
            BLOCKED_IPS.insert(*client_ip, BlockedIpEntry {
                blocked_at: now,
                expires_at: now + Duration::from_secs(60 * 60),
                reason: "Excessive rate limit violations".to_string(),
                violation_count: entry.violations,
            });

            log_security_event(
                state,
                SecurityEventType::IpAutoBlocked,
                client_ip,
                request_id,
                format!("IP auto-blocked after {} violations", entry.violations),
            ).await;
        }

        let error = ErrorResponse::rate_limit_error(
            "Rate limit exceeded for IP address".to_string(),
            Some(60),
        );

        log_security_event(
            state,
            SecurityEventType::RateLimitViolation,
            client_ip,
            request_id,
            format!("Rate limit exceeded: {} requests in window", entry.count),
        ).await;

        return Err((StatusCode::TOO_MANY_REQUESTS, Json(error)).into_response());
    }

    Ok(())
}

/// Check request fingerprint for anomaly detection
async fn check_request_fingerprint(
    request: &Request,
    client_ip: &IpAddr,
    state: &Arc<AppState>,
    request_id: &str,
) -> Result<(), Response> {
    let headers = request.headers();
    let user_agent = headers
        .get("user-agent")
        .and_then(|v| v.to_str().ok())
        .unwrap_or("unknown")
        .to_string();
    
    let headers_hash = calculate_headers_hash(headers);
    let fingerprint_key = format!("{}:{}", client_ip, headers_hash);
    
    let now = Instant::now();
    let mut fingerprint = REQUEST_FINGERPRINTS
        .entry(fingerprint_key.clone())
        .or_insert_with(|| RequestFingerprint {
            user_agent: user_agent.clone(),
            headers_hash: headers_hash.clone(),
            request_count: 0,
            first_seen: now,
            last_seen: now,
            suspicious_patterns: Vec::new(),
        });

    fingerprint.request_count += 1;
    fingerprint.last_seen = now;

    // Check for suspicious patterns
    if user_agent.len() > 1000 {
        fingerprint.suspicious_patterns.push("oversized_user_agent".to_string());
    }

    if user_agent.contains("bot") || user_agent.contains("crawler") {
        fingerprint.suspicious_patterns.push("automated_client".to_string());
    }

    if fingerprint.request_count > 10000 {
        fingerprint.suspicious_patterns.push("high_request_volume".to_string());
    }

    // Check for anomalies
    if fingerprint.suspicious_patterns.len() >= 2 {
        log_security_event(
            state,
            SecurityEventType::SuspiciousFingerprint,
            client_ip,
            request_id,
            format!("Suspicious fingerprint patterns: {:?}", fingerprint.suspicious_patterns),
        ).await;
    }

    // Clean up old fingerprints
    if REQUEST_FINGERPRINTS.len() > 10000 {
        cleanup_old_fingerprints().await;
    }

    Ok(())
}

/// Check for threat patterns in request
async fn check_threat_patterns(
    request: &Request,
    client_ip: &IpAddr,
    state: &Arc<AppState>,
    request_id: &str,
) -> Result<(), Response> {
    let uri = request.uri().to_string();
    let query = request.uri().query().unwrap_or("");
    
    // Check URI and query for threat patterns
    for pattern_entry in THREAT_PATTERNS.iter() {
        let pattern = &pattern_entry.value().pattern;
        
        if let Ok(regex) = regex::Regex::new(pattern) {
            if regex.is_match(&uri) || regex.is_match(query) {
                let threat = pattern_entry.value();
                
                // Log threat detection
                log_security_event(
                    state,
                    SecurityEventType::ThreatDetected,
                    client_ip,
                    request_id,
                    format!("Threat pattern detected: {}", threat.description),
                ).await;

                // Block IP for critical threats
                if threat.severity == ThreatSeverity::Critical {
                    BLOCKED_IPS.insert(*client_ip, BlockedIpEntry {
                        blocked_at: Instant::now(),
                        expires_at: Instant::now() + threat.block_duration,
                        reason: threat.description.clone(),
                        violation_count: 1,
                    });

                    log_security_event(
                        state,
                        SecurityEventType::IpAutoBlocked,
                        client_ip,
                        request_id,
                        format!("IP blocked due to critical threat: {}", threat.description),
                    ).await;
                }

                let error = ErrorResponse::new(
                    ErrorType::Authorization,
                    "Request blocked due to security policy violation".to_string(),
                );

                return Err((StatusCode::FORBIDDEN, Json(error)).into_response());
            }
        }
    }

    Ok(())
}

/// Add security headers to request
fn add_security_headers(request: &mut Request) {
    let headers = request.headers_mut();
    
    // Add request ID for tracing
    if let Ok(request_id) = Uuid::new_v4().to_string().parse() {
        headers.insert("X-Request-ID", request_id);
    }
    
    // Add timestamp
    if let Ok(timestamp) = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs()
        .to_string()
        .parse()
    {
        headers.insert("X-Request-Timestamp", timestamp);
    }
}

/// Add security headers to response
fn add_response_security_headers(response: &mut Response) {
    let headers = response.headers_mut();
    
    // Security headers
    headers.insert("X-Content-Type-Options", "nosniff".parse().unwrap());
    headers.insert("X-Frame-Options", "DENY".parse().unwrap());
    headers.insert("X-XSS-Protection", "1; mode=block".parse().unwrap());
    headers.insert("Referrer-Policy", "strict-origin-when-cross-origin".parse().unwrap());
    headers.insert("Content-Security-Policy", "default-src 'self'".parse().unwrap());
    
    // Remove server information
    headers.remove("Server");
    headers.remove("X-Powered-By");
    
    // Add security timestamp
    if let Ok(timestamp) = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs()
        .to_string()
        .parse()
    {
        headers.insert("X-Response-Timestamp", timestamp);
    }
}

/// Calculate hash of request headers for fingerprinting
fn calculate_headers_hash(headers: &HeaderMap) -> String {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};
    
    let mut hasher = DefaultHasher::new();
    
    // Hash relevant headers
    let relevant_headers = vec![
        "accept",
        "accept-encoding",
        "accept-language",
        "user-agent",
        "connection",
    ];
    
    for header_name in relevant_headers {
        if let Some(value) = headers.get(header_name) {
            if let Ok(value_str) = value.to_str() {
                header_name.hash(&mut hasher);
                value_str.hash(&mut hasher);
            }
        }
    }
    
    format!("{:x}", hasher.finish())
}

/// Security event types for logging
#[derive(Debug, Clone, Copy)]
enum SecurityEventType {
    BlockedIpRequest,
    RateLimitViolation,
    IpAutoBlocked,
    SuspiciousFingerprint,
    ThreatDetected,
}

/// Log security events for monitoring and alerting
async fn log_security_event(
    state: &Arc<AppState>,
    event_type: SecurityEventType,
    client_ip: &IpAddr,
    request_id: &str,
    details: String,
) {
    let audit_logger = AuditLogger::new(state.spanner_pool.clone());
    
    let (action, severity) = match event_type {
        SecurityEventType::BlockedIpRequest => (AuditAction::SecurityPolicyViolation, AuditSeverity::Warning),
        SecurityEventType::RateLimitViolation => (AuditAction::SecurityPolicyViolation, AuditSeverity::Info),
        SecurityEventType::IpAutoBlocked => (AuditAction::SecurityPolicyViolation, AuditSeverity::Critical),
        SecurityEventType::SuspiciousFingerprint => (AuditAction::SecurityPolicyViolation, AuditSeverity::Warning),
        SecurityEventType::ThreatDetected => (AuditAction::SecurityPolicyViolation, AuditSeverity::Critical),
    };
    
    let event = AuditEventBuilder::new(action)
        .outcome(AuditOutcome::Failure)
        .severity(severity)
        .metadata(serde_json::json!({
            "event_type": format!("{:?}", event_type),
            "client_ip": client_ip.to_string(),
            "request_id": request_id,
            "details": details,
            "timestamp": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
        }))
        .build();
    
    if let Err(e) = audit_logger.log_event(event).await {
        tracing::error!("Failed to log security event: {}", e);
    }
    
    // Also log to application logs
    tracing::warn!(
        "Security event: {:?} - IP: {} - Request: {} - Details: {}",
        event_type,
        client_ip,
        request_id,
        details
    );
}

/// Cleanup old fingerprints to prevent memory exhaustion
async fn cleanup_old_fingerprints() {
    let cutoff = Instant::now() - Duration::from_secs(24 * 60 * 60);
    
    REQUEST_FINGERPRINTS.retain(|_, fingerprint| {
        fingerprint.last_seen > cutoff
    });
    
    tracing::debug!("Cleaned up old request fingerprints, remaining: {}", REQUEST_FINGERPRINTS.len());
}

/// Get security middleware statistics
pub async fn get_security_stats() -> SecurityStats {
    let now = Instant::now();
    
    // Count active rate limits
    let active_rate_limits = IP_RATE_LIMITS.len();
    
    // Count blocked IPs
    let blocked_ips = BLOCKED_IPS
        .iter()
        .filter(|entry| entry.expires_at > now)
        .count();
    
    // Count fingerprints
    let fingerprints = REQUEST_FINGERPRINTS.len();
    
    // Count threat patterns
    let threat_patterns = THREAT_PATTERNS.len();
    
    SecurityStats {
        active_rate_limits,
        blocked_ips,
        fingerprints,
        threat_patterns,
        uptime_seconds: 0, // Would be calculated from service start time
    }
}

/// Security middleware statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityStats {
    pub active_rate_limits: usize,
    pub blocked_ips: usize,
    pub fingerprints: usize,
    pub threat_patterns: usize,
    pub uptime_seconds: u64,
}

/// Manual IP blocking for administrative use
pub async fn block_ip_manually(
    ip: IpAddr,
    reason: String,
    duration: Duration,
) -> Result<(), String> {
    let now = Instant::now();
    
    BLOCKED_IPS.insert(ip, BlockedIpEntry {
        blocked_at: now,
        expires_at: now + duration,
        reason,
        violation_count: 0,
    });
    
    tracing::info!("IP {} manually blocked for {:?}", ip, duration);
    Ok(())
}

/// Unblock IP manually
pub async fn unblock_ip_manually(ip: IpAddr) -> Result<(), String> {
    if BLOCKED_IPS.remove(&ip).is_some() {
        tracing::info!("IP {} manually unblocked", ip);
        Ok(())
    } else {
        Err("IP not found in blocked list".to_string())
    }
}

/// Get list of blocked IPs
pub async fn get_blocked_ips() -> Vec<(IpAddr, BlockedIpEntry)> {
    let now = Instant::now();
    
    BLOCKED_IPS
        .iter()
        .filter_map(|entry| {
            if entry.expires_at > now {
                Some((*entry.key(), entry.value().clone()))
            } else {
                None
            }
        })
        .collect()
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::body::Body;
    use axum::http::Method;
    use std::net::Ipv4Addr;

    #[test]
    fn test_threat_pattern_detection() {
        SecurityMiddleware::init_threat_patterns();
        
        let test_cases = vec![
            ("SELECT * FROM users", true),
            ("../../../etc/passwd", true),
            ("<script>alert('xss')</script>", true),
            ("normal request", false),
        ];
        
        for (input, should_match) in test_cases {
            let mut found_threat = false;
            
            for pattern_entry in THREAT_PATTERNS.iter() {
                if let Ok(regex) = regex::Regex::new(&pattern_entry.value().pattern) {
                    if regex.is_match(input) {
                        found_threat = true;
                        break;
                    }
                }
            }
            
            assert_eq!(found_threat, should_match, "Failed for input: {}", input);
        }
    }

    #[test]
    fn test_headers_hash() {
        let mut headers = HeaderMap::new();
        headers.insert("user-agent", "test-agent".parse().unwrap());
        headers.insert("accept", "application/json".parse().unwrap());
        
        let hash1 = calculate_headers_hash(&headers);
        let hash2 = calculate_headers_hash(&headers);
        
        assert_eq!(hash1, hash2, "Headers hash should be consistent");
        
        headers.insert("accept", "text/html".parse().unwrap());
        let hash3 = calculate_headers_hash(&headers);
        
        assert_ne!(hash1, hash3, "Headers hash should change when headers change");
    }
}