//! Error handling framework for the analysis engine
//!
//! This module provides domain-specific error types with proper error propagation
//! and context preservation to replace unwrap() calls throughout the codebase.

use thiserror::Error;

/// Main error type for the analysis engine
#[derive(Error, Debug)]
pub enum AnalysisError {
    #[error("Parser error: {0}")]
    Parser(#[from] ParserError),

    #[error("Storage error: {0}")]
    Storage(#[from] StorageError),

    #[error("Security validation failed: {0}")]
    Security(String),

    #[error("Configuration error: {0}")]
    Config(String),

    #[error("Authentication error: {0}")]
    Auth(String),

    #[error("Rate limiting error: {0}")]
    RateLimit(String),

    #[error("Network error: {0}")]
    Network(String),

    #[error("Timeout error: {0}")]
    Timeout(String),

    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("Resource not found: {0}")]
    NotFound(String),

    #[error("Service unavailable: {0}")]
    ServiceUnavailable(String),

    #[error("Internal error: {0}")]
    Internal(String),
}

/// Parser-specific error types
#[derive(Error, Debug)]
pub enum ParserError {
    #[error("Language not supported: {language}")]
    UnsupportedLanguage { language: String },

    #[error("Failed to parse file '{file}': {reason}")]
    ParseFailed { file: String, reason: String },

    #[error("Invalid syntax in file '{file}' at line {line}: {details}")]
    SyntaxError {
        file: String,
        line: u32,
        details: String,
    },

    #[error("Tree-sitter error: {0}")]
    TreeSitter(String),

    #[error("Regex compilation failed: {pattern} - {reason}")]
    RegexCompilation { pattern: String, reason: String },

    #[error("File too large: {size} bytes exceeds limit of {limit} bytes")]
    FileTooLarge { size: u64, limit: u64 },

    #[error("Parse timeout: exceeded {timeout_seconds} seconds")]
    Timeout { timeout_seconds: u64 },

    #[error("Invalid file encoding: {0}")]
    InvalidEncoding(String),

    #[error("Dependency parsing error: {0}")]
    DependencyParsing(String),

    #[error("AST traversal error: {0}")]
    AstTraversal(String),

    #[error("Parser pool error: {0}")]
    PoolError(String),
}

/// Storage-specific error types
#[derive(Error, Debug)]
pub enum StorageError {
    #[error("Database connection failed: {0}")]
    ConnectionFailed(String),

    #[error("Query execution failed: {query} - {reason}")]
    QueryFailed { query: String, reason: String },

    #[error("Transaction failed: {0}")]
    TransactionFailed(String),

    #[error("Cache error: {0}")]
    Cache(String),

    #[error("Serialization error: {0}")]
    Serialization(String),

    #[error("Deserialization error: {0}")]
    Deserialization(String),

    #[error("Record not found: {0}")]
    NotFound(String),

    #[error("Constraint violation: {0}")]
    ConstraintViolation(String),

    #[error("Connection pool exhausted")]
    PoolExhausted,

    #[error("Storage quota exceeded: {current} / {limit}")]
    QuotaExceeded { current: u64, limit: u64 },
}

/// HTTP client error types
#[derive(Error, Debug)]
pub enum HttpError {
    #[error("HTTP request failed: {status} {url}")]
    RequestFailed { status: u16, url: String },

    #[error("Connection timeout: {0}")]
    Timeout(String),

    #[error("Invalid URL: {0}")]
    InvalidUrl(String),

    #[error("Network error: {0}")]
    Network(String),

    #[error("SSL/TLS error: {0}")]
    Tls(String),
}

/// Git operation error types
#[derive(Error, Debug)]
pub enum GitError {
    #[error("Repository not found: {0}")]
    RepoNotFound(String),

    #[error("Invalid repository: {0}")]
    InvalidRepo(String),

    #[error("Git operation failed: {0}")]
    OperationFailed(String),

    #[error("Authentication failed: {0}")]
    AuthFailed(String),

    #[error("Branch not found: {0}")]
    BranchNotFound(String),

    #[error("Commit not found: {0}")]
    CommitNotFound(String),
}

/// JSON/YAML parsing error types
#[derive(Error, Debug)]
pub enum SerializationError {
    #[error("JSON parsing error: {0}")]
    Json(String),

    #[error("YAML parsing error: {0}")]
    Yaml(String),

    #[error("TOML parsing error: {0}")]
    Toml(String),

    #[error("XML parsing error: {0}")]
    Xml(String),

    #[error("Serialization error: {0}")]
    Serialize(String),

    #[error("Deserialization error: {0}")]
    Deserialize(String),
}

// Type aliases for convenience
pub type AnalysisResult<T> = Result<T, AnalysisError>;
pub type ParserResult<T> = Result<T, ParserError>;
pub type StorageResult<T> = Result<T, StorageError>;
pub type HttpResult<T> = Result<T, HttpError>;
pub type GitResult<T> = Result<T, GitError>;
pub type SerializationResult<T> = Result<T, SerializationError>;

// Convert common error types to our domain errors
impl From<std::io::Error> for AnalysisError {
    fn from(err: std::io::Error) -> Self {
        AnalysisError::Internal(format!("IO error: {err}"))
    }
}

impl From<std::io::Error> for ParserError {
    fn from(err: std::io::Error) -> Self {
        ParserError::TreeSitter(format!("IO error: {err}"))
    }
}

impl From<std::io::Error> for StorageError {
    fn from(err: std::io::Error) -> Self {
        StorageError::ConnectionFailed(format!("IO error: {err}"))
    }
}

impl From<serde_json::Error> for SerializationError {
    fn from(err: serde_json::Error) -> Self {
        SerializationError::Json(err.to_string())
    }
}

impl From<serde_json::Error> for AnalysisError {
    fn from(err: serde_json::Error) -> Self {
        AnalysisError::Internal(format!("JSON parsing error: {err}"))
    }
}

impl From<serde_yaml::Error> for SerializationError {
    fn from(err: serde_yaml::Error) -> Self {
        SerializationError::Yaml(err.to_string())
    }
}

impl From<toml::de::Error> for SerializationError {
    fn from(err: toml::de::Error) -> Self {
        SerializationError::Toml(err.to_string())
    }
}

impl From<toml::de::Error> for AnalysisError {
    fn from(err: toml::de::Error) -> Self {
        AnalysisError::Internal(format!("TOML parsing error: {err}"))
    }
}

impl From<regex::Error> for ParserError {
    fn from(err: regex::Error) -> Self {
        ParserError::RegexCompilation {
            pattern: "unknown".to_string(),
            reason: err.to_string(),
        }
    }
}

impl From<git2::Error> for GitError {
    fn from(err: git2::Error) -> Self {
        GitError::OperationFailed(err.to_string())
    }
}

impl From<reqwest::Error> for HttpError {
    fn from(err: reqwest::Error) -> Self {
        if err.is_timeout() {
            HttpError::Timeout(err.to_string())
        } else {
            HttpError::Network(err.to_string())
        }
    }
}

// Parser and Storage errors are automatically converted via #[from] annotations

// Auto-convert serialization errors to analysis errors
impl From<SerializationError> for AnalysisError {
    fn from(err: SerializationError) -> Self {
        AnalysisError::Internal(err.to_string())
    }
}

// Auto-convert HTTP errors to analysis errors
impl From<HttpError> for AnalysisError {
    fn from(err: HttpError) -> Self {
        AnalysisError::Network(err.to_string())
    }
}

// Auto-convert Git errors to analysis errors
impl From<GitError> for AnalysisError {
    fn from(err: GitError) -> Self {
        AnalysisError::Internal(err.to_string())
    }
}

impl From<quick_xml::Error> for AnalysisError {
    fn from(err: quick_xml::Error) -> Self {
        AnalysisError::Internal(format!("XML parsing error: {err}"))
    }
}

impl From<regex::Error> for AnalysisError {
    fn from(err: regex::Error) -> Self {
        AnalysisError::Internal(format!("Regex error: {err}"))
    }
}

impl From<std::str::Utf8Error> for AnalysisError {
    fn from(err: std::str::Utf8Error) -> Self {
        AnalysisError::Internal(format!("UTF-8 error: {err}"))
    }
}

// Helper functions for creating common errors
impl AnalysisError {
    pub fn config(msg: impl Into<String>) -> Self {
        AnalysisError::Config(msg.into())
    }

    pub fn auth(msg: impl Into<String>) -> Self {
        AnalysisError::Auth(msg.into())
    }

    pub fn rate_limit(msg: impl Into<String>) -> Self {
        AnalysisError::RateLimit(msg.into())
    }

    pub fn timeout(msg: impl Into<String>) -> Self {
        AnalysisError::Timeout(msg.into())
    }

    pub fn invalid_input(msg: impl Into<String>) -> Self {
        AnalysisError::InvalidInput(msg.into())
    }

    pub fn not_found(msg: impl Into<String>) -> Self {
        AnalysisError::NotFound(msg.into())
    }

    pub fn service_unavailable(msg: impl Into<String>) -> Self {
        AnalysisError::ServiceUnavailable(msg.into())
    }

    pub fn internal(msg: impl Into<String>) -> Self {
        AnalysisError::Internal(msg.into())
    }
}

impl ParserError {
    pub fn unsupported_language(language: impl Into<String>) -> Self {
        ParserError::UnsupportedLanguage {
            language: language.into(),
        }
    }

    pub fn parse_failed(file: impl Into<String>, reason: impl Into<String>) -> Self {
        ParserError::ParseFailed {
            file: file.into(),
            reason: reason.into(),
        }
    }

    pub fn syntax_error(file: impl Into<String>, line: u32, details: impl Into<String>) -> Self {
        ParserError::SyntaxError {
            file: file.into(),
            line,
            details: details.into(),
        }
    }

    pub fn regex_compilation(pattern: impl Into<String>, reason: impl Into<String>) -> Self {
        ParserError::RegexCompilation {
            pattern: pattern.into(),
            reason: reason.into(),
        }
    }

    pub fn file_too_large(size: u64, limit: u64) -> Self {
        ParserError::FileTooLarge { size, limit }
    }

    pub fn timeout(timeout_seconds: u64) -> Self {
        ParserError::Timeout { timeout_seconds }
    }
}

impl StorageError {
    pub fn connection_failed(msg: impl Into<String>) -> Self {
        StorageError::ConnectionFailed(msg.into())
    }

    pub fn query_failed(query: impl Into<String>, reason: impl Into<String>) -> Self {
        StorageError::QueryFailed {
            query: query.into(),
            reason: reason.into(),
        }
    }

    pub fn not_found(msg: impl Into<String>) -> Self {
        StorageError::NotFound(msg.into())
    }

    pub fn quota_exceeded(current: u64, limit: u64) -> Self {
        StorageError::QuotaExceeded { current, limit }
    }
}

/// Context helper for adding error context
pub trait ErrorContext<T> {
    fn with_context(self, context: &str) -> Result<T, AnalysisError>;
}

impl<T, E> ErrorContext<T> for Result<T, E>
where
    E: Into<AnalysisError>,
{
    fn with_context(self, context: &str) -> Result<T, AnalysisError> {
        self.map_err(|e| {
            let base_error = e.into();
            AnalysisError::Internal(format!("{context}: {base_error}"))
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parser_error_conversion() {
        let parser_err = ParserError::unsupported_language("unknown");
        let analysis_err: AnalysisError = parser_err.into();
        assert!(matches!(analysis_err, AnalysisError::Parser(_)));
    }

    #[test]
    fn test_storage_error_conversion() {
        let storage_err = StorageError::connection_failed("DB down");
        let analysis_err: AnalysisError = storage_err.into();
        assert!(matches!(analysis_err, AnalysisError::Storage(_)));
    }

    #[test]
    fn test_http_error_conversion() {
        let http_err = HttpError::Timeout("request timeout".to_string());
        let analysis_err: AnalysisError = http_err.into();
        assert!(matches!(analysis_err, AnalysisError::Network(_)));
    }

    #[test]
    fn test_git_error_conversion() {
        let git_err = GitError::RepoNotFound("repo not found".to_string());
        let analysis_err: AnalysisError = git_err.into();
        assert!(matches!(analysis_err, AnalysisError::Internal(_)));
    }

    #[test]
    fn test_serialization_error_conversion() {
        let ser_err = SerializationError::Json("invalid json".to_string());
        let analysis_err: AnalysisError = ser_err.into();
        assert!(matches!(analysis_err, AnalysisError::Internal(_)));
    }

    #[test]
    fn test_io_error_conversion() {
        let io_err = std::io::Error::new(std::io::ErrorKind::NotFound, "file not found");
        let analysis_err: AnalysisError = io_err.into();
        assert!(matches!(analysis_err, AnalysisError::Internal(_)));
    }

    #[test]
    fn test_analysis_error_helpers() {
        let config_err = AnalysisError::config("missing config");
        assert!(matches!(config_err, AnalysisError::Config(_)));

        let auth_err = AnalysisError::auth("invalid token");
        assert!(matches!(auth_err, AnalysisError::Auth(_)));

        let rate_limit_err = AnalysisError::rate_limit("too many requests");
        assert!(matches!(rate_limit_err, AnalysisError::RateLimit(_)));

        let timeout_err = AnalysisError::timeout("operation timed out");
        assert!(matches!(timeout_err, AnalysisError::Timeout(_)));

        let invalid_input_err = AnalysisError::invalid_input("bad input");
        assert!(matches!(invalid_input_err, AnalysisError::InvalidInput(_)));

        let not_found_err = AnalysisError::not_found("resource not found");
        assert!(matches!(not_found_err, AnalysisError::NotFound(_)));

        let service_unavailable_err = AnalysisError::service_unavailable("service down");
        assert!(matches!(
            service_unavailable_err,
            AnalysisError::ServiceUnavailable(_)
        ));

        let internal_err = AnalysisError::internal("internal error");
        assert!(matches!(internal_err, AnalysisError::Internal(_)));
    }

    #[test]
    fn test_parser_error_helpers() {
        let unsupported_err = ParserError::unsupported_language("brainfuck");
        assert!(matches!(
            unsupported_err,
            ParserError::UnsupportedLanguage { .. }
        ));

        let parse_failed_err = ParserError::parse_failed("file.rs", "syntax error");
        assert!(matches!(parse_failed_err, ParserError::ParseFailed { .. }));

        let syntax_err = ParserError::syntax_error("file.rs", 42, "missing semicolon");
        assert!(matches!(syntax_err, ParserError::SyntaxError { .. }));

        let regex_err = ParserError::regex_compilation("([", "unclosed bracket");
        assert!(matches!(regex_err, ParserError::RegexCompilation { .. }));

        let file_too_large_err = ParserError::file_too_large(1000, 500);
        assert!(matches!(
            file_too_large_err,
            ParserError::FileTooLarge { .. }
        ));

        let timeout_err = ParserError::timeout(30);
        assert!(matches!(timeout_err, ParserError::Timeout { .. }));
    }

    #[test]
    fn test_storage_error_helpers() {
        let connection_err = StorageError::connection_failed("connection refused");
        assert!(matches!(connection_err, StorageError::ConnectionFailed(_)));

        let query_err = StorageError::query_failed("SELECT * FROM users", "table not found");
        assert!(matches!(query_err, StorageError::QueryFailed { .. }));

        let not_found_err = StorageError::not_found("user not found");
        assert!(matches!(not_found_err, StorageError::NotFound(_)));

        let quota_err = StorageError::quota_exceeded(1000, 500);
        assert!(matches!(quota_err, StorageError::QuotaExceeded { .. }));
    }

    #[test]
    fn test_error_context() {
        let result: Result<(), std::io::Error> = Err(std::io::Error::new(
            std::io::ErrorKind::NotFound,
            "file not found",
        ));

        let with_context = result.with_context("Failed to read config");
        assert!(with_context.is_err());

        let err = with_context.expect_err("Error context test should fail");
        assert!(matches!(err, AnalysisError::Internal(_)));
        assert!(err.to_string().contains("Failed to read config"));
    }

    #[test]
    fn test_error_context_with_parser_error() {
        let result: Result<(), ParserError> = Err(ParserError::unsupported_language("unknown"));

        let with_context = result.with_context("Failed to parse file");
        assert!(with_context.is_err());

        let err = with_context.expect_err("Error context with parser error test should fail");
        assert!(matches!(err, AnalysisError::Internal(_)));
        assert!(err.to_string().contains("Failed to parse file"));
    }

    #[test]
    fn test_error_display() {
        let parser_err = ParserError::syntax_error("test.rs", 10, "missing semicolon");
        assert!(parser_err.to_string().contains("test.rs"));
        assert!(parser_err.to_string().contains("10"));
        assert!(parser_err.to_string().contains("missing semicolon"));

        let storage_err = StorageError::query_failed("SELECT * FROM users", "table not found");
        assert!(storage_err.to_string().contains("SELECT * FROM users"));
        assert!(storage_err.to_string().contains("table not found"));

        let http_err = HttpError::RequestFailed {
            status: 404,
            url: "https://example.com".to_string(),
        };
        assert!(http_err.to_string().contains("404"));
        assert!(http_err.to_string().contains("https://example.com"));
    }

    #[test]
    fn test_type_aliases() {
        let result: AnalysisResult<i32> = Ok(42);
        assert_eq!(result.expect("AnalysisResult test should succeed"), 42);

        let result: ParserResult<String> = Err(ParserError::unsupported_language("unknown"));
        assert!(result.is_err());

        let result: StorageResult<()> = Err(StorageError::connection_failed("db down"));
        assert!(result.is_err());

        let result: HttpResult<Vec<u8>> = Err(HttpError::Timeout("timeout".to_string()));
        assert!(result.is_err());

        let result: GitResult<String> = Err(GitError::RepoNotFound("repo not found".to_string()));
        assert!(result.is_err());

        let result: SerializationResult<serde_json::Value> =
            Err(SerializationError::Json("invalid json".to_string()));
        assert!(result.is_err());
    }

    #[test]
    fn test_error_chain() {
        // Test that errors can be chained properly
        let io_err = std::io::Error::new(std::io::ErrorKind::PermissionDenied, "access denied");
        let parser_err: ParserError = io_err.into();
        let analysis_err: AnalysisError = parser_err.into();

        assert!(matches!(analysis_err, AnalysisError::Parser(_)));
        assert!(analysis_err.to_string().contains("access denied"));
    }

    #[test]
    fn test_error_debugging() {
        let err = AnalysisError::config("test error");
        let debug_str = format!("{err:?}");
        assert!(debug_str.contains("Config"));
        assert!(debug_str.contains("test error"));
    }
}
