use analysis_engine::config::ServiceConfig;
use analysis_engine::parser::{TreeSitterParser, parallel::ParallelProcessor};
use anyhow::Result;
use std::collections::HashMap;
use std::path::Path;
use std::sync::Arc;
use std::time::Instant;
use tokio::fs;
use walkdir::WalkDir;

#[derive(Debug, serde::Serialize)]
pub struct PerformanceMetrics {
    pub total_files: usize,
    pub total_lines: usize,
    pub duration_seconds: f64,
    pub lines_per_second: f64,
    pub files_per_second: f64,
    pub memory_usage_mb: f64,
    pub success_rate: f64,
    pub language_metrics: HashMap<String, LanguageMetrics>,
}

#[derive(Debug, serde::Serialize)]
pub struct LanguageMetrics {
    pub files_count: usize,
    pub lines_count: usize,
    pub successful_files: usize,
    pub failed_files: usize,
    pub success_rate: f64,
    pub average_file_size: f64,
    pub processing_time_seconds: f64,
    pub lines_per_second: f64,
    pub ast_nodes_total: usize,
    pub symbols_total: usize,
    pub patterns_total: usize,
}

#[derive(Debug, serde::Serialize)]
pub struct FileMetrics {
    pub lines: usize,
    pub ast_nodes: usize,
    pub symbols: usize,
    pub patterns: usize,
    pub language: String,
    pub file_path: String,
    pub processing_time_ms: f64,
}

pub struct PerformanceValidator {
    #[allow(dead_code)]
    config: Arc<ServiceConfig>,
    parser: Arc<TreeSitterParser>,
}

impl PerformanceValidator {
    pub async fn new() -> Result<Self> {
        let config = Arc::new(ServiceConfig::from_env()?);
        let parser = Arc::new(TreeSitterParser::new(config.clone())?);
        
        Ok(Self {
            config,
            parser,
        })
    }

    pub async fn validate_performance(&self, repo_path: &Path) -> Result<PerformanceMetrics> {
        println!("🚀 Starting performance validation for: {}", repo_path.display());
        
        let start_time = Instant::now();
        
        // Collect all supported files with language detection
        let files = self.collect_files_with_language(repo_path)?;
        let files_len = files.len();
        println!("📁 Found {files_len} files to analyze");
        
        // Create parallel processor
        let processor = ParallelProcessor::new(self.parser.clone());
        
        let mut total_lines = 0;
        let mut successful_files = 0;
        let mut _failed_files = 0;
        let mut language_metrics: HashMap<String, LanguageMetrics> = HashMap::new();
        
        // Process files in parallel batches
        let batch_size = 50; // Moderate batch size to avoid stack overflow
        let total_batches = files.len().div_ceil(batch_size);
        let max_threads = num_cpus::get().min(8); // Limit to 8 threads max
        
        for (i, batch) in files.chunks(batch_size).enumerate() {
            println!("📊 Processing batch {}/{} ({} files)", 
                i + 1, 
                total_batches,
                batch.len()
            );
            
            // Process batch in parallel with limited threads
            let batch_results = processor.process_files_parallel(&batch.iter().map(|(path, _)| path).cloned().collect::<Vec<_>>(), max_threads);
            
            // Count results with language tracking
            for ((file_path, language), result) in batch.iter().zip(batch_results.iter()) {
                let language_entry = language_metrics.entry(language.clone()).or_insert_with(|| LanguageMetrics {
                    files_count: 0,
                    lines_count: 0,
                    successful_files: 0,
                    failed_files: 0,
                    success_rate: 0.0,
                    average_file_size: 0.0,
                    processing_time_seconds: 0.0,
                    lines_per_second: 0.0,
                    ast_nodes_total: 0,
                    symbols_total: 0,
                    patterns_total: 0,
                });
                
                language_entry.files_count += 1;
                
                match result {
                    Ok(analysis) => {
                        let file_lines = analysis.metrics.lines_of_code as usize;
                        total_lines += file_lines;
                        successful_files += 1;
                        language_entry.successful_files += 1;
                        language_entry.lines_count += file_lines;
                    }
                    Err(e) => {
                        _failed_files += 1;
                        language_entry.failed_files += 1;
                        eprintln!("❌ Failed to analyze {}: {}", file_path.display(), e.message);
                    }
                }
            }
            
            // Progress update
            let processed = (i + 1) * batch_size;
            let processed = processed.min(files.len());
            let elapsed = start_time.elapsed().as_secs_f64();
            let current_lps = total_lines as f64 / elapsed;
            
            let progress = (processed as f64 / files_len as f64) * 100.0;
            println!("⏱️  Progress: {processed}/{files_len} files ({progress:.1}%), {current_lps:.0} LOC/s");
        }
        
        let duration = start_time.elapsed().as_secs_f64();
        let success_rate = successful_files as f64 / files.len() as f64;
        
        // Calculate language-specific metrics
        for (_language, metrics) in language_metrics.iter_mut() {
            if metrics.files_count > 0 {
                metrics.success_rate = metrics.successful_files as f64 / metrics.files_count as f64;
                metrics.average_file_size = metrics.lines_count as f64 / metrics.files_count as f64;
                metrics.processing_time_seconds = duration * (metrics.files_count as f64 / files.len() as f64);
                if metrics.processing_time_seconds > 0.0 {
                    metrics.lines_per_second = metrics.lines_count as f64 / metrics.processing_time_seconds;
                }
            }
        }
        
        let metrics = PerformanceMetrics {
            total_files: files.len(),
            total_lines,
            duration_seconds: duration,
            lines_per_second: total_lines as f64 / duration,
            files_per_second: files.len() as f64 / duration,
            memory_usage_mb: self.get_memory_usage(),
            success_rate,
            language_metrics,
        };
        
        self.print_results(&metrics);
        
        Ok(metrics)
    }
    
    fn collect_files(&self, repo_path: &Path) -> Result<Vec<std::path::PathBuf>> {
        let files_with_lang = self.collect_files_with_language(repo_path)?;
        Ok(files_with_lang.into_iter().map(|(path, _)| path).collect())
    }
    
    fn collect_files_with_language(&self, repo_path: &Path) -> Result<Vec<(std::path::PathBuf, String)>> {
        let mut files = Vec::new();
        
        // Language to extension mapping (updated for 21 supported languages)
        let language_extensions = vec![
            // Tree-sitter languages (18)
            ("rust", vec!["rs"]),
            ("python", vec!["py"]),
            ("javascript", vec!["js", "jsx"]),
            ("typescript", vec!["ts", "tsx"]),
            ("go", vec!["go"]),
            ("java", vec!["java"]),
            ("c", vec!["c", "h"]),
            ("cpp", vec!["cpp", "cc", "cxx", "hpp"]),
            ("ruby", vec!["rb"]),
            ("bash", vec!["sh", "bash"]),
            ("julia", vec!["jl"]),
            ("scala", vec!["scala"]),
            ("php", vec!["php"]),
            ("ocaml", vec!["ml", "mli"]),
            ("html", vec!["html", "htm"]),
            ("css", vec!["css", "scss", "sass"]),
            ("json", vec!["json"]),
            ("markdown", vec!["md"]),
            // Adapter languages (3)
            ("sql", vec!["sql"]),
            ("xml", vec!["xml"]),
            ("toml", vec!["toml"]),
        ];
        
        // Build extension to language mapping
        let mut ext_to_lang = HashMap::new();
        for (language, extensions) in language_extensions {
            for ext in extensions {
                ext_to_lang.insert(ext, language.to_string());
            }
        }
        
        for entry in WalkDir::new(repo_path)
            .into_iter()
            .filter_map(|e| e.ok())
            .filter(|e| e.file_type().is_file())
        {
            let path = entry.path();
            if let Some(ext) = path.extension() {
                if let Some(ext_str) = ext.to_str() {
                    if let Some(language) = ext_to_lang.get(ext_str) {
                        files.push((path.to_path_buf(), language.clone()));
                    }
                }
            }
        }
        
        Ok(files)
    }
    
    #[allow(dead_code)]
    async fn analyze_file(&self, file_path: &Path) -> Result<usize> {
        let content = fs::read_to_string(file_path).await?;
        let line_count = content.lines().count();
        
        // Use the full analysis service for comprehensive analysis
        let analysis = self.parser.parse_content(file_path, &content).await?;
        
        // Validate the analysis has required components
        if analysis.ast.node_type.is_empty() {
            return Err(anyhow::anyhow!("Invalid AST generated for {}", file_path.display()));
        }
        
        if analysis.metrics.lines_of_code == 0 && line_count > 0 {
            return Err(anyhow::anyhow!("No metrics calculated for {}", file_path.display()));
        }
        
        Ok(line_count)
    }
    
    pub async fn analyze_repository_comprehensive(&self, repo_path: &Path) -> Result<PerformanceMetrics> {
        println!("🚀 Starting comprehensive repository analysis for: {}", repo_path.display());
        
        let start_time = Instant::now();
        
        // Use the repository manager for comprehensive analysis
        let files = self.collect_files(repo_path)?;
        let files_len = files.len();
        println!("📁 Found {files_len} files for comprehensive analysis");
        
        let mut total_lines = 0;
        let mut successful_files = 0;
        let mut _failed_files = 0;
        let mut total_ast_nodes = 0;
        let mut total_symbols = 0;
        let mut total_patterns = 0;
        
        // Process files with full analysis pipeline
        let batch_size = 25; // Smaller batches for comprehensive analysis
        for (i, batch) in files.chunks(batch_size).enumerate() {
            println!("📊 Processing comprehensive batch {}/{} ({} files)", 
                i + 1, 
                files.len().div_ceil(batch_size),
                batch.len()
            );
            
            for file_path in batch {
                match self.analyze_file_comprehensive(file_path).await {
                    Ok(metrics) => {
                        total_lines += metrics.lines;
                        total_ast_nodes += metrics.ast_nodes;
                        total_symbols += metrics.symbols;
                        total_patterns += metrics.patterns;
                        successful_files += 1;
                    }
                    Err(e) => {
                        _failed_files += 1;
                        eprintln!("❌ Failed comprehensive analysis for {}: {}", file_path.display(), e);
                    }
                }
            }
            
            // Progress update
            let processed = (i + 1) * batch_size;
            let processed = processed.min(files.len());
            let elapsed = start_time.elapsed().as_secs_f64();
            let current_lps = total_lines as f64 / elapsed;
            
            let progress = (processed as f64 / files_len as f64) * 100.0;
            println!("⏱️  Comprehensive Progress: {processed}/{files_len} files ({progress:.1}%), {current_lps:.0} LOC/s, {total_ast_nodes} AST nodes, {total_symbols} symbols");
        }
        
        let duration = start_time.elapsed().as_secs_f64();
        let success_rate = successful_files as f64 / files.len() as f64;
        
        let metrics = PerformanceMetrics {
            total_files: files.len(),
            total_lines,
            duration_seconds: duration,
            lines_per_second: total_lines as f64 / duration,
            files_per_second: files.len() as f64 / duration,
            memory_usage_mb: self.get_memory_usage(),
            success_rate,
            language_metrics: HashMap::new(),
        };
        
        println!("\n🎯 COMPREHENSIVE ANALYSIS RESULTS");
        println!("==================================");
        println!("📁 Files analyzed: {}", metrics.total_files);
        println!("📝 Lines processed: {}", metrics.total_lines);
        println!("🌳 AST nodes generated: {total_ast_nodes}");
        println!("🔍 Symbols extracted: {total_symbols}");
        println!("🎯 Patterns detected: {total_patterns}");
        println!("⏱️  Duration: {:.2} seconds", metrics.duration_seconds);
        println!("🚀 Lines/Second: {:.0}", metrics.lines_per_second);
        println!("✅ Success Rate: {:.1}%", metrics.success_rate * 100.0);
        
        self.print_results(&metrics);
        
        Ok(metrics)
    }
    
    async fn analyze_file_comprehensive(&self, file_path: &Path) -> Result<FileMetrics> {
        let content = fs::read_to_string(file_path).await?;
        let line_count = content.lines().count();
        
        // Full analysis pipeline
        let analysis = self.parser.parse_content(file_path, &content).await?;
        
        // Count AST nodes recursively
        let ast_nodes = self.count_ast_nodes(&analysis.ast);
        
        // Count symbols
        let symbols = analysis.symbols.as_ref().map(|s| s.len()).unwrap_or(0);
        
        // Count patterns (from chunks)
        let patterns = analysis.chunks.as_ref().map(|c| c.len()).unwrap_or(0);
        
        Ok(FileMetrics {
            lines: line_count,
            ast_nodes,
            symbols,
            patterns,
            language: "unknown".to_string(),
            file_path: file_path.to_string_lossy().to_string(),
            processing_time_ms: 0.0,
        })
    }
    
    fn count_ast_nodes(&self, node: &analysis_engine::models::AstNode) -> usize {
        fn count_recursive(node: &analysis_engine::models::AstNode) -> usize {
            let mut count = 1; // Current node
            for child in &node.children {
                count += count_recursive(child);
            }
            count
        }
        count_recursive(node)
    }
    
    fn get_memory_usage(&self) -> f64 {
        // Simple memory usage estimation
        // In a real implementation, you'd use proper memory tracking
        0.0
    }
    
    fn print_results(&self, metrics: &PerformanceMetrics) {
        println!("\n🎯 PERFORMANCE VALIDATION RESULTS");
        println!("=====================================");
        println!("📁 Total Files: {}", metrics.total_files);
        println!("📝 Total Lines: {}", metrics.total_lines);
        println!("⏱️  Duration: {:.2} seconds", metrics.duration_seconds);
        println!("🚀 Lines/Second: {:.0}", metrics.lines_per_second);
        println!("📊 Files/Second: {:.1}", metrics.files_per_second);
        println!("✅ Success Rate: {:.1}%", metrics.success_rate * 100.0);
        println!("💾 Memory Usage: {:.1} MB", metrics.memory_usage_mb);
        
        // Print language-specific metrics
        println!("\n📈 LANGUAGE-SPECIFIC PERFORMANCE");
        println!("====================================");
        
        let mut sorted_languages: Vec<_> = metrics.language_metrics.iter().collect();
        sorted_languages.sort_by(|a, b| b.1.lines_count.cmp(&a.1.lines_count));
        
        for (language, lang_metrics) in sorted_languages {
            println!("📦 {} ({} files, {} LOC):", 
                language.to_uppercase(), 
                lang_metrics.files_count, 
                lang_metrics.lines_count);
            println!("  ✅ Success Rate: {:.1}%", lang_metrics.success_rate * 100.0);
            println!("  🚀 Throughput: {:.0} LOC/s", lang_metrics.lines_per_second);
            println!("  📊 Avg File Size: {:.0} LOC", lang_metrics.average_file_size);
            
            if lang_metrics.success_rate < 0.8 {
                println!("  ⚠️  Warning: Low success rate for {language}");
            }
        }
        
        // Validate the 1M LOC in 5 minutes claim
        let target_lines = 1_000_000;
        let target_duration = 300.0; // 5 minutes
        
        if metrics.total_lines >= target_lines {
            let projected_duration = target_lines as f64 / metrics.lines_per_second;
            println!("\n🎯 1M LOC PERFORMANCE CLAIM VALIDATION:");
            println!("Target: {target_lines} LOC in {target_duration} seconds");
            println!("Actual: {} LOC in {:.2} seconds", metrics.total_lines, metrics.duration_seconds);
            println!("Projected 1M LOC time: {projected_duration:.2} seconds");
            
            if projected_duration <= target_duration {
                println!("✅ CLAIM VALIDATED: Can process 1M LOC in under 5 minutes!");
                println!("🚀 Performance margin: {:.1}x faster than required", 
                    target_duration / projected_duration);
            } else {
                println!("❌ CLAIM FAILED: Would take {projected_duration:.2} seconds for 1M LOC");
                println!("⚠️  Performance gap: {:.1}x slower than required",
                    projected_duration / target_duration);
            }
        } else {
            let projected_duration = target_lines as f64 / metrics.lines_per_second;
            println!("\n🎯 1M LOC PERFORMANCE PROJECTION:");
            println!("Based on current throughput: {projected_duration:.2} seconds for 1M LOC");
            
            if projected_duration <= target_duration {
                println!("✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes");
            } else {
                println!("❌ PROJECTION: May not achieve 1M LOC in 5 minutes");
            }
        }
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt()
        .with_env_filter("analysis_engine=info")
        .init();
    
    let args: Vec<String> = std::env::args().collect();
    if args.len() < 2 {
        eprintln!("Usage: {} <repository_path>", args[0]);
        std::process::exit(1);
    }
    
    let repo_path = Path::new(&args[1]);
    if !repo_path.exists() {
        eprintln!("Error: Repository path does not exist: {}", repo_path.display());
        std::process::exit(1);
    }
    
    let validator = PerformanceValidator::new().await?;
    
    // Run both basic and comprehensive analysis
    println!("📊 Running basic performance validation...");
    let basic_metrics = validator.validate_performance(repo_path).await?;
    
    println!("\n📊 Running comprehensive analysis...");
    let comprehensive_metrics = validator.analyze_repository_comprehensive(repo_path).await?;
    
    // Write results to file for analysis
    let results_file = "performance_results.json";
    let results_json = serde_json::to_string_pretty(&serde_json::json!({
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "repository": repo_path.display().to_string(),
        "basic_metrics": basic_metrics,
        "comprehensive_metrics": comprehensive_metrics,
        "analysis_summary": {
            "total_languages": basic_metrics.language_metrics.len(),
            "supported_languages": basic_metrics.language_metrics.keys().collect::<Vec<_>>(),
            "performance_target_met": basic_metrics.lines_per_second >= 3333.0,
            "claim_validation": {
                "can_process_1m_loc_in_5min": 1_000_000.0 / basic_metrics.lines_per_second <= 300.0,
                "projected_1m_loc_time_seconds": 1_000_000.0 / basic_metrics.lines_per_second,
                "performance_margin": basic_metrics.lines_per_second / 3333.0
            }
        }
    }))?;
    
    fs::write(results_file, results_json).await?;
    println!("\n📊 Results saved to: {results_file}");
    
    Ok(())
}