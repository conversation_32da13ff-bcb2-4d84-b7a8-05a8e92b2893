//! Contract compliance validation module
//!
//! This module provides comprehensive validation for ensuring all outputs
//! comply with the CCL service integration contracts, particularly the
//! AST output v1 schema.

pub mod contract_compliance;
pub mod id_generator;

pub use contract_compliance::{
    validate_ast_output, validate_against_schema, ContractValidator, ValidationError,
};
pub use id_generator::{generate_analysis_id, generate_chunk_id, generate_repository_id};

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Validation configuration
#[derive(Debug, <PERSON>lone, Deserialize, Serialize)]
pub struct ValidationConfig {
    /// Enable runtime validation
    pub enabled: bool,
    /// Validate on every API response
    pub validate_responses: bool,
    /// Maximum validation time in milliseconds
    pub max_validation_time_ms: u64,
    /// Schema cache size
    pub schema_cache_size: usize,
}

impl Default for ValidationConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            validate_responses: true,
            max_validation_time_ms: 5,
            schema_cache_size: 10,
        }
    }
}

/// Validation metrics for monitoring
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct ValidationMetrics {
    pub total_validations: u64,
    pub successful_validations: u64,
    pub failed_validations: u64,
    pub validation_errors: HashMap<String, u64>,
    pub average_validation_time_ms: f64,
    pub schema_version: String,
}

impl ValidationMetrics {
    pub fn record_validation(&mut self, result: &ValidationResult, duration_ms: f64) {
        self.total_validations += 1;
        
        match result {
            ValidationResult::Valid => {
                self.successful_validations += 1;
            }
            ValidationResult::Invalid(errors) => {
                self.failed_validations += 1;
                for error in errors {
                    *self.validation_errors.entry(error.code.clone()).or_insert(0) += 1;
                }
            }
        }
        
        // Update average validation time
        let n = self.total_validations as f64;
        self.average_validation_time_ms = 
            (self.average_validation_time_ms * (n - 1.0) + duration_ms) / n;
    }
    
    pub fn success_rate(&self) -> f64 {
        if self.total_validations == 0 {
            return 100.0;
        }
        (self.successful_validations as f64 / self.total_validations as f64) * 100.0
    }
}

/// Validation result types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ValidationResult {
    Valid,
    Invalid(Vec<ValidationError>),
}

impl ValidationResult {
    pub fn is_valid(&self) -> bool {
        matches!(self, ValidationResult::Valid)
    }
    
    pub fn errors(&self) -> Option<&[ValidationError]> {
        match self {
            ValidationResult::Valid => None,
            ValidationResult::Invalid(errors) => Some(errors),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_validation_metrics() {
        let mut metrics = ValidationMetrics::default();
        
        // Record successful validation
        metrics.record_validation(&ValidationResult::Valid, 2.5);
        assert_eq!(metrics.successful_validations, 1);
        assert_eq!(metrics.failed_validations, 0);
        assert_eq!(metrics.average_validation_time_ms, 2.5);
        
        // Record failed validation
        let errors = vec![ValidationError {
            code: "INVALID_ID".to_string(),
            message: "Invalid repository ID format".to_string(),
            path: "/repository/id".to_string(),
            severity: crate::validation::contract_compliance::ValidationSeverity::Error,
        }];
        metrics.record_validation(&ValidationResult::Invalid(errors), 3.5);
        assert_eq!(metrics.successful_validations, 1);
        assert_eq!(metrics.failed_validations, 1);
        assert_eq!(metrics.average_validation_time_ms, 3.0);
        assert_eq!(metrics.validation_errors.get("INVALID_ID"), Some(&1));
    }
    
    #[test]
    fn test_success_rate() {
        let mut metrics = ValidationMetrics::default();
        assert_eq!(metrics.success_rate(), 100.0);
        
        metrics.record_validation(&ValidationResult::Valid, 1.0);
        metrics.record_validation(&ValidationResult::Valid, 1.0);
        metrics.record_validation(&ValidationResult::Invalid(vec![]), 1.0);
        
        assert_eq!(metrics.success_rate(), 66.66666666666667);
    }
}