//! ID generation utilities for contract compliance
//!
//! This module provides functions to generate IDs that comply with
//! the CCL contract patterns:
//! - Repository ID: `repo_[a-zA-Z0-9]{16}`
//! - Analysis ID: `analysis_[a-zA-Z0-9]{16}`
//! - Chunk ID: `chunk_[a-zA-Z0-9]{16}`

use rand::Rng;

/// Characters allowed in ID generation (alphanumeric)
const ID_CHARS: &[u8] = b"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

/// Generate a compliant repository ID
///
/// Returns an ID in the format `repo_[a-zA-Z0-9]{16}`
///
/// # Example
/// ```
/// use analysis_engine::validation::id_generator::generate_repository_id;
/// 
/// let id = generate_repository_id();
/// assert!(id.starts_with("repo_"));
/// assert_eq!(id.len(), 21);
/// ```
pub fn generate_repository_id() -> String {
    format!("repo_{}", generate_random_alphanumeric(16))
}

/// Generate a compliant analysis ID
///
/// Returns an ID in the format `analysis_[a-zA-Z0-9]{16}`
///
/// # Example
/// ```
/// use analysis_engine::validation::id_generator::generate_analysis_id;
/// 
/// let id = generate_analysis_id();
/// assert!(id.starts_with("analysis_"));
/// assert_eq!(id.len(), 25);
/// ```
pub fn generate_analysis_id() -> String {
    format!("analysis_{}", generate_random_alphanumeric(16))
}

/// Generate a compliant chunk ID
///
/// Returns an ID in the format `chunk_[a-zA-Z0-9]{16}`
///
/// # Example
/// ```
/// use analysis_engine::validation::id_generator::generate_chunk_id;
/// 
/// let id = generate_chunk_id();
/// assert!(id.starts_with("chunk_"));
/// assert_eq!(id.len(), 22);
/// ```
pub fn generate_chunk_id() -> String {
    format!("chunk_{}", generate_random_alphanumeric(16))
}

/// Generate a random alphanumeric string of specified length
fn generate_random_alphanumeric(length: usize) -> String {
    let mut rng = rand::thread_rng();
    (0..length)
        .map(|_| {
            let idx = rng.gen_range(0..ID_CHARS.len());
            ID_CHARS[idx] as char
        })
        .collect()
}

/// Create a deterministic ID from a seed value (useful for testing)
///
/// This function creates reproducible IDs based on a seed string,
/// which is useful for testing and ensuring consistent IDs for
/// the same input.
pub fn generate_deterministic_id(prefix: &str, seed: &str) -> String {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};
    
    let mut hasher = DefaultHasher::new();
    seed.hash(&mut hasher);
    let hash = hasher.finish();
    
    // Convert hash to alphanumeric string
    let chars: String = format!("{:x}", hash)
        .chars()
        .map(|c| {
            if c.is_numeric() {
                c
            } else {
                // Convert hex letters to uppercase
                c.to_ascii_uppercase()
            }
        })
        .collect();
    
    // Ensure we have exactly 16 characters
    let padded = format!("{:0>16}", chars);
    let truncated: String = padded.chars().take(16).collect();
    
    format!("{}_{}", prefix, truncated)
}

#[cfg(test)]
mod tests {
    use super::*;
    use regex::Regex;
    
    #[test]
    fn test_repository_id_format() {
        let id = generate_repository_id();
        assert!(id.starts_with("repo_"));
        assert_eq!(id.len(), 21);
        
        // Verify alphanumeric pattern
        let re = Regex::new(r"^repo_[a-zA-Z0-9]{16}$").unwrap();
        assert!(re.is_match(&id));
    }
    
    #[test]
    fn test_analysis_id_format() {
        let id = generate_analysis_id();
        assert!(id.starts_with("analysis_"));
        assert_eq!(id.len(), 25);
        
        // Verify alphanumeric pattern
        let re = Regex::new(r"^analysis_[a-zA-Z0-9]{16}$").unwrap();
        assert!(re.is_match(&id));
    }
    
    #[test]
    fn test_chunk_id_format() {
        let id = generate_chunk_id();
        assert!(id.starts_with("chunk_"));
        assert_eq!(id.len(), 22);
        
        // Verify alphanumeric pattern
        let re = Regex::new(r"^chunk_[a-zA-Z0-9]{16}$").unwrap();
        assert!(re.is_match(&id));
    }
    
    #[test]
    fn test_id_uniqueness() {
        // Generate multiple IDs and ensure they're unique
        let mut ids = std::collections::HashSet::new();
        
        for _ in 0..100 {
            ids.insert(generate_repository_id());
            ids.insert(generate_analysis_id());
            ids.insert(generate_chunk_id());
        }
        
        // We should have 300 unique IDs
        assert_eq!(ids.len(), 300);
    }
    
    #[test]
    fn test_deterministic_id() {
        let id1 = generate_deterministic_id("repo", "test-seed");
        let id2 = generate_deterministic_id("repo", "test-seed");
        assert_eq!(id1, id2, "Same seed should produce same ID");
        
        let id3 = generate_deterministic_id("repo", "different-seed");
        assert_ne!(id1, id3, "Different seeds should produce different IDs");
        
        // Verify format
        assert!(id1.starts_with("repo_"));
        assert_eq!(id1.len(), 21);
    }
    
    #[test]
    fn test_random_alphanumeric() {
        let s = generate_random_alphanumeric(16);
        assert_eq!(s.len(), 16);
        assert!(s.chars().all(|c| c.is_alphanumeric()));
    }
}