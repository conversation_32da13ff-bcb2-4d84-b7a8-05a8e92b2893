//! Error handling utilities and helper functions
//!
//! This module provides convenience functions and extensions for error handling
//! to make replacing unwrap() calls easier and more consistent.

use crate::errors::{AnalysisError, AnalysisResult};
use std::fmt::Display;

/// Extension trait for Option types to convert None to errors
pub trait OptionExt<T> {
    /// Convert None to an AnalysisError with a custom message
    fn ok_or_internal(self, msg: impl Display) -> AnalysisResult<T>;
    
    /// Convert None to an AnalysisError::NotFound with a custom message
    fn ok_or_not_found(self, msg: impl Display) -> AnalysisResult<T>;
    
    /// Convert None to an AnalysisError::InvalidInput with a custom message
    fn ok_or_invalid(self, msg: impl Display) -> AnalysisResult<T>;
}

impl<T> OptionExt<T> for Option<T> {
    fn ok_or_internal(self, msg: impl Display) -> AnalysisResult<T> {
        self.ok_or_else(|| AnalysisError::internal(msg.to_string()))
    }
    
    fn ok_or_not_found(self, msg: impl Display) -> AnalysisResult<T> {
        self.ok_or_else(|| AnalysisError::not_found(msg.to_string()))
    }
    
    fn ok_or_invalid(self, msg: impl Display) -> AnalysisResult<T> {
        self.ok_or_else(|| AnalysisError::invalid_input(msg.to_string()))
    }
}

/// Extension trait for parsing with better error messages
pub trait ParseExt {
    /// Parse with a custom error message on failure
    fn parse_or_err<F: std::str::FromStr>(&self, msg: impl Display) -> AnalysisResult<F>;
}

impl ParseExt for str {
    fn parse_or_err<F: std::str::FromStr>(&self, msg: impl Display) -> AnalysisResult<F> {
        self.parse()
            .map_err(|_| AnalysisError::invalid_input(format!("{msg}: '{self}'")))
    }
}

/// Helper for environment variable access
pub fn env_var_or_err(key: &str) -> AnalysisResult<String> {
    std::env::var(key)
        .map_err(|_| AnalysisError::config(format!("Missing environment variable: {key}")))
}

/// Helper for environment variable with default
pub fn env_var_or_default(key: &str, default: &str) -> String {
    std::env::var(key).unwrap_or_else(|_| default.to_string())
}

/// Helper for safe array/vec access
pub trait SafeIndex<T> {
    /// Get element at index or return error
    fn get_or_err(&self, index: usize, context: &str) -> AnalysisResult<&T>;
    
    /// Get first element or return error
    fn first_or_err(&self, context: &str) -> AnalysisResult<&T>;
    
    /// Get last element or return error
    fn last_or_err(&self, context: &str) -> AnalysisResult<&T>;
}

impl<T> SafeIndex<T> for Vec<T> {
    fn get_or_err(&self, index: usize, context: &str) -> AnalysisResult<&T> {
        self.get(index)
            .ok_or_internal(format!("{}: index {} out of bounds (len: {})", context, index, self.len()))
    }
    
    fn first_or_err(&self, context: &str) -> AnalysisResult<&T> {
        self.first()
            .ok_or_internal(format!("{context}: empty collection"))
    }
    
    fn last_or_err(&self, context: &str) -> AnalysisResult<&T> {
        self.last()
            .ok_or_internal(format!("{context}: empty collection"))
    }
}

impl<T> SafeIndex<T> for [T] {
    fn get_or_err(&self, index: usize, context: &str) -> AnalysisResult<&T> {
        self.get(index)
            .ok_or_internal(format!("{}: index {} out of bounds (len: {})", context, index, self.len()))
    }
    
    fn first_or_err(&self, context: &str) -> AnalysisResult<&T> {
        self.first()
            .ok_or_internal(format!("{context}: empty slice"))
    }
    
    fn last_or_err(&self, context: &str) -> AnalysisResult<&T> {
        self.last()
            .ok_or_internal(format!("{context}: empty slice"))
    }
}

/// Helper for regex compilation with proper error handling
#[macro_export]
macro_rules! compile_regex {
    ($pattern:expr) => {{
        regex::Regex::new($pattern)
            .map_err(|e| $crate::errors::ParserError::regex_compilation($pattern, e.to_string()))
    }};
}

/// Helper for lazy static regex with error handling
#[macro_export]
macro_rules! lazy_regex {
    ($name:ident, $pattern:expr) => {
        lazy_static::lazy_static! {
            static ref $name: Result<regex::Regex, $crate::errors::ParserError> = 
                $crate::compile_regex!($pattern);
        }
    };
}

/// Helper for safe JSON parsing
pub trait JsonExt {
    /// Parse JSON with context
    fn parse_json_or_err<T: serde::de::DeserializeOwned>(
        json_str: &str, 
        context: &str
    ) -> AnalysisResult<T>;
}

impl JsonExt for str {
    fn parse_json_or_err<T: serde::de::DeserializeOwned>(
        json_str: &str, 
        context: &str
    ) -> AnalysisResult<T> {
        serde_json::from_str(json_str)
            .map_err(|e| AnalysisError::internal(format!("{context}: {e}")))
    }
}

/// Helper for safe YAML parsing
pub trait YamlExt {
    /// Parse YAML with context
    fn parse_yaml_or_err<T: serde::de::DeserializeOwned>(
        yaml_str: &str, 
        context: &str
    ) -> AnalysisResult<T>;
}

impl YamlExt for str {
    fn parse_yaml_or_err<T: serde::de::DeserializeOwned>(
        yaml_str: &str, 
        context: &str
    ) -> AnalysisResult<T> {
        serde_yaml::from_str(yaml_str)
            .map_err(|e| AnalysisError::internal(format!("{context}: {e}")))
    }
}

/// Helper for safe TOML parsing
pub trait TomlExt {
    /// Parse TOML with context
    fn parse_toml_or_err<T: serde::de::DeserializeOwned>(
        toml_str: &str, 
        context: &str
    ) -> AnalysisResult<T>;
}

impl TomlExt for str {
    fn parse_toml_or_err<T: serde::de::DeserializeOwned>(
        toml_str: &str, 
        context: &str
    ) -> AnalysisResult<T> {
        toml::from_str(toml_str)
            .map_err(|e| AnalysisError::internal(format!("{context}: {e}")))
    }
}

/// Helper for UTF-8 conversion
pub trait Utf8Ext {
    /// Convert to UTF-8 string with context
    fn to_utf8_or_err(&self, context: &str) -> AnalysisResult<&str>;
}

impl Utf8Ext for [u8] {
    fn to_utf8_or_err(&self, context: &str) -> AnalysisResult<&str> {
        std::str::from_utf8(self)
            .map_err(|e| AnalysisError::internal(format!("{context}: invalid UTF-8: {e}")))
    }
}

/// Helper for path operations
pub trait PathExt {
    /// Convert path to string with error handling
    fn to_str_or_err(&self) -> AnalysisResult<&str>;
    
    /// Get file name as string with error handling
    fn file_name_or_err(&self) -> AnalysisResult<&str>;
    
    /// Get parent directory with error handling
    fn parent_or_err(&self) -> AnalysisResult<&std::path::Path>;
}

impl PathExt for std::path::Path {
    fn to_str_or_err(&self) -> AnalysisResult<&str> {
        self.to_str()
            .ok_or_internal(format!("Invalid path: {self:?}"))
    }
    
    fn file_name_or_err(&self) -> AnalysisResult<&str> {
        self.file_name()
            .and_then(|n| n.to_str())
            .ok_or_internal(format!("Invalid file name in path: {self:?}"))
    }
    
    fn parent_or_err(&self) -> AnalysisResult<&std::path::Path> {
        self.parent()
            .ok_or_internal(format!("Path has no parent: {self:?}"))
    }
}

/// Helper for mutex operations
pub trait MutexExt<T> {
    /// Lock mutex with error handling
    fn lock_or_err(&self) -> AnalysisResult<std::sync::MutexGuard<T>>;
}

impl<T> MutexExt<T> for std::sync::Mutex<T> {
    fn lock_or_err(&self) -> AnalysisResult<std::sync::MutexGuard<T>> {
        self.lock()
            .map_err(|_| AnalysisError::internal("Mutex poisoned"))
    }
}

/// Helper for RwLock operations
pub trait RwLockExt<T> {
    /// Read lock with error handling
    fn read_or_err(&self) -> AnalysisResult<std::sync::RwLockReadGuard<T>>;
    
    /// Write lock with error handling
    fn write_or_err(&self) -> AnalysisResult<std::sync::RwLockWriteGuard<T>>;
}

impl<T> RwLockExt<T> for std::sync::RwLock<T> {
    fn read_or_err(&self) -> AnalysisResult<std::sync::RwLockReadGuard<T>> {
        self.read()
            .map_err(|_| AnalysisError::internal("RwLock poisoned"))
    }
    
    fn write_or_err(&self) -> AnalysisResult<std::sync::RwLockWriteGuard<T>> {
        self.write()
            .map_err(|_| AnalysisError::internal("RwLock poisoned"))
    }
}

/// Helper for channel operations
pub trait ChannelExt<T> {
    /// Try to receive with timeout
    fn recv_timeout_or_err(&self, timeout: std::time::Duration) -> AnalysisResult<T>;
}

impl<T> ChannelExt<T> for std::sync::mpsc::Receiver<T> {
    fn recv_timeout_or_err(&self, timeout: std::time::Duration) -> AnalysisResult<T> {
        self.recv_timeout(timeout)
            .map_err(|e| match e {
                std::sync::mpsc::RecvTimeoutError::Timeout => {
                    AnalysisError::timeout(format!("Channel receive timeout after {timeout:?}"))
                }
                std::sync::mpsc::RecvTimeoutError::Disconnected => {
                    AnalysisError::internal("Channel disconnected")
                }
            })
    }
}

/// Macro for creating parser errors with file context
#[macro_export]
macro_rules! parse_error {
    ($file:expr, $reason:expr) => {
        $crate::errors::ParserError::parse_failed($file, $reason)
    };
    ($file:expr, $reason:expr, $($arg:tt)*) => {
        $crate::errors::ParserError::parse_failed($file, format!($reason, $($arg)*))
    };
}

/// Macro for creating storage errors with query context
#[macro_export]
macro_rules! storage_error {
    ($query:expr, $reason:expr) => {
        $crate::errors::StorageError::query_failed($query, $reason)
    };
    ($query:expr, $reason:expr, $($arg:tt)*) => {
        $crate::errors::StorageError::query_failed($query, format!($reason, $($arg)*))
    };
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::errors::ParserError;

    #[test]
    fn test_option_ext() {
        let some_value: Option<i32> = Some(42);
        assert_eq!(some_value.ok_or_internal("test").expect("Test some_value should be valid"), 42);
        
        let none_value: Option<i32> = None;
        let err = none_value.ok_or_internal("missing value").expect_err("Test none_value should return error");
        assert!(matches!(err, AnalysisError::Internal(_)));
        
        let err = none_value.ok_or_not_found("not found").expect_err("Test none_value should return not found error");
        assert!(matches!(err, AnalysisError::NotFound(_)));
        
        let err = none_value.ok_or_invalid("invalid").expect_err("Test none_value should return invalid error");
        assert!(matches!(err, AnalysisError::InvalidInput(_)));
    }

    #[test]
    fn test_safe_index() {
        let vec = vec![1, 2, 3];
        assert_eq!(*vec.get_or_err(1, "test").expect("Test vec should have element at index 1"), 2);
        assert_eq!(*vec.first_or_err("test").expect("Test vec should have first element"), 1);
        assert_eq!(*vec.last_or_err("test").expect("Test vec should have last element"), 3);
        
        let err = vec.get_or_err(10, "test").expect_err("Test vec should return error for invalid index");
        assert!(matches!(err, AnalysisError::Internal(_)));
        
        let empty: Vec<i32> = vec![];
        let err = empty.first_or_err("test").expect_err("Test empty vec should return error");
        assert!(matches!(err, AnalysisError::Internal(_)));
    }

    #[test]
    fn test_utf8_ext() {
        let valid_utf8 = b"hello world";
        assert_eq!(valid_utf8.to_utf8_or_err("test").expect("Test valid UTF-8 should convert successfully"), "hello world");
        
        let invalid_utf8 = b"\xFF\xFE";
        let err = invalid_utf8.to_utf8_or_err("test").expect_err("Test invalid UTF-8 should return error");
        assert!(matches!(err, AnalysisError::Internal(_)));
    }

    #[test]
    fn test_json_ext() {
        #[derive(serde::Deserialize, PartialEq, Debug)]
        struct TestStruct {
            value: i32,
        }
        
        let json = r#"{"value": 42}"#;
        let parsed: TestStruct = str::parse_json_or_err(json, "test").expect("Test valid JSON should parse successfully");
        assert_eq!(parsed.value, 42);
        
        let invalid_json = r#"{"value": }"#;
        let err = str::parse_json_or_err::<TestStruct>(invalid_json, "test").expect_err("Test invalid JSON should return error");
        assert!(matches!(err, AnalysisError::Internal(_)));
    }

    #[test]
    fn test_compile_regex_macro() {
        let regex = compile_regex!(r"\d+").expect("Test regex pattern should compile successfully");
        assert!(regex.is_match("123"));
        
        let err = compile_regex!(r"[").expect_err("Test invalid regex pattern should return error");
        assert!(matches!(err, ParserError::RegexCompilation { .. }));
    }
}