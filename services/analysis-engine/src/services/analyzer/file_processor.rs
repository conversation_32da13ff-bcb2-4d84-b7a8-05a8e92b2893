use crate::backpressure::BackpressureManager;
use crate::config::ServiceConfig;
use crate::models::*;
use crate::parser::TreeSitterParser;
use crate::storage::CacheManager;
use anyhow::Result;
use chrono::Utc;
use std::path::{Path, PathBuf};
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::Arc;
use tokio::sync::{mpsc, Semaphore};

pub struct FileProcessor {
    parser: Arc<TreeSitterParser>,
    config: Arc<ServiceConfig>,
    backpressure_manager: Option<Arc<BackpressureManager>>,
    cache_manager: Arc<CacheManager>,
}

impl FileProcessor {
    pub fn new(
        parser: Arc<TreeSitterParser>,
        config: Arc<ServiceConfig>,
        backpressure_manager: Option<Arc<BackpressureManager>>,
        cache_manager: Arc<CacheManager>,
    ) -> Self {
        Self {
            parser,
            config,
            backpressure_manager,
            cache_manager,
        }
    }

    /// Parse files in parallel with intelligent concurrency control
    pub async fn parse_files_parallel(
        &self,
        files: &[PathBuf],
        progress_tx: mpsc::Sender<ProgressUpdate>,
        analysis_id: String,
    ) -> Result<Vec<Result<FileAnalysis, ParseError>>> {
        let total_files = files.len();
        let progress_counter = Arc::new(AtomicUsize::new(0));
        let error_counter = Arc::new(AtomicUsize::new(0));
        let parser = self.parser.clone();
        let timeout_seconds = self.config.analysis.analysis_timeout_seconds;

        // Enhanced adaptive batch sizing with intelligent scheduling
        let available_memory = self.get_available_memory();
        let cpu_count = num_cpus::get();

        // Calculate optimal concurrency based on system resources and expected load
        let max_concurrent_files = self
            .calculate_optimal_concurrency(available_memory, cpu_count)
            .await;

        tracing::info!(
            "Processing {} files with {} max concurrent processors",
            total_files,
            max_concurrent_files
        );

        // Use semaphore to control concurrent processing
        let semaphore = Arc::new(Semaphore::new(max_concurrent_files));
        let mut all_results = Vec::with_capacity(files.len());

        // Process files in smaller batches with intelligent load balancing
        let batch_size = self
            .get_optimal_batch_size(total_files, max_concurrent_files)
            .await;

        for (batch_idx, chunk) in files.chunks(batch_size).enumerate() {
            let mut batch_handles = Vec::new();

            for file_path in chunk {
                let file_path = file_path.clone();
                let progress_tx = progress_tx.clone();
                let analysis_id = analysis_id.clone();
                let progress_counter = progress_counter.clone();
                let error_counter = error_counter.clone();
                let parser = parser.clone();
                let semaphore = semaphore.clone();
                let backpressure_manager = self.backpressure_manager.clone();
                let cache_manager = self.cache_manager.clone();

                let handle = tokio::spawn(async move {
                    // Acquire semaphore permit
                    let _permit = semaphore.acquire().await.expect("Failed to acquire semaphore permit for file processing");

                    // Check backpressure before processing
                    if let Some(bp_manager) = &backpressure_manager {
                        if let Err(e) = bp_manager.check_memory_pressure().await {
                            tracing::warn!("Memory pressure detected, throttling: {}", e);
                            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                        }
                    }

                    // Acquire parsing permit for backpressure control
                    let _parsing_permit = if let Some(bp_manager) = &backpressure_manager {
                        match bp_manager.acquire_parsing_permit().await {
                            Ok(permit) => Some(permit),
                            Err(e) => {
                                tracing::warn!("Failed to acquire parsing permit: {}", e);
                                return Err(ParseError {
                                    file_path: file_path.to_string_lossy().to_string(),
                                    error_type: ParseErrorType::Other,
                                    message: "System overloaded - parsing permit unavailable"
                                        .to_string(),
                                    position: None,
                                });
                            }
                        }
                    } else {
                        None
                    };

                    // Check if we have a cached AST first
                    let parsed_result = match Self::try_get_cached_ast(&file_path, &cache_manager).await {
                        Ok(Some(cached_ast)) => {
                            tracing::debug!("Using cached AST for file: {}", file_path.display());
                            Ok(cached_ast)
                        }
                        Ok(None) => {
                            // No cached AST, perform parsing
                            let result = tokio::time::timeout(
                                std::time::Duration::from_secs(timeout_seconds),
                                parser.parse_file(&file_path),
                            )
                            .await;

                            let parsed_result = match result {
                                Ok(Ok(ast)) => {
                                    // Cache the parsed AST for future use
                                    Self::cache_ast_if_possible(&file_path, &ast, &cache_manager).await;
                                    Ok(ast)
                                }
                                Ok(Err(e)) => Err(e),
                                Err(_) => Err(ParseError {
                                    file_path: file_path.to_string_lossy().to_string(),
                                    error_type: ParseErrorType::Timeout,
                                    message: "Parse timeout".to_string(),
                                    position: None,
                                }),
                            };
                            
                            parsed_result
                        }
                        Err(e) => {
                            tracing::warn!("Failed to check AST cache for {}: {}", file_path.display(), e);
                            // Fall back to parsing if cache check fails
                            let result = tokio::time::timeout(
                                std::time::Duration::from_secs(timeout_seconds),
                                parser.parse_file(&file_path),
                            )
                            .await;

                            match result {
                                Ok(Ok(ast)) => Ok(ast),
                                Ok(Err(e)) => Err(e),
                                Err(_) => Err(ParseError {
                                    file_path: file_path.to_string_lossy().to_string(),
                                    error_type: ParseErrorType::Timeout,
                                    message: "Parse timeout".to_string(),
                                    position: None,
                                }),
                            }
                        }
                    };

                    // Update counters
                    let completed = progress_counter.fetch_add(1, Ordering::Relaxed) + 1;
                    if parsed_result.is_err() {
                        error_counter.fetch_add(1, Ordering::Relaxed);
                    }

                    // Report progress more frequently for better UX
                    if completed % 5 == 0 || completed == total_files {
                        let progress = 35.0 + (completed as f64 / total_files as f64) * 35.0;
                        let errors = error_counter.load(Ordering::Relaxed);
                        let success_rate = if completed > 0 {
                            ((completed - errors) as f64 / completed as f64) * 100.0
                        } else {
                            100.0
                        };

                        let _ = progress_tx
                            .send(ProgressUpdate {
                                analysis_id: analysis_id.clone(),
                                progress,
                                stage: format!("Parsed {completed}/{total_files} files ({success_rate:.1}% success)"),
                                message: Some(format!("Concurrent processing: {max_concurrent_files} active")),
                                timestamp: Utc::now(),
                                files_processed: Some(completed),
                                total_files: Some(total_files),
                            })
                            .await;
                    }

                    parsed_result
                });

                batch_handles.push(handle);
            }

            // Wait for batch to complete
            for handle in batch_handles {
                match handle.await {
                    Ok(result) => all_results.push(result),
                    Err(e) => {
                        tracing::error!("Task panicked during file parsing: {}", e);
                        all_results.push(Err(ParseError {
                            file_path: "unknown".to_string(),
                            error_type: ParseErrorType::Other,
                            message: format!("Task panicked: {e}"),
                            position: None,
                        }));
                    }
                }
            }

            // Adaptive delay between batches based on system load
            if batch_idx % 10 == 0 && batch_idx > 0 {
                if let Some(bp_manager) = &self.backpressure_manager {
                    let metrics = bp_manager.get_metrics().await;
                    if metrics.memory_usage_mb > 2500 {
                        // High memory usage
                        tokio::time::sleep(std::time::Duration::from_millis(50)).await;
                    } else if metrics.cpu_usage_percent > 80.0 {
                        // High CPU usage
                        tokio::time::sleep(std::time::Duration::from_millis(25)).await;
                    }
                }
            }
        }

        let total_processed = progress_counter.load(Ordering::Relaxed);
        let total_errors = error_counter.load(Ordering::Relaxed);
        let final_success_rate = if total_processed > 0 {
            ((total_processed - total_errors) as f64 / total_processed as f64) * 100.0
        } else {
            100.0
        };

        tracing::info!(
            "Parallel processing completed: {}/{} files processed, {:.1}% success rate",
            total_processed,
            total_files,
            final_success_rate
        );

        Ok(all_results)
    }

    /// Calculate optimal concurrency based on system resources
    async fn calculate_optimal_concurrency(
        &self,
        available_memory: u64,
        cpu_count: usize,
    ) -> usize {
        // Base concurrency on available memory and CPU cores
        let memory_factor = if available_memory < 1024 * 1024 * 1024 {
            // Less than 1GB
            1 // Very conservative
        } else if available_memory < 2 * 1024 * 1024 * 1024 {
            // Less than 2GB
            2 // Conservative
        } else if available_memory < 4 * 1024 * 1024 * 1024 {
            // Less than 4GB
            3 // Moderate
        } else {
            4 // Aggressive
        };

        let base_concurrency = cpu_count * memory_factor;

        // Adjust based on backpressure manager if available
        let adjusted_concurrency = if let Some(bp_manager) = &self.backpressure_manager {
            let metrics = bp_manager.get_metrics().await;
            if metrics.memory_usage_mb > 2000 {
                // High memory usage
                (base_concurrency / 2).max(1)
            } else if metrics.cpu_usage_percent > 70.0 {
                // High CPU usage
                (base_concurrency * 2 / 3).max(1)
            } else {
                base_concurrency
            }
        } else {
            base_concurrency
        };

        // Cap at reasonable limits for stability
        let max_concurrency = if self.config.analysis.max_concurrent_analyses > 10 {
            self.config.analysis.max_concurrent_analyses * 2 // Allow 2x file processing vs analysis
        } else {
            20 // Reasonable default
        };

        debug_assert!(
            max_concurrency >= 1,
            "clamp bounds invalid: max_concurrency={max_concurrency}, min=1"
        );
        adjusted_concurrency.clamp(1, max_concurrency)
    }

    /// Adaptive load balancing for concurrent analyses
    async fn get_optimal_batch_size(&self, total_items: usize, current_load: usize) -> usize {
        let system_memory = self.get_available_memory();
        let cpu_cores = num_cpus::get();

        // Base batch size on available resources
        let memory_based_batch = (system_memory / (100 * 1024 * 1024)).max(1) as usize; // 100MB per batch
        let cpu_based_batch = cpu_cores * 2; // 2 items per core

        // Choose the minimum to avoid resource exhaustion
        let base_batch_size = memory_based_batch.min(cpu_based_batch);

        // Adjust based on current load
        let load_factor = if current_load > 0 {
            (50.0 / current_load as f64).clamp(0.5, 2.0) // Scale between 0.5x and 2x
        } else {
            1.0
        };

        let adaptive_batch_size = ((base_batch_size as f64 * load_factor) as usize)
            .max(1)
            .min(total_items);

        tracing::debug!(
            "Calculated optimal batch size: {} (memory: {}, cpu: {}, load_factor: {:.2})",
            adaptive_batch_size,
            memory_based_batch,
            cpu_based_batch,
            load_factor
        );

        adaptive_batch_size
    }

    fn get_available_memory(&self) -> u64 {
        // Platform-specific memory check
        #[cfg(target_os = "linux")]
        {
            if let Ok(contents) = std::fs::read_to_string("/proc/meminfo") {
                for line in contents.lines() {
                    if line.starts_with("MemAvailable:") {
                        if let Some(kb_str) = line.split_whitespace().nth(1) {
                            if let Ok(kb) = kb_str.parse::<u64>() {
                                return kb * 1024; // Convert KB to bytes
                            }
                        }
                    }
                }
            }
        }

        // Default to 4GB if we can't determine
        4 * 1024 * 1024 * 1024
    }

    /// Try to get cached AST for a file by computing its content hash
    async fn try_get_cached_ast(
        file_path: &Path,
        cache_manager: &CacheManager,
    ) -> Result<Option<FileAnalysis>> {
        use sha2::{Digest, Sha256};
        use std::fs;

        // Read file content to compute hash
        let file_content = match fs::read_to_string(file_path) {
            Ok(content) => content,
            Err(e) => {
                tracing::warn!("Failed to read file for cache check {}: {}", file_path.display(), e);
                return Ok(None);
            }
        };

        // Compute content hash
        let mut hasher = Sha256::new();
        hasher.update(&file_content);
        let content_hash = format!("{:x}", hasher.finalize());

        // Check cache for AST
        match cache_manager.get_ast_by_content_hash(&content_hash).await {
            Ok(Some(ast)) => {
                // Create FileAnalysis with cached AST
                let file_analysis = FileAnalysis {
                    path: file_path.to_string_lossy().to_string(),
                    language: Self::detect_language_from_extension(file_path),
                    content_hash: content_hash.clone(),
                    size_bytes: Some(file_content.len() as u64),
                    ast,
                    metrics: Default::default(), // Will be computed later if needed
                    chunks: None,
                    symbols: None,
                };
                Ok(Some(file_analysis))
            }
            Ok(None) => Ok(None),
            Err(e) => {
                tracing::warn!("Cache lookup failed for {}: {}", file_path.display(), e);
                Ok(None)
            }
        }
    }

    /// Cache an AST if possible
    async fn cache_ast_if_possible(
        file_path: &Path,
        analysis: &FileAnalysis,
        cache_manager: &CacheManager,
    ) {
        if let Err(e) = cache_manager
            .set_ast_by_content_hash(&analysis.content_hash, &analysis.ast)
            .await
        {
            tracing::warn!("Failed to cache AST for {}: {}", file_path.display(), e);
        } else {
            tracing::debug!("Successfully cached AST for {}", file_path.display());
        }
    }

    /// Simple language detection from file extension
    fn detect_language_from_extension(file_path: &Path) -> String {
        match file_path.extension().and_then(|e| e.to_str()) {
            Some("rs") => "rust".to_string(),
            Some("py") => "python".to_string(),
            Some("js") => "javascript".to_string(),
            Some("ts") => "typescript".to_string(),
            Some("tsx") => "typescript".to_string(),
            Some("jsx") => "javascript".to_string(),
            Some("go") => "go".to_string(),
            Some("java") => "java".to_string(),
            Some("c") => "c".to_string(),
            Some("cpp") | Some("cc") | Some("cxx") => "cpp".to_string(),
            Some("rb") => "ruby".to_string(),
            Some("sh") => "bash".to_string(),
            Some("jl") => "julia".to_string(),
            Some("scala") => "scala".to_string(),
            Some("php") => "php".to_string(),
            Some("ml") | Some("mli") => "ocaml".to_string(),
            Some("html") => "html".to_string(),
            Some("css") => "css".to_string(),
            Some("json") => "json".to_string(),
            Some("md") => "markdown".to_string(),
            _ => "unknown".to_string(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_config() -> ServiceConfig {
        ServiceConfig {
            service: crate::config::ServiceSettings {
                name: "test".to_string(),
                version: "0.1.0".to_string(),
                port: 8001,
                host: "127.0.0.1".to_string(),
                environment: crate::config::Environment::Development,
            },
            gcp: crate::config::GcpSettings {
                project_id: "test".to_string(),
                spanner_instance: "test".to_string(),
                spanner_database: "test".to_string(),
                storage_bucket: "test".to_string(),
                storage_bucket_name: "test".to_string(),
                pubsub_topic: "test".to_string(),
                region: "us-central1".to_string(),
            },
            analysis: crate::config::AnalysisSettings {
                max_concurrent_analyses: 10,
                max_repository_size_gb: 10,
                analysis_timeout_seconds: 300,
                max_file_size_mb: 10,
                temp_dir: "/tmp".to_string(),
                supported_languages: vec!["rust".to_string()],
            },
            security: crate::config::SecuritySettings {
                enable_auth: false,
                api_key_header: "x-api-key".to_string(),
                jwt_secret: Some("test".to_string()),
                cors_origins: vec!["*".to_string()],
            },
            observability: crate::config::ObservabilitySettings {
                enable_tracing: false,
                enable_metrics: false,
                log_level: "info".to_string(),
                otel_endpoint: None,
            },
            circuit_breaker: crate::config::CircuitBreakerConfig {
                failure_threshold: 5,
                reset_timeout_secs: 60,
            },
            redis: crate::config::RedisSettings::default(),
            resource_limits: crate::config::ResourceLimitConfig::default(),
        }
    }

    #[tokio::test]
    async fn test_calculate_optimal_concurrency() {
        let config = Arc::new(create_test_config());
        let parser = Arc::new(TreeSitterParser::new(config.clone()).expect("Failed to create TreeSitterParser for test"));
        let cache_manager = Arc::new(CacheManager::new_without_redis());
        let processor = FileProcessor::new(parser, config, None, cache_manager);

        // Test with different memory sizes
        let concurrency_low_mem = processor
            .calculate_optimal_concurrency(512 * 1024 * 1024, 4)
            .await;
        assert_eq!(concurrency_low_mem, 4); // 4 cores * 1 (very conservative)

        let concurrency_med_mem = processor
            .calculate_optimal_concurrency(3 * 1024 * 1024 * 1024, 4)
            .await;
        assert_eq!(concurrency_med_mem, 12); // 4 cores * 3 (moderate)

        let concurrency_high_mem = processor
            .calculate_optimal_concurrency(8 * 1024 * 1024 * 1024, 4)
            .await;
        assert_eq!(concurrency_high_mem, 16); // 4 cores * 4 (aggressive)
    }

    #[tokio::test]
    async fn test_get_optimal_batch_size() {
        let config = Arc::new(create_test_config());
        let parser = Arc::new(TreeSitterParser::new(config.clone()).expect("Failed to create TreeSitterParser for test"));
        let cache_manager = Arc::new(CacheManager::new_without_redis());
        let processor = FileProcessor::new(parser, config, None, cache_manager);

        // Test batch size calculation
        let batch_size = processor.get_optimal_batch_size(100, 10).await;
        assert!(batch_size > 0 && batch_size <= 100);

        // Test with high load
        let batch_size_high_load = processor.get_optimal_batch_size(100, 100).await;
        assert!(batch_size_high_load > 0 && batch_size_high_load < batch_size);

        // Test with no load
        let batch_size_no_load = processor.get_optimal_batch_size(100, 0).await;
        assert!(batch_size_no_load > 0 && batch_size_no_load <= 100);
    }
}
