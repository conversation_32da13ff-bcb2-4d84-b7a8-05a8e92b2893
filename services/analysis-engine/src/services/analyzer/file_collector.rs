use crate::config::ServiceConfig;
use anyhow::Result;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use walkdir::WalkDir;

pub struct FileCollector {
    config: Arc<ServiceConfig>,
}

impl FileCollector {
    pub fn new(config: Arc<ServiceConfig>) -> Self {
        Self { config }
    }

    /// Collect source files from a repository path with include/exclude patterns
    pub fn collect_source_files(
        &self,
        repo_path: &Path,
        include_patterns: &[String],
        exclude_patterns: &[String],
    ) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();
        let default_exclude = [
            "**/target/**",
            "**/node_modules/**",
            "*.lock",
            "**/.git/**",
            "**/dist/**",
            "**/build/**",
            "**/.cache/**",
            "**/.venv/**",
            "**/__pycache__/**",
            "*.pyc",
            "*.pyo",
            "*.so",
            "*.dylib",
            "*.dll",
            "*.exe",
        ];

        for entry in WalkDir::new(repo_path)
            .follow_links(true)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                let path = entry.path();

                // Skip if path cannot be made relative to repo
                let relative_path = match path.strip_prefix(repo_path) {
                    Ok(rel) => rel,
                    Err(_) => continue,
                };

                let path_str = relative_path.to_string_lossy();

                // Check default excludes
                let is_default_excluded = default_exclude
                    .iter()
                    .any(|p| glob_match::glob_match(p, &path_str));

                if is_default_excluded {
                    continue;
                }

                // Check include patterns (if specified)
                let included = include_patterns.is_empty()
                    || include_patterns
                        .iter()
                        .any(|p| glob_match::glob_match(p, &path_str));

                // Check exclude patterns
                let excluded = exclude_patterns
                    .iter()
                    .any(|p| glob_match::glob_match(p, &path_str));

                if included && !excluded {
                    // Check file size limit
                    if let Ok(metadata) = entry.metadata() {
                        let max_size_bytes = self.config.resource_limits.max_file_size_bytes;
                        if metadata.len() <= max_size_bytes {
                            files.push(path.to_path_buf());
                        } else {
                            tracing::warn!(
                                "Skipping file {} due to size: {} bytes > {} bytes limit",
                                path.display(),
                                metadata.len(),
                                max_size_bytes
                            );
                        }
                    }
                }
            }
        }

        tracing::info!(
            "Collected {} source files from {} with {} include and {} exclude patterns",
            files.len(),
            repo_path.display(),
            include_patterns.len(),
            exclude_patterns.len()
        );

        Ok(files)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::TempDir;

    fn create_test_config() -> ServiceConfig {
        ServiceConfig {
            service: crate::config::ServiceSettings {
                name: "test".to_string(),
                version: "0.1.0".to_string(),
                port: 8001,
                host: "127.0.0.1".to_string(),
                environment: crate::config::Environment::Development,
            },
            gcp: crate::config::GcpSettings {
                project_id: "test".to_string(),
                spanner_instance: "test".to_string(),
                spanner_database: "test".to_string(),
                storage_bucket: "test".to_string(),
                storage_bucket_name: "test".to_string(),
                pubsub_topic: "test".to_string(),
                region: "us-central1".to_string(),
            },
            analysis: crate::config::AnalysisSettings {
                max_concurrent_analyses: 10,
                max_repository_size_gb: 10,
                analysis_timeout_seconds: 300,
                max_file_size_mb: 10,
                temp_dir: "/tmp".to_string(),
                supported_languages: vec!["rust".to_string()],
            },
            security: crate::config::SecuritySettings {
                enable_auth: false,
                api_key_header: "x-api-key".to_string(),
                jwt_secret: Some("test".to_string()),
                cors_origins: vec!["*".to_string()],
            },
            observability: crate::config::ObservabilitySettings {
                enable_tracing: false,
                enable_metrics: false,
                log_level: "info".to_string(),
                otel_endpoint: None,
            },
            circuit_breaker: Default::default(),
            redis: Default::default(),
            resource_limits: crate::config::ResourceLimitConfig {
                max_file_size_bytes: 10 * 1024 * 1024,
                parse_timeout_seconds: 10,
                max_analysis_memory_mb: 2048,
                max_dependency_count: 10000,
            },
        }
    }

    #[test]
    fn test_file_collection_with_patterns() {
        let temp_dir = TempDir::new().expect("Failed to create temp directory for test");
        let repo_path = temp_dir.path();

        // Create test file structure
        fs::create_dir_all(repo_path.join("src")).expect("Failed to create src directory");
        fs::write(repo_path.join("src/main.rs"), "fn main() {}").expect("Failed to write main.rs");
        fs::write(repo_path.join("src/lib.rs"), "pub fn lib() {}").expect("Failed to write lib.rs");

        fs::create_dir_all(repo_path.join("tests")).expect("Failed to create tests directory");
        fs::write(repo_path.join("tests/test.rs"), "#[test] fn test() {}").expect("Failed to write test.rs");

        fs::create_dir_all(repo_path.join("target/debug")).expect("Failed to create target/debug directory");
        fs::write(repo_path.join("target/debug/app"), "binary").expect("Failed to write target/debug/app");

        fs::create_dir_all(repo_path.join("docs")).expect("Failed to create docs directory");
        fs::write(repo_path.join("docs/README.md"), "# Docs").expect("Failed to write README.md");

        let config = Arc::new(create_test_config());
        let collector = FileCollector::new(config);

        // Test with no patterns (should exclude target/)
        let files = collector.collect_source_files(repo_path, &[], &[]).expect("Failed to collect source files");
        assert_eq!(files.len(), 4); // main.rs, lib.rs, test.rs, README.md

        // Test with include pattern
        let files = collector
            .collect_source_files(repo_path, &["**/*.rs".to_string()], &[])
            .expect("Failed to collect source files with include pattern");
        assert_eq!(files.len(), 3); // main.rs, lib.rs, test.rs

        // Test with exclude pattern
        let files = collector
            .collect_source_files(repo_path, &[], &["**/tests/*".to_string()])
            .expect("Failed to collect source files with exclude pattern");
        assert_eq!(files.len(), 3); // main.rs, lib.rs, README.md

        // Test with both include and exclude
        let files = collector
            .collect_source_files(
                repo_path,
                &["**/*.rs".to_string()],
                &["**/tests/*".to_string()],
            )
            .expect("Failed to collect source files with include and exclude patterns");
        assert_eq!(files.len(), 2); // main.rs, lib.rs
    }

    #[test]
    fn test_file_size_limit() {
        let temp_dir = TempDir::new().expect("Failed to create temp directory for size limit test");
        let repo_path = temp_dir.path();

        // Create test files
        fs::create_dir_all(repo_path.join("src")).expect("Failed to create src directory for size test");

        // Small file
        fs::write(repo_path.join("src/small.rs"), "a".repeat(1000)).expect("Failed to write small.rs");

        // Large file (over 10MB limit)
        let large_content = "b".repeat(11 * 1024 * 1024); // 11MB
        fs::write(repo_path.join("src/large.rs"), large_content).expect("Failed to write large.rs");

        let config = Arc::new(create_test_config());
        let collector = FileCollector::new(config);

        let files = collector.collect_source_files(repo_path, &[], &[]).expect("Failed to collect source files for size test");
        assert_eq!(files.len(), 1); // Only small.rs should be collected
    }

    #[test]
    fn test_default_excludes() {
        let temp_dir = TempDir::new().expect("Failed to create temp directory for excludes test");
        let repo_path = temp_dir.path();

        // Create files in excluded directories
        fs::create_dir_all(repo_path.join("node_modules")).expect("Failed to create node_modules directory");
        fs::write(
            repo_path.join("node_modules/index.js"),
            "module.exports = {}",
        )
        .expect("Failed to write node_modules/index.js");

        fs::create_dir_all(repo_path.join(".git")).expect("Failed to create .git directory");
        fs::write(repo_path.join(".git/config"), "[core]").expect("Failed to write .git/config");

        fs::write(repo_path.join("Cargo.lock"), "# lock file").expect("Failed to write Cargo.lock");

        // Create files that should be included
        fs::write(repo_path.join("main.rs"), "fn main() {}").expect("Failed to write main.rs");

        let config = Arc::new(create_test_config());
        let collector = FileCollector::new(config);

        let files = collector.collect_source_files(repo_path, &[], &[]).expect("Failed to collect source files for excludes test");
        assert_eq!(files.len(), 1); // Only main.rs should be collected
    }
}
