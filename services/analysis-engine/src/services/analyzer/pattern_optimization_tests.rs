//! Pattern optimization tests for Agent 03 validation
//! Tests comprehensive edge cases for clamp optimizations

#[cfg(test)]
mod pattern_optimization_tests {
    

    #[test]
    fn test_clamp_edge_cases() {
        // Test NaN behavior
        assert!(f64::NAN.clamp(0.0, 1.0).is_nan());

        // Test infinity behavior
        assert_eq!(f64::INFINITY.clamp(0.0, 1.0), 1.0);
        assert_eq!(f64::NEG_INFINITY.clamp(0.0, 1.0), 0.0);

        // Test normal ranges
        assert_eq!(0.5_f64.clamp(0.0, 1.0), 0.5);
        assert_eq!((-0.5_f64).clamp(0.0, 1.0), 0.0);
        assert_eq!(1.5_f64.clamp(0.0, 1.0), 1.0);
    }

    #[test]
    fn test_original_vs_optimized_behavior() {
        // Verify clamp() produces same results as min().max() for non-NaN values
        // Note: NaN behavior differs between min().max() and clamp():
        // - min()/max() ignore NaN and return the non-NaN value
        // - clamp() propagates NaN (returns NaN if input is NaN)
        // Both behaviors are correct, just semantically different operations.
        
        let test_values = vec![0.0, 0.5, 1.0, 1.5, -0.5, f64::INFINITY, f64::NEG_INFINITY];
        let min = 0.0;
        let max = 1.0;

        for value in test_values {
            let original = value.min(max).max(min);
            let optimized = value.clamp(min, max);
            
            // For non-NaN values, both should produce the same result
            assert_eq!(original, optimized, "Behavior changed for value: {value}");
        }
        
        // Test NaN behavior separately - both behaviors are valid
        let nan_original = f64::NAN.min(max).max(min);
        let nan_optimized = f64::NAN.clamp(min, max);
        
        // min(NaN, max) returns max, then max.max(min) returns max (since max > min)
        assert_eq!(nan_original, max, "NaN.min(max).max(min) should return max when max > min");
        
        // clamp() returns NaN when input is NaN
        assert!(nan_optimized.is_nan(), "clamp() should return NaN for NaN input");
        
        // These are different behaviors, and that's OK - they're semantically different operations
    }

    #[test]
    fn test_specific_clamp_bounds() {
        // Test all actual bounds used in the codebase

        // Load factor bounds (0.5, 2.0)
        assert_eq!(0.25_f64.clamp(0.5, 2.0), 0.5);
        assert_eq!(1.0_f64.clamp(0.5, 2.0), 1.0);
        assert_eq!(3.0_f64.clamp(0.5, 2.0), 2.0);

        // Score bounds (0.0, 1.0)
        assert_eq!((-0.1_f64).clamp(0.0, 1.0), 0.0);
        assert_eq!(0.5_f64.clamp(0.0, 1.0), 0.5);
        assert_eq!(1.5_f64.clamp(0.0, 1.0), 1.0);

        // Batch size bounds (1, 50)
        assert_eq!(0_usize.clamp(1, 50), 1);
        assert_eq!(25_usize.clamp(1, 50), 25);
        assert_eq!(100_usize.clamp(1, 50), 50);

        // Parser pool bounds (1, 4)
        assert_eq!(0_usize.clamp(1, 4), 1);
        assert_eq!(2_usize.clamp(1, 4), 2);
        assert_eq!(10_usize.clamp(1, 4), 4);
    }

    #[test]
    fn test_concurrency_bounds_validation() {
        // Test the dynamic concurrency bounds case
        let max_concurrency = 8_usize;
        let adjusted_concurrency = 5_usize;

        // Original pattern: adjusted_concurrency.min(max_concurrency).max(1)
        let original = adjusted_concurrency.min(max_concurrency).max(1);

        // Optimized pattern: adjusted_concurrency.clamp(1, max_concurrency)
        let optimized = adjusted_concurrency.clamp(1, max_concurrency);

        assert_eq!(original, optimized);

        // Test edge case where max_concurrency could be 0 (should be handled in calling code)
        // This test documents the assumption that max_concurrency >= 1
        assert!(
            max_concurrency >= 1,
            "max_concurrency must be >= 1 for clamp to work"
        );
    }
}
