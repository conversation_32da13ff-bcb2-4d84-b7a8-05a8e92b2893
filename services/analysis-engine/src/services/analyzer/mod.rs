pub mod events;
pub mod file_collector;
pub mod file_processor;
pub mod pattern_optimization_tests;
pub mod performance;
pub mod progress;
pub mod repository;
pub mod results;
pub mod storage;
pub mod streaming_processor;

use crate::backpressure::BackpressureManager;
use crate::config::ServiceConfig;
use crate::git::GitService;
use crate::metrics::MetricsService;
use crate::models::*;
use crate::parser::{StreamingFileProcessor, TreeSitterParser};
use crate::services::embeddings::EmbeddingsService;
use crate::services::language_detector::LanguageDetector;
use crate::storage::{
    connection_pool::SpannerPool, CacheManager, PubSubOperations, StorageOperations,
};
use anyhow::{Context, Result};
use chrono::Utc;
use std::sync::Arc;
use tokio::sync::{broadcast, mpsc};

pub use self::events::EventManager;
pub use self::file_collector::FileCollector;
pub use self::file_processor::FileProcessor;
pub use self::performance::PerformanceManager;
pub use self::progress::ProgressManager;
pub use self::repository::RepositoryManager;
pub use self::results::ResultProcessor;
pub use self::storage::StorageManager;
pub use self::streaming_processor::StreamingProcessor;

pub struct AnalysisService {
    spanner_pool: Option<Arc<SpannerPool>>,
    storage_client: Arc<StorageOperations>,
    pubsub_client: Arc<PubSubOperations>,
    cache_manager: Arc<CacheManager>,
    config: Arc<ServiceConfig>,
    git_service: Arc<GitService>,
    language_detector: Arc<LanguageDetector>,
    parser: Arc<TreeSitterParser>,
    embeddings_service: Arc<EmbeddingsService>,
    backpressure_manager: Option<Arc<BackpressureManager>>,
    #[allow(dead_code)]
    streaming_processor: Option<Arc<StreamingFileProcessor>>,
}

impl AnalysisService {
    pub async fn new(
        spanner_pool: Option<Arc<SpannerPool>>,
        storage_client: Arc<StorageOperations>,
        pubsub_client: Arc<PubSubOperations>,
        cache_manager: Arc<CacheManager>,
        config: Arc<ServiceConfig>,
    ) -> Result<Self> {
        let embeddings_service = EmbeddingsService::new()
            .await
            .context("Failed to create embeddings service")?;

        // Create parser with enhanced configuration for concurrent processing
        let parser_pool_config = crate::parser::parser_pool::ParserPoolConfig {
            max_parsers_per_language: (config.analysis.max_concurrent_analyses / 4).max(8),
            warm_up_on_create: true,
            target_utilization: 75.0,
            min_utilization: 25.0,
        };

        let parser_config = crate::parser::config::ParserConfig {
            pool: parser_pool_config,
            streaming: Default::default(),
            analysis: Default::default(),
            language_detection: Default::default(),
            ast: Default::default(),
        };

        let parser = Arc::new(
            TreeSitterParser::new_with_config(Arc::clone(&config), parser_config)
                .map_err(|e| anyhow::anyhow!("Failed to create TreeSitter parser: {e}"))?,
        );

        // Parser pools are warmed up during creation if warm_up_on_create is true

        // Pre-warm cache for concurrent load
        if let Err(e) = cache_manager
            .prepare_for_concurrent_load(config.analysis.max_concurrent_analyses)
            .await
        {
            tracing::warn!("Failed to prepare cache for concurrent load: {}", e);
        }

        Ok(Self {
            spanner_pool,
            storage_client,
            pubsub_client,
            cache_manager,
            config,
            git_service: Arc::new(GitService::new()),
            language_detector: Arc::new(LanguageDetector::new()),
            parser,
            embeddings_service: Arc::new(embeddings_service),
            backpressure_manager: None,
            streaming_processor: None,
        })
    }

    /// Create a new AnalysisService for testing with minimal dependencies
    pub async fn new_for_testing(
        spanner_pool: Option<Arc<SpannerPool>>,
        cache_manager: Arc<CacheManager>,
        config: Arc<ServiceConfig>,
    ) -> Result<Self> {
        let embeddings_service = EmbeddingsService::new()
            .await
            .context("Failed to create embeddings service")?;

        // Create parser with minimal configuration for testing
        let parser_pool_config = crate::parser::parser_pool::ParserPoolConfig {
            max_parsers_per_language: 2,
            warm_up_on_create: false,
            target_utilization: 75.0,
            min_utilization: 25.0,
        };

        let parser_config = crate::parser::config::ParserConfig {
            pool: parser_pool_config,
            streaming: Default::default(),
            analysis: Default::default(),
            language_detection: Default::default(),
            ast: Default::default(),
        };

        let parser = Arc::new(TreeSitterParser::new_with_config(config.clone(), parser_config)?);

        // Create dummy storage and pubsub for testing
        use crate::storage::StorageOperations;
        use crate::storage::PubSubOperations;

        // Create mock storage and pubsub operations
        let storage_client = Arc::new(StorageOperations::new_for_testing().await?);
        let pubsub_client = Arc::new(PubSubOperations::new_for_testing().await?);

        Ok(Self {
            spanner_pool,
            storage_client,
            pubsub_client,
            cache_manager,
            config,
            git_service: Arc::new(GitService::new()),
            language_detector: Arc::new(LanguageDetector::new()),
            parser,
            embeddings_service: Arc::new(embeddings_service),
            backpressure_manager: None,
            streaming_processor: None,
        })
    }

    /// Public method for repository analysis (for testing and performance validation)
    pub async fn analyze_repository_for_testing(&self, repository_path: &str) -> Result<AnalysisResult> {
        let request = AnalysisRequest {
            repository_url: repository_path.to_string(),
            branch: Some("main".to_string()),
            include_patterns: vec![],
            exclude_patterns: vec![],
            languages: vec![],
            webhook_url: None,
            enable_patterns: true,
            enable_embeddings: false,
        };

        let (progress_tx, _) = tokio::sync::mpsc::channel(1000);
        self.analyze_repository(&request, progress_tx, "test-user").await
    }

    /// Set the backpressure manager for this service
    pub fn set_backpressure_manager(&mut self, manager: Arc<BackpressureManager>) {
        self.backpressure_manager = Some(manager);
    }

    /// Prepare the service for expected concurrent load with intelligent resource management
    pub async fn prepare_for_load(&self, expected_concurrent_analyses: usize) -> Result<()> {
        tracing::info!(
            "Preparing analysis service for {} concurrent analyses",
            expected_concurrent_analyses
        );

        // Parser pools are dynamically sized based on load

        // Pre-warm cache for expected load
        if let Err(e) = self
            .cache_manager
            .prepare_for_concurrent_load(expected_concurrent_analyses)
            .await
        {
            tracing::warn!("Failed to prepare cache for concurrent load: {}", e);
        }

        // Initialize backpressure management if available
        if let Some(bp_manager) = &self.backpressure_manager {
            bp_manager
                .prepare_for_load(expected_concurrent_analyses)
                .await?;
        }

        // Parser pool statistics logging will be added when pool stats API is available

        // Log system resource status
        let perf_manager =
            PerformanceManager::new(self.config.clone(), self.backpressure_manager.clone());
        let available_memory = perf_manager.get_available_memory();
        tracing::info!(
            "System prepared for concurrent load - Available memory: {} MB, CPU cores: {}",
            available_memory / 1024 / 1024,
            num_cpus::get()
        );

        Ok(())
    }

    /// Get current performance metrics
    pub async fn get_performance_metrics(&self) -> Result<PerformanceMetrics> {
        let perf_manager =
            PerformanceManager::new(self.config.clone(), self.backpressure_manager.clone());
        perf_manager.get_performance_metrics(&self.parser).await
    }

    /// Test-only method for direct repository analysis
    #[cfg(test)]
    pub async fn analyze_code(&self, request: &AnalysisRequest) -> Result<AnalysisResult> {
        let (progress_tx, _progress_rx) = mpsc::channel(100);
        self.analyze_repository(request, progress_tx, "test-user").await
    }

    pub async fn start_analysis(
        &self,
        analysis_id: String,
        request: AnalysisRequest,
        user_id: String,
        progress_broadcast: broadcast::Sender<ProgressUpdate>,
    ) -> Result<()> {
        let (progress_tx, mut progress_rx) = mpsc::channel::<ProgressUpdate>(100);

        // Spawn task to handle progress updates
        let analysis_id_clone = analysis_id.clone();
        let pubsub_clone = self.pubsub_client.clone();
        let broadcast_tx = progress_broadcast.clone();
        tokio::spawn(async move {
            while let Some(update) = progress_rx.recv().await {
                tracing::info!("Analysis {} progress: {:?}", analysis_id_clone, update);

                // Broadcast to WebSocket clients
                if let Err(e) = broadcast_tx.send(update.clone()) {
                    tracing::warn!("Failed to broadcast progress update: {}", e);
                }

                // Also publish to PubSub for external consumers
                if let Err(e) = pubsub_clone.publish_progress(&update).await {
                    tracing::error!("Failed to publish progress update to PubSub: {}", e);
                }
            }
        });

        // Execute analysis
        match self
            .analyze_repository(&request, progress_tx.clone(), &user_id)
            .await
        {
            Ok(result) => {
                tracing::info!("Analysis {} completed successfully", analysis_id);

                let storage_mgr = StorageManager::new(
                    self.spanner_pool.clone(),
                    self.storage_client.clone(),
                    self.backpressure_manager.clone(),
                );
                storage_mgr.store_analysis_result(&result).await?;

                let event_mgr = EventManager::new(self.pubsub_client.clone());
                event_mgr.publish_completion_event(&result).await?;

                if let Some(webhook_url) = &request.webhook_url {
                    event_mgr
                        .send_webhook_notification(webhook_url, &result)
                        .await
                        .unwrap_or_else(|e| tracing::warn!("Failed to send webhook: {}", e));
                }
            }
            Err(e) => {
                tracing::error!("Analysis {} failed: {}", analysis_id, e);
                let result = AnalysisResult {
                    id: analysis_id,
                    status: AnalysisStatus::Failed,
                    error_message: Some(e.to_string()),
                    user_id,
                    ..Default::default()
                };

                let storage_mgr = StorageManager::new(
                    self.spanner_pool.clone(),
                    self.storage_client.clone(),
                    self.backpressure_manager.clone(),
                );
                storage_mgr.store_analysis_result(&result).await?;

                let event_mgr = EventManager::new(self.pubsub_client.clone());
                event_mgr.publish_completion_event(&result).await?;
            }
        }

        Ok(())
    }

    async fn analyze_repository(
        &self,
        opts: &AnalysisRequest,
        progress_tx: mpsc::Sender<ProgressUpdate>,
        user_id: &str,
    ) -> Result<AnalysisResult> {
        let analysis_id = uuid::Uuid::new_v4().to_string();
        let start_time = Utc::now();
        let mut performance_metrics = PerformanceMetrics::default();
        let mut warnings = Vec::new();
        let progress_mgr = ProgressManager::new();

        // Check cache for existing analysis results
        let cache_key = format!(
            "{}:{}",
            opts.repository_url,
            opts.branch.as_ref().unwrap_or(&"main".to_string())
        );
        let repo_mgr = RepositoryManager::new(self.git_service.clone(), self.cache_manager.clone());

        // Try to get cached result
        match repo_mgr
            .check_cache_and_get_result(
                repository::CacheCheckConfig {
                    cache_key: &cache_key,
                    repository_url: &opts.repository_url,
                    branch: &opts.branch,
                    analysis_id: &analysis_id,
                    start_time,
                    user_id,
                },
                &mut warnings,
                &mut performance_metrics,
            )
            .await
        {
            Ok(Some(cached_result)) => return Ok(cached_result),
            Ok(None) => {
                tracing::info!("Cache miss for repository: {}", opts.repository_url);
            }
            Err(e) => {
                tracing::warn!("Cache check failed: {}", e);
            }
        }

        // Clone repository
        progress_mgr
            .send_progress(&progress_tx, &analysis_id, 5.0, "Cloning repository")
            .await?;
        let (repo_path, clone_time_ms, actual_commit_hash) = repo_mgr
            .clone_repository(
                &opts.repository_url,
                &opts.branch,
                &analysis_id,
                &mut warnings,
            )
            .await?;
        performance_metrics.clone_duration_ms = clone_time_ms;

        // Calculate repository size
        let repository_size_bytes = repo_mgr.calculate_directory_size(&repo_path).await?;
        repo_mgr.check_repository_size(repository_size_bytes, &mut warnings);

        // Detect languages
        progress_mgr
            .send_progress(&progress_tx, &analysis_id, 15.0, "Detecting languages")
            .await?;
        let lang_start = std::time::Instant::now();
        let languages = self
            .language_detector
            .detect_languages_with_stats(&repo_path)?;
        self.language_detector
            .validate_supported_languages(&languages, &opts.languages)?;
        performance_metrics.language_detection_ms = lang_start.elapsed().as_millis() as u64;

        // Collect files
        progress_mgr
            .send_progress(&progress_tx, &analysis_id, 25.0, "Collecting source files")
            .await?;
        let collect_start = std::time::Instant::now();
        let file_collector = FileCollector::new(self.config.clone());
        let files = file_collector.collect_source_files(
            &repo_path,
            &opts.include_patterns,
            &opts.exclude_patterns,
        )?;
        performance_metrics.file_collection_ms = collect_start.elapsed().as_millis() as u64;

        // Parse files
        progress_mgr
            .send_progress(&progress_tx, &analysis_id, 35.0, "Parsing source files")
            .await?;
        let parse_start = std::time::Instant::now();

        let ast_results = if repository_size_bytes > 1024 * 1024 * 1024 {
            // >1GB
            let streaming_proc = StreamingProcessor::new(
                self.parser.clone(),
                self.backpressure_manager.clone(),
                self.cache_manager.clone(),
            );
            streaming_proc
                .parse_files_with_streaming(&files, progress_tx.clone(), analysis_id.clone())
                .await?
        } else {
            let file_proc = FileProcessor::new(
                self.parser.clone(),
                self.config.clone(),
                self.backpressure_manager.clone(),
                self.cache_manager.clone(),
            );
            file_proc
                .parse_files_parallel(&files, progress_tx.clone(), analysis_id.clone())
                .await?
        };

        let result_processor = ResultProcessor::new();
        let (successful_analyses, failed_files) =
            result_processor.partition_results_with_warnings(ast_results, &mut warnings);
        performance_metrics.ast_parsing_ms = parse_start.elapsed().as_millis() as u64;
        performance_metrics.files_analyzed = files.len() as u64;
        performance_metrics.successful_parses = successful_analyses.len() as u64;
        performance_metrics.failed_parses = failed_files.len() as u64;

        // Extract metrics
        progress_mgr
            .send_progress(&progress_tx, &analysis_id, 70.0, "Extracting metrics")
            .await?;
        let metrics_start = std::time::Instant::now();
        let metrics_service = MetricsService::new();
        let metrics = metrics_service.calculate_repository_metrics(&successful_analyses);
        performance_metrics.metrics_calculation_ms = metrics_start.elapsed().as_millis() as u64;

        // Detect patterns
        let patterns = if opts.enable_patterns {
            progress_mgr
                .send_progress(&progress_tx, &analysis_id, 85.0, "Detecting patterns")
                .await?;
            let pattern_start = std::time::Instant::now();
            let detected_patterns = self.detect_patterns(&successful_analyses).await?;
            performance_metrics.pattern_detection_ms = pattern_start.elapsed().as_millis() as u64;
            performance_metrics.patterns_detected = detected_patterns.len() as u64;
            detected_patterns
        } else {
            vec![]
        };

        // Generate embeddings
        let embeddings = if opts.enable_embeddings {
            progress_mgr
                .send_progress(&progress_tx, &analysis_id, 95.0, "Generating embeddings")
                .await?;
            let embedding_start = std::time::Instant::now();
            match self
                .embeddings_service
                .generate_embeddings(&successful_analyses)
                .await
            {
                Ok(generated_embeddings) => {
                    performance_metrics.embedding_generation_ms =
                        embedding_start.elapsed().as_millis() as u64;
                    performance_metrics.embeddings_generated = generated_embeddings.len() as u64;
                    Some(generated_embeddings)
                }
                Err(e) => {
                    warnings.push(AnalysisWarning::new(
                        WarningType::EmbeddingFailure,
                        format!("Failed to generate embeddings: {e}"),
                        WarningSeverity::Medium,
                    ));
                    performance_metrics.embedding_generation_ms =
                        embedding_start.elapsed().as_millis() as u64;
                    performance_metrics.embeddings_generated = 0;
                    None
                }
            }
        } else {
            None
        };

        // Cleanup
        let cleanup_start = std::time::Instant::now();
        self.git_service.cleanup_repository(&repo_path).await?;
        performance_metrics.cleanup_ms = cleanup_start.elapsed().as_millis() as u64;

        // Calculate total processing time
        performance_metrics.total_processing_ms =
            (Utc::now() - start_time).num_milliseconds() as u64;

        // Track memory usage
        let perf_manager =
            PerformanceManager::new(self.config.clone(), self.backpressure_manager.clone());
        performance_metrics.memory_peak_mb = perf_manager.get_peak_memory_usage();
        perf_manager.add_memory_warnings(&mut warnings, performance_metrics.memory_peak_mb);

        // Add performance warnings
        let total_duration = (Utc::now() - start_time).num_seconds() as u64;
        if total_duration > 300 {
            warnings.push(AnalysisWarning::new(
                WarningType::TimeoutRisk,
                format!(
                    "Analysis took {} minutes, which may indicate performance issues",
                    total_duration / 60
                ),
                WarningSeverity::Medium,
            ));
        }

        progress_mgr
            .send_progress(&progress_tx, &analysis_id, 100.0, "Analysis complete")
            .await?;

        // Cache the results
        repo_mgr
            .cache_results(
                &cache_key,
                &successful_analyses,
                &actual_commit_hash,
                &patterns,
                &embeddings,
            )
            .await;

        Ok(AnalysisResult {
            id: analysis_id.clone(),
            repository_url: opts.repository_url.clone(),
            branch: opts.branch.clone().unwrap_or_else(|| "main".to_string()),
            commit_hash: Some(actual_commit_hash),
            repository_size_bytes: Some(repository_size_bytes),
            clone_time_ms: Some(clone_time_ms),
            status: AnalysisStatus::Completed,
            started_at: start_time,
            completed_at: Some(Utc::now()),
            duration_seconds: Some((Utc::now() - start_time).num_seconds() as u64),
            progress: Some(100.0),
            current_stage: Some("Completed".to_string()),
            estimated_completion: None,
            metrics: Some(metrics),
            patterns,
            languages,
            embeddings,
            error_message: None,
            failed_files,
            successful_analyses: Some(successful_analyses.clone()),
            user_id: user_id.to_string(),
            webhook_url: opts.webhook_url.clone(),
            file_count: files.len(),
            success_rate: (successful_analyses.len() as f64 / files.len() as f64) * 100.0,
            performance_metrics: Some(performance_metrics),
            warnings,
        })
    }

    async fn detect_patterns(&self, analyses: &[FileAnalysis]) -> Result<Vec<DetectedPattern>> {
        use crate::services::pattern_detector::PatternDetector;

        let detector = PatternDetector::new();
        let mut all_patterns = Vec::new();

        // Detect patterns in each file using proper AST analysis
        for analysis in analyses {
            let patterns = detector.detect_patterns(analysis);
            all_patterns.extend(patterns);
        }

        // Publish pattern detection events if patterns were found
        if !all_patterns.is_empty() {
            let pattern_count = all_patterns.len();
            let pattern_types: std::collections::HashSet<_> = all_patterns
                .iter()
                .map(|p| format!("{:?}", p.pattern_type))
                .collect();

            for pattern_type in pattern_types {
                let count = all_patterns
                    .iter()
                    .filter(|p| format!("{:?}", p.pattern_type) == pattern_type)
                    .count();

                if let Err(e) = self
                    .pubsub_client
                    .publish_pattern_detected(&analyses[0].path, &pattern_type, count)
                    .await
                {
                    tracing::warn!("Failed to publish pattern detected event: {}", e);
                }
            }

            tracing::info!(
                "Detected {} patterns across {} files",
                pattern_count,
                analyses.len()
            );
        }

        Ok(all_patterns)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use std::path::PathBuf;
    use tempfile::TempDir;

    fn create_test_config() -> ServiceConfig {
        ServiceConfig {
            service: crate::config::ServiceSettings {
                name: "analysis-engine-test".to_string(),
                version: "0.1.0".to_string(),
                port: 8001,
                host: "127.0.0.1".to_string(),
                environment: crate::config::Environment::Development,
            },
            gcp: crate::config::GcpSettings {
                project_id: "test-project".to_string(),
                spanner_instance: "test-instance".to_string(),
                spanner_database: "test-database".to_string(),
                storage_bucket: "test-bucket".to_string(),
                storage_bucket_name: "test-bucket-name".to_string(),
                pubsub_topic: "test-topic".to_string(),
                region: "us-central1".to_string(),
            },
            analysis: crate::config::AnalysisSettings {
                max_concurrent_analyses: 50,
                max_repository_size_gb: 10,
                analysis_timeout_seconds: 300,
                max_file_size_mb: 10,
                temp_dir: "/tmp/test".to_string(),
                supported_languages: vec![
                    "rust".to_string(),
                    "python".to_string(),
                    "javascript".to_string(),
                    "typescript".to_string(),
                ],
            },
            security: crate::config::SecuritySettings {
                enable_auth: false,
                api_key_header: "x-api-key".to_string(),
                jwt_secret: Some("test-secret".to_string()),
                cors_origins: vec!["*".to_string()],
            },
            observability: crate::config::ObservabilitySettings {
                enable_tracing: false,
                enable_metrics: false,
                log_level: "info".to_string(),
                otel_endpoint: None,
            },
            circuit_breaker: Default::default(),
            redis: Default::default(),
            resource_limits: Default::default(),
        }
    }

    fn create_test_repo(temp_dir: &TempDir) -> PathBuf {
        let repo_path = temp_dir.path().to_path_buf();

        // Create some test files
        fs::create_dir_all(repo_path.join("src")).expect("Failed to create src directory");
        fs::write(
            repo_path.join("src/main.rs"),
            r#"
fn main() {
    println!("Hello, world!");
}

fn add(a: i32, b: i32) -> i32 {
    a + b
}

#[test]
fn test_add() {
    assert_eq!(add(2, 2), 4);
}
"#,
        )
        .expect("Failed to write main.rs");

        fs::write(
            repo_path.join("src/lib.rs"),
            r#"
pub struct Config {
    pub name: String,
    pub value: i32,
}

impl Config {
    pub fn new(name: String, value: i32) -> Self {
        Self { name, value }
    }
}
"#,
        )
        .expect("Failed to write lib.rs");

        // Create a file to be excluded
        fs::create_dir_all(repo_path.join("target")).expect("Failed to create target directory");
        fs::write(repo_path.join("target/debug.rs"), "// Should be excluded").expect("Failed to write debug.rs");

        repo_path
    }

    #[tokio::test]
    async fn test_send_progress_channel() {
        let (tx, mut rx) = mpsc::channel(10);

        // Test sending progress update directly
        let update = ProgressUpdate {
            analysis_id: "test-id".to_string(),
            progress: 50.0,
            stage: "Processing".to_string(),
            message: None,
            timestamp: Utc::now(),
            files_processed: None,
            total_files: None,
        };

        tx.send(update.clone()).await.expect("Failed to send progress update");

        let received = rx.recv().await.expect("Failed to receive progress update");
        assert_eq!(received.analysis_id, "test-id");
        assert_eq!(received.progress, 50.0);
        assert_eq!(received.stage, "Processing");
    }
}
