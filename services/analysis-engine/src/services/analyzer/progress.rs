use crate::models::ProgressUpdate;
use anyhow::Result;
use chrono::Utc;
use tokio::sync::mpsc;

/// Configuration for detailed progress updates
#[derive(Default)]
pub struct ProgressDetails {
    pub message: Option<String>,
    pub files_processed: Option<usize>,
    pub total_files: Option<usize>,
}


pub struct ProgressManager;

impl ProgressManager {
    pub fn new() -> Self {
        Self
    }

    /// Send a progress update through the channel
    pub async fn send_progress(
        &self,
        tx: &mpsc::Sender<ProgressUpdate>,
        analysis_id: &str,
        progress: f64,
        stage: &str,
    ) -> Result<()> {
        self.send_progress_with_details(
            tx,
            analysis_id,
            progress,
            stage,
            ProgressDetails::default(),
        )
        .await
    }

    /// Send a detailed progress update with optional file processing information
    pub async fn send_progress_with_details(
        &self,
        tx: &mpsc::Sender<ProgressUpdate>,
        analysis_id: &str,
        progress: f64,
        stage: &str,
        details: ProgressDetails,
    ) -> Result<()> {
        tx.send(ProgressUpdate {
            analysis_id: analysis_id.to_string(),
            progress,
            stage: stage.to_string(),
            message: details.message,
            timestamp: Utc::now(),
            files_processed: details.files_processed,
            total_files: details.total_files,
        })
        .await
        .map_err(|e| anyhow::anyhow!("Failed to send progress: {e}"))
    }

    /// Create a progress update for repository cloning
    pub fn create_clone_progress(&self, analysis_id: &str) -> ProgressUpdate {
        ProgressUpdate {
            analysis_id: analysis_id.to_string(),
            progress: 5.0,
            stage: "Cloning repository".to_string(),
            message: Some("Fetching repository content from remote".to_string()),
            timestamp: Utc::now(),
            files_processed: None,
            total_files: None,
        }
    }

    /// Create a progress update for language detection
    pub fn create_language_detection_progress(&self, analysis_id: &str) -> ProgressUpdate {
        ProgressUpdate {
            analysis_id: analysis_id.to_string(),
            progress: 15.0,
            stage: "Detecting languages".to_string(),
            message: Some("Analyzing repository language composition".to_string()),
            timestamp: Utc::now(),
            files_processed: None,
            total_files: None,
        }
    }

    /// Create a progress update for file collection
    pub fn create_file_collection_progress(
        &self,
        analysis_id: &str,
        file_count: Option<usize>,
    ) -> ProgressUpdate {
        ProgressUpdate {
            analysis_id: analysis_id.to_string(),
            progress: 25.0,
            stage: "Collecting source files".to_string(),
            message: file_count.map(|count| format!("Found {count} source files")),
            timestamp: Utc::now(),
            files_processed: None,
            total_files: file_count,
        }
    }

    /// Create a progress update for file parsing
    pub fn create_parsing_progress(
        &self,
        analysis_id: &str,
        files_processed: usize,
        total_files: usize,
        success_rate: f64,
    ) -> ProgressUpdate {
        let progress = 35.0 + (files_processed as f64 / total_files as f64) * 35.0;
        ProgressUpdate {
            analysis_id: analysis_id.to_string(),
            progress,
            stage: format!(
                "Parsed {files_processed}/{total_files} files ({success_rate:.1}% success)"
            ),
            message: Some("Analyzing source code structure".to_string()),
            timestamp: Utc::now(),
            files_processed: Some(files_processed),
            total_files: Some(total_files),
        }
    }

    /// Create a progress update for metric extraction
    pub fn create_metrics_progress(&self, analysis_id: &str) -> ProgressUpdate {
        ProgressUpdate {
            analysis_id: analysis_id.to_string(),
            progress: 70.0,
            stage: "Extracting metrics".to_string(),
            message: Some("Calculating code quality metrics".to_string()),
            timestamp: Utc::now(),
            files_processed: None,
            total_files: None,
        }
    }

    /// Create a progress update for pattern detection
    pub fn create_pattern_detection_progress(
        &self,
        analysis_id: &str,
        pattern_count: Option<usize>,
    ) -> ProgressUpdate {
        ProgressUpdate {
            analysis_id: analysis_id.to_string(),
            progress: 85.0,
            stage: "Detecting patterns".to_string(),
            message: pattern_count.map(|count| format!("Found {count} code patterns")),
            timestamp: Utc::now(),
            files_processed: None,
            total_files: None,
        }
    }

    /// Create a progress update for embedding generation
    pub fn create_embedding_progress(
        &self,
        analysis_id: &str,
        embedding_count: Option<usize>,
    ) -> ProgressUpdate {
        ProgressUpdate {
            analysis_id: analysis_id.to_string(),
            progress: 95.0,
            stage: "Generating embeddings".to_string(),
            message: embedding_count.map(|count| format!("Generated {count} embeddings")),
            timestamp: Utc::now(),
            files_processed: None,
            total_files: None,
        }
    }

    /// Create a completion progress update
    pub fn create_completion_progress(&self, analysis_id: &str) -> ProgressUpdate {
        ProgressUpdate {
            analysis_id: analysis_id.to_string(),
            progress: 100.0,
            stage: "Analysis complete".to_string(),
            message: Some("All processing tasks completed successfully".to_string()),
            timestamp: Utc::now(),
            files_processed: None,
            total_files: None,
        }
    }

    /// Create an error progress update
    pub fn create_error_progress(&self, analysis_id: &str, error: &str) -> ProgressUpdate {
        ProgressUpdate {
            analysis_id: analysis_id.to_string(),
            progress: 0.0,
            stage: "Analysis failed".to_string(),
            message: Some(format!("Error: {error}")),
            timestamp: Utc::now(),
            files_processed: None,
            total_files: None,
        }
    }
}

impl Default for ProgressManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_send_progress() {
        let (tx, mut rx) = mpsc::channel(10);
        let progress_mgr = ProgressManager::new();

        // Test sending basic progress
        progress_mgr
            .send_progress(&tx, "test-id", 50.0, "Processing")
            .await
            .expect("Failed to send progress update");

        let received = rx.recv().await.expect("Failed to receive progress update");
        assert_eq!(received.analysis_id, "test-id");
        assert_eq!(received.progress, 50.0);
        assert_eq!(received.stage, "Processing");
        assert!(received.message.is_none());
    }

    #[tokio::test]
    async fn test_send_progress_with_details() {
        let (tx, mut rx) = mpsc::channel(10);
        let progress_mgr = ProgressManager::new();

        // Test sending detailed progress
        progress_mgr
            .send_progress_with_details(
                &tx,
                "test-id",
                75.0,
                "Analyzing",
                ProgressDetails {
                    message: Some("Processing file 75 of 100".to_string()),
                    files_processed: Some(75),
                    total_files: Some(100),
                },
            )
            .await
            .expect("Failed to send detailed progress update");

        let received = rx.recv().await.expect("Failed to receive detailed progress update");
        assert_eq!(received.analysis_id, "test-id");
        assert_eq!(received.progress, 75.0);
        assert_eq!(received.stage, "Analyzing");
        assert_eq!(
            received.message,
            Some("Processing file 75 of 100".to_string())
        );
        assert_eq!(received.files_processed, Some(75));
        assert_eq!(received.total_files, Some(100));
    }

    #[test]
    fn test_create_progress_updates() {
        let progress_mgr = ProgressManager::new();

        // Test clone progress
        let clone_progress = progress_mgr.create_clone_progress("test-id");
        assert_eq!(clone_progress.progress, 5.0);
        assert_eq!(clone_progress.stage, "Cloning repository");

        // Test parsing progress
        let parsing_progress = progress_mgr.create_parsing_progress("test-id", 50, 100, 95.0);
        assert!(parsing_progress.progress > 35.0 && parsing_progress.progress < 70.0);
        assert!(parsing_progress.stage.contains("50/100"));
        assert!(parsing_progress.stage.contains("95.0%"));

        // Test completion progress
        let completion_progress = progress_mgr.create_completion_progress("test-id");
        assert_eq!(completion_progress.progress, 100.0);
        assert_eq!(completion_progress.stage, "Analysis complete");

        // Test error progress
        let error_progress = progress_mgr.create_error_progress("test-id", "Test error");
        assert_eq!(error_progress.progress, 0.0);
        assert_eq!(error_progress.stage, "Analysis failed");
        assert!(error_progress.message.expect("Error progress should have message").contains("Test error"));
    }
}
