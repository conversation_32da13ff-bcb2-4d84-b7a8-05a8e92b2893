use crate::models::{DetectedPattern, FileAnalysis, RepositoryMetrics};
use crate::services::code_quality_assessor::CodeQualityAssessment;
use crate::services::embeddings_enhancement::{EnhancedEmbeddingsService, FeatureToggles};
use anyhow::{Context, Result};
use chrono::{DateTime, Utc};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::env;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Mutex;

const GEMINI_TIMEOUT: Duration = Duration::from_secs(45);
const MAX_RETRIES: u32 = 3;
const INITIAL_RETRY_DELAY: Duration = Duration::from_millis(1000);

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RepositoryInsights {
    pub summary: RepositorySummary,
    pub architecture_analysis: ArchitectureAnalysis,
    pub technology_stack: TechnologyStackAnalysis,
    pub development_practices: DevelopmentPracticesAnalysis,
    pub security_assessment: SecurityAssessment,
    pub performance_insights: PerformanceInsights,
    pub maintainability_insights: MaintainabilityInsights,
    pub recommendations: Vec<RepositoryRecommendation>,
    pub trends: Vec<CodeTrend>,
    pub metrics: InsightsMetrics,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RepositorySummary {
    pub overview: String,
    pub primary_language: String,
    pub languages_used: Vec<LanguageUsage>,
    pub codebase_size: CodebaseSize,
    pub complexity_overview: ComplexityOverview,
    pub key_strengths: Vec<String>,
    pub main_concerns: Vec<String>,
    pub overall_health_score: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LanguageUsage {
    pub language: String,
    pub percentage: f64,
    pub file_count: u32,
    pub lines_of_code: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CodebaseSize {
    pub total_files: u32,
    pub total_lines: u32,
    pub total_characters: u64,
    pub size_category: String, // "Small", "Medium", "Large", "Enterprise"
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ComplexityOverview {
    pub average_complexity: f64,
    pub complexity_distribution: HashMap<String, u32>, // "Low", "Medium", "High", "Critical"
    pub most_complex_files: Vec<String>,
    pub complexity_trends: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ArchitectureAnalysis {
    pub architecture_style: String,
    pub design_patterns: Vec<PatternUsage>,
    pub module_organization: ModuleOrganization,
    pub dependency_analysis: DependencyAnalysis,
    pub architecture_score: f64,
    pub architectural_debt: Vec<ArchitecturalDebt>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PatternUsage {
    pub pattern_name: String,
    pub pattern_type: String,
    pub usage_count: u32,
    pub confidence: f64,
    pub files: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ModuleOrganization {
    pub structure_score: f64,
    pub coupling_analysis: String,
    pub cohesion_analysis: String,
    pub module_boundaries: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DependencyAnalysis {
    pub dependency_count: u32,
    pub circular_dependencies: Vec<String>,
    pub dependency_depth: u32,
    pub external_dependencies: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ArchitecturalDebt {
    pub debt_type: String,
    pub severity: String,
    pub description: String,
    pub affected_components: Vec<String>,
    pub remediation_effort: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TechnologyStackAnalysis {
    pub frameworks: Vec<TechnologyUsage>,
    pub libraries: Vec<TechnologyUsage>,
    pub tools: Vec<TechnologyUsage>,
    pub version_analysis: VersionAnalysis,
    pub compatibility_assessment: CompatibilityAssessment,
    pub modernization_opportunities: Vec<ModernizationOpportunity>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TechnologyUsage {
    pub name: String,
    pub version: Option<String>,
    pub usage_context: String,
    pub adoption_level: String, // "Minimal", "Moderate", "Extensive"
    pub assessment: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct VersionAnalysis {
    pub outdated_dependencies: Vec<String>,
    pub security_vulnerabilities: Vec<String>,
    pub upgrade_recommendations: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CompatibilityAssessment {
    pub compatibility_score: f64,
    pub potential_conflicts: Vec<String>,
    pub compatibility_risks: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ModernizationOpportunity {
    pub technology: String,
    pub current_version: String,
    pub recommended_version: String,
    pub benefits: Vec<String>,
    pub migration_effort: String,
    pub priority: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DevelopmentPracticesAnalysis {
    pub code_style_consistency: f64,
    pub testing_practices: TestingPractices,
    pub documentation_quality: DocumentationQuality,
    pub error_handling_patterns: ErrorHandlingPatterns,
    pub development_workflow: DevelopmentWorkflow,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TestingPractices {
    pub test_coverage_estimate: f64,
    pub test_types: Vec<String>,
    pub testing_frameworks: Vec<String>,
    pub test_quality_score: f64,
    pub testing_gaps: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DocumentationQuality {
    pub documentation_score: f64,
    pub documentation_coverage: f64,
    pub documentation_types: Vec<String>,
    pub documentation_gaps: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ErrorHandlingPatterns {
    pub error_handling_score: f64,
    pub error_patterns: Vec<String>,
    pub error_handling_consistency: f64,
    pub improvement_areas: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DevelopmentWorkflow {
    pub workflow_indicators: Vec<String>,
    pub development_maturity: String,
    pub collaboration_patterns: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SecurityAssessment {
    pub security_score: f64,
    pub security_patterns: Vec<String>,
    pub vulnerability_categories: Vec<VulnerabilityCategory>,
    pub security_best_practices: Vec<SecurityPractice>,
    pub security_recommendations: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct VulnerabilityCategory {
    pub category: String,
    pub severity: String,
    pub count: u32,
    pub examples: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SecurityPractice {
    pub practice: String,
    pub adoption_level: String,
    pub effectiveness: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PerformanceInsights {
    pub performance_score: f64,
    pub performance_patterns: Vec<String>,
    pub bottleneck_analysis: BottleneckAnalysis,
    pub optimization_opportunities: Vec<OptimizationOpportunity>,
    pub resource_usage_patterns: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BottleneckAnalysis {
    pub algorithmic_complexity: Vec<String>,
    pub resource_intensive_operations: Vec<String>,
    pub scalability_concerns: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct OptimizationOpportunity {
    pub opportunity_type: String,
    pub description: String,
    pub expected_impact: String,
    pub implementation_effort: String,
    pub affected_files: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MaintainabilityInsights {
    pub maintainability_score: f64,
    pub maintainability_factors: Vec<MaintainabilityFactor>,
    pub technical_debt_analysis: TechnicalDebtAnalysis,
    pub refactoring_opportunities: Vec<RefactoringOpportunity>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MaintainabilityFactor {
    pub factor: String,
    pub score: f64,
    pub impact: String,
    pub improvement_suggestions: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TechnicalDebtAnalysis {
    pub debt_score: f64,
    pub debt_categories: Vec<DebtCategory>,
    pub debt_trends: String,
    pub remediation_priority: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DebtCategory {
    pub category: String,
    pub severity: String,
    pub estimated_hours: f64,
    pub affected_files: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RefactoringOpportunity {
    pub refactoring_type: String,
    pub description: String,
    pub benefits: Vec<String>,
    pub effort_estimate: String,
    pub priority: String,
    pub files_affected: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RepositoryRecommendation {
    pub category: String,
    pub title: String,
    pub description: String,
    pub priority: String,
    pub impact: String,
    pub effort: String,
    pub action_items: Vec<String>,
    pub expected_outcomes: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CodeTrend {
    pub trend_type: String,
    pub description: String,
    pub trend_direction: String, // "Improving", "Stable", "Declining"
    pub confidence: f64,
    pub supporting_data: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct InsightsMetrics {
    pub analysis_completeness: f64,
    pub confidence_score: f64,
    pub data_quality: f64,
    pub coverage_metrics: HashMap<String, f64>,
}

// Gemini API structures (reusing from previous services)
#[derive(Debug, Serialize)]
struct GeminiInsightsRequest {
    contents: Vec<GeminiContent>,
    generation_config: GeminiGenerationConfig,
    safety_settings: Vec<GeminiSafetySettings>,
}

#[derive(Debug, Serialize)]
struct GeminiContent {
    parts: Vec<GeminiPart>,
    role: String,
}

#[derive(Debug, Serialize)]
struct GeminiPart {
    text: String,
}

#[derive(Debug, Serialize)]
struct GeminiGenerationConfig {
    temperature: f32,
    top_p: f32,
    top_k: i32,
    max_output_tokens: i32,
    response_mime_type: String,
}

#[derive(Debug, Serialize)]
struct GeminiSafetySettings {
    category: String,
    threshold: String,
}

#[derive(Debug, Deserialize)]
struct GeminiResponse {
    candidates: Vec<GeminiCandidate>,
}

#[derive(Debug, Deserialize)]
struct GeminiCandidate {
    content: GeminiResponseContent,
}

#[derive(Debug, Deserialize)]
struct GeminiResponseContent {
    parts: Vec<GeminiResponsePart>,
}

#[derive(Debug, Deserialize)]
struct GeminiResponsePart {
    text: String,
}

// Circuit breaker states
#[derive(Debug, Clone)]
enum CircuitState {
    Closed,
    Open(DateTime<Utc>),
    HalfOpen,
}

#[derive(Debug, Default)]
pub struct InsightsMetricsData {
    total_analyses: u64,
    successful_analyses: u64,
    failed_analyses: u64,
    average_response_time_ms: f64,
    circuit_breaker_opens: u64,
    fallback_uses: u64,
}

pub struct RepositoryInsightsService {
    client: Client,
    project_id: String,
    location: String,
    model_name: String,
    circuit_state: Arc<Mutex<CircuitState>>,
    failure_count: Arc<Mutex<u32>>,
    failure_threshold: u32,
    reset_timeout: Duration,
    feature_toggles: Arc<FeatureToggles>,
    metrics: Arc<Mutex<InsightsMetricsData>>,
    #[allow(dead_code)]
    embeddings_service: Arc<EnhancedEmbeddingsService>,
}

impl RepositoryInsightsService {
    pub async fn new(embeddings_service: Arc<EnhancedEmbeddingsService>) -> Result<Self> {
        let project_id =
            env::var("GCP_PROJECT_ID").unwrap_or_else(|_| "vibe-match-463114".to_string());
        let location = env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string());
        let model_name =
            env::var("GEMINI_MODEL_NAME").unwrap_or_else(|_| "gemini-2.5-pro".to_string());

        let client = Client::builder()
            .timeout(GEMINI_TIMEOUT)
            .build()
            .context("Failed to create HTTP client")?;

        let feature_toggles = embeddings_service.get_feature_toggles();

        Ok(Self {
            client,
            project_id,
            location,
            model_name,
            circuit_state: Arc::new(Mutex::new(CircuitState::Closed)),
            failure_count: Arc::new(Mutex::new(0)),
            failure_threshold: 5,
            reset_timeout: Duration::from_secs(60),
            feature_toggles,
            metrics: Arc::new(Mutex::new(InsightsMetricsData::default())),
            embeddings_service,
        })
    }

    pub async fn generate_repository_insights(
        &self,
        analyses: &[FileAnalysis],
        repository_metrics: &RepositoryMetrics,
        patterns: &[DetectedPattern],
        quality_assessment: Option<&CodeQualityAssessment>,
        languages: &HashMap<String, crate::models::LanguageStats>,
    ) -> Result<RepositoryInsights> {
        if !self.feature_toggles.enable_repository_insights {
            tracing::info!("Repository insights disabled by feature toggle");
            return Ok(self.create_fallback_insights(
                analyses,
                repository_metrics,
                patterns,
                quality_assessment,
                languages,
            ));
        }

        // Check circuit breaker
        if !self.check_circuit_breaker().await? {
            tracing::warn!("Circuit breaker is open, using fallback repository insights");
            self.record_fallback_use().await;
            return Ok(self.create_fallback_insights(
                analyses,
                repository_metrics,
                patterns,
                quality_assessment,
                languages,
            ));
        }

        let start_time = std::time::Instant::now();

        match self
            .perform_ai_insights_analysis(
                analyses,
                repository_metrics,
                patterns,
                quality_assessment,
                languages,
            )
            .await
        {
            Ok(insights) => {
                self.record_success().await;
                self.record_response_time(start_time.elapsed().as_millis() as f64)
                    .await;
                Ok(insights)
            }
            Err(e) => {
                tracing::error!("AI repository insights failed: {}", e);
                self.record_failure().await;

                // Use fallback insights
                self.record_fallback_use().await;
                Ok(self.create_fallback_insights(
                    analyses,
                    repository_metrics,
                    patterns,
                    quality_assessment,
                    languages,
                ))
            }
        }
    }

    async fn perform_ai_insights_analysis(
        &self,
        analyses: &[FileAnalysis],
        repository_metrics: &RepositoryMetrics,
        patterns: &[DetectedPattern],
        quality_assessment: Option<&CodeQualityAssessment>,
        languages: &HashMap<String, crate::models::LanguageStats>,
    ) -> Result<RepositoryInsights> {
        let prompt = self.build_insights_prompt(
            analyses,
            repository_metrics,
            patterns,
            quality_assessment,
            languages,
        )?;

        let mut retry_delay = INITIAL_RETRY_DELAY;

        for attempt in 0..MAX_RETRIES {
            match self.call_gemini_for_insights(&prompt).await {
                Ok(insights) => {
                    return Ok(insights);
                }
                Err(e) => {
                    if !self.is_retryable_error(&e) || attempt == MAX_RETRIES - 1 {
                        return Err(e);
                    }

                    tracing::warn!(
                        "Insights analysis attempt {} failed, retrying in {:?}: {}",
                        attempt + 1,
                        retry_delay,
                        e
                    );

                    tokio::time::sleep(retry_delay).await;
                    retry_delay = std::cmp::min(retry_delay * 2, Duration::from_secs(30));
                }
            }
        }

        Err(anyhow::anyhow!("All insights analysis attempts exhausted"))
    }

    fn build_insights_prompt(
        &self,
        analyses: &[FileAnalysis],
        repository_metrics: &RepositoryMetrics,
        patterns: &[DetectedPattern],
        quality_assessment: Option<&CodeQualityAssessment>,
        languages: &HashMap<String, crate::models::LanguageStats>,
    ) -> Result<String> {
        let mut prompt = String::new();

        prompt.push_str("You are a senior technical architect and code analysis expert. Analyze this repository comprehensively and provide detailed insights. Return a JSON response with the following structure (ensure all fields are present):\n\n");

        // Include comprehensive JSON schema
        prompt.push_str("{\n");
        prompt.push_str("  \"summary\": {\n");
        prompt.push_str("    \"overview\": \"Comprehensive overview of the repository\",\n");
        prompt.push_str("    \"primary_language\": \"rust\",\n");
        prompt.push_str("    \"languages_used\": [{\"language\": \"rust\", \"percentage\": 80.0, \"file_count\": 25, \"lines_of_code\": 4000}],\n");
        prompt.push_str("    \"codebase_size\": {\"total_files\": 30, \"total_lines\": 5000, \"total_characters\": 150000, \"size_category\": \"Medium\"},\n");
        prompt.push_str("    \"complexity_overview\": {\"average_complexity\": 6.5, \"complexity_distribution\": {\"Low\": 15, \"Medium\": 10, \"High\": 4, \"Critical\": 1}, \"most_complex_files\": [\"file1.rs\"], \"complexity_trends\": \"Stable\"},\n");
        prompt.push_str(
            "    \"key_strengths\": [\"Strong type system\", \"Good error handling\"],\n",
        );
        prompt.push_str("    \"main_concerns\": [\"High complexity in some modules\"],\n");
        prompt.push_str("    \"overall_health_score\": 82.5\n");
        prompt.push_str("  },\n");
        prompt.push_str("  \"architecture_analysis\": {\n");
        prompt.push_str("    \"architecture_style\": \"Modular monolith\",\n");
        prompt.push_str("    \"design_patterns\": [{\"pattern_name\": \"Builder\", \"pattern_type\": \"Creational\", \"usage_count\": 3, \"confidence\": 0.9, \"files\": [\"builder.rs\"]}],\n");
        prompt.push_str("    \"module_organization\": {\"structure_score\": 85.0, \"coupling_analysis\": \"Low coupling\", \"cohesion_analysis\": \"High cohesion\", \"module_boundaries\": [\"service\", \"model\", \"api\"]},\n");
        prompt.push_str("    \"dependency_analysis\": {\"dependency_count\": 15, \"circular_dependencies\": [], \"dependency_depth\": 3, \"external_dependencies\": [\"serde\", \"tokio\"]},\n");
        prompt.push_str("    \"architecture_score\": 88.0,\n");
        prompt.push_str("    \"architectural_debt\": [{\"debt_type\": \"Module coupling\", \"severity\": \"Medium\", \"description\": \"Some tight coupling between modules\", \"affected_components\": [\"service\"], \"remediation_effort\": \"Medium\"}]\n");
        prompt.push_str("  },\n");

        // Add more sections with proper structure...
        prompt.push_str("  \"technology_stack\": {\n");
        prompt.push_str("    \"frameworks\": [{\"name\": \"Tokio\", \"version\": \"1.0\", \"usage_context\": \"Async runtime\", \"adoption_level\": \"Extensive\", \"assessment\": \"Well integrated\"}],\n");
        prompt.push_str("    \"libraries\": [{\"name\": \"serde\", \"version\": \"1.0\", \"usage_context\": \"Serialization\", \"adoption_level\": \"Extensive\", \"assessment\": \"Standard choice\"}],\n");
        prompt.push_str("    \"tools\": [{\"name\": \"cargo\", \"version\": \"latest\", \"usage_context\": \"Build system\", \"adoption_level\": \"Extensive\", \"assessment\": \"Standard toolchain\"}],\n");
        prompt.push_str("    \"version_analysis\": {\"outdated_dependencies\": [], \"security_vulnerabilities\": [], \"upgrade_recommendations\": []},\n");
        prompt.push_str("    \"compatibility_assessment\": {\"compatibility_score\": 95.0, \"potential_conflicts\": [], \"compatibility_risks\": []},\n");
        prompt.push_str("    \"modernization_opportunities\": []\n");
        prompt.push_str("  },\n");

        // Continue with remaining sections...
        prompt.push_str("  \"development_practices\": {\n");
        prompt.push_str("    \"code_style_consistency\": 90.0,\n");
        prompt.push_str("    \"testing_practices\": {\"test_coverage_estimate\": 75.0, \"test_types\": [\"unit\", \"integration\"], \"testing_frameworks\": [\"cargo test\"], \"test_quality_score\": 80.0, \"testing_gaps\": []},\n");
        prompt.push_str("    \"documentation_quality\": {\"documentation_score\": 70.0, \"documentation_coverage\": 60.0, \"documentation_types\": [\"inline\", \"readme\"], \"documentation_gaps\": [\"API docs\"]},\n");
        prompt.push_str("    \"error_handling_patterns\": {\"error_handling_score\": 85.0, \"error_patterns\": [\"Result type\"], \"error_handling_consistency\": 90.0, \"improvement_areas\": []},\n");
        prompt.push_str("    \"development_workflow\": {\"workflow_indicators\": [\"CI/CD\"], \"development_maturity\": \"Mature\", \"collaboration_patterns\": [\"Code review\"]}\n");
        prompt.push_str("  },\n");

        prompt.push_str("  \"security_assessment\": {\n");
        prompt.push_str("    \"security_score\": 85.0,\n");
        prompt.push_str("    \"security_patterns\": [\"Input validation\"],\n");
        prompt.push_str("    \"vulnerability_categories\": [],\n");
        prompt.push_str("    \"security_best_practices\": [{\"practice\": \"Memory safety\", \"adoption_level\": \"High\", \"effectiveness\": 95.0}],\n");
        prompt.push_str("    \"security_recommendations\": []\n");
        prompt.push_str("  },\n");

        prompt.push_str("  \"performance_insights\": {\n");
        prompt.push_str("    \"performance_score\": 80.0,\n");
        prompt.push_str("    \"performance_patterns\": [\"Zero-copy\"],\n");
        prompt.push_str("    \"bottleneck_analysis\": {\"algorithmic_complexity\": [], \"resource_intensive_operations\": [], \"scalability_concerns\": []},\n");
        prompt.push_str("    \"optimization_opportunities\": [],\n");
        prompt.push_str("    \"resource_usage_patterns\": [\"Memory efficient\"]\n");
        prompt.push_str("  },\n");

        prompt.push_str("  \"maintainability_insights\": {\n");
        prompt.push_str("    \"maintainability_score\": 85.0,\n");
        prompt.push_str("    \"maintainability_factors\": [{\"factor\": \"Modularity\", \"score\": 90.0, \"impact\": \"High\", \"improvement_suggestions\": []}],\n");
        prompt.push_str("    \"technical_debt_analysis\": {\"debt_score\": 15.0, \"debt_categories\": [], \"debt_trends\": \"Stable\", \"remediation_priority\": []},\n");
        prompt.push_str("    \"refactoring_opportunities\": []\n");
        prompt.push_str("  },\n");

        prompt.push_str("  \"recommendations\": [\n");
        prompt.push_str("    {\n");
        prompt.push_str("      \"category\": \"Testing\",\n");
        prompt.push_str("      \"title\": \"Improve test coverage\",\n");
        prompt.push_str("      \"description\": \"Add more comprehensive tests\",\n");
        prompt.push_str("      \"priority\": \"High\",\n");
        prompt.push_str("      \"impact\": \"High\",\n");
        prompt.push_str("      \"effort\": \"Medium\",\n");
        prompt.push_str("      \"action_items\": [\"Add unit tests\"],\n");
        prompt.push_str("      \"expected_outcomes\": [\"Better reliability\"]\n");
        prompt.push_str("    }\n");
        prompt.push_str("  ],\n");

        prompt.push_str("  \"trends\": [\n");
        prompt.push_str("    {\n");
        prompt.push_str("      \"trend_type\": \"Code Quality\",\n");
        prompt.push_str("      \"description\": \"Consistent code quality maintained\",\n");
        prompt.push_str("      \"trend_direction\": \"Stable\",\n");
        prompt.push_str("      \"confidence\": 0.85,\n");
        prompt.push_str("      \"supporting_data\": [\"Consistent metrics\"]\n");
        prompt.push_str("    }\n");
        prompt.push_str("  ],\n");

        prompt.push_str("  \"metrics\": {\n");
        prompt.push_str("    \"analysis_completeness\": 95.0,\n");
        prompt.push_str("    \"confidence_score\": 90.0,\n");
        prompt.push_str("    \"data_quality\": 88.0,\n");
        prompt.push_str(
            "    \"coverage_metrics\": {\"files_analyzed\": 100.0, \"patterns_detected\": 85.0}\n",
        );
        prompt.push_str("  }\n");
        prompt.push_str("}\n\n");

        // Add repository context
        prompt.push_str("Repository Context:\n");
        prompt.push_str(&format!("Files analyzed: {}\n", analyses.len()));
        prompt.push_str(&format!(
            "Total lines: {}\n",
            repository_metrics.total_lines
        ));
        prompt.push_str(&format!(
            "Average complexity: {:.2}\n",
            repository_metrics.average_complexity.unwrap_or(0.0)
        ));
        prompt.push_str(&format!(
            "Maintainability score: {:.2}\n",
            repository_metrics.maintainability_score.unwrap_or(0.0)
        ));

        // Add language information
        prompt.push_str("\nLanguage distribution:\n");
        for (lang, stats) in languages.iter().take(5) {
            prompt.push_str(&format!(
                "- {}: {:.1}% ({} files, {} lines)\n",
                lang, stats.percentage, stats.files, stats.lines
            ));
        }

        // Add pattern information
        if !patterns.is_empty() {
            prompt.push_str(&format!("\nDetected patterns: {}\n", patterns.len()));
            for pattern in patterns.iter().take(5) {
                prompt.push_str(&format!(
                    "- {:?}: {}\n",
                    pattern.pattern_type,
                    pattern
                        .description
                        .as_ref()
                        .unwrap_or(&"No description".to_string())
                ));
            }
        }

        // Add quality assessment if available
        if let Some(quality) = quality_assessment {
            prompt.push_str("\nQuality Assessment:\n");
            prompt.push_str(&format!("Overall score: {:.1}\n", quality.overall_score));
            prompt.push_str(&format!(
                "Maintainability: {:.1}\n",
                quality.maintainability_score
            ));
            prompt.push_str(&format!("Security: {:.1}\n", quality.security_score));
            prompt.push_str(&format!("Performance: {:.1}\n", quality.performance_score));
        }

        // Add sample files
        prompt.push_str("\nSample files for analysis:\n");
        for analysis in analyses.iter().take(5) {
            prompt.push_str(&format!("\nFile: {}\n", analysis.path));
            prompt.push_str(&format!("Language: {}\n", analysis.language));
            prompt.push_str(&format!("Complexity: {}\n", analysis.metrics.complexity));

            if let Some(text) = &analysis.ast.text {
                let preview = text.chars().take(800).collect::<String>();
                prompt.push_str(&format!("Code sample:\n```\n{preview}\n```\n"));
            }
        }

        prompt.push_str("\nProvide comprehensive, actionable insights based on this analysis. Focus on practical recommendations and clear explanations.");

        Ok(prompt)
    }

    async fn call_gemini_for_insights(&self, prompt: &str) -> Result<RepositoryInsights> {
        let auth_token = self.get_auth_token().await?;

        let endpoint = format!(
            "https://{}-aiplatform.googleapis.com/v1/projects/{}/locations/{}/publishers/google/models/{}:generateContent",
            self.location, self.project_id, self.location, self.model_name
        );

        let request = GeminiInsightsRequest {
            contents: vec![GeminiContent {
                parts: vec![GeminiPart {
                    text: prompt.to_string(),
                }],
                role: "user".to_string(),
            }],
            generation_config: GeminiGenerationConfig {
                temperature: 0.1,
                top_p: 0.8,
                top_k: 40,
                max_output_tokens: 8192,
                response_mime_type: "application/json".to_string(),
            },
            safety_settings: vec![
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_HARASSMENT".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_HATE_SPEECH".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_DANGEROUS_CONTENT".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
            ],
        };

        let response = self
            .client
            .post(&endpoint)
            .bearer_auth(&auth_token)
            .json(&request)
            .send()
            .await
            .context("Failed to send request to Gemini API")?;

        let status = response.status();

        if !status.is_success() {
            let error_body = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow::anyhow!(
                "Gemini API request failed with status {}: {}",
                status,
                error_body
            ));
        }

        let gemini_response: GeminiResponse = response
            .json()
            .await
            .context("Failed to parse Gemini response")?;

        if gemini_response.candidates.is_empty() {
            return Err(anyhow::anyhow!("No candidates in Gemini response"));
        }

        let response_text = &gemini_response.candidates[0].content.parts[0].text;

        let mut insights: RepositoryInsights = serde_json::from_str(response_text)
            .context("Failed to parse repository insights JSON")?;

        insights.timestamp = Utc::now();

        Ok(insights)
    }

    fn create_fallback_insights(
        &self,
        analyses: &[FileAnalysis],
        repository_metrics: &RepositoryMetrics,
        _patterns: &[DetectedPattern],
        quality_assessment: Option<&CodeQualityAssessment>,
        languages: &HashMap<String, crate::models::LanguageStats>,
    ) -> RepositoryInsights {
        // Create basic insights from available data
        let primary_language = languages
            .iter()
            .max_by_key(|(_, stats)| stats.lines)
            .map(|(lang, _)| lang.clone())
            .unwrap_or_else(|| "Unknown".to_string());

        let languages_used: Vec<LanguageUsage> = languages
            .iter()
            .map(|(lang, stats)| LanguageUsage {
                language: lang.clone(),
                percentage: stats.percentage,
                file_count: stats.files as u32,
                lines_of_code: stats.lines as u32,
            })
            .collect();

        let overall_health_score = quality_assessment
            .map(|qa| qa.overall_score)
            .unwrap_or(75.0); // Default score

        let summary = RepositorySummary {
            overview: format!(
                "Repository with {} files across {} languages",
                analyses.len(),
                languages.len()
            ),
            primary_language,
            languages_used,
            codebase_size: CodebaseSize {
                total_files: repository_metrics.total_files,
                total_lines: repository_metrics.total_lines,
                total_characters: repository_metrics.total_lines as u64 * 50, // Estimate
                size_category: if repository_metrics.total_lines < 10000 {
                    "Small".to_string()
                } else if repository_metrics.total_lines < 100000 {
                    "Medium".to_string()
                } else {
                    "Large".to_string()
                },
            },
            complexity_overview: ComplexityOverview {
                average_complexity: repository_metrics.average_complexity.unwrap_or(0.0),
                complexity_distribution: HashMap::new(),
                most_complex_files: Vec::new(),
                complexity_trends: "Stable".to_string(),
            },
            key_strengths: vec!["Structured codebase".to_string()],
            main_concerns: vec!["Requires analysis".to_string()],
            overall_health_score,
        };

        RepositoryInsights {
            summary,
            architecture_analysis: ArchitectureAnalysis {
                architecture_style: "Modular".to_string(),
                design_patterns: Vec::new(),
                module_organization: ModuleOrganization {
                    structure_score: 80.0,
                    coupling_analysis: "Moderate coupling".to_string(),
                    cohesion_analysis: "Good cohesion".to_string(),
                    module_boundaries: Vec::new(),
                },
                dependency_analysis: DependencyAnalysis {
                    dependency_count: 0,
                    circular_dependencies: Vec::new(),
                    dependency_depth: 0,
                    external_dependencies: Vec::new(),
                },
                architecture_score: 80.0,
                architectural_debt: Vec::new(),
            },
            technology_stack: TechnologyStackAnalysis {
                frameworks: Vec::new(),
                libraries: Vec::new(),
                tools: Vec::new(),
                version_analysis: VersionAnalysis {
                    outdated_dependencies: Vec::new(),
                    security_vulnerabilities: Vec::new(),
                    upgrade_recommendations: Vec::new(),
                },
                compatibility_assessment: CompatibilityAssessment {
                    compatibility_score: 85.0,
                    potential_conflicts: Vec::new(),
                    compatibility_risks: Vec::new(),
                },
                modernization_opportunities: Vec::new(),
            },
            development_practices: DevelopmentPracticesAnalysis {
                code_style_consistency: 80.0,
                testing_practices: TestingPractices {
                    test_coverage_estimate: repository_metrics
                        .test_coverage_estimate
                        .unwrap_or(0.0),
                    test_types: Vec::new(),
                    testing_frameworks: Vec::new(),
                    test_quality_score: 70.0,
                    testing_gaps: Vec::new(),
                },
                documentation_quality: DocumentationQuality {
                    documentation_score: 70.0,
                    documentation_coverage: 60.0,
                    documentation_types: Vec::new(),
                    documentation_gaps: Vec::new(),
                },
                error_handling_patterns: ErrorHandlingPatterns {
                    error_handling_score: 75.0,
                    error_patterns: Vec::new(),
                    error_handling_consistency: 80.0,
                    improvement_areas: Vec::new(),
                },
                development_workflow: DevelopmentWorkflow {
                    workflow_indicators: Vec::new(),
                    development_maturity: "Developing".to_string(),
                    collaboration_patterns: Vec::new(),
                },
            },
            security_assessment: SecurityAssessment {
                security_score: 80.0,
                security_patterns: Vec::new(),
                vulnerability_categories: Vec::new(),
                security_best_practices: Vec::new(),
                security_recommendations: Vec::new(),
            },
            performance_insights: PerformanceInsights {
                performance_score: 75.0,
                performance_patterns: Vec::new(),
                bottleneck_analysis: BottleneckAnalysis {
                    algorithmic_complexity: Vec::new(),
                    resource_intensive_operations: Vec::new(),
                    scalability_concerns: Vec::new(),
                },
                optimization_opportunities: Vec::new(),
                resource_usage_patterns: Vec::new(),
            },
            maintainability_insights: MaintainabilityInsights {
                maintainability_score: repository_metrics.maintainability_score.unwrap_or(0.0),
                maintainability_factors: Vec::new(),
                technical_debt_analysis: TechnicalDebtAnalysis {
                    debt_score: 20.0,
                    debt_categories: Vec::new(),
                    debt_trends: "Stable".to_string(),
                    remediation_priority: Vec::new(),
                },
                refactoring_opportunities: Vec::new(),
            },
            recommendations: Vec::new(),
            trends: Vec::new(),
            metrics: InsightsMetrics {
                analysis_completeness: 70.0,
                confidence_score: 60.0,
                data_quality: 75.0,
                coverage_metrics: HashMap::new(),
            },
            timestamp: Utc::now(),
        }
    }

    async fn get_auth_token(&self) -> Result<String> {
        // Reuse auth token logic from previous services
        if env::var("K_SERVICE").is_ok() || env::var("GAE_ENV").is_ok() {
            let metadata_url = format!(
                "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token?scopes={}",
                "https://www.googleapis.com/auth/cloud-platform"
            );

            let response = self
                .client
                .get(&metadata_url)
                .header("Metadata-Flavor", "Google")
                .send()
                .await
                .context("Failed to fetch token from metadata server")?;

            if !response.status().is_success() {
                return Err(anyhow::anyhow!(
                    "Metadata server returned error: {}",
                    response.status()
                ));
            }

            #[derive(Deserialize)]
            struct TokenResponse {
                access_token: String,
            }

            let token_response: TokenResponse = response
                .json()
                .await
                .context("Failed to parse token response")?;

            Ok(token_response.access_token)
        } else if let Ok(_creds_path) = env::var("GOOGLE_APPLICATION_CREDENTIALS") {
            use std::process::Command;

            let output = Command::new("gcloud")
                .args(["auth", "application-default", "print-access-token"])
                .output()
                .context("Failed to run gcloud command")?;

            if !output.status.success() {
                return Err(anyhow::anyhow!(
                    "gcloud command failed: {}",
                    String::from_utf8_lossy(&output.stderr)
                ));
            }

            let token = String::from_utf8(output.stdout)
                .context("Invalid UTF-8 in token")?
                .trim()
                .to_string();

            Ok(token)
        } else {
            Err(anyhow::anyhow!(
                "No authentication method available. Set GOOGLE_APPLICATION_CREDENTIALS or run on Cloud Run"
            ))
        }
    }

    // Circuit breaker implementation (reuse from previous services)
    async fn check_circuit_breaker(&self) -> Result<bool> {
        let mut state = self.circuit_state.lock().await;

        match *state {
            CircuitState::Closed => Ok(true),
            CircuitState::Open(reset_time) => {
                if Utc::now() > reset_time {
                    *state = CircuitState::HalfOpen;
                    tracing::info!(
                        "Repository insights circuit breaker transitioning to half-open"
                    );
                    Ok(true)
                } else {
                    Ok(false)
                }
            }
            CircuitState::HalfOpen => Ok(true),
        }
    }

    async fn record_success(&self) {
        let mut state = self.circuit_state.lock().await;
        let mut failures = self.failure_count.lock().await;
        let mut metrics = self.metrics.lock().await;

        *failures = 0;
        metrics.successful_analyses += 1;

        if matches!(*state, CircuitState::HalfOpen) {
            *state = CircuitState::Closed;
            tracing::info!("Repository insights circuit breaker closed after successful request");
        }
    }

    async fn record_failure(&self) {
        let mut state = self.circuit_state.lock().await;
        let mut failures = self.failure_count.lock().await;
        let mut metrics = self.metrics.lock().await;

        *failures += 1;
        metrics.failed_analyses += 1;

        if *failures >= self.failure_threshold {
            let reset_time = Utc::now()
                + chrono::Duration::from_std(self.reset_timeout)
                    .unwrap_or_else(|_| chrono::Duration::seconds(300));
            *state = CircuitState::Open(reset_time);
            *failures = 0;
            metrics.circuit_breaker_opens += 1;

            tracing::error!(
                "Repository insights circuit breaker opened after {} failures, will reset at {}",
                self.failure_threshold,
                reset_time
            );
        }
    }

    async fn record_fallback_use(&self) {
        let mut metrics = self.metrics.lock().await;
        metrics.fallback_uses += 1;
    }

    async fn record_response_time(&self, response_time_ms: f64) {
        let mut metrics = self.metrics.lock().await;
        metrics.total_analyses += 1;

        let total_analyses = metrics.total_analyses as f64;
        metrics.average_response_time_ms =
            ((metrics.average_response_time_ms * (total_analyses - 1.0)) + response_time_ms)
                / total_analyses;
    }

    fn is_retryable_error(&self, error: &anyhow::Error) -> bool {
        let error_str = error.to_string().to_lowercase();

        error_str.contains("timeout") ||
        error_str.contains("temporarily unavailable") ||
        error_str.contains("429") || // Rate limit
        error_str.contains("500") || // Internal server error
        error_str.contains("502") || // Bad gateway
        error_str.contains("503") || // Service unavailable
        error_str.contains("504") // Gateway timeout
    }

    pub async fn get_metrics(&self) -> InsightsMetricsData {
        let metrics = self.metrics.lock().await;
        InsightsMetricsData {
            total_analyses: metrics.total_analyses,
            successful_analyses: metrics.successful_analyses,
            failed_analyses: metrics.failed_analyses,
            average_response_time_ms: metrics.average_response_time_ms,
            circuit_breaker_opens: metrics.circuit_breaker_opens,
            fallback_uses: metrics.fallback_uses,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    use crate::models::{AstNode, FileMetrics, Position, Range};
    use std::collections::HashMap;

    #[tokio::test]
    async fn test_fallback_insights_creation() {
        let embeddings_service = Arc::new(EnhancedEmbeddingsService::new().await.expect("Failed to create embeddings service for test"));
        let insights_service = RepositoryInsightsService::new(embeddings_service)
            .await
            .expect("Failed to create insights service for test");

        let analysis = FileAnalysis {
            path: "test.rs".to_string(),
            language: "rust".to_string(),
            content_hash: "hash123".to_string(),
            size_bytes: Some(1000),
            ast: AstNode {
                node_type: "root".to_string(),
                name: None,
                range: Range {
                    start: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                    end: Position {
                        line: 10,
                        column: 0,
                        byte: 100,
                    },
                },
                children: vec![],
                properties: None,
                text: None,
            },
            metrics: FileMetrics {
                lines_of_code: 50,
                total_lines: Some(60),
                complexity: 5,
                maintainability_index: 85.0,
                function_count: 3,
                class_count: 1,
                comment_ratio: 0.25,
            },
            chunks: None,
            symbols: None,
        };

        let repo_metrics = RepositoryMetrics {
            total_files: 1,
            total_lines: 50,
            total_complexity: 5,
            average_complexity: Some(5.0),
            maintainability_score: Some(85.0),
            technical_debt_minutes: Some(60),
            test_coverage_estimate: Some(70.0),
        };

        let mut languages = HashMap::new();
        languages.insert(
            "rust".to_string(),
            crate::models::LanguageStats {
                lines: 50,
                files: 1,
                percentage: 100.0,
                bytes: Some(1000),
            },
        );

        let insights = insights_service.create_fallback_insights(
            &[analysis],
            &repo_metrics,
            &[],
            None,
            &languages,
        );

        assert!(insights.summary.overall_health_score > 0.0);
        assert_eq!(insights.summary.primary_language, "rust");
        assert_eq!(insights.summary.languages_used.len(), 1);
        assert_eq!(insights.summary.codebase_size.total_files, 1);
        assert_eq!(insights.summary.codebase_size.total_lines, 50);
        assert_eq!(insights.summary.codebase_size.size_category, "Small");
        assert!(insights.architecture_analysis.architecture_score > 0.0);
        assert!(insights.maintainability_insights.maintainability_score > 0.0);
    }

    #[test]
    fn test_repository_insights_serialization() {
        let insights = RepositoryInsights {
            summary: RepositorySummary {
                overview: "Test repository".to_string(),
                primary_language: "rust".to_string(),
                languages_used: vec![],
                codebase_size: CodebaseSize {
                    total_files: 10,
                    total_lines: 1000,
                    total_characters: 50000,
                    size_category: "Medium".to_string(),
                },
                complexity_overview: ComplexityOverview {
                    average_complexity: 5.0,
                    complexity_distribution: HashMap::new(),
                    most_complex_files: vec![],
                    complexity_trends: "Stable".to_string(),
                },
                key_strengths: vec!["Good structure".to_string()],
                main_concerns: vec!["Needs tests".to_string()],
                overall_health_score: 80.0,
            },
            architecture_analysis: ArchitectureAnalysis {
                architecture_style: "Modular".to_string(),
                design_patterns: vec![],
                module_organization: ModuleOrganization {
                    structure_score: 85.0,
                    coupling_analysis: "Low coupling".to_string(),
                    cohesion_analysis: "High cohesion".to_string(),
                    module_boundaries: vec![],
                },
                dependency_analysis: DependencyAnalysis {
                    dependency_count: 5,
                    circular_dependencies: vec![],
                    dependency_depth: 2,
                    external_dependencies: vec![],
                },
                architecture_score: 85.0,
                architectural_debt: vec![],
            },
            technology_stack: TechnologyStackAnalysis {
                frameworks: vec![],
                libraries: vec![],
                tools: vec![],
                version_analysis: VersionAnalysis {
                    outdated_dependencies: vec![],
                    security_vulnerabilities: vec![],
                    upgrade_recommendations: vec![],
                },
                compatibility_assessment: CompatibilityAssessment {
                    compatibility_score: 90.0,
                    potential_conflicts: vec![],
                    compatibility_risks: vec![],
                },
                modernization_opportunities: vec![],
            },
            development_practices: DevelopmentPracticesAnalysis {
                code_style_consistency: 85.0,
                testing_practices: TestingPractices {
                    test_coverage_estimate: 75.0,
                    test_types: vec![],
                    testing_frameworks: vec![],
                    test_quality_score: 80.0,
                    testing_gaps: vec![],
                },
                documentation_quality: DocumentationQuality {
                    documentation_score: 70.0,
                    documentation_coverage: 60.0,
                    documentation_types: vec![],
                    documentation_gaps: vec![],
                },
                error_handling_patterns: ErrorHandlingPatterns {
                    error_handling_score: 80.0,
                    error_patterns: vec![],
                    error_handling_consistency: 85.0,
                    improvement_areas: vec![],
                },
                development_workflow: DevelopmentWorkflow {
                    workflow_indicators: vec![],
                    development_maturity: "Mature".to_string(),
                    collaboration_patterns: vec![],
                },
            },
            security_assessment: crate::services::repository_insights::SecurityAssessment {
                security_score: 85.0,
                security_patterns: vec![],
                vulnerability_categories: vec![],
                security_best_practices: vec![],
                security_recommendations: vec![],
            },
            performance_insights: PerformanceInsights {
                performance_score: 80.0,
                performance_patterns: vec![],
                bottleneck_analysis: BottleneckAnalysis {
                    algorithmic_complexity: vec![],
                    resource_intensive_operations: vec![],
                    scalability_concerns: vec![],
                },
                optimization_opportunities: vec![],
                resource_usage_patterns: vec![],
            },
            maintainability_insights: MaintainabilityInsights {
                maintainability_score: 85.0,
                maintainability_factors: vec![],
                technical_debt_analysis: TechnicalDebtAnalysis {
                    debt_score: 15.0,
                    debt_categories: vec![],
                    debt_trends: "Stable".to_string(),
                    remediation_priority: vec![],
                },
                refactoring_opportunities: vec![],
            },
            recommendations: vec![],
            trends: vec![],
            metrics: InsightsMetrics {
                analysis_completeness: 95.0,
                confidence_score: 90.0,
                data_quality: 88.0,
                coverage_metrics: HashMap::new(),
            },
            timestamp: Utc::now(),
        };

        let json = serde_json::to_string(&insights).expect("Failed to serialize insights for test");
        let deserialized: RepositoryInsights = serde_json::from_str(&json).expect("Failed to deserialize insights for test");

        assert_eq!(
            insights.summary.overall_health_score,
            deserialized.summary.overall_health_score
        );
        assert_eq!(
            insights.architecture_analysis.architecture_score,
            deserialized.architecture_analysis.architecture_score
        );
        assert_eq!(
            insights.metrics.analysis_completeness,
            deserialized.metrics.analysis_completeness
        );
    }
}
