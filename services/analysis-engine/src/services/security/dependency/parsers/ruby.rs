//! Ruby package file parsers (<PERSON><PERSON><PERSON><PERSON>, Gemfile.lock)

use crate::errors::AnalysisResult;
use crate::models::production::{DependencyInfo, UpdatePriority};
use crate::models::FileAnalysis;
use once_cell::sync::Lazy;
use regex::Regex;

/// Parse Gemfile
pub fn parse_gemfile(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let mut dependencies = Vec::new();
    let mut current_group: Option<String> = None;

    // Regex patterns for Gemfile parsing
    static GEM_REGEX: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r#"^\s*gem\s+['"]([^'"]+)['"]\s*(?:,\s*['"]([^'"]+)['"])?"#).expect("Gem regex pattern is valid")
    });
    static GROUP_REGEX: Lazy<Regex> = Lazy::new(|| Regex::new(r"^\s*group\s+:(\w+)").expect("Group regex pattern is valid"));

    for line in content.lines() {
        let line = line.trim();

        // Skip comments and empty lines
        if line.is_empty() || line.starts_with('#') {
            continue;
        }

        // Check for group declaration
        if let Some(captures) = GROUP_REGEX.captures(line) {
            if let Some(group) = captures.get(1).map(|m| m.as_str()) {
                current_group = Some(group.to_string());
                continue;
            }
        }

        // Check for end statement
        if line == "end" {
            current_group = None;
            continue;
        }

        // Parse gem declarations
        if let Some(captures) = GEM_REGEX.captures(line) {
            if let Some(name) = captures.get(1).map(|m| m.as_str()) {
                let version = captures
                    .get(2)
                    .map(|m| m.as_str())
                    .unwrap_or("*")
                    .to_string();
                let is_dev = current_group
                    .as_ref()
                    .is_some_and(|g| g == "development" || g == "test");

                dependencies.push(DependencyInfo {
                    name: name.to_string(),
                    current_version: clean_ruby_version(&version),
                    latest_version: String::new(),
                    vulnerability_count: 0,
                    update_priority: UpdatePriority::Minor,
                    registry: Some("rubygems".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: true,
                    is_dev,
                    is_optional: false,
                    dependency_path: None,
                });
            }
        }
    }

    Ok(dependencies)
}

/// Parse Gemfile.lock
pub fn parse_gemfile_lock(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let mut dependencies = Vec::new();
    let mut in_gem_section = false;
    let mut in_specs_section = false;
    let _current_gem: Option<(String, String)> = None;

    // Regex patterns
    static GEM_REGEX_LOCK: Lazy<Regex> =
        Lazy::new(|| Regex::new(r"^\s{4}(\S+)\s+\(([^)]+)\)").expect("Gem lock regex pattern is valid"));
    static SPEC_SECTION_REGEX: Lazy<Regex> = Lazy::new(|| Regex::new(r"^\s{2}specs:").expect("Spec section regex pattern is valid"));

    for line in content.lines() {
        // Check for GEM section
        if line.trim() == "GEM" {
            in_gem_section = true;
            continue;
        }

        // Check for other sections that end GEM
        if !line.starts_with(' ') && line.trim() != "GEM" && !line.trim().is_empty() {
            in_gem_section = false;
            in_specs_section = false;
            continue;
        }

        if in_gem_section {
            // Check for specs subsection
            if SPEC_SECTION_REGEX.is_match(line) {
                in_specs_section = true;
                continue;
            }

            if in_specs_section {
                // Parse gem entries
                if let Some(captures) = GEM_REGEX_LOCK.captures(line) {
                    if let (Some(name), Some(version)) = (
                        captures.get(1).map(|m| m.as_str()),
                        captures.get(2).map(|m| m.as_str()),
                    ) {
                        dependencies.push(DependencyInfo {
                            name: name.to_string(),
                            current_version: version.to_string(),
                            latest_version: String::new(),
                            vulnerability_count: 0,
                            update_priority: UpdatePriority::Minor,
                            registry: Some("rubygems".to_string()),
                            license: None,
                            description: None,
                            homepage: None,
                            repository_url: None,
                            is_direct: false,
                            is_dev: false,
                            is_optional: false,
                            dependency_path: None,
                        });
                    }
                }
            }
        }
    }

    Ok(dependencies)
}

/// Clean Ruby version string
fn clean_ruby_version(version: &str) -> String {
    // Remove common version operators
    version
        .trim()
        .trim_start_matches('~')
        .trim_start_matches('>')
        .trim_start_matches('<')
        .trim_start_matches('=')
        .trim()
        .to_string()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_clean_ruby_version() {
        assert_eq!(clean_ruby_version("~> 1.0"), "1.0");
        assert_eq!(clean_ruby_version(">= 2.0.0"), "2.0.0");
        assert_eq!(clean_ruby_version("= 3.1.4"), "3.1.4");
    }
}
