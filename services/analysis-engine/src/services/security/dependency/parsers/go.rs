//! Go package file parsers (go.mod, go.sum)

use crate::errors::AnalysisResult;
use crate::models::production::{DependencyInfo, UpdatePriority};
use crate::models::FileAnalysis;
use once_cell::sync::Lazy;
use regex::Regex;

/// Parse go.mod file
pub fn parse_go_mod(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let mut dependencies = Vec::new();
    let mut in_require_block = false;
    let mut in_replace_block = false;

    // Regex patterns for go.mod parsing
    static SINGLE_REQUIRE_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r"^require\s+(\S+)\s+v(.+)$").expect("Single require regex pattern is valid"));
    static BLOCK_REQUIRE_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r"^\s*(\S+)\s+v(.+?)(?:\s*//.*)?$").expect("Block require regex pattern is valid"));

    for line in content.lines() {
        let line = line.trim();

        // Skip comments and empty lines
        if line.is_empty() || line.starts_with("//") {
            continue;
        }

        // Check for require block start
        if line == "require (" {
            in_require_block = true;
            continue;
        }

        // Check for replace block start
        if line == "replace (" {
            in_replace_block = true;
            in_require_block = false;
            continue;
        }

        // Check for block end
        if line == ")" {
            in_require_block = false;
            in_replace_block = false;
            continue;
        }

        // Skip replace directives
        if in_replace_block {
            continue;
        }

        // Parse single line require
        if !in_require_block {
            if let Some(captures) = SINGLE_REQUIRE_REGEX.captures(line) {
                if let (Some(module), Some(version)) = (
                    captures.get(1).map(|m| m.as_str()),
                    captures.get(2).map(|m| m.as_str()),
                ) {
                    dependencies.push(DependencyInfo {
                        name: module.to_string(),
                        current_version: format!("v{version}"),
                        latest_version: String::new(),
                        vulnerability_count: 0,
                        update_priority: UpdatePriority::Minor,
                        registry: Some("go".to_string()),
                        license: None,
                        description: None,
                        homepage: None,
                        repository_url: None,
                        is_direct: true,
                        is_dev: false,
                        is_optional: false,
                        dependency_path: None,
                    });
                }
            }
        }

        // Parse require block entries
        if in_require_block {
            if let Some(captures) = BLOCK_REQUIRE_REGEX.captures(line) {
                if let (Some(module), Some(version)) = (
                    captures.get(1).map(|m| m.as_str()),
                    captures.get(2).map(|m| m.as_str()),
                ) {
                    dependencies.push(DependencyInfo {
                        name: module.to_string(),
                        current_version: format!("v{version}"),
                        latest_version: String::new(),
                        vulnerability_count: 0,
                        update_priority: UpdatePriority::Minor,
                        registry: Some("go".to_string()),
                        license: None,
                        description: None,
                        homepage: None,
                        repository_url: None,
                        is_direct: true,
                        is_dev: false,
                        is_optional: false,
                        dependency_path: None,
                    });
                }
            }
        }
    }

    Ok(dependencies)
}

/// Parse go.sum file
pub fn parse_go_sum(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let mut dependencies = Vec::new();
    let mut seen_modules = std::collections::HashSet::new();

    // Regex pattern for go.sum entries
    static SUM_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r"^(\S+)\s+(v\S+)(?:/go\.mod)?\s+h1:").expect("Sum regex pattern is valid"));

    for line in content.lines() {
        let line = line.trim();

        if line.is_empty() {
            continue;
        }

        if let Some(captures) = SUM_REGEX.captures(line) {
            if let (Some(module), Some(version)) = (
                captures.get(1).map(|m| m.as_str()),
                captures.get(2).map(|m| m.as_str()),
            ) {
                // Skip if we've already seen this module/version combination
                let key = format!("{module}@{version}");
                if seen_modules.contains(&key) {
                    continue;
                }
                seen_modules.insert(key);

                dependencies.push(DependencyInfo {
                    name: module.to_string(),
                    current_version: version.to_string(),
                    latest_version: String::new(),
                    vulnerability_count: 0,
                    update_priority: UpdatePriority::Minor,
                    registry: Some("go".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: false,
                    is_dev: false,
                    is_optional: false,
                    dependency_path: None,
                });
            }
        }
    }

    Ok(dependencies)
}

#[cfg(test)]
mod tests {

    #[test]
    fn test_go_mod_parsing() {
        // Test data would go here
        // For now, just ensure the functions compile
        assert!(true);
    }
}
