//! NPM package file parsers (package.json, package-lock.json, yarn.lock)

use crate::errors::AnalysisResult;
use crate::models::production::DependencyInfo;
use crate::models::FileAnalysis;
use once_cell::sync::Lazy;
use regex::Regex;
use serde_json;

/// Parse package.json file
pub fn parse_package_json(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let package_json: serde_json::Value = serde_json::from_str(&content)?;

    let mut dependencies = Vec::new();

    // Extract regular dependencies
    if let Some(deps) = package_json.get("dependencies").and_then(|d| d.as_object()) {
        for (name, version) in deps {
            if let Some(version_str) = version.as_str() {
                dependencies.push(DependencyInfo {
                    name: name.clone(),
                    current_version: clean_version(version_str),
                    latest_version: String::new(), // Default to empty string
                    vulnerability_count: 0,        // Default to 0
                    update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                    registry: Some("npm".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: true,    // Direct dependency
                    is_dev: false,      // Not a dev dependency
                    is_optional: false, // Not optional
                    dependency_path: None,
                });
            }
        }
    }

    // Extract dev dependencies
    if let Some(dev_deps) = package_json
        .get("devDependencies")
        .and_then(|d| d.as_object())
    {
        for (name, version) in dev_deps {
            if let Some(version_str) = version.as_str() {
                dependencies.push(DependencyInfo {
                    name: name.clone(),
                    current_version: clean_version(version_str),
                    latest_version: String::new(), // Default to empty string
                    vulnerability_count: 0,        // Default to 0
                    update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                    registry: Some("npm".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: true,    // Direct dependency
                    is_dev: true,       // This is a dev dependency
                    is_optional: false, // Not optional
                    dependency_path: None,
                });
            }
        }
    }

    // Extract peer dependencies
    if let Some(peer_deps) = package_json
        .get("peerDependencies")
        .and_then(|d| d.as_object())
    {
        for (name, version) in peer_deps {
            if let Some(version_str) = version.as_str() {
                dependencies.push(DependencyInfo {
                    name: name.clone(),
                    current_version: clean_version(version_str),
                    latest_version: String::new(), // Default to empty string
                    vulnerability_count: 0,        // Default to 0
                    update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                    registry: Some("npm".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: true,    // Direct dependency
                    is_dev: false,      // Not a dev dependency
                    is_optional: false, // Not optional
                    dependency_path: None,
                });
            }
        }
    }

    // Extract optional dependencies
    if let Some(opt_deps) = package_json
        .get("optionalDependencies")
        .and_then(|d| d.as_object())
    {
        for (name, version) in opt_deps {
            if let Some(version_str) = version.as_str() {
                dependencies.push(DependencyInfo {
                    name: name.clone(),
                    current_version: clean_version(version_str),
                    latest_version: String::new(), // Default to empty string
                    vulnerability_count: 0,        // Default to 0
                    update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                    registry: Some("npm".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: true,   // Direct dependency
                    is_dev: false,     // Not a dev dependency
                    is_optional: true, // This is an optional dependency
                    dependency_path: None,
                });
            }
        }
    }

    Ok(dependencies)
}

/// Parse package-lock.json file
pub fn parse_package_lock_json(
    file_analysis: &FileAnalysis,
) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let package_lock: serde_json::Value = serde_json::from_str(&content)?;

    let mut dependencies = Vec::new();

    // Handle lockfile version 1
    if let Some(deps) = package_lock.get("dependencies").and_then(|d| d.as_object()) {
        for (name, info) in deps {
            if let Some(version) = info.get("version").and_then(|v| v.as_str()) {
                let is_dev = info.get("dev").and_then(|d| d.as_bool()).unwrap_or(false);
                let is_optional = info
                    .get("optional")
                    .and_then(|o| o.as_bool())
                    .unwrap_or(false);

                dependencies.push(DependencyInfo {
                    name: name.clone(),
                    current_version: version.to_string(),
                    latest_version: String::new(), // Default to empty string
                    vulnerability_count: 0,        // Default to 0
                    update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                    registry: Some("npm".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: true, // Direct dependency
                    is_dev,          // Use extracted is_dev flag
                    is_optional,     // Use extracted is_optional flag
                    dependency_path: None,
                });
            }
        }
    }

    // Handle lockfile version 2 and 3
    if let Some(packages) = package_lock.get("packages").and_then(|p| p.as_object()) {
        for (path, info) in packages {
            // Skip the root package
            if path.is_empty() {
                continue;
            }

            if let Some(version) = info.get("version").and_then(|v| v.as_str()) {
                let name = path.split('/').next_back().unwrap_or(path);
                let is_dev = info.get("dev").and_then(|d| d.as_bool()).unwrap_or(false);
                let is_optional = info
                    .get("optional")
                    .and_then(|o| o.as_bool())
                    .unwrap_or(false);

                dependencies.push(DependencyInfo {
                    name: name.to_string(),
                    current_version: version.to_string(),
                    latest_version: String::new(), // Default to empty string
                    vulnerability_count: 0,        // Default to 0
                    update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                    registry: Some("npm".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: false, // Not a direct dependency
                    is_dev,           // Use extracted is_dev flag
                    is_optional,      // Use extracted is_optional flag
                    dependency_path: Some(path.clone()),
                });
            }
        }
    }

    Ok(dependencies)
}

/// Parse yarn.lock file
pub fn parse_yarn_lock(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let mut dependencies = Vec::new();

    // Simple regex-based parser for yarn.lock
    // In production, use a proper yarn.lock parser
    static ENTRY_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r#"^"?([^@\s"]+)@([^":]+)"?:\s*$"#)
            .expect("ENTRY_REGEX pattern is valid"));
    static VERSION_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r#"^\s+version\s+"([^"]+)""#)
            .expect("VERSION_REGEX pattern is valid"));

    let mut current_package: Option<String> = None;

    for line in content.lines() {
        if let Some(captures) = ENTRY_REGEX.captures(line) {
            if let Some(name) = captures.get(1) {
                current_package = Some(name.as_str().to_string());
            }
        } else if let Some(ref package_name) = current_package {
            if let Some(captures) = VERSION_REGEX.captures(line) {
                if let Some(version) = captures.get(1) {
                    dependencies.push(DependencyInfo {
                        name: package_name.clone(),
                        current_version: version.as_str().to_string(),
                        latest_version: String::new(), // Default to empty string
                        vulnerability_count: 0,        // Default to 0
                        update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                        registry: Some("npm".to_string()),
                        license: None,
                        description: None,
                        homepage: None,
                        repository_url: None,
                        is_direct: false,   // Not a direct dependency
                        is_dev: false,      // Not a dev dependency
                        is_optional: false, // Not optional
                        dependency_path: None,
                    });
                    current_package = None;
                }
            }
        }
    }

    Ok(dependencies)
}

/// Clean version string by removing common prefixes
fn clean_version(version: &str) -> String {
    version
        .trim_start_matches('^')
        .trim_start_matches('~')
        .trim_start_matches('>')
        .trim_start_matches('<')
        .trim_start_matches('=')
        .trim_start_matches('v')
        .to_string()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_clean_version() {
        assert_eq!(clean_version("^1.0.0"), "1.0.0");
        assert_eq!(clean_version("~2.3.4"), "2.3.4");
        assert_eq!(clean_version(">=3.0.0"), "3.0.0");
        assert_eq!(clean_version("v4.5.6"), "4.5.6");
    }
}
