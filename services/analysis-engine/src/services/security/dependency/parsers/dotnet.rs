//! .NET package file parsers (packages.config, *.csproj, *.vbproj, *.fsproj)

use crate::errors::AnalysisResult;
use crate::models::production::{DependencyInfo, UpdatePriority};
use crate::models::FileAnalysis;
use quick_xml::events::Event;
use quick_xml::reader::Reader;

/// Parse packages.config file
pub fn parse_packages_config(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let mut reader = Reader::from_str(&content);
    reader.trim_text(true);

    let mut dependencies = Vec::new();

    loop {
        match reader.read_event() {
            Ok(Event::Empty(ref e)) => {
                if e.name().as_ref() == b"package" {
                    let mut name = String::new();
                    let mut version = String::new();
                    let mut is_dev = false;

                    for attr in e.attributes().flatten() {
                        match attr.key.as_ref() {
                            b"id" => name = String::from_utf8_lossy(&attr.value).to_string(),
                            b"version" => {
                                version = String::from_utf8_lossy(&attr.value).to_string()
                            }
                            b"developmentDependency" => {
                                is_dev = String::from_utf8_lossy(&attr.value).to_lowercase()
                                    == "true"
                            }
                            _ => {}
                        }
                    }

                    if !name.is_empty() && !version.is_empty() {
                        dependencies.push(DependencyInfo {
                            name,
                            current_version: version,
                            latest_version: String::new(),
                            vulnerability_count: 0,
                            update_priority: UpdatePriority::Minor,
                            registry: Some("nuget".to_string()),
                            license: None,
                            description: None,
                            homepage: None,
                            repository_url: None,
                            is_direct: true,
                            is_dev,
                            is_optional: false,
                            dependency_path: None,
                        });
                    }
                }
            }
            Ok(Event::Eof) => break,
            Err(e) => {
                tracing::error!("Error parsing packages.config: {}", e);
                break;
            }
            _ => {}
        }
    }

    Ok(dependencies)
}

/// Parse .NET project files (*.csproj, *.vbproj, *.fsproj)
pub fn parse_dotnet_project(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let mut reader = Reader::from_str(&content);
    reader.trim_text(true);

    let mut dependencies = Vec::new();
    let mut in_item_group = false;

    loop {
        match reader.read_event() {
            Ok(Event::Start(ref e)) => {
                if e.name().as_ref() == b"ItemGroup" {
                    in_item_group = true;
                }
            }
            Ok(Event::Empty(ref e)) => {
                if in_item_group && e.name().as_ref() == b"PackageReference" {
                    let mut name = String::new();
                    let mut version = String::new();
                    let mut is_private = false;

                    for attr in e.attributes().flatten() {
                        match attr.key.as_ref() {
                            b"Include" => {
                                name = String::from_utf8_lossy(&attr.value).to_string()
                            }
                            b"Version" => {
                                version = String::from_utf8_lossy(&attr.value).to_string()
                            }
                            b"PrivateAssets" => {
                                is_private =
                                    String::from_utf8_lossy(&attr.value).contains("all")
                            }
                            _ => {}
                        }
                    }

                    if !name.is_empty() && !version.is_empty() {
                        dependencies.push(DependencyInfo {
                            name,
                            current_version: clean_dotnet_version(&version),
                            latest_version: String::new(),
                            vulnerability_count: 0,
                            update_priority: UpdatePriority::Minor,
                            registry: Some("nuget".to_string()),
                            license: None,
                            description: None,
                            homepage: None,
                            repository_url: None,
                            is_direct: true,
                            is_dev: is_private,
                            is_optional: false,
                            dependency_path: None,
                        });
                    }
                }
            }
            Ok(Event::End(ref e)) => {
                if e.name().as_ref() == b"ItemGroup" {
                    in_item_group = false;
                }
            }
            Ok(Event::Eof) => break,
            Err(e) => {
                tracing::error!("Error parsing .NET project file: {}", e);
                break;
            }
            _ => {}
        }
    }

    Ok(dependencies)
}

/// Clean .NET version string
fn clean_dotnet_version(version: &str) -> String {
    // Remove version ranges if present
    if version.contains('[') || version.contains('(') {
        // For version ranges, just take the lower bound
        version
            .trim()
            .trim_start_matches('[')
            .trim_start_matches('(')
            .split(',')
            .next()
            .unwrap_or(version)
            .trim()
            .to_string()
    } else {
        version.trim().to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_clean_dotnet_version() {
        assert_eq!(clean_dotnet_version("1.0.0"), "1.0.0");
        assert_eq!(clean_dotnet_version("[1.0.0,2.0.0)"), "1.0.0");
        assert_eq!(clean_dotnet_version("(1.0.0,)"), "1.0.0");
    }
}
