//! Python package file parsers (requirements.txt, Pipfile, pyproject.toml)

use crate::errors::AnalysisResult;
use crate::models::production::DependencyInfo;
use crate::models::FileAnalysis;
use once_cell::sync::Lazy;
use regex::Regex;
use toml;

/// Parse requirements.txt file
pub fn parse_requirements_txt(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let mut dependencies = Vec::new();

    // Regex patterns for different requirement formats
    static VERSION_PATTERN: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r"^([a-zA-Z0-9_\-\.]+)\s*([=<>!~]+)\s*(.+?)(?:\s*[;#].*)?$")
            .expect("VERSION_PATTERN regex is valid")
    });
    static SIMPLE_PATTERN: Lazy<Regex> =
        Lazy::new(|| Regex::new(r"^([a-zA-Z0-9_\-\.]+)\s*(?:[;#].*)?$")
            .expect("SIMPLE_PATTERN regex is valid"));

    for line in content.lines() {
        let line = line.trim();

        // Skip empty lines and comments
        if line.is_empty() || line.starts_with('#') {
            continue;
        }

        // Skip special pip directives
        if line.starts_with('-') || line.starts_with("git+") || line.starts_with("http") {
            continue;
        }

        // Try to match versioned dependency (e.g., "django==3.2.0", "flask>=2.0.0")
        if let Some(captures) = VERSION_PATTERN.captures(line) {
            if let (Some(name), Some(op), Some(version)) = (
                captures.get(1).map(|m| m.as_str()),
                captures.get(2).map(|m| m.as_str()),
                captures.get(3).map(|m| m.as_str()),
            ) {
                // Normalize version string
                let version_str = if op.contains('=') || op.contains('<') || op.contains('>') {
                    format!("{op}{version}")
                } else {
                    version.to_string()
                };

                dependencies.push(DependencyInfo {
                    name: name.to_string(),
                    current_version: clean_python_version(&version_str),
                    latest_version: String::new(), // Default to empty string
                    vulnerability_count: 0,        // Default to 0
                    update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                    registry: Some("pypi".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: true,
                    is_dev: false,
                    is_optional: false,
                    dependency_path: None,
                });
                continue;
            }
        }

        // Try to match simple dependency without version (e.g., "requests")
        if let Some(captures) = SIMPLE_PATTERN.captures(line) {
            if let Some(name) = captures.get(1).map(|m| m.as_str()) {
                dependencies.push(DependencyInfo {
                    name: name.to_string(),
                    current_version: "*".to_string(), // No version specified
                    latest_version: String::new(),    // Default to empty string
                    vulnerability_count: 0,           // Default to 0
                    update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                    registry: Some("pypi".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: true,
                    is_dev: false,
                    is_optional: false,
                    dependency_path: None,
                });
            }
        }
    }

    Ok(dependencies)
}

/// Parse Pipfile
pub fn parse_pipfile(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let pipfile: toml::Value = toml::from_str(&content)?;
    let mut dependencies = Vec::new();

    // Parse regular packages
    if let Some(packages) = pipfile.get("packages").and_then(|p| p.as_table()) {
        for (name, spec) in packages {
            let version = match spec {
                toml::Value::String(v) => v.clone(),
                toml::Value::Table(t) => t
                    .get("version")
                    .and_then(|v| v.as_str())
                    .unwrap_or("*")
                    .to_string(),
                _ => "*".to_string(),
            };

            dependencies.push(DependencyInfo {
                name: name.clone(),
                current_version: clean_python_version(&version),
                latest_version: String::new(), // Default to empty string
                vulnerability_count: 0,        // Default to 0
                update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                registry: Some("pypi".to_string()),
                license: None,
                description: None,
                homepage: None,
                repository_url: None,
                is_direct: true,
                is_dev: false,
                is_optional: false,
                dependency_path: None,
            });
        }
    }

    // Parse dev packages
    if let Some(dev_packages) = pipfile.get("dev-packages").and_then(|p| p.as_table()) {
        for (name, spec) in dev_packages {
            let version = match spec {
                toml::Value::String(v) => v.clone(),
                toml::Value::Table(t) => t
                    .get("version")
                    .and_then(|v| v.as_str())
                    .unwrap_or("*")
                    .to_string(),
                _ => "*".to_string(),
            };

            dependencies.push(DependencyInfo {
                name: name.clone(),
                current_version: clean_python_version(&version),
                latest_version: String::new(), // Default to empty string
                vulnerability_count: 0,        // Default to 0
                update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                registry: Some("pypi".to_string()),
                license: None,
                description: None,
                homepage: None,
                repository_url: None,
                is_direct: true,
                is_dev: true,
                is_optional: false,
                dependency_path: None,
            });
        }
    }

    Ok(dependencies)
}

/// Parse pyproject.toml
pub fn parse_pyproject_toml(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();

    if content.trim().is_empty() {
        return Ok(Vec::new());
    }

    let pyproject: toml::Value = toml::from_str(&content)?;
    let mut dependencies = Vec::new();

    // Check for PEP 621 dependencies
    if let Some(project) = pyproject.get("project").and_then(|p| p.as_table()) {
        // Parse dependencies array
        if let Some(deps) = project.get("dependencies").and_then(|d| d.as_array()) {
            for dep in deps {
                if let Some(dep_str) = dep.as_str() {
                    let (name, version) = parse_pep508_dependency(dep_str);
                    dependencies.push(DependencyInfo {
                        name,
                        current_version: version,
                        latest_version: String::new(), // Default to empty string
                        vulnerability_count: 0,        // Default to 0
                        update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                        registry: Some("pypi".to_string()),
                        license: None,
                        description: None,
                        homepage: None,
                        repository_url: None,
                        is_direct: true,
                        is_dev: false,
                        is_optional: false,
                        dependency_path: None,
                    });
                }
            }
        }

        // Parse optional dependencies
        if let Some(optional_deps) = project
            .get("optional-dependencies")
            .and_then(|o| o.as_table())
        {
            for (_group, deps) in optional_deps {
                if let Some(deps_array) = deps.as_array() {
                    for dep in deps_array {
                        if let Some(dep_str) = dep.as_str() {
                            let (name, version) = parse_pep508_dependency(dep_str);
                            dependencies.push(DependencyInfo {
                                name,
                                current_version: version,
                                latest_version: String::new(), // Default to empty string
                                vulnerability_count: 0,        // Default to 0
                                update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                                registry: Some("pypi".to_string()),
                                license: None,
                                description: None,
                                homepage: None,
                                repository_url: None,
                                is_direct: true,
                                is_dev: false,
                                is_optional: true,
                                dependency_path: None,
                            });
                        }
                    }
                }
            }
        }
    }

    // Check for Poetry dependencies
    if let Some(tool) = pyproject.get("tool").and_then(|t| t.as_table()) {
        if let Some(poetry) = tool.get("poetry").and_then(|p| p.as_table()) {
            // Parse poetry dependencies
            if let Some(deps) = poetry.get("dependencies").and_then(|d| d.as_table()) {
                for (name, spec) in deps {
                    if name == "python" {
                        continue; // Skip Python version constraint
                    }

                    let version = match spec {
                        toml::Value::String(v) => v.clone(),
                        toml::Value::Table(t) => t
                            .get("version")
                            .and_then(|v| v.as_str())
                            .unwrap_or("*")
                            .to_string(),
                        _ => "*".to_string(),
                    };

                    dependencies.push(DependencyInfo {
                        name: name.clone(),
                        current_version: clean_python_version(&version),
                        latest_version: String::new(), // Default to empty string
                        vulnerability_count: 0,        // Default to 0
                        update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                        registry: Some("pypi".to_string()),
                        license: None,
                        description: None,
                        homepage: None,
                        repository_url: None,
                        is_direct: true,
                        is_dev: false,
                        is_optional: false,
                        dependency_path: None,
                    });
                }
            }

            // Parse poetry dev dependencies
            if let Some(dev_deps) = poetry.get("dev-dependencies").and_then(|d| d.as_table()) {
                for (name, spec) in dev_deps {
                    let version = match spec {
                        toml::Value::String(v) => v.clone(),
                        toml::Value::Table(t) => t
                            .get("version")
                            .and_then(|v| v.as_str())
                            .unwrap_or("*")
                            .to_string(),
                        _ => "*".to_string(),
                    };

                    dependencies.push(DependencyInfo {
                        name: name.clone(),
                        current_version: clean_python_version(&version),
                        latest_version: String::new(), // Default to empty string
                        vulnerability_count: 0,        // Default to 0
                        update_priority: crate::models::production::UpdatePriority::Minor, // Default to Minor
                        registry: Some("pypi".to_string()),
                        license: None,
                        description: None,
                        homepage: None,
                        repository_url: None,
                        is_direct: true,
                        is_dev: true,
                        is_optional: false,
                        dependency_path: None,
                    });
                }
            }
        }
    }

    Ok(dependencies)
}

/// Parse PEP 508 dependency specification
fn parse_pep508_dependency(dep_str: &str) -> (String, String) {
    // Simple regex to extract package name and version
    static VERSION_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r"^([a-zA-Z0-9_\-\.]+)\s*([=<>!~]+.+)?$")
            .expect("VERSION_REGEX is valid"));

    if let Some(captures) = VERSION_REGEX.captures(dep_str.trim()) {
        let name = captures
            .get(1)
            .map(|m| m.as_str())
            .unwrap_or(dep_str)
            .to_string();
        let version = captures
            .get(2)
            .map(|m| m.as_str().trim())
            .unwrap_or("*")
            .to_string();
        (name, clean_python_version(&version))
    } else {
        (dep_str.trim().to_string(), "*".to_string())
    }
}

/// Clean Python version string
fn clean_python_version(version: &str) -> String {
    if version.is_empty() || version == "*" {
        return "*".to_string();
    }

    // Remove common prefixes and clean up
    let trimmed = version.trim();

    // Only remove ^ prefix, keep ~= as it has specific meaning in Python
    if trimmed.starts_with('^') {
        trimmed.trim_start_matches('^').to_string()
    } else {
        trimmed.to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_pep508_dependency() {
        assert_eq!(
            parse_pep508_dependency("django"),
            ("django".to_string(), "*".to_string())
        );
        assert_eq!(
            parse_pep508_dependency("flask>=2.0.0"),
            ("flask".to_string(), ">=2.0.0".to_string())
        );
        assert_eq!(
            parse_pep508_dependency("requests==2.28.0"),
            ("requests".to_string(), "==2.28.0".to_string())
        );
    }

    #[test]
    fn test_clean_python_version() {
        assert_eq!(clean_python_version("^1.0.0"), "1.0.0");
        assert_eq!(clean_python_version("~=2.0"), "~=2.0");
        assert_eq!(clean_python_version(""), "*");
    }
}
