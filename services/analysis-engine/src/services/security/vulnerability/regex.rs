use once_cell::sync::Lazy;
use regex::Regex;

pub static SQL_INJECTION_1: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)(query|execute|exec)\s*\(\s*["'].*\+.*["']"#)
        .expect("SQL_INJECTION_1 regex pattern is valid"));

pub static SQL_INJECTION_2: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)(cursor\.execute|execute)\s*\(\s*["'].*%s.*["']\s*%"#)
        .expect("SQL_INJECTION_2 regex pattern is valid"));

pub static SQL_INJECTION_3: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)preparedStatement\s*=\s*connection\.prepareStatement\s*\(\s*["'].*\+.*["']"#)
        .expect("SQL_INJECTION_3 regex pattern is valid")
});

pub static SQL_INJECTION_4: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)(query|execute)\s*\(\s*`.*\$\{.*\}`"#)
        .expect("SQL_INJECTION_4 regex pattern is valid"));

pub static SQL_INJECTION_5: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)mysql_query\s*\(\s*["'].*\.\s*\$"#)
        .expect("SQL_INJECTION_5 regex pattern is valid"));

pub static XSS_1: Lazy<Regex> = Lazy::new(|| Regex::new(r#"(?i)innerHTML\s*=\s*[^"']"#)
    .expect("XSS_1 regex pattern is valid"));

pub static XSS_2: Lazy<Regex> = Lazy::new(|| Regex::new(r#"(?i)document\.write\s*\("#)
    .expect("XSS_2 regex pattern is valid"));

pub static XSS_3: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)\$\s*\([^)]+\)\.html\s*\("#)
        .expect("XSS_3 regex pattern is valid"));

pub static CMD_INJECTION_1: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)exec\s*\(\s*["'`].*\$"#)
        .expect("CMD_INJECTION_1 regex pattern is valid"));

pub static CMD_INJECTION_2: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)os\.system\s*\("#)
        .expect("CMD_INJECTION_2 regex pattern is valid"));

pub static CMD_INJECTION_3: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"`[^`]*\#\{[^}]+\}[^`]*`"#)
        .expect("CMD_INJECTION_3 regex pattern is valid"));

pub static PATH_TRAVERSAL_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(open|readFile|createReadStream)\s*\(\s*["'].*\+.*["']"#)
        .expect("PATH_TRAVERSAL_1 regex pattern is valid")
});

pub static PATH_TRAVERSAL_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(include|require|include_once|require_once)\s*\(\s*\$"#)
        .expect("PATH_TRAVERSAL_2 regex pattern is valid")
});

pub static HARDCODED_SECRET_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(api[_-]?key|apikey)\s*[:=]\s*["'][0-9a-zA-Z]{20,}["']"#)
        .expect("HARDCODED_SECRET_1 regex pattern is valid")
});

pub static HARDCODED_SECRET_2: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"AKIA[0-9A-Z]{16}"#)
        .expect("HARDCODED_SECRET_2 regex pattern is valid"));

pub static HARDCODED_SECRET_3: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"-----BEGIN (RSA |EC |DSA |OPENSSH )?PRIVATE KEY-----"#)
        .expect("HARDCODED_SECRET_3 regex pattern is valid"));

pub static INSECURE_RANDOM_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(token|password|secret|key|salt|iv|nonce).*Math\.random\(\)"#)
        .expect("INSECURE_RANDOM_1 regex pattern is valid")
});

pub static INSECURE_RANDOM_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(token|password|secret|key|salt|iv|nonce).*random\.(random|randint|choice)"#)
        .expect("INSECURE_RANDOM_2 regex pattern is valid")
});

pub static XXE_1: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)\.setFeature\s*\(\s*["'].*external.*["']\s*,\s*true"#)
        .expect("XXE_1 regex pattern is valid"));

pub static LDAP_INJECTION_1: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)ldap.*search.*filter.*\+.*["']"#)
        .expect("LDAP_INJECTION_1 regex pattern is valid"));

pub static DESERIALIZATION_1: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)pickle\.loads?\("#)
        .expect("DESERIALIZATION_1 regex pattern is valid"));

pub static DESERIALIZATION_2: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)new\s+ObjectInputStream"#)
        .expect("DESERIALIZATION_2 regex pattern is valid"));

pub static MISCONFIG_1: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)(debug|DEBUG)\s*[:=]\s*(true|True|TRUE|1)"#)
        .expect("MISCONFIG_1 regex pattern is valid"));

pub static MISCONFIG_2: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)(TLSv1\.0|TLSv1\.1|SSLv2|SSLv3)"#)
        .expect("MISCONFIG_2 regex pattern is valid"));

pub static MISCONFIG_3: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)Access-Control-Allow-Origin.*\*"#)
        .expect("MISCONFIG_3 regex pattern is valid"));

pub static BUFFER_OVERFLOW_1: Lazy<Regex> = Lazy::new(|| Regex::new(r#"\bstrcpy\s*\("#)
    .expect("BUFFER_OVERFLOW_1 regex pattern is valid"));

pub static BUFFER_OVERFLOW_2: Lazy<Regex> = Lazy::new(|| Regex::new(r#"\bgets\s*\("#)
    .expect("BUFFER_OVERFLOW_2 regex pattern is valid"));

pub static BUFFER_OVERFLOW_3: Lazy<Regex> = Lazy::new(|| Regex::new(r#"\bsprintf\s*\("#)
    .expect("BUFFER_OVERFLOW_3 regex pattern is valid"));

pub static RESPONSE_SPLITTING_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(setHeader|addHeader|header)\s*\([^,]+,\s*[^)]*\+[^)]*\)"#)
        .expect("RESPONSE_SPLITTING_1 regex pattern is valid")
});

pub static WEAK_CRYPTO_1: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)\b(MD5|md5)\s*\("#)
        .expect("WEAK_CRYPTO_1 regex pattern is valid"));

pub static WEAK_CRYPTO_2: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)\b(SHA1|sha1)\s*\("#)
        .expect("WEAK_CRYPTO_2 regex pattern is valid"));

pub static WEAK_CRYPTO_3: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)\b(DES|DESede)\s*\("#)
        .expect("WEAK_CRYPTO_3 regex pattern is valid"));

pub static NOSQL_INJECTION_1: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)\.find\s*\(\s*\{[^}]*\$where"#)
        .expect("NOSQL_INJECTION_1 regex pattern is valid"));

pub static NOSQL_INJECTION_2: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)db\.eval\s*\("#)
        .expect("NOSQL_INJECTION_2 regex pattern is valid"));

pub static TEMPLATE_INJECTION_1: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)\{\{.*\|safe"#)
        .expect("TEMPLATE_INJECTION_1 regex pattern is valid"));

pub static OPEN_REDIRECT_1: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)redirect.*request\.(GET|POST|params)\["#)
        .expect("OPEN_REDIRECT_1 regex pattern is valid"));

pub static COOKIE_SECURITY_1: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"(?i)set[_-]?cookie"#)
        .expect("COOKIE_SECURITY_1 regex pattern is valid"));
