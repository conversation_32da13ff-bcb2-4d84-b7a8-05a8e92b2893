use crate::models::*;
use crate::services::ai_pattern_detector::AIPatternDetector;
use crate::services::code_quality_assessor::CodeQualityAssessor;
use crate::services::embeddings_enhancement::EnhancedEmbeddingsService;
use crate::services::repository_insights::RepositoryInsightsService;
use crate::services::semantic_search::SemanticSearchService;
use anyhow::Result;
use std::sync::Arc;

pub struct AIServicesTest {
    pub embeddings_service: Arc<EnhancedEmbeddingsService>,
    pub ai_pattern_detector: Arc<AIPatternDetector>,
    pub code_quality_assessor: Arc<CodeQualityAssessor>,
    pub semantic_search_service: Arc<SemanticSearchService>,
    pub repository_insights_service: Arc<RepositoryInsightsService>,
}

impl AIServicesTest {
    pub async fn new() -> Result<Self> {
        let embeddings_service = Arc::new(EnhancedEmbeddingsService::new().await?);

        let ai_pattern_detector =
            Arc::new(AIPatternDetector::new(embeddings_service.clone()).await?);
        let code_quality_assessor =
            Arc::new(CodeQualityAssessor::new(embeddings_service.clone()).await?);
        let semantic_search_service =
            Arc::new(SemanticSearchService::new(embeddings_service.clone()).await?);
        let repository_insights_service =
            Arc::new(RepositoryInsightsService::new(embeddings_service.clone()).await?);

        Ok(Self {
            embeddings_service,
            ai_pattern_detector,
            code_quality_assessor,
            semantic_search_service,
            repository_insights_service,
        })
    }

    pub async fn test_ai_pattern_detection(&self) -> Result<()> {
        // Create a sample file analysis
        let sample_analysis = self.create_sample_file_analysis();

        // Test AI pattern detection
        let patterns = self
            .ai_pattern_detector
            .detect_ai_patterns(&[sample_analysis])
            .await?;

        println!("Detected {} AI patterns", patterns.len());
        for pattern in patterns {
            println!(
                "Pattern: {:?} - Confidence: {:.2}",
                pattern.pattern_type, pattern.confidence
            );
        }

        Ok(())
    }

    pub async fn test_code_quality_assessment(&self) -> Result<()> {
        // Create a sample file analysis and repository metrics
        let sample_analysis = self.create_sample_file_analysis();
        let sample_metrics = self.create_sample_repository_metrics();

        // Test code quality assessment
        let assessment = self
            .code_quality_assessor
            .assess_code_quality(&[sample_analysis], &sample_metrics)
            .await?;

        println!("Code Quality Assessment:");
        println!("  Overall Score: {:.2}", assessment.overall_score);
        println!("  Maintainability: {:.2}", assessment.maintainability_score);
        println!("  Security: {:.2}", assessment.security_score);
        println!("  Performance: {:.2}", assessment.performance_score);

        Ok(())
    }

    pub async fn test_semantic_search(&self) -> Result<()> {
        // Create sample embeddings and analyses
        let sample_analysis = self.create_sample_file_analysis();
        let sample_embeddings = vec![self.create_sample_embedding()];

        // Index the files
        self.semantic_search_service
            .index_files(&[sample_analysis], &sample_embeddings)
            .await?;

        // Test semantic search
        let query = crate::services::semantic_search::SemanticSearchQuery {
            query: "test function".to_string(),
            ..Default::default()
        };

        let results = self.semantic_search_service.search(query).await?;

        println!("Semantic Search Results:");
        println!("  Total Results: {}", results.total_results);
        println!("  Search Time: {}ms", results.search_time_ms);

        Ok(())
    }

    pub async fn test_repository_insights(&self) -> Result<()> {
        // Create sample data
        let sample_analysis = self.create_sample_file_analysis();
        let sample_metrics = self.create_sample_repository_metrics();
        let sample_patterns = vec![self.create_sample_pattern()];
        let sample_languages = std::collections::HashMap::from([
            (
                "rust".to_string(),
                LanguageStats {
                    lines: 1000,
                    files: 10,
                    percentage: 80.0,
                    bytes: Some(50000),
                },
            ),
            (
                "javascript".to_string(),
                LanguageStats {
                    lines: 250,
                    files: 5,
                    percentage: 20.0,
                    bytes: Some(12500),
                },
            ),
        ]);

        // Test repository insights
        let insights = self
            .repository_insights_service
            .generate_repository_insights(
                &[sample_analysis],
                &sample_metrics,
                &sample_patterns,
                None,
                &sample_languages,
            )
            .await?;

        println!("Repository Insights:");
        println!(
            "  Overall Health Score: {:.2}",
            insights.summary.overall_health_score
        );
        println!("  Primary Language: {}", insights.summary.primary_language);
        println!(
            "  Architecture Score: {:.2}",
            insights.architecture_analysis.architecture_score
        );
        println!(
            "  Security Score: {:.2}",
            insights.security_assessment.security_score
        );

        Ok(())
    }

    fn create_sample_file_analysis(&self) -> FileAnalysis {
        FileAnalysis {
            path: "src/main.rs".to_string(),
            language: "rust".to_string(),
            content_hash: "abc123".to_string(),
            size_bytes: Some(1000),
            ast: AstNode {
                node_type: "source_file".to_string(),
                name: None,
                range: Range {
                    start: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                    end: Position {
                        line: 20,
                        column: 0,
                        byte: 500,
                    },
                },
                children: vec![],
                properties: None,
                text: Some("fn main() {\n    println!(\"Hello, world!\");\n}".to_string()),
            },
            metrics: FileMetrics {
                lines_of_code: 20,
                total_lines: Some(25),
                complexity: 3,
                maintainability_index: 85.0,
                function_count: 2,
                class_count: 0,
                comment_ratio: 0.1,
            },
            chunks: None,
            symbols: Some(vec![Symbol {
                name: "main".to_string(),
                symbol_type: SymbolType::Function,
                range: Range {
                    start: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                    end: Position {
                        line: 2,
                        column: 1,
                        byte: 50,
                    },
                },
                visibility: Some(SymbolVisibility::Public),
                signature: Some("fn main()".to_string()),
                documentation: None,
            }]),
        }
    }

    fn create_sample_repository_metrics(&self) -> RepositoryMetrics {
        RepositoryMetrics {
            total_files: 10,
            total_lines: 1000,
            total_complexity: 30,
            average_complexity: Some(3.0),
            maintainability_score: Some(85.0),
            technical_debt_minutes: Some(120),
            test_coverage_estimate: Some(75.0),
        }
    }

    fn create_sample_pattern(&self) -> DetectedPattern {
        DetectedPattern {
            pattern_id: "pattern_1".to_string(),
            pattern_type: PatternType::DesignPattern,
            confidence: 0.9,
            location: PatternLocation {
                file_path: "src/main.rs".to_string(),
                range: Range {
                    start: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                    end: Position {
                        line: 5,
                        column: 0,
                        byte: 100,
                    },
                },
            },
            description: Some("Singleton pattern detected".to_string()),
        }
    }

    fn create_sample_embedding(&self) -> CodeEmbedding {
        CodeEmbedding {
            chunk_id: "chunk_1".to_string(),
            vector: vec![0.1; 768], // 768-dimensional embedding
            model: "text-embedding-005".to_string(),
            metadata: Some(EmbeddingMetadata {
                tokens_used: Some(50),
                created_at: Some(chrono::Utc::now()),
            }),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_ai_services_initialization() {
        let ai_services = AIServicesTest::new().await.expect("Failed to create AI services for test");

        // Just test that all services can be created
        assert!(
            ai_services
                .embeddings_service
                .get_feature_toggles()
                .enable_ai_pattern_detection
        );
        assert!(
            ai_services
                .embeddings_service
                .get_feature_toggles()
                .enable_code_quality_assessment
        );
        assert!(
            ai_services
                .embeddings_service
                .get_feature_toggles()
                .enable_semantic_search
        );
        assert!(
            ai_services
                .embeddings_service
                .get_feature_toggles()
                .enable_repository_insights
        );
    }
}
