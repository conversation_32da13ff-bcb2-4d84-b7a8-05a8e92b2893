use crate::models::FileMetrics;
use lazy_static::lazy_static;
use regex::Regex;
use std::collections::HashMap;

/// Language-specific metrics calculator
pub struct LanguageMetricsCalculator {
    language_patterns: HashMap<String, LanguagePatterns>,
}

/// Language-specific patterns for metrics calculation
#[derive(Debug, <PERSON>lone)]
pub struct LanguagePatterns {
    pub function_patterns: Vec<String>,
    pub class_patterns: Vec<String>,
    pub comment_patterns: Vec<String>,
    #[allow(dead_code)]
    pub import_patterns: Vec<String>,
    pub complexity_patterns: Vec<String>,
    #[allow(dead_code)]
    pub cyclomatic_complexity_keywords: Vec<String>,
    pub maintainability_factors: MaintainabilityFactors,
}

#[derive(Debug)]
struct CompiledPatterns {
    function_regexes: Vec<Regex>,
    class_regexes: Vec<Regex>,
    comment_regexes: Vec<Regex>,
    complexity_regexes: Vec<Regex>,
}

/// Factors that affect maintainability index calculation
#[derive(Debug, <PERSON><PERSON>)]
pub struct MaintainabilityFactors {
    pub max_line_length: usize,
    pub max_function_length: usize,
    #[allow(dead_code)]
    pub max_class_length: usize,
    pub max_cyclomatic_complexity: usize,
    pub comment_ratio_weight: f64,
    pub function_length_weight: f64,
    pub complexity_weight: f64,
}

impl Default for MaintainabilityFactors {
    fn default() -> Self {
        Self {
            max_line_length: 120,
            max_function_length: 50,
            max_class_length: 500,
            max_cyclomatic_complexity: 10,
            comment_ratio_weight: 0.3,
            function_length_weight: 0.4,
            complexity_weight: 0.3,
        }
    }
}

impl Default for LanguageMetricsCalculator {
    fn default() -> Self {
        Self::new()
    }
}

impl LanguageMetricsCalculator {
    pub fn new() -> Self {
        let mut calculator = Self {
            language_patterns: HashMap::new(),
        };
        calculator.initialize_patterns();
        calculator
    }

    fn initialize_patterns(&mut self) {
        // Rust patterns
        self.language_patterns.insert(
            "rust".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*fn\s+\w+".to_string(),
                    r"^\s*pub\s+fn\s+\w+".to_string(),
                    r"^\s*async\s+fn\s+\w+".to_string(),
                    r"^\s*pub\s+async\s+fn\s+\w+".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*struct\s+\w+".to_string(),
                    r"^\s*pub\s+struct\s+\w+".to_string(),
                    r"^\s*enum\s+\w+".to_string(),
                    r"^\s*pub\s+enum\s+\w+".to_string(),
                    r"^\s*trait\s+\w+".to_string(),
                    r"^\s*pub\s+trait\s+\w+".to_string(),
                ],
                comment_patterns: vec![r"^\s*//".to_string(), r"/\*.*?\*/".to_string()],
                import_patterns: vec![
                    r"^\s*use\s+".to_string(),
                    r"^\s*extern\s+crate\s+".to_string(),
                ],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\bmatch\b".to_string(),
                    r"\bloop\b".to_string(),
                    r"\?\?".to_string(),
                    r"\?".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "match".to_string(),
                    "loop".to_string(),
                    "catch".to_string(),
                    "&&".to_string(),
                    "||".to_string(),
                    "?".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // Python patterns
        self.language_patterns.insert(
            "python".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*def\s+\w+".to_string(),
                    r"^\s*async\s+def\s+\w+".to_string(),
                    // Lambda functions
                    r"\blambda\s+[^:]+:".to_string(),
                    // Decorated functions
                    r"^\s*@\w+\s*\n\s*def\s+\w+".to_string(),
                    r"^\s*@\w+\s*\n\s*async\s+def\s+\w+".to_string(),
                    // Class methods with self/cls
                    r"^\s+def\s+\w+\s*\(self".to_string(),
                    r"^\s+def\s+\w+\s*\(cls".to_string(),
                    r"^\s+async\s+def\s+\w+\s*\(self".to_string(),
                    r"^\s+async\s+def\s+\w+\s*\(cls".to_string(),
                    // Static methods
                    r"^\s+@staticmethod\s*\n\s+def\s+\w+".to_string(),
                    r"^\s+@classmethod\s*\n\s+def\s+\w+".to_string(),
                ],
                class_patterns: vec![r"^\s*class\s+\w+".to_string()],
                comment_patterns: vec![
                    r"^\s*#".to_string(),
                    r#"""".*?"""#.to_string(),
                    r"'''.*?'''".to_string(),
                ],
                import_patterns: vec![
                    r"^\s*import\s+".to_string(),
                    r"^\s*from\s+.+\s+import\s+".to_string(),
                ],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bexcept\b".to_string(),
                    r"\bfinally\b".to_string(),
                    r"\bwith\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "elif".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "try".to_string(),
                    "except".to_string(),
                    "and".to_string(),
                    "or".to_string(),
                    "with".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors {
                    max_line_length: 88, // PEP 8 modern standard
                    ..Default::default()
                },
            },
        );

        // JavaScript patterns
        self.language_patterns.insert(
            "javascript".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*function\s+\w+".to_string(),
                    r"^\s*async\s+function\s+\w+".to_string(),
                    r"^\s*const\s+\w+\s*=\s*\(".to_string(),
                    r"^\s*let\s+\w+\s*=\s*\(".to_string(),
                    r"^\s*var\s+\w+\s*=\s*\(".to_string(),
                    r"^\s*\w+\s*=\s*\(".to_string(),
                    r"^\s*\w+:\s*function".to_string(),
                    r"^\s*\w+:\s*async\s+function".to_string(),
                    // Arrow functions
                    r"^\s*const\s+\w+\s*=\s*\([^)]*\)\s*=>".to_string(),
                    r"^\s*let\s+\w+\s*=\s*\([^)]*\)\s*=>".to_string(),
                    r"^\s*var\s+\w+\s*=\s*\([^)]*\)\s*=>".to_string(),
                    r"^\s*const\s+\w+\s*=\s*\w+\s*=>".to_string(),
                    r"^\s*let\s+\w+\s*=\s*\w+\s*=>".to_string(),
                    r"^\s*var\s+\w+\s*=\s*\w+\s*=>".to_string(),
                    // Class methods (inside class body)
                    r"^\s+\w+\s*\([^)]*\)\s*\{".to_string(),
                    r"^\s+async\s+\w+\s*\([^)]*\)\s*\{".to_string(),
                    r"^\s+static\s+\w+\s*\([^)]*\)\s*\{".to_string(),
                    r"^\s+static\s+async\s+\w+\s*\([^)]*\)\s*\{".to_string(),
                    // Object method shorthand
                    r"^\s*\w+\s*\([^)]*\)\s*\{".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*class\s+\w+".to_string(),
                    r"^\s*export\s+class\s+\w+".to_string(),
                ],
                comment_patterns: vec![r"^\s*//".to_string(), r"/\*.*?\*/".to_string()],
                import_patterns: vec![
                    r"^\s*import\s+".to_string(),
                    r"^\s*const\s+.+\s*=\s*require\(".to_string(),
                    r"^\s*let\s+.+\s*=\s*require\(".to_string(),
                    r"^\s*var\s+.+\s*=\s*require\(".to_string(),
                ],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\bswitch\b".to_string(),
                    r"\bcase\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bcatch\b".to_string(),
                    r"\bfinally\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "switch".to_string(),
                    "case".to_string(),
                    "try".to_string(),
                    "catch".to_string(),
                    "&&".to_string(),
                    "||".to_string(),
                    "?".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // TypeScript patterns (similar to JavaScript)
        self.language_patterns.insert(
            "typescript".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*function\s+\w+".to_string(),
                    r"^\s*async\s+function\s+\w+".to_string(),
                    r"^\s*const\s+\w+\s*=\s*\(".to_string(),
                    r"^\s*let\s+\w+\s*=\s*\(".to_string(),
                    r"^\s*\w+\s*=\s*\(".to_string(),
                    r"^\s*\w+:\s*function".to_string(),
                    r"^\s*\w+:\s*async\s+function".to_string(),
                    r"^\s*public\s+\w+\s*\(".to_string(),
                    r"^\s*private\s+\w+\s*\(".to_string(),
                    r"^\s*protected\s+\w+\s*\(".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*class\s+\w+".to_string(),
                    r"^\s*export\s+class\s+\w+".to_string(),
                    r"^\s*interface\s+\w+".to_string(),
                    r"^\s*export\s+interface\s+\w+".to_string(),
                    r"^\s*type\s+\w+".to_string(),
                    r"^\s*enum\s+\w+".to_string(),
                ],
                comment_patterns: vec![r"^\s*//".to_string(), r"/\*.*?\*/".to_string()],
                import_patterns: vec![r"^\s*import\s+".to_string(), r"^\s*export\s+".to_string()],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\bswitch\b".to_string(),
                    r"\bcase\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bcatch\b".to_string(),
                    r"\bfinally\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "switch".to_string(),
                    "case".to_string(),
                    "try".to_string(),
                    "catch".to_string(),
                    "&&".to_string(),
                    "||".to_string(),
                    "?".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // Go patterns
        self.language_patterns.insert(
            "go".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*func\s+\w+".to_string(),
                    r"^\s*func\s+\([^)]*\)\s+\w+".to_string(), // method
                ],
                class_patterns: vec![
                    r"^\s*type\s+\w+\s+struct".to_string(),
                    r"^\s*type\s+\w+\s+interface".to_string(),
                ],
                comment_patterns: vec![r"^\s*//".to_string(), r"/\*.*?\*/".to_string()],
                import_patterns: vec![r"^\s*import\s+".to_string()],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bswitch\b".to_string(),
                    r"\bcase\b".to_string(),
                    r"\bselect\b".to_string(),
                    r"\bgo\b".to_string(),
                    r"\bdefer\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "switch".to_string(),
                    "case".to_string(),
                    "select".to_string(),
                    "&&".to_string(),
                    "||".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // Java patterns
        self.language_patterns.insert(
            "java".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*public\s+\w+.*\(".to_string(),
                    r"^\s*private\s+\w+.*\(".to_string(),
                    r"^\s*protected\s+\w+.*\(".to_string(),
                    r"^\s*static\s+\w+.*\(".to_string(),
                    r"^\s*\w+\s+\w+\s*\(".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*public\s+class\s+\w+".to_string(),
                    r"^\s*private\s+class\s+\w+".to_string(),
                    r"^\s*protected\s+class\s+\w+".to_string(),
                    r"^\s*class\s+\w+".to_string(),
                    r"^\s*interface\s+\w+".to_string(),
                    r"^\s*enum\s+\w+".to_string(),
                    r"^\s*abstract\s+class\s+\w+".to_string(),
                ],
                comment_patterns: vec![
                    r"^\s*//".to_string(),
                    r"/\*.*?\*/".to_string(),
                    r"/\*\*.*?\*/".to_string(),
                ],
                import_patterns: vec![r"^\s*import\s+".to_string(), r"^\s*package\s+".to_string()],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\bswitch\b".to_string(),
                    r"\bcase\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bcatch\b".to_string(),
                    r"\bfinally\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "switch".to_string(),
                    "case".to_string(),
                    "try".to_string(),
                    "catch".to_string(),
                    "&&".to_string(),
                    "||".to_string(),
                    "?".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // C++ patterns
        self.language_patterns.insert(
            "cpp".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*\w+\s+\w+\s*\(".to_string(),
                    r"^\s*template\s*<.*>\s*\w+\s+\w+\s*\(".to_string(),
                    r"^\s*inline\s+\w+\s+\w+\s*\(".to_string(),
                    r"^\s*static\s+\w+\s+\w+\s*\(".to_string(),
                    r"^\s*virtual\s+\w+\s+\w+\s*\(".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*class\s+\w+".to_string(),
                    r"^\s*struct\s+\w+".to_string(),
                    r"^\s*template\s*<.*>\s*class\s+\w+".to_string(),
                    r"^\s*template\s*<.*>\s*struct\s+\w+".to_string(),
                    r"^\s*union\s+\w+".to_string(),
                    r"^\s*enum\s+\w+".to_string(),
                    r"^\s*namespace\s+\w+".to_string(),
                ],
                comment_patterns: vec![r"^\s*//".to_string(), r"/\*.*?\*/".to_string()],
                import_patterns: vec![r"^\s*#include\s+".to_string(), r"^\s*using\s+".to_string()],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\bswitch\b".to_string(),
                    r"\bcase\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bcatch\b".to_string(),
                    r"\bthrow\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "switch".to_string(),
                    "case".to_string(),
                    "try".to_string(),
                    "catch".to_string(),
                    "&&".to_string(),
                    "||".to_string(),
                    "?".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // Add more language patterns as needed
        self.add_functional_language_patterns();
        self.add_mobile_language_patterns();
        self.add_data_science_language_patterns();
        self.add_additional_language_patterns();
    }

    fn add_functional_language_patterns(&mut self) {
        // Haskell patterns
        self.language_patterns.insert(
            "haskell".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*\w+\s*::".to_string(),
                    r"^\s*\w+\s+\w+.*=".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*data\s+\w+".to_string(),
                    r"^\s*newtype\s+\w+".to_string(),
                    r"^\s*type\s+\w+".to_string(),
                    r"^\s*class\s+\w+".to_string(),
                    r"^\s*instance\s+\w+".to_string(),
                ],
                comment_patterns: vec![r"^\s*--".to_string(), r"\{-.*?-\}".to_string()],
                import_patterns: vec![r"^\s*import\s+".to_string(), r"^\s*module\s+".to_string()],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\bthen\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bcase\b".to_string(),
                    r"\bof\b".to_string(),
                    r"\bwhere\b".to_string(),
                    r"\blet\b".to_string(),
                    r"\bin\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "case".to_string(),
                    "of".to_string(),
                    "where".to_string(),
                    "let".to_string(),
                    "in".to_string(),
                    "guard".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // Scala patterns
        self.language_patterns.insert(
            "scala".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*def\s+\w+".to_string(),
                    r"^\s*private\s+def\s+\w+".to_string(),
                    r"^\s*protected\s+def\s+\w+".to_string(),
                    r"^\s*override\s+def\s+\w+".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*class\s+\w+".to_string(),
                    r"^\s*object\s+\w+".to_string(),
                    r"^\s*trait\s+\w+".to_string(),
                    r"^\s*case\s+class\s+\w+".to_string(),
                    r"^\s*abstract\s+class\s+\w+".to_string(),
                ],
                comment_patterns: vec![r"^\s*//".to_string(), r"/\*.*?\*/".to_string()],
                import_patterns: vec![r"^\s*import\s+".to_string(), r"^\s*package\s+".to_string()],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\bmatch\b".to_string(),
                    r"\bcase\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bcatch\b".to_string(),
                    r"\bfinally\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "match".to_string(),
                    "case".to_string(),
                    "try".to_string(),
                    "catch".to_string(),
                    "&&".to_string(),
                    "||".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );
    }

    fn add_mobile_language_patterns(&mut self) {
        // Swift patterns
        self.language_patterns.insert(
            "swift".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*func\s+\w+".to_string(),
                    r"^\s*private\s+func\s+\w+".to_string(),
                    r"^\s*public\s+func\s+\w+".to_string(),
                    r"^\s*internal\s+func\s+\w+".to_string(),
                    r"^\s*fileprivate\s+func\s+\w+".to_string(),
                    r"^\s*open\s+func\s+\w+".to_string(),
                    r"^\s*static\s+func\s+\w+".to_string(),
                    r"^\s*class\s+func\s+\w+".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*class\s+\w+".to_string(),
                    r"^\s*struct\s+\w+".to_string(),
                    r"^\s*enum\s+\w+".to_string(),
                    r"^\s*protocol\s+\w+".to_string(),
                    r"^\s*extension\s+\w+".to_string(),
                    r"^\s*public\s+class\s+\w+".to_string(),
                    r"^\s*public\s+struct\s+\w+".to_string(),
                ],
                comment_patterns: vec![r"^\s*//".to_string(), r"/\*.*?\*/".to_string()],
                import_patterns: vec![r"^\s*import\s+".to_string()],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\bswitch\b".to_string(),
                    r"\bcase\b".to_string(),
                    r"\bguard\b".to_string(),
                    r"\bdo\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bcatch\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "switch".to_string(),
                    "case".to_string(),
                    "guard".to_string(),
                    "try".to_string(),
                    "catch".to_string(),
                    "&&".to_string(),
                    "||".to_string(),
                    "?".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // Kotlin patterns
        self.language_patterns.insert(
            "kotlin".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*fun\s+\w+".to_string(),
                    r"^\s*private\s+fun\s+\w+".to_string(),
                    r"^\s*public\s+fun\s+\w+".to_string(),
                    r"^\s*internal\s+fun\s+\w+".to_string(),
                    r"^\s*protected\s+fun\s+\w+".to_string(),
                    r"^\s*override\s+fun\s+\w+".to_string(),
                    r"^\s*suspend\s+fun\s+\w+".to_string(),
                    r"^\s*inline\s+fun\s+\w+".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*class\s+\w+".to_string(),
                    r"^\s*data\s+class\s+\w+".to_string(),
                    r"^\s*sealed\s+class\s+\w+".to_string(),
                    r"^\s*abstract\s+class\s+\w+".to_string(),
                    r"^\s*interface\s+\w+".to_string(),
                    r"^\s*object\s+\w+".to_string(),
                    r"^\s*enum\s+class\s+\w+".to_string(),
                ],
                comment_patterns: vec![r"^\s*//".to_string(), r"/\*.*?\*/".to_string()],
                import_patterns: vec![r"^\s*import\s+".to_string(), r"^\s*package\s+".to_string()],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\bwhen\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bcatch\b".to_string(),
                    r"\bfinally\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "when".to_string(),
                    "try".to_string(),
                    "catch".to_string(),
                    "&&".to_string(),
                    "||".to_string(),
                    "?".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );
    }

    fn add_data_science_language_patterns(&mut self) {
        // R patterns
        self.language_patterns.insert(
            "r".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*\w+\s*<-\s*function".to_string(),
                    r"^\s*\w+\s*=\s*function".to_string(),
                    r"^\s*function\s*\(".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*setClass\s*\(".to_string(),
                    r"^\s*setMethod\s*\(".to_string(),
                    r"^\s*setGeneric\s*\(".to_string(),
                ],
                comment_patterns: vec![r"^\s*#".to_string()],
                import_patterns: vec![
                    r"^\s*library\s*\(".to_string(),
                    r"^\s*require\s*\(".to_string(),
                    r"^\s*source\s*\(".to_string(),
                ],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\brepeat\b".to_string(),
                    r"\bswitch\b".to_string(),
                    r"\btryCatch\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "repeat".to_string(),
                    "switch".to_string(),
                    "tryCatch".to_string(),
                    "&".to_string(),
                    "|".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // Julia patterns
        self.language_patterns.insert(
            "julia".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*function\s+\w+".to_string(),
                    r"^\s*\w+\s*\(.*\)\s*=".to_string(),
                    r"^\s*macro\s+\w+".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*struct\s+\w+".to_string(),
                    r"^\s*mutable\s+struct\s+\w+".to_string(),
                    r"^\s*abstract\s+type\s+\w+".to_string(),
                    r"^\s*primitive\s+type\s+\w+".to_string(),
                    r"^\s*module\s+\w+".to_string(),
                ],
                comment_patterns: vec![r"^\s*#".to_string(), r"#=.*?=#".to_string()],
                import_patterns: vec![
                    r"^\s*using\s+".to_string(),
                    r"^\s*import\s+".to_string(),
                    r"^\s*include\s*\(".to_string(),
                ],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\belseif\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bcatch\b".to_string(),
                    r"\bfinally\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "elseif".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "try".to_string(),
                    "catch".to_string(),
                    "&&".to_string(),
                    "||".to_string(),
                    "?".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );
    }

    fn add_additional_language_patterns(&mut self) {
        // Lua patterns
        self.language_patterns.insert(
            "lua".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*function\s+\w+".to_string(),
                    r"^\s*local\s+function\s+\w+".to_string(),
                    r"^\s*\w+\s*=\s*function".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*\w+\s*=\s*\{\}".to_string(),
                    r"^\s*local\s+\w+\s*=\s*\{\}".to_string(),
                ],
                comment_patterns: vec![r"^\s*--".to_string(), r"--\[\[.*?\]\]".to_string()],
                import_patterns: vec![
                    r"^\s*require\s*\(".to_string(),
                    r"^\s*local\s+\w+\s*=\s*require".to_string(),
                ],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\bthen\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\belseif\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\brepeat\b".to_string(),
                    r"\buntil\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "then".to_string(),
                    "else".to_string(),
                    "elseif".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "repeat".to_string(),
                    "until".to_string(),
                    "and".to_string(),
                    "or".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // Dart patterns
        self.language_patterns.insert(
            "dart".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*\w+\s+\w+\s*\(".to_string(),
                    r"^\s*void\s+\w+\s*\(".to_string(),
                    r"^\s*Future\s*<.*>\s*\w+\s*\(".to_string(),
                    r"^\s*Stream\s*<.*>\s*\w+\s*\(".to_string(),
                    r"^\s*static\s+\w+\s+\w+\s*\(".to_string(),
                    r"^\s*@override\s+\w+\s+\w+\s*\(".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*class\s+\w+".to_string(),
                    r"^\s*abstract\s+class\s+\w+".to_string(),
                    r"^\s*mixin\s+\w+".to_string(),
                    r"^\s*enum\s+\w+".to_string(),
                    r"^\s*extension\s+\w+".to_string(),
                ],
                comment_patterns: vec![
                    r"^\s*//".to_string(),
                    r"/\*.*?\*/".to_string(),
                    r"///".to_string(),
                ],
                import_patterns: vec![
                    r"^\s*import\s+".to_string(),
                    r"^\s*export\s+".to_string(),
                    r"^\s*part\s+".to_string(),
                    r"^\s*library\s+".to_string(),
                ],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\bswitch\b".to_string(),
                    r"\bcase\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bcatch\b".to_string(),
                    r"\bfinally\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "switch".to_string(),
                    "case".to_string(),
                    "try".to_string(),
                    "catch".to_string(),
                    "&&".to_string(),
                    "||".to_string(),
                    "?".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // OCaml patterns
        self.language_patterns.insert(
            "ocaml".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*let\s+\w+.*=".to_string(),
                    r"^\s*let\s+rec\s+\w+.*=".to_string(),
                    r"^\s*val\s+\w+\s*:".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*type\s+\w+".to_string(),
                    r"^\s*class\s+\w+".to_string(),
                    r"^\s*module\s+\w+".to_string(),
                    r"^\s*sig\s+".to_string(),
                    r"^\s*struct\s+".to_string(),
                ],
                comment_patterns: vec![r"^\s*\(\*".to_string(), r"\(\*.*?\*\)".to_string()],
                import_patterns: vec![
                    r"^\s*open\s+".to_string(),
                    r"^\s*include\s+".to_string(),
                    r"^\s*#require\s+".to_string(),
                ],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\bthen\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bmatch\b".to_string(),
                    r"\bwith\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bwhen\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "then".to_string(),
                    "else".to_string(),
                    "match".to_string(),
                    "with".to_string(),
                    "try".to_string(),
                    "when".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // Nix patterns
        self.language_patterns.insert(
            "nix".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*\w+\s*=\s*\{".to_string(),
                    r"^\s*\w+\s*=\s*\w+:".to_string(),
                    r"^\s*let\s+\w+\s*=".to_string(),
                ],
                class_patterns: vec![r"^\s*\{\s*".to_string(), r"^\s*with\s+\w+;".to_string()],
                comment_patterns: vec![r"^\s*#".to_string(), r"/\*.*?\*/".to_string()],
                import_patterns: vec![r"^\s*import\s+".to_string(), r"^\s*with\s+".to_string()],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\bthen\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\blet\b".to_string(),
                    r"\bin\b".to_string(),
                    r"\bwith\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "then".to_string(),
                    "else".to_string(),
                    "let".to_string(),
                    "in".to_string(),
                    "with".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // Zig patterns
        self.language_patterns.insert(
            "zig".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*fn\s+\w+".to_string(),
                    r"^\s*pub\s+fn\s+\w+".to_string(),
                    r"^\s*export\s+fn\s+\w+".to_string(),
                    r"^\s*extern\s+fn\s+\w+".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*const\s+\w+\s*=\s*struct".to_string(),
                    r"^\s*const\s+\w+\s*=\s*enum".to_string(),
                    r"^\s*const\s+\w+\s*=\s*union".to_string(),
                    r"^\s*pub\s+const\s+\w+\s*=\s*struct".to_string(),
                ],
                comment_patterns: vec![r"^\s*//".to_string(), r"^\s*///".to_string()],
                import_patterns: vec![
                    r"^\s*const\s+\w+\s*=\s*@import".to_string(),
                    r"^\s*usingnamespace\s+".to_string(),
                ],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\bswitch\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bcatch\b".to_string(),
                    r"\bdefer\b".to_string(),
                    r"\berrdefer\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "switch".to_string(),
                    "try".to_string(),
                    "catch".to_string(),
                    "defer".to_string(),
                    "and".to_string(),
                    "or".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );

        // D patterns
        self.language_patterns.insert(
            "d".to_string(),
            LanguagePatterns {
                function_patterns: vec![
                    r"^\s*\w+\s+\w+\s*\(".to_string(),
                    r"^\s*auto\s+\w+\s*\(".to_string(),
                    r"^\s*void\s+\w+\s*\(".to_string(),
                    r"^\s*public\s+\w+\s+\w+\s*\(".to_string(),
                    r"^\s*private\s+\w+\s+\w+\s*\(".to_string(),
                    r"^\s*protected\s+\w+\s+\w+\s*\(".to_string(),
                ],
                class_patterns: vec![
                    r"^\s*class\s+\w+".to_string(),
                    r"^\s*struct\s+\w+".to_string(),
                    r"^\s*interface\s+\w+".to_string(),
                    r"^\s*enum\s+\w+".to_string(),
                    r"^\s*union\s+\w+".to_string(),
                    r"^\s*template\s+\w+".to_string(),
                ],
                comment_patterns: vec![
                    r"^\s*//".to_string(),
                    r"/\*.*?\*/".to_string(),
                    r"/\+.*?\+/".to_string(),
                ],
                import_patterns: vec![r"^\s*import\s+".to_string(), r"^\s*module\s+".to_string()],
                complexity_patterns: vec![
                    r"\bif\b".to_string(),
                    r"\belse\b".to_string(),
                    r"\bfor\b".to_string(),
                    r"\bwhile\b".to_string(),
                    r"\bswitch\b".to_string(),
                    r"\bcase\b".to_string(),
                    r"\btry\b".to_string(),
                    r"\bcatch\b".to_string(),
                    r"\bfinally\b".to_string(),
                    r"\bforeach\b".to_string(),
                ],
                cyclomatic_complexity_keywords: vec![
                    "if".to_string(),
                    "else".to_string(),
                    "for".to_string(),
                    "while".to_string(),
                    "switch".to_string(),
                    "case".to_string(),
                    "try".to_string(),
                    "catch".to_string(),
                    "foreach".to_string(),
                    "&&".to_string(),
                    "||".to_string(),
                    "?".to_string(),
                ],
                maintainability_factors: MaintainabilityFactors::default(),
            },
        );
    }

    /// Calculate language-specific metrics for a given code content
    pub fn calculate_metrics(&self, language: &str, content: &str) -> FileMetrics {
        let patterns = match self.language_patterns.get(language) {
            Some(p) => p,
            None => {
                // Fall back to generic metrics
                return self.calculate_generic_metrics(content);
            }
        };
        let lines: Vec<&str> = content.lines().collect();
        let total_lines = lines.len() as u32;

        let mut function_count = 0;
        let mut class_count = 0;
        let mut comment_lines = 0;
        let mut code_lines = 0;
        let mut _blank_lines = 0;
        let mut complexity = 0;
        let mut long_lines = 0;
        let mut max_line_length = 0;

        lazy_static! {
            static ref COMPILED_PATTERNS: HashMap<String, CompiledPatterns> = {
                let mut calculator = LanguageMetricsCalculator::new();
                let mut compiled_patterns = HashMap::new();
                for (lang, patterns) in calculator.language_patterns.drain() {
                    let compiled = CompiledPatterns {
                        function_regexes: patterns
                            .function_patterns
                            .iter()
                            .filter_map(|p| Regex::new(p).ok())
                            .collect(),
                        class_regexes: patterns
                            .class_patterns
                            .iter()
                            .filter_map(|p| Regex::new(p).ok())
                            .collect(),
                        comment_regexes: patterns
                            .comment_patterns
                            .iter()
                            .filter_map(|p| Regex::new(p).ok())
                            .collect(),
                        complexity_regexes: patterns
                            .complexity_patterns
                            .iter()
                            .filter_map(|p| Regex::new(p).ok())
                            .collect(),
                    };
                    compiled_patterns.insert(lang, compiled);
                }
                compiled_patterns
            };
        }

        let compiled = match COMPILED_PATTERNS.get(language) {
            Some(c) => c,
            None => return self.calculate_generic_metrics(content),
        };

        for line in &lines {
            let line_length = line.len();
            max_line_length = max_line_length.max(line_length);

            if line_length > patterns.maintainability_factors.max_line_length {
                long_lines += 1;
            }

            if line.trim().is_empty() {
                _blank_lines += 1;
                continue;
            }

            let mut is_comment = false;
            for regex in &compiled.comment_regexes {
                if regex.is_match(line) {
                    comment_lines += 1;
                    is_comment = true;
                    break;
                }
            }

            if !is_comment {
                code_lines += 1;

                // Check for functions
                for regex in &compiled.function_regexes {
                    if regex.is_match(line) {
                        function_count += 1;
                        break;
                    }
                }

                // Check for classes
                for regex in &compiled.class_regexes {
                    if regex.is_match(line) {
                        class_count += 1;
                        break;
                    }
                }

                // Calculate complexity
                for regex in &compiled.complexity_regexes {
                    complexity += regex.find_iter(line).count() as u32;
                }
            }
        }

        let comment_ratio = if total_lines > 0 {
            comment_lines as f64 / total_lines as f64
        } else {
            0.0
        };

        // Calculate maintainability index
        let maintainability_index = self.calculate_maintainability_index(
            patterns,
            code_lines,
            comment_ratio,
            complexity,
            function_count,
            long_lines,
        );

        FileMetrics {
            lines_of_code: code_lines,
            total_lines: Some(total_lines),
            complexity,
            maintainability_index,
            function_count,
            class_count,
            comment_ratio,
        }
    }

    fn calculate_generic_metrics(&self, content: &str) -> FileMetrics {
        let lines: Vec<&str> = content.lines().collect();
        let total_lines = lines.len() as u32;

        let mut code_lines = 0;
        let mut comment_lines = 0;

        for line in &lines {
            if line.trim().is_empty() {
                continue;
            }

            let trimmed = line.trim();
            if trimmed.starts_with("//") || trimmed.starts_with("#") || trimmed.starts_with("/*") {
                comment_lines += 1;
            } else {
                code_lines += 1;
            }
        }

        let comment_ratio = if total_lines > 0 {
            comment_lines as f64 / total_lines as f64
        } else {
            0.0
        };

        FileMetrics {
            lines_of_code: code_lines,
            total_lines: Some(total_lines),
            complexity: 1,               // Basic complexity
            maintainability_index: 85.0, // Default maintainability
            function_count: 0,
            class_count: 0,
            comment_ratio,
        }
    }

    fn calculate_maintainability_index(
        &self,
        patterns: &LanguagePatterns,
        code_lines: u32,
        comment_ratio: f64,
        complexity: u32,
        function_count: u32,
        long_lines: u32,
    ) -> f64 {
        let factors = &patterns.maintainability_factors;

        // Base maintainability score
        let mut score = 100.0;

        // Penalize high complexity
        if complexity > factors.max_cyclomatic_complexity as u32 {
            score -= (complexity as f64 - factors.max_cyclomatic_complexity as f64)
                * factors.complexity_weight
                * 5.0;
        }

        // Reward good comment ratio
        if comment_ratio >= 0.2 {
            score += comment_ratio * factors.comment_ratio_weight * 20.0;
        } else {
            score -= (0.2 - comment_ratio) * factors.comment_ratio_weight * 30.0;
        }

        // Penalize long functions (estimated)
        if function_count > 0 {
            let avg_function_length = code_lines as f64 / function_count as f64;
            if avg_function_length > factors.max_function_length as f64 {
                score -= (avg_function_length - factors.max_function_length as f64)
                    * factors.function_length_weight
                    * 2.0;
            }
        }

        // Penalize long lines
        if long_lines > 0 {
            let long_line_ratio = long_lines as f64 / code_lines as f64;
            score -= long_line_ratio * 10.0;
        }

        // Ensure score is within reasonable bounds
        score.clamp(0.0, 100.0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_rust_metrics() {
        let calculator = LanguageMetricsCalculator::new();
        let rust_code = r#"
        // This is a comment
        fn main() {
            println!("Hello, world!");
            if true {
                let x = 5;
            }
        }

        pub struct MyStruct {
            field: i32,
        }

        impl MyStruct {
            pub fn new() -> Self {
                Self { field: 0 }
            }
        }
        "#;

        let metrics = calculator.calculate_metrics("rust", rust_code);
        println!("Rust Metrics: function_count={}, class_count={}, complexity={}, comment_ratio={}", 
                 metrics.function_count, metrics.class_count, metrics.complexity, metrics.comment_ratio);
        assert!(metrics.function_count >= 2);
        assert!(metrics.class_count >= 1);
        assert!(metrics.comment_ratio > 0.0);
        assert!(metrics.complexity >= 1);
    }

    #[test]
    fn test_python_metrics() {
        let calculator = LanguageMetricsCalculator::new();
        let python_code = r#"
        # This is a comment
        def hello_world():
            """This is a docstring"""
            print("Hello, world!")
            if True:
                x = 5

        class MyClass:
            def __init__(self):
                self.value = 0

            def get_value(self):
                return self.value
        "#;

        let metrics = calculator.calculate_metrics("python", python_code);
        println!("Python Metrics: function_count={}, class_count={}, complexity={}, comment_ratio={}", 
                 metrics.function_count, metrics.class_count, metrics.complexity, metrics.comment_ratio);
        assert!(metrics.function_count >= 3);
        assert!(metrics.class_count >= 1);
        assert!(metrics.comment_ratio > 0.0);
        assert!(metrics.complexity >= 1);
    }

    #[test]
    fn test_javascript_metrics() {
        let calculator = LanguageMetricsCalculator::new();
        let js_code = r#"
        // This is a comment
        function helloWorld() {
            console.log("Hello, world!");
            if (true) {
                let x = 5;
            }
        }

        class MyClass {
            constructor() {
                this.value = 0;
            }

            getValue() {
                return this.value;
            }
        }

        const arrowFunction = () => {
            return 42;
        };
        "#;

        let metrics = calculator.calculate_metrics("javascript", js_code);
        println!("JavaScript Metrics: function_count={}, class_count={}, complexity={}, comment_ratio={}", 
                 metrics.function_count, metrics.class_count, metrics.complexity, metrics.comment_ratio);
        assert!(metrics.function_count >= 3);
        assert!(metrics.class_count >= 1);
        assert!(metrics.comment_ratio > 0.0);
        assert!(metrics.complexity >= 1);
    }

    #[test]
    fn test_unsupported_language() {
        let calculator = LanguageMetricsCalculator::new();
        let code = r#"
        // Some code
        function test() {
            return 42;
        }
        "#;

        let metrics = calculator.calculate_metrics("unknown", code);
        assert_eq!(metrics.function_count, 0);
        assert_eq!(metrics.class_count, 0);
        assert_eq!(metrics.complexity, 1);
        assert_eq!(metrics.maintainability_index, 85.0);
    }
}
