use crate::models::FileAnalysis;
use crate::parser::{TreeSitterParser, ParseError};
use anyhow::Result;
use rayon::prelude::*;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::runtime::Handle;

/// Parallel file processor using rayon
pub struct ParallelProcessor {
    parser: Arc<TreeSitterParser>,
    runtime_handle: Handle,
}

impl ParallelProcessor {
    pub fn new(parser: Arc<TreeSitterParser>) -> Self {
        Self {
            parser,
            runtime_handle: Handle::current(),
        }
    }

    /// Process multiple files in parallel
    pub fn process_files_parallel(
        &self,
        file_paths: &[PathBuf],
        batch_size: usize,
    ) -> Vec<Result<FileAnalysis, ParseError>> {
        use rayon::prelude::*;
        
        // Configure thread pool to prevent stack overflow
        match rayon::ThreadPoolBuilder::new()
            .num_threads(batch_size.min(num_cpus::get()))
            .stack_size(8 * 1024 * 1024) // 8MB stack per thread
            .build()
        {
            Ok(thread_pool) => {
                // Process files in the configured thread pool
                thread_pool.install(|| {
                    file_paths
                        .par_iter()
                        .map(|file_path| self.process_single_file(file_path))
                        .collect()
                })
            }
            Err(_) => {
                // Fallback to default thread pool
                file_paths
                    .par_iter()
                    .map(|file_path| self.process_single_file(file_path))
                    .collect()
            }
        }
    }

    /// Process a single file (bridge between sync rayon and async parser)
    fn process_single_file(&self, file_path: &Path) -> Result<FileAnalysis, ParseError> {
        // Use the tokio runtime handle to run async code in sync context
        self.runtime_handle.block_on(async {
            // Read file content
            let content = tokio::fs::read_to_string(file_path)
                .await
                .map_err(|e| ParseError {
                    file_path: file_path.to_string_lossy().to_string(),
                    error_type: crate::models::ParseErrorType::Other,
                    message: format!("Failed to read file: {e}"),
                    position: None,
                })?;

            // Parse the content
            self.parser.parse_content(file_path, &content).await
        })
    }

    /// Process files with progress callback
    pub fn process_files_with_progress<F>(
        &self,
        file_paths: &[PathBuf],
        batch_size: usize,
        progress_callback: F,
    ) -> Vec<Result<FileAnalysis, ParseError>>
    where
        F: FnMut(usize, usize) + Send + Sync,
    {
        use std::sync::atomic::{AtomicUsize, Ordering};
        use std::sync::Mutex;

        let total_files = file_paths.len();
        let processed = AtomicUsize::new(0);
        let callback = Arc::new(Mutex::new(progress_callback));

        file_paths
            .par_chunks(batch_size)
            .flat_map(|batch| {
                let results: Vec<_> = batch
                    .par_iter()
                    .map(|file_path| {
                        let result = self.process_single_file(file_path);
                        
                        // Update progress
                        let count = processed.fetch_add(1, Ordering::SeqCst) + 1;
                        if let Ok(mut cb) = callback.lock() {
                            cb(count, total_files);
                        }
                        
                        result
                    })
                    .collect();
                
                results
            })
            .collect()
    }
}

/// Parallel batch processor for optimal performance
pub struct BatchProcessor {
    parser: Arc<TreeSitterParser>,
    num_threads: usize,
}

impl BatchProcessor {
    pub fn new(parser: TreeSitterParser, num_threads: Option<usize>) -> Self {
        let num_threads = num_threads.unwrap_or_else(num_cpus::get);
        
        // Configure rayon thread pool
        rayon::ThreadPoolBuilder::new()
            .num_threads(num_threads)
            .build_global()
            .ok();

        Self {
            parser: Arc::new(parser),
            num_threads,
        }
    }

    /// Process files in optimized batches
    pub async fn process_batch_async(
        &self,
        file_paths: Vec<PathBuf>,
    ) -> Vec<Result<FileAnalysis, ParseError>> {
        // Optimal batch size based on thread count
        let batch_size = (file_paths.len() / self.num_threads).max(10);
        
        // Use tokio to spawn the parallel processing
        let parser = self.parser.clone();
        let runtime_handle = Handle::current();
        tokio::task::spawn_blocking(move || {
            let processor = ParallelProcessor {
                parser: parser.clone(),
                runtime_handle,
            };
            processor.process_files_parallel(&file_paths, batch_size)
        })
        .await
        .unwrap_or_else(|e| {
            vec![Err(ParseError {
                file_path: "batch_processing".to_string(),
                error_type: crate::models::ParseErrorType::Other,
                message: format!("Batch processing failed: {e}"),
                position: None,
            })]
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::ServiceConfig;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_parallel_processing() {
        let config = Arc::new(ServiceConfig::from_env().expect("Failed to load test config"));
        let parser = Arc::new(TreeSitterParser::new(config).expect("Failed to create parser"));
        let processor = ParallelProcessor::new(parser);

        // Create test file paths
        let test_files: Vec<PathBuf> = vec![
            "src/main.rs".into(),
            "src/lib.rs".into(),
            "Cargo.toml".into(),
        ];

        let results = processor.process_files_parallel(&test_files, 2);
        assert_eq!(results.len(), test_files.len());
    }
}