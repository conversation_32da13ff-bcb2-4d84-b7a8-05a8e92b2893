//! SAFETY MODULE: All unsafe tree-sitter FFI calls are isolated here
//!
//! This module is the ONLY place in the codebase allowed to contain unsafe
//! calls to tree-sitter language functions. All unsafe operations must be
//! thoroughly documented with safety invariants.
//!
//! ## Safety Invariants
//!
//! 1. **Function Validity**: All tree-sitter language functions are guaranteed
//!    to be valid by the build system that compiles and links them.
//! 2. **Memory Safety**: Tree-sitter language functions return valid Language
//!    structs or panic (following C FFI contract). No manual memory management required.
//! 3. **Thread Safety**: Language structs are immutable once created and safe
//!    to share across threads.
//! 4. **Initialization**: All functions are properly initialized during static
//!    library linking phase.

use once_cell::sync::Lazy;
use std::collections::HashMap;
use tree_sitter::Language;

/// Error type for language loading failures
#[derive(Debug, thiserror::Error)]
pub enum LanguageLoadError {
    #[error("Language '{0}' is not supported")]
    Unsupported(String),
    #[error("FFI call failed for language '{0}': {1}")]
    FfiFailure(String, String),
}

/// Static registry of all supported languages for fast lookup
static SUPPORTED_LANGUAGE_SET: Lazy<HashMap<&'static str, &'static str>> = Lazy::new(|| {
    let mut map = HashMap::new();
    // Pattern A languages (older crates)
    // map.insert("yaml", "tree_sitter_yaml");  // Temporarily disabled - version conflicts
    // map.insert("kotlin", "tree_sitter_kotlin");  // Temporarily disabled - version conflicts
    // map.insert("erlang", "tree_sitter_erlang");  // Temporarily disabled - version conflicts
    // map.insert("d", "tree_sitter_d");  // Temporarily disabled due to version conflicts
    // map.insert("lua", "tree_sitter_lua");  // Temporarily disabled - version conflicts
    map.insert("html", "tree_sitter_html");
    map.insert("css", "tree_sitter_css");
    map.insert("json", "tree_sitter_json");
    
    // Pattern B languages (newer crates)
    map.insert("rust", "tree_sitter_rust");
    map.insert("python", "tree_sitter_python");
    map.insert("javascript", "tree_sitter_javascript");
    map.insert("typescript", "tree_sitter_typescript");
    map.insert("tsx", "tree_sitter_typescript");
    map.insert("go", "tree_sitter_go");
    map.insert("java", "tree_sitter_java");
    map.insert("c", "tree_sitter_c");
    map.insert("cpp", "tree_sitter_cpp");
    map.insert("ruby", "tree_sitter_ruby");
    map.insert("bash", "tree_sitter_bash");
    // map.insert("objc", "tree_sitter_objc");  // Temporarily disabled - version conflicts
    // map.insert("r", "tree_sitter_r");  // Temporarily disabled - version conflicts
    map.insert("julia", "tree_sitter_julia");
    map.insert("scala", "tree_sitter_scala");
    // map.insert("zig", "tree_sitter_zig");  // Temporarily disabled - version conflicts
    map.insert("php", "tree_sitter_php");
    map.insert("ocaml", "tree_sitter_ocaml");
    map.insert("ocaml_interface", "tree_sitter_ocaml");
    // map.insert("haskell", "tree_sitter_haskell");  // Temporarily disabled - version conflicts
    // map.insert("xml", "tree_sitter_xml");  // Temporarily disabled - version conflicts
    map.insert("md", "tree_sitter_markdown");
    map.insert("markdown", "tree_sitter_markdown");
    // map.insert("swift", "tree_sitter_swift");  // Temporarily disabled - version conflicts
    // map.insert("elixir", "tree_sitter_elixir");  // Temporarily disabled - version conflicts
    // map.insert("nix", "tree_sitter_nix");  // Temporarily disabled - version conflicts
    map
});

// External C function declarations for tree-sitter language parsers
//
// SAFETY: These functions are provided by the tree-sitter static library
// compiled during the build process. Each function returns a valid Language
// struct that represents the grammar for the respective programming language.
//
// Note: All tree-sitter crates currently use Pattern A (direct functions),
// despite documentation suggesting newer versions use LANGUAGE constants.
extern "C" {
    // Core languages compatible with tree-sitter 0.24
    fn tree_sitter_html() -> Language;
    fn tree_sitter_css() -> Language;
    fn tree_sitter_json() -> Language;
    fn tree_sitter_rust() -> Language;
    fn tree_sitter_python() -> Language;
    fn tree_sitter_javascript() -> Language;
    fn tree_sitter_typescript() -> Language;
    fn tree_sitter_go() -> Language;
    fn tree_sitter_java() -> Language;
    fn tree_sitter_c() -> Language;
    fn tree_sitter_cpp() -> Language;
    fn tree_sitter_ruby() -> Language;
    fn tree_sitter_bash() -> Language;
    fn tree_sitter_julia() -> Language;
    fn tree_sitter_scala() -> Language;
    fn tree_sitter_php() -> Language;
    fn tree_sitter_ocaml() -> Language;
    fn tree_sitter_markdown() -> Language;
}

/// SAFETY: This function contains unsafe FFI calls to tree-sitter language functions.
///
/// ## Safety Invariants:
/// - All tree-sitter language functions are guaranteed to be valid by the build system
/// - Language functions return valid Language structs or panic (C FFI contract)
/// - No memory management required - tree-sitter handles internal allocations
/// - Functions are statically linked and initialized during program startup
///
/// ## Error Handling:
/// - Returns `LanguageLoadError::Unsupported` for unknown language names
/// - Panics are allowed to propagate as they indicate serious system issues
///
/// ## Performance:
/// - Uses static HashMap for O(1) language name lookup
/// - FFI calls are minimal overhead once validated
pub fn load_language_unsafe(name: &str) -> Result<Language, LanguageLoadError> {
    // Fast path: check if language is supported before attempting FFI call
    if !SUPPORTED_LANGUAGE_SET.contains_key(name) {
        return Err(LanguageLoadError::Unsupported(name.to_string()));
    }

    // SAFETY: All match arms call functions that are:
    // 1. Declared in extern "C" block above
    // 2. Linked by build.rs during compilation
    // 3. Guaranteed to return valid Language structs
    // 4. Part of the tree-sitter C library contract
    let language = unsafe {
        match name {
            "html" => tree_sitter_html(),
            "css" => tree_sitter_css(),
            "json" => tree_sitter_json(),
            "rust" => tree_sitter_rust(),
            "python" => tree_sitter_python(),
            "javascript" => tree_sitter_javascript(),
            "typescript" => tree_sitter_typescript(),
            "tsx" => tree_sitter_typescript(), // TypeScript parser handles TSX
            "go" => tree_sitter_go(),
            "java" => tree_sitter_java(),
            "c" => tree_sitter_c(),
            "cpp" => tree_sitter_cpp(),
            "ruby" => tree_sitter_ruby(),
            "bash" => tree_sitter_bash(),
            "julia" => tree_sitter_julia(),
            "scala" => tree_sitter_scala(),
            "php" => tree_sitter_php(),
            "ocaml" => tree_sitter_ocaml(),
            "ocaml_interface" => tree_sitter_ocaml(), // OCaml parser handles interface files
            "md" | "markdown" => tree_sitter_markdown(),
            
            _ => {
                // This should never happen due to the check above, but provides defense in depth
                return Err(LanguageLoadError::Unsupported(name.to_string()));
            }
        }
    };

    Ok(language)
}

/// Check if a language is supported without loading it
pub fn is_language_supported(name: &str) -> bool {
    SUPPORTED_LANGUAGE_SET.contains_key(name)
}

/// Get all supported language names
pub fn supported_languages() -> Vec<&'static str> {
    SUPPORTED_LANGUAGE_SET.keys().copied().collect()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_supported_language_check() {
        assert!(is_language_supported("rust"));
        assert!(is_language_supported("python"));
        assert!(is_language_supported("javascript"));
        assert!(!is_language_supported("invalid_language"));
    }

    #[test]
    fn test_load_supported_language() {
        let result = load_language_unsafe("rust");
        assert!(result.is_ok());

        let result = load_language_unsafe("python");
        assert!(result.is_ok());
    }

    #[test]
    fn test_load_unsupported_language() {
        let result = load_language_unsafe("invalid_language");
        assert!(result.is_err());

        match result.unwrap_err() {
            LanguageLoadError::Unsupported(lang) => {
                assert_eq!(lang, "invalid_language");
            }
            _ => panic!("Expected Unsupported error"),
        }
    }

    #[test]
    fn test_supported_languages_list() {
        let languages = supported_languages();
        assert!(languages.contains(&"rust"));
        assert!(languages.contains(&"python"));
        assert!(languages.contains(&"javascript"));
        assert_eq!(languages.len(), 21); // Should match the number of supported languages (many temporarily disabled due to version conflicts)
    }
}
