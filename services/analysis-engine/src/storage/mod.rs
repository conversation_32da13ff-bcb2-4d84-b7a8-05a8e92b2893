pub mod cache;
pub mod connection_pool;
pub mod gcp_clients;
pub mod pubsub;
pub mod redis_client;
pub mod spanner;
pub mod operations;

// Re-export the wrapper types with clear names
pub use cache::CacheManager;
pub use pubsub::PubSubOperations;
pub use redis_client::RedisClient;
pub use spanner::SpannerOperations;
pub use operations::StorageOperations;

// Re-export the actual GCP clients from gcp_clients module
