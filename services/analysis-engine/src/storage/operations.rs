use crate::models::AnalysisResult;
use crate::storage::gcp_clients::StorageClient;
use anyhow::{Context, Result};
use google_cloud_storage::http::objects::download::Range;
use google_cloud_storage::http::objects::get::GetObjectRequest;
use google_cloud_storage::http::objects::list::ListObjectsRequest;
use google_cloud_storage::http::objects::upload::{Media, UploadObjectRequest, UploadType};
use std::env;

pub struct StorageOperations {
    client: StorageClient,
    bucket_name: String,
}

impl StorageOperations {
    pub async fn new(client: StorageClient) -> Result<Self> {
        let project_id =
            env::var("GCP_PROJECT_ID").context("GCP_PROJECT_ID environment variable not set")?;

        let bucket_name = env::var("STORAGE_BUCKET_NAME")
            .unwrap_or_else(|_| format!("ccl-analysis-{project_id}"));

        Ok(Self {
            client,
            bucket_name,
        })
    }

    /// Create a new StorageOperations for testing without external dependencies
    pub async fn new_for_testing() -> Result<Self> {
        // For testing, we create a minimal storage client
        // This bypasses the need for real GCP credentials
        let config = google_cloud_storage::client::ClientConfig::default();
        let client = StorageClient::new(config);

        Ok(Self {
            client,
            bucket_name: "test-bucket".to_string(),
        })
    }

    pub async fn store_analysis_results(&self, analysis: &AnalysisResult) -> Result<String> {
        let object_name = format!("analysis_results/{}.json", analysis.id);
        let content = serde_json::to_vec(analysis)?;

        self.client
            .upload_object(
                &UploadObjectRequest {
                    bucket: self.bucket_name.clone(),
                    ..Default::default()
                },
                content,
                &UploadType::Simple(Media {
                    name: std::borrow::Cow::Owned(object_name.clone()),
                    content_type: std::borrow::Cow::Borrowed("application/json"),
                    content_length: None,
                }),
            )
            .await
            .context("Failed to upload analysis results")?;

        Ok(format!("gs://{}/{}", self.bucket_name, object_name))
    }

    pub async fn get_analysis_results(&self, analysis_id: &str) -> Result<Option<AnalysisResult>> {
        let object_name = format!("analysis_results/{analysis_id}.json");

        let req = GetObjectRequest {
            bucket: self.bucket_name.clone(),
            object: object_name.clone(),
            ..Default::default()
        };

        match self.client.download_object(&req, &Range::default()).await {
            Ok(content) => {
                let analysis: AnalysisResult = serde_json::from_slice(&content)
                    .context("Failed to deserialize analysis results")?;
                Ok(Some(analysis))
            }
            Err(e) => {
                if e.to_string().contains("404") {
                    Ok(None)
                } else {
                    Err(e.into())
                }
            }
        }
    }

    pub async fn list_analysis_results(&self, prefix: Option<String>) -> Result<Vec<String>> {
        let req = ListObjectsRequest {
            bucket: self.bucket_name.clone(),
            prefix: Some(prefix.unwrap_or_else(|| "analysis_results/".to_string())),
            ..Default::default()
        };

        let response = self.client.list_objects(&req).await?;

        let mut results = Vec::new();
        if let Some(items) = response.items {
            for item in items {
                results.push(item.name);
            }
        }

        Ok(results)
    }

    pub async fn delete_analysis_results(&self, analysis_id: &str) -> Result<()> {
        let object_name = format!("analysis_results/{analysis_id}.json");

        self.client
            .delete_object(
                &google_cloud_storage::http::objects::delete::DeleteObjectRequest {
                    bucket: self.bucket_name.clone(),
                    object: object_name,
                    ..Default::default()
                },
            )
            .await
            .context("Failed to delete analysis results")?;

        Ok(())
    }

    pub async fn store_raw_data(&self, path: &str, data: &[u8]) -> Result<()> {
        self.client
            .upload_object(
                &UploadObjectRequest {
                    bucket: self.bucket_name.clone(),
                    ..Default::default()
                },
                data.to_vec(),
                &UploadType::Simple(Media {
                    name: std::borrow::Cow::Owned(path.to_string()),
                    content_type: std::borrow::Cow::Borrowed("application/octet-stream"),
                    content_length: None,
                }),
            )
            .await
            .context("Failed to upload raw data")?;

        Ok(())
    }

    pub async fn health_check(&self) -> Result<()> {
        // Try to get bucket metadata first (requires storage.buckets.get permission)
        use google_cloud_storage::http::buckets::get::GetBucketRequest;
        match self
            .client
            .get_bucket(&GetBucketRequest {
                bucket: self.bucket_name.clone(),
                ..Default::default()
            })
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => {
                let error_str = e.to_string();
                // Check if it's a permission error
                if error_str.contains("storage.buckets.get") || error_str.contains("403") {
                    // Permission issue - try a lightweight list operation instead
                    tracing::debug!(
                        "Bucket metadata access denied, trying list operation as fallback"
                    );

                    use google_cloud_storage::http::objects::list::ListObjectsRequest;
                    match self
                        .client
                        .list_objects(&ListObjectsRequest {
                            bucket: self.bucket_name.clone(),
                            max_results: Some(1),
                            ..Default::default()
                        })
                        .await
                    {
                        Ok(_) => {
                            tracing::warn!("Storage health check: bucket accessible but metadata permissions missing. Consider granting legacyBucketReader role.");
                            Ok(())
                        }
                        Err(list_err) => {
                            tracing::error!(
                                "Storage health check failed on both get and list operations"
                            );
                            Err(anyhow::anyhow!("Storage health check failed: {list_err}"))
                        }
                    }
                } else {
                    // Not a permission issue, propagate the original error
                    Err(anyhow::anyhow!("Storage health check failed: {e}"))
                }
            }
        }
    }
}
