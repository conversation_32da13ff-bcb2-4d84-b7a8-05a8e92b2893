//! System-wide resource monitoring service
//!
//! Provides real-time monitoring of CPU, memory, and other system resources
//! with integration to Prometheus metrics and backpressure management.

use std::sync::Arc;
use std::time::Duration;
use sysinfo::{System, SystemExt, CpuExt, ProcessExt, DiskExt, NetworkExt};
use tokio::sync::RwLock;
use tokio::time::{interval, MissedTickBehavior};
use tracing::{debug, info, warn, error};

use crate::metrics::prometheus::{
    MEMORY_USAGE_BYTES, CPU_USAGE_PERCENT, update_system_metrics,
    update_system_resource_metrics, record_resource_limit_breach
};

/// System monitoring configuration
#[derive(Debug, Clone)]
pub struct SystemMonitorConfig {
    /// CPU usage limit percentage (0-100)
    pub cpu_limit_percent: f32,
    /// Memory usage limit in MB
    pub memory_limit_mb: u64,
    /// CPU warning threshold percentage
    pub cpu_warning_threshold: f32,
    /// Memory warning threshold percentage of limit
    pub memory_warning_threshold_percent: f32,
    /// Monitoring interval
    pub monitoring_interval: Duration,
    /// Enable detailed process monitoring
    pub enable_process_monitoring: bool,
    /// Enable disk monitoring
    pub enable_disk_monitoring: bool,
    /// Enable network monitoring
    pub enable_network_monitoring: bool,
}

impl Default for SystemMonitorConfig {
    fn default() -> Self {
        Self {
            cpu_limit_percent: 90.0,
            memory_limit_mb: 4096,  // 4GB default
            cpu_warning_threshold: 80.0,
            memory_warning_threshold_percent: 85.0,
            monitoring_interval: Duration::from_secs(5),
            enable_process_monitoring: true,
            enable_disk_monitoring: false,
            enable_network_monitoring: false,
        }
    }
}

/// System resource metrics
#[derive(Debug, Clone, Default)]
pub struct SystemMetrics {
    // Memory metrics
    pub total_memory: u64,
    pub used_memory: u64,
    pub available_memory: u64,
    pub memory_usage_percent: f32,
    pub process_memory: u64,
    
    // CPU metrics
    pub cpu_count: usize,
    pub cpu_usage_percent: f32,
    pub process_cpu_percent: f32,
    pub cpu_frequency_mhz: u64,
    
    // Load average (Linux/Unix only)
    pub load_average_1m: f64,
    pub load_average_5m: f64,
    pub load_average_15m: f64,
    
    // Disk metrics (optional)
    pub disk_usage_percent: Option<f32>,
    pub disk_available_gb: Option<u64>,
    
    // Network metrics (optional)
    pub network_rx_bytes_per_sec: Option<u64>,
    pub network_tx_bytes_per_sec: Option<u64>,
    
    // Process metrics
    pub thread_count: Option<usize>,
    pub open_file_descriptors: Option<u64>,
    
    // Timestamp
    pub timestamp: u64,
}

/// System monitor service
pub struct SystemMonitor {
    config: SystemMonitorConfig,
    system: Arc<RwLock<System>>,
    metrics: Arc<RwLock<SystemMetrics>>,
    monitor_handle: Arc<RwLock<Option<tokio::task::JoinHandle<()>>>>,
}

impl SystemMonitor {
    /// Create a new system monitor
    pub fn new(config: SystemMonitorConfig) -> Self {
        let mut system = System::new();
        system.refresh_all();
        
        Self {
            config,
            system: Arc::new(RwLock::new(system)),
            metrics: Arc::new(RwLock::new(SystemMetrics::default())),
            monitor_handle: Arc::new(RwLock::new(None)),
        }
    }
    
    /// Start the monitoring service
    pub async fn start(&self) -> Result<(), anyhow::Error> {
        let mut handle = self.monitor_handle.write().await;
        if handle.is_some() {
            return Err(anyhow::anyhow!("System monitor is already running"));
        }
        
        let config = self.config.clone();
        let system = Arc::clone(&self.system);
        let metrics = Arc::clone(&self.metrics);
        
        let monitor_task = tokio::spawn(async move {
            let mut ticker = interval(config.monitoring_interval);
            ticker.set_missed_tick_behavior(MissedTickBehavior::Skip);
            
            info!("System monitor started with interval: {:?}", config.monitoring_interval);
            
            loop {
                ticker.tick().await;
                
                // Update system information
                let mut sys = system.write().await;
                sys.refresh_memory();
                sys.refresh_cpu();
                
                if config.enable_process_monitoring {
                    sys.refresh_processes();
                }
                if config.enable_disk_monitoring {
                    sys.refresh_disks();
                }
                if config.enable_network_monitoring {
                    sys.refresh_networks();
                }
                
                drop(sys); // Release write lock
                
                // Collect metrics
                let new_metrics = Self::collect_metrics(&system, &config).await;
                
                // Check thresholds
                Self::check_thresholds(&new_metrics, &config);
                
                // Update Prometheus metrics
                Self::update_prometheus_metrics(&new_metrics);
                
                // Store metrics
                let mut metrics_guard = metrics.write().await;
                *metrics_guard = new_metrics;
            }
        });
        
        *handle = Some(monitor_task);
        Ok(())
    }
    
    /// Stop the monitoring service
    pub async fn stop(&self) -> Result<(), anyhow::Error> {
        let mut handle = self.monitor_handle.write().await;
        if let Some(task) = handle.take() {
            task.abort();
            info!("System monitor stopped");
        }
        Ok(())
    }
    
    /// Get current metrics
    pub async fn get_metrics(&self) -> SystemMetrics {
        self.metrics.read().await.clone()
    }
    
    /// Get immediate system metrics (refresh and return)
    pub async fn get_current_metrics(&self) -> SystemMetrics {
        let mut system = self.system.write().await;
        system.refresh_all();
        drop(system);
        
        Self::collect_metrics(&self.system, &self.config).await
    }
    
    /// Check if system is under pressure
    pub async fn is_under_pressure(&self) -> bool {
        let metrics = self.metrics.read().await;
        
        metrics.cpu_usage_percent > self.config.cpu_limit_percent ||
        metrics.memory_usage_percent > (self.config.memory_warning_threshold_percent)
    }
    
    /// Get resource usage summary
    pub async fn get_resource_summary(&self) -> ResourceSummary {
        let metrics = self.metrics.read().await;
        
        ResourceSummary {
            cpu_usage: metrics.cpu_usage_percent,
            memory_usage_mb: metrics.used_memory / 1024 / 1024,
            memory_usage_percent: metrics.memory_usage_percent,
            process_cpu: metrics.process_cpu_percent,
            process_memory_mb: metrics.process_memory / 1024 / 1024,
            is_cpu_limited: metrics.cpu_usage_percent > self.config.cpu_limit_percent,
            is_memory_limited: metrics.memory_usage_percent > self.config.memory_warning_threshold_percent,
        }
    }
    
    /// Collect metrics from the system
    async fn collect_metrics(system: &Arc<RwLock<System>>, config: &SystemMonitorConfig) -> SystemMetrics {
        let sys = system.read().await;
        
        let mut metrics = SystemMetrics {
            total_memory: sys.total_memory(),
            used_memory: sys.used_memory(),
            available_memory: sys.available_memory(),
            memory_usage_percent: (sys.used_memory() as f32 / sys.total_memory() as f32) * 100.0,
            cpu_count: sys.cpus().len(),
            cpu_usage_percent: sys.global_cpu_info().cpu_usage(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            ..Default::default()
        };
        
        // CPU frequency (average)
        let total_freq: u64 = sys.cpus().iter().map(|cpu| cpu.frequency()).sum();
        metrics.cpu_frequency_mhz = total_freq / sys.cpus().len() as u64;
        
        // Load average
        let load_avg = sys.load_average();
        metrics.load_average_1m = load_avg.one;
        metrics.load_average_5m = load_avg.five;
        metrics.load_average_15m = load_avg.fifteen;
        
        // Process-specific metrics
        if config.enable_process_monitoring {
            if let Ok(pid) = sysinfo::get_current_pid() {
                if let Some(process) = sys.process(pid) {
                    metrics.process_memory = process.memory();
                    metrics.process_cpu_percent = process.cpu_usage();
                    metrics.thread_count = Some(process.threads().len());
                }
            }
        }
        
        // Disk metrics
        if config.enable_disk_monitoring && !sys.disks().is_empty() {
            let total_space: u64 = sys.disks().iter().map(|d| d.total_space()).sum();
            let available_space: u64 = sys.disks().iter().map(|d| d.available_space()).sum();
            
            if total_space > 0 {
                metrics.disk_usage_percent = Some(((total_space - available_space) as f32 / total_space as f32) * 100.0);
                metrics.disk_available_gb = Some(available_space / 1024 / 1024 / 1024);
            }
        }
        
        metrics
    }
    
    /// Check resource thresholds and log warnings
    fn check_thresholds(metrics: &SystemMetrics, config: &SystemMonitorConfig) {
        // CPU threshold check
        if metrics.cpu_usage_percent > config.cpu_limit_percent {
            error!(
                "CPU usage exceeds limit: {:.1}% > {:.1}%",
                metrics.cpu_usage_percent, config.cpu_limit_percent
            );
            record_resource_limit_breach("cpu", "system_limit");
        } else if metrics.cpu_usage_percent > config.cpu_warning_threshold {
            warn!(
                "CPU usage approaching limit: {:.1}% (warning at {:.1}%, limit at {:.1}%)",
                metrics.cpu_usage_percent, config.cpu_warning_threshold, config.cpu_limit_percent
            );
            record_resource_limit_breach("cpu", "warning_threshold");
        }
        
        // Memory threshold check
        let memory_limit_bytes = config.memory_limit_mb * 1024 * 1024;
        let memory_warning_bytes = (memory_limit_bytes as f32 * config.memory_warning_threshold_percent / 100.0) as u64;
        
        if metrics.used_memory > memory_limit_bytes {
            error!(
                "Memory usage exceeds limit: {} MB > {} MB",
                metrics.used_memory / 1024 / 1024, config.memory_limit_mb
            );
            record_resource_limit_breach("memory", "system_limit");
        } else if metrics.used_memory > memory_warning_bytes {
            warn!(
                "Memory usage approaching limit: {} MB / {} MB ({:.1}%)",
                metrics.used_memory / 1024 / 1024,
                config.memory_limit_mb,
                metrics.memory_usage_percent
            );
            record_resource_limit_breach("memory", "warning_threshold");
        }
        
        // Process-specific warnings
        if let (Some(process_cpu), Some(_thread_count)) = (Some(metrics.process_cpu_percent), metrics.thread_count) {
            if process_cpu > config.cpu_limit_percent / 2.0 {
                debug!(
                    "Process CPU usage is high: {:.1}% (system: {:.1}%)",
                    process_cpu, metrics.cpu_usage_percent
                );
            }
        }
    }
    
    /// Update Prometheus metrics
    fn update_prometheus_metrics(metrics: &SystemMetrics) {
        // Update memory metric
        MEMORY_USAGE_BYTES.set(metrics.used_memory as i64);
        
        // Update CPU metrics
        CPU_USAGE_PERCENT
            .with_label_values(&["total"])
            .set(metrics.cpu_usage_percent as f64);
        
        if metrics.process_cpu_percent > 0.0 {
            CPU_USAGE_PERCENT
                .with_label_values(&["process"])
                .set(metrics.process_cpu_percent as f64);
        }
        
        // Update system metrics using the helper function
        update_system_metrics(
            metrics.used_memory as i64,
            metrics.cpu_usage_percent as f64,
        );
        
        // Update comprehensive resource metrics
        update_system_resource_metrics(
            metrics.total_memory,
            metrics.available_memory,
            metrics.cpu_count,
            metrics.process_memory,
            metrics.process_cpu_percent,
            metrics.load_average_1m,
            metrics.load_average_5m,
            metrics.load_average_15m,
        );
    }
}

/// Resource usage summary
#[derive(Debug, Clone)]
pub struct ResourceSummary {
    pub cpu_usage: f32,
    pub memory_usage_mb: u64,
    pub memory_usage_percent: f32,
    pub process_cpu: f32,
    pub process_memory_mb: u64,
    pub is_cpu_limited: bool,
    pub is_memory_limited: bool,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_system_monitor_creation() {
        let config = SystemMonitorConfig::default();
        let monitor = SystemMonitor::new(config);
        
        // Should be able to get metrics even before starting
        let metrics = monitor.get_current_metrics().await;
        assert!(metrics.total_memory > 0);
        assert!(metrics.cpu_count > 0);
    }
    
    #[tokio::test]
    async fn test_resource_summary() {
        let config = SystemMonitorConfig {
            cpu_limit_percent: 50.0,
            memory_warning_threshold_percent: 50.0,
            ..Default::default()
        };
        
        let monitor = SystemMonitor::new(config);
        let summary = monitor.get_resource_summary().await;
        
        assert!(summary.memory_usage_mb >= 0);
        assert!(summary.cpu_usage >= 0.0 && summary.cpu_usage <= 100.0);
    }
}