    Checking analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
warning: unused import: `super::*`
 --> src/services/analyzer/pattern_optimization_tests.rs:6:9
  |
6 |     use super::*;
  |         ^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: function `create_test_analysis_result` is never used
   --> src/services/analyzer/storage.rs:210:8
    |
210 |     fn create_test_analysis_result() -> AnalysisResult {
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = note: `#[warn(dead_code)]` on by default

warning: function `create_test_config` is never used
   --> src/services/analyzer/mod.rs:543:8
    |
543 |     fn create_test_config() -> ServiceConfig {
    |        ^^^^^^^^^^^^^^^^^^

warning: function `create_test_repo` is never used
   --> src/services/analyzer/mod.rs:592:8
    |
592 |     fn create_test_repo(temp_dir: &TempDir) -> PathBuf {
    |        ^^^^^^^^^^^^^^^^

warning: this `map_or` can be simplified
   --> src/api/handlers/analysis.rs:447:29
    |
447 | / ...                   w.file_path
448 | | ...                       .as_ref()
449 | | ...                       .map_or(false, |p| p.contains(file_path))
    | |___________________________________________________________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or
    = note: `#[warn(clippy::unnecessary_map_or)]` on by default
help: use is_some_and instead
    |
449 -                                 .map_or(false, |p| p.contains(file_path))
449 +                                 .is_some_and(|p| p.contains(file_path))
    |

warning: using `clone` on type `WarningType` which implements the `Copy` trait
   --> src/api/handlers/analysis.rs:462:36
    |
462 | ...                   .entry(warning.warning_type.clone())
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `warning.warning_type`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy
    = note: `#[warn(clippy::clone_on_copy)]` on by default

warning: this `map_or` can be simplified
   --> src/api/middleware/auth_layer.rs:165:30
    |
165 |             .retain(|_, key| key.expires_at.map_or(true, |expires| expires > now));
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or
help: use is_none_or instead
    |
165 -             .retain(|_, key| key.expires_at.map_or(true, |expires| expires > now));
165 +             .retain(|_, key| key.expires_at.is_none_or(|expires| expires > now));
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:758:30
    |
758 |                 .map_err(|e| format!("Failed to create read transaction: {}", e))?;
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
    = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
    |
758 -                 .map_err(|e| format!("Failed to create read transaction: {}", e))?;
758 +                 .map_err(|e| format!("Failed to create read transaction: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:762:30
    |
762 |                 .map_err(|e| format!("Failed to query user: {}", e))?;
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
762 -                 .map_err(|e| format!("Failed to query user: {}", e))?;
762 +                 .map_err(|e| format!("Failed to query user: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:766:30
    |
766 |                 .map_err(|e| format!("Failed to read row: {}", e))?
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
766 -                 .map_err(|e| format!("Failed to read row: {}", e))?
766 +                 .map_err(|e| format!("Failed to read row: {e}"))?
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:776:26
    |
776 |             .map_err(|e| format!("Failed to read rate_limit: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
776 -             .map_err(|e| format!("Failed to read rate_limit: {}", e))?;
776 +             .map_err(|e| format!("Failed to read rate_limit: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/middleware/auth_layer.rs:847:5
    |
847 |     format!("{:x}", result)
    |     ^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
847 -     format!("{:x}", result)
847 +     format!("{result:x}")
    |

warning: variables can be used directly in the `format!` string
    --> src/api/middleware/auth_layer.rs:1089:22
     |
1089 |         description: format!("Wait {} seconds before making another request", retry_after),
     |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |
     = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
     |
1089 -         description: format!("Wait {} seconds before making another request", retry_after),
1089 +         description: format!("Wait {retry_after} seconds before making another request"),
     |

warning: variables can be used directly in the `format!` string
    --> src/api/middleware/auth_layer.rs:1137:15
     |
1137 |     let key = format!("rate_limit:{}", user_id);
     |               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |
     = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
     |
1137 -     let key = format!("rate_limit:{}", user_id);
1137 +     let key = format!("rate_limit:{user_id}");
     |

warning: variables can be used directly in the `format!` string
  --> src/api/rate_limit_extractor.rs:68:17
   |
68 |                 format!("Rate limit check failed: {}", e),
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
68 -                 format!("Rate limit check failed: {}", e),
68 +                 format!("Rate limit check failed: {e}"),
   |

warning: variables can be used directly in the `format!` string
   --> src/api/rate_limit_extractor.rs:173:15
    |
173 |     let key = format!("rate_limit:{}", user_id);
    |               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
173 -     let key = format!("rate_limit:{}", user_id);
173 +     let key = format!("rate_limit:{user_id}");
    |

warning: variables can be used directly in the `format!` string
   --> src/api/rate_limit_extractor.rs:193:22
    |
193 |         .map_err(|e| format!("Redis error: {}", e))?;
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
193 -         .map_err(|e| format!("Redis error: {}", e))?;
193 +         .map_err(|e| format!("Redis error: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/rate_limit_extractor.rs:200:26
    |
200 |             .map_err(|e| format!("Redis error: {}", e))?;
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
200 -             .map_err(|e| format!("Redis error: {}", e))?;
200 +             .map_err(|e| format!("Redis error: {e}"))?;
    |

warning: variables can be used directly in the `format!` string
   --> src/api/rate_limit_extractor.rs:256:9
    |
256 |         format!("{} seconds", seconds_until_reset)
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
256 -         format!("{} seconds", seconds_until_reset)
256 +         format!("{seconds_until_reset} seconds")
    |

warning: this match could be written as a `let` statement
  --> src/api/mod.rs:49:9
   |
49 | /         let redis_pool = match RedisConnectionManager::new(config.redis.clone()) {
50 | |             manager => match Pool::builder().build(manager).await {
51 | |                 Ok(pool) => {
52 | |                     tracing::info!("Redis connection pool created successfully");
...  |
59 | |             },
60 | |         };
   | |__________^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#match_single_binding
   = note: `#[warn(clippy::match_single_binding)]` on by default
help: consider using a `let` statement
   |
49 ~         let manager = RedisConnectionManager::new(config.redis.clone());
50 +         let redis_pool = match Pool::builder().build(manager).await {
51 +             Ok(pool) => {
52 +                 tracing::info!("Redis connection pool created successfully");
53 +                 Some(Arc::new(pool))
54 +             }
55 +             Err(e) => {
56 +                 tracing::warn!("Failed to create Redis connection pool: {}. Continuing without Redis caching.", e);
57 +                 None
58 +             }
59 +         };
   |

warning: `format!` in `format!` args
   --> src/audit/mod.rs:254:32
    |
254 |               anyhow::Error::msg(anyhow::__private::format!(
    |  ________________________________^
255 | |                 "Failed to get spanner connection for audit log: {}",
256 | |                 format!("{:?}", e)
257 | |             ))
    | |_____________^
    |
    = help: combine the `format!(..)` arguments with the outer `format!(..)` call
    = help: or consider changing `format!` to `format_args!`
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#format_in_format_args
    = note: `#[warn(clippy::format_in_format_args)]` on by default

warning: redundant closure
   --> src/contracts.rs:213:38
    |
213 | ...                   .map(|chunk| convert_code_chunk(chunk))
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: replace the closure with the function itself: `convert_code_chunk`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#redundant_closure
    = note: `#[warn(clippy::redundant_closure)]` on by default

warning: redundant closure
   --> src/contracts.rs:223:38
    |
223 | ...                   .map(|symbol| convert_symbol(symbol))
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: replace the closure with the function itself: `convert_symbol`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#redundant_closure

warning: this `if` has identical blocks
   --> src/errors.rs:262:36
    |
262 |           } else if err.is_connect() {
    |  ____________________________________^
263 | |             HttpError::Network(err.to_string())
264 | |         } else {
    | |_________^
    |
note: same as this
   --> src/errors.rs:264:16
    |
264 |           } else {
    |  ________________^
265 | |             HttpError::Network(err.to_string())
266 | |         }
    | |_________^
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#if_same_then_else
    = note: `#[warn(clippy::if_same_then_else)]` on by default

warning: you should consider adding a `Default` implementation for `GitService`
  --> src/git/mod.rs:11:5
   |
11 | /     pub fn new() -> Self {
12 | |         Self
13 | |     }
   | |_____^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
   = note: `#[warn(clippy::new_without_default)]` on by default
help: try adding this
   |
10 + impl Default for GitService {
11 +     fn default() -> Self {
12 +         Self::new()
13 +     }
14 + }
   |

warning: you should consider adding a `Default` implementation for `GranularMetricsCollector`
   --> src/metrics/granular.rs:223:5
    |
223 | /     pub fn new() -> Self {
224 | |         Self {
225 | |             operation_metrics: Arc::new(RwLock::new(HashMap::new())),
226 | |             language_metrics: Arc::new(RwLock::new(HashMap::new())),
...   |
232 | |     }
    | |_____^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
    |
221 + impl Default for GranularMetricsCollector {
222 +     fn default() -> Self {
223 +         Self::new()
224 +     }
225 + }
    |

warning: this `impl` can be derived
   --> src/metrics/granular.rs:549:1
    |
549 | / impl Default for ConcurrencyMetrics {
550 | |     fn default() -> Self {
551 | |         Self {
552 | |             active_analyses: 0,
...   |
567 | | }
    | |_^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#derivable_impls
    = note: `#[warn(clippy::derivable_impls)]` on by default
help: replace the manual implementation with a derive attribute
    |
155 + #[derive(Default)]
156 ~ pub struct ConcurrencyMetrics {
    |

warning: this `impl` can be derived
   --> src/metrics/granular.rs:569:1
    |
569 | / impl Default for ErrorMetrics {
570 | |     fn default() -> Self {
571 | |         Self {
572 | |             parser_errors: 0,
...   |
588 | | }
    | |_^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#derivable_impls
help: replace the manual implementation with a derive attribute
    |
179 + #[derive(Default)]
180 ~ pub struct ErrorMetrics {
    |

warning: used `unwrap()` on `Ok` value
   --> src/errors.rs:589:20
    |
589 |         assert_eq!(result.unwrap(), 42);
    |                    ^^^^^^^^^^^^^^^
    |
help: remove the `Ok` and `unwrap()`
   --> src/errors.rs:588:43
    |
588 |         let result: AnalysisResult<i32> = Ok(42);
    |                                           ^^^^^^
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_literal_unwrap
    = note: `#[warn(clippy::unnecessary_literal_unwrap)]` on by default

warning: you should consider adding a `Default` implementation for `MetricsCollector`
   --> src/metrics/mod.rs:364:5
    |
364 | /     pub fn new() -> Self {
365 | |         Self {
366 | |             current_metrics: PerformanceMetrics::default(),
367 | |             metrics_history: Vec::new(),
...   |
372 | |     }
    | |_____^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
    |
363 + impl Default for MetricsCollector {
364 +     fn default() -> Self {
365 +         Self::new()
366 +     }
367 + }
    |

warning: use of `or_insert_with` to construct default value
   --> src/metrics/mod.rs:399:14
    |
399 |             .or_insert_with(Vec::new)
    |              ^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `or_default()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unwrap_or_default
    = note: `#[warn(clippy::unwrap_or_default)]` on by default

warning: you should consider adding a `Default` implementation for `SystemResourceMonitor`
   --> src/metrics/mod.rs:423:5
    |
423 | /     pub fn new() -> Self {
424 | |         let monitor = Self {
425 | |             update_interval: Duration::from_secs(30),
426 | |             current_stats: Arc::new(RwLock::new(SystemStats::default())),
...   |
432 | |         monitor
433 | |     }
    | |_____^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
    |
422 + impl Default for SystemResourceMonitor {
423 +     fn default() -> Self {
424 +         Self::new()
425 +     }
426 + }
    |

warning: unneeded `return` statement
   --> src/metrics/mod.rs:529:13
    |
529 | /             return Ok(MemoryInfo {
530 | |                 total,
531 | |                 used,
532 | |                 available,
533 | |             });
    | |______________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_return
    = note: `#[warn(clippy::needless_return)]` on by default
help: remove `return`
    |
529 ~             Ok(MemoryInfo {
530 +                 total,
531 +                 used,
532 +                 available,
533 ~             })
    |

warning: you should consider adding a `Default` implementation for `MetricsService`
   --> src/metrics/mod.rs:810:5
    |
810 | /     pub fn new() -> Self {
811 | |         Self
812 | |     }
    | |_____^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
    |
809 + impl Default for MetricsService {
810 +     fn default() -> Self {
811 +         Self::new()
812 +     }
813 + }
    |

warning: variables can be used directly in the `format!` string
   --> src/migrations/mod.rs:102:38
    |
102 |                     .with_context(|| format!("Failed to read migration file: {:?}", path))?;
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
102 -                     .with_context(|| format!("Failed to read migration file: {:?}", path))?;
102 +                     .with_context(|| format!("Failed to read migration file: {path:?}"))?;
    |

warning: this `if let` can be collapsed into the outer `match`
   --> src/parser/adapters.rs:140:13
    |
140 | /             if let Some(name) = name {
141 | |                 symbols.push(Symbol {
142 | |                     name: name.to_string(),
143 | |                     symbol_type: SymbolType::Variable, // Indexes as variables
...   |
159 | |                 });
160 | |             }
    | |_____________^
    |
help: the outer pattern can be modified to include the inner pattern
   --> src/parser/adapters.rs:139:34
    |
139 |         Statement::CreateIndex { name, .. } => {
    |                                  ^^^^ replace this binding
140 |             if let Some(name) = name {
    |                    ^^^^^^^^^^ with this pattern, prefixed by `name`:
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#collapsible_match
    = note: `#[warn(clippy::collapsible_match)]` on by default

warning: casting to the same type is unnecessary (`u32` -> `u32`)
   --> src/parser/adapters.rs:203:39
    |
203 | ...                   line: current_line as u32,
    |                             ^^^^^^^^^^^^^^^^^^^ help: try: `current_line`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_cast
    = note: `#[warn(clippy::unnecessary_cast)]` on by default

warning: casting to the same type is unnecessary (`u32` -> `u32`)
   --> src/parser/adapters.rs:208:39
    |
208 | ...                   line: current_line as u32,
    |                             ^^^^^^^^^^^^^^^^^^^ help: try: `current_line`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_cast

warning: unnecessary `if let` since only the `Ok` variant of the iterator element is used
   --> src/parser/adapters.rs:219:21
    |
219 |                       for attr in e.attributes() {
    |                       ^           -------------- help: try: `e.attributes().flatten()`
    |  _____________________|
    | |
220 | |                         if let Ok(attr) = attr {
221 | |                             let attr_name = String::from_utf8_lossy(attr.key.as_ref()).to_string();
222 | |                             symbols.push(Symbol {
...   |
242 | |                     }
    | |_____________________^
    |
help: ...and remove the `if let` statement in the for loop
   --> src/parser/adapters.rs:220:25
    |
220 | /                         if let Ok(attr) = attr {
221 | |                             let attr_name = String::from_utf8_lossy(attr.key.as_ref()).to_string();
222 | |                             symbols.push(Symbol {
223 | |                                 name: format!("{}.{}", name, attr_name),
...   |
240 | |                             });
241 | |                         }
    | |_________________________^
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_flatten
    = note: `#[warn(clippy::manual_flatten)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:223:39
    |
223 | ...                   name: format!("{}.{}", name, attr_name),
    |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
223 -                                 name: format!("{}.{}", name, attr_name),
223 +                                 name: format!("{name}.{attr_name}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:303:21
    |
303 |                     format!("{}.{}", prefix, key)
    |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
303 -                     format!("{}.{}", prefix, key)
303 +                     format!("{prefix}.{key}")
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:412:31
    |
412 |                         name: format!("code_block_{}", lang),
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
412 -                         name: format!("code_block_{}", lang),
412 +                         name: format!("code_block_{lang}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:428:45
    |
428 |                         documentation: Some(format!("Code block in {}", lang)),
    |                                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
428 -                         documentation: Some(format!("Code block in {}", lang)),
428 +                         documentation: Some(format!("Code block in {lang}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:480:37
    |
480 |                     signature: Some(format!("H{} {}", level, title)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
480 -                     signature: Some(format!("H{} {}", level, title)),
480 +                     signature: Some(format!("H{level} {title}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:481:41
    |
481 |                     documentation: Some(format!("Heading level {} in documentation", level)),
    |                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
481 -                     documentation: Some(format!("Heading level {} in documentation", level)),
481 +                     documentation: Some(format!("Heading level {level} in documentation")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:493:27
    |
493 |                     name: format!("task: {}", task_text),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
493 -                     name: format!("task: {}", task_text),
493 +                     name: format!("task: {task_text}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:524:27
    |
524 |                     name: format!("list_item: {}", item_text),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
524 -                     name: format!("list_item: {}", item_text),
524 +                     name: format!("list_item: {item_text}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:539:37
    |
539 |                     signature: Some(format!("List item: {}", item_text)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
539 -                     signature: Some(format!("List item: {}", item_text)),
539 +                     signature: Some(format!("List item: {item_text}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:549:31
    |
549 |                         name: format!("table_row_{}", current_line),
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
549 -                         name: format!("table_row_{}", current_line),
549 +                         name: format!("table_row_{current_line}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:576:27
    |
576 |                     name: format!("link: {}", link_text),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
576 -                     name: format!("link: {}", link_text),
576 +                     name: format!("link: {link_text}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:591:37
    |
591 |                     signature: Some(format!("Link: {} -> {}", link_text, link_url)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
591 -                     signature: Some(format!("Link: {} -> {}", link_text, link_url)),
591 +                     signature: Some(format!("Link: {link_text} -> {link_url}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:602:27
    |
602 |                     name: format!("image: {}", alt_text),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
602 -                     name: format!("image: {}", alt_text),
602 +                     name: format!("image: {alt_text}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:617:37
    |
617 |                     signature: Some(format!("Image: {} -> {}", alt_text, image_url)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
617 -                     signature: Some(format!("Image: {} -> {}", alt_text, image_url)),
617 +                     signature: Some(format!("Image: {alt_text} -> {image_url}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:628:27
    |
628 |                     name: format!("reference: {}", ref_name),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
628 -                     name: format!("reference: {}", ref_name),
628 +                     name: format!("reference: {ref_name}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:643:37
    |
643 |                     signature: Some(format!("Reference: {} -> {}", ref_name, ref_url)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
643 -                     signature: Some(format!("Reference: {} -> {}", ref_name, ref_url)),
643 +                     signature: Some(format!("Reference: {ref_name} -> {ref_url}")),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:653:27
    |
653 |                     name: format!("inline_code: {}", code),
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
653 -                     name: format!("inline_code: {}", code),
653 +                     name: format!("inline_code: {code}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/adapters.rs:668:37
    |
668 |                     signature: Some(format!("Inline code: {}", code)),
    |                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
668 -                     signature: Some(format!("Inline code: {}", code)),
668 +                     signature: Some(format!("Inline code: {code}")),
    |

warning: you should consider adding a `Default` implementation for `LanguageMetricsCalculator`
  --> src/parser/language_metrics.rs:61:5
   |
61 | /     pub fn new() -> Self {
62 | |         let mut calculator = Self {
63 | |             language_patterns: HashMap::new(),
64 | |         };
65 | |         calculator.initialize_patterns();
66 | |         calculator
67 | |     }
   | |_____^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
   |
60 + impl Default for LanguageMetricsCalculator {
61 +     fn default() -> Self {
62 +         Self::new()
63 +     }
64 + }
   |

warning: `debug_assert!(true)` will be optimized out by the compiler
    --> src/parser/language_metrics.rs:1238:9
     |
1238 |         debug_assert!(100.0 >= 0.0, "clamp bounds invalid: max=100.0, min=0.0");
     |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     |
     = help: remove it
     = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#assertions_on_constants
     = note: `#[warn(clippy::assertions_on_constants)]` on by default

warning: variables can be used directly in the `format!` string
  --> src/parser/validation_demo.rs:15:13
   |
15 |             println!("❌ Failed to create parser: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
15 -             println!("❌ Failed to create parser: {}", e);
15 +             println!("❌ Failed to create parser: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/parser/validation_demo.rs:66:21
   |
66 |                     println!("✅ {} → {}", filename, detected_lang);
   |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
66 -                     println!("✅ {} → {}", filename, detected_lang);
66 +                     println!("✅ {filename} → {detected_lang}");
   |

warning: variables can be used directly in the `format!` string
  --> src/parser/validation_demo.rs:69:21
   |
69 | /                     println!(
70 | |                         "⚠️  {} → {} (expected {})",
71 | |                         filename, detected_lang, expected_lang
72 | |                     );
   | |_____________________^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
  --> src/parser/validation_demo.rs:83:5
   |
83 |     println!("Successfully detected: {}", detected_count);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
83 -     println!("Successfully detected: {}", detected_count);
83 +     println!("Successfully detected: {detected_count}");
   |

warning: variables can be used directly in the `format!` string
  --> src/parser/validation_demo.rs:95:9
   |
95 |         println!("• {}", lang);
   |         ^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
95 -         println!("• {}", lang);
95 +         println!("• {lang}");
   |

warning: variables can be used directly in the `format!` string
   --> src/parser/validation_demo.rs:112:18
    |
112 |             _ => format!("test.{}", lang),
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
112 -             _ => format!("test.{}", lang),
112 +             _ => format!("test.{lang}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/ast/chunk_extractor.rs:118:24
    |
118 |         let chunk_id = format!("chunk_{:016x}", counter);
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
118 -         let chunk_id = format!("chunk_{:016x}", counter);
118 +         let chunk_id = format!("chunk_{counter:016x}");
    |

warning: this `if` statement can be collapsed
   --> src/parser/language_detection.rs:115:13
    |
115 | /             if ext == "h" {
116 | |                 if self.is_cpp_header(content) {
117 | |                     return Ok(Some("cpp".to_string()));
118 | |                 }
119 | |             }
    | |_____________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#collapsible_if
    = note: `#[warn(clippy::collapsible_if)]` on by default
help: collapse nested if block
    |
115 ~             if ext == "h"
116 ~                 && self.is_cpp_header(content) {
117 |                     return Ok(Some("cpp".to_string()));
118 ~                 }
    |

warning: this `if` statement can be collapsed
   --> src/parser/language_detection.rs:156:9
    |
156 | /         if sample.trim_start().starts_with("<?xml") || sample.trim_start().starts_with("<") {
157 | |             if self.looks_like_xml(sample) {
158 | |                 return Ok(Some("xml".to_string()));
159 | |             }
160 | |         }
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#collapsible_if
help: collapse nested if block
    |
156 ~         if (sample.trim_start().starts_with("<?xml") || sample.trim_start().starts_with("<")) {
157 ~             && self.looks_like_xml(sample) {
158 |                 return Ok(Some("xml".to_string()));
159 ~             }
    |

warning: redundant closure
  --> src/parser/language_registry.rs:30:15
   |
30 |     Lazy::new(|| HashMap::new());
   |               ^^^^^^^^^^^^^^^^^ help: replace the closure with the function itself: `HashMap::new`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#redundant_closure

warning: `debug_assert!(true)` will be optimized out by the compiler
  --> src/parser/parser_pool.rs:84:9
   |
84 |         debug_assert!(4 >= 1, "clamp bounds invalid: max=4, min=1");
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: remove it
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#assertions_on_constants

warning: manual implementation of `Option::map`
  --> src/parser/streaming/file_processor.rs:81:31
   |
81 |           let _monitor_handle = if let Some(monitor) = &self.memory_monitor {
   |  _______________________________^
82 | |             Some(monitor.start_monitoring())
83 | |         } else {
84 | |             None
85 | |         };
   | |_________^ help: try: `self.memory_monitor.as_ref().map(|monitor| monitor.start_monitoring())`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_map
   = note: `#[warn(clippy::manual_map)]` on by default

warning: variables can be used directly in the `format!` string
  --> src/parser/streaming/hasher.rs:24:9
   |
24 |         format!("{:x}", result)
   |         ^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
24 -         format!("{:x}", result)
24 +         format!("{result:x}")
   |

warning: length comparison to zero
   --> src/parser/adapters.rs:718:17
    |
718 |         assert!(symbols.len() > 0);
    |                 ^^^^^^^^^^^^^^^^^ help: using `!is_empty` is clearer and more explicit: `!symbols.is_empty()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#len_zero
    = note: `#[warn(clippy::len_zero)]` on by default

warning: length comparison to zero
   --> src/parser/adapters.rs:739:17
    |
739 |         assert!(symbols.len() > 0);
    |                 ^^^^^^^^^^^^^^^^^ help: using `!is_empty` is clearer and more explicit: `!symbols.is_empty()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#len_zero

warning: length comparison to zero
   --> src/parser/adapters.rs:783:17
    |
783 |         assert!(symbols.len() > 0);
    |                 ^^^^^^^^^^^^^^^^^ help: using `!is_empty` is clearer and more explicit: `!symbols.is_empty()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#len_zero

warning: calling `push_str()` using a single-character string literal
   --> src/services/ai_pattern_detector.rs:305:17
    |
305 |                 prompt.push_str("\n");
    |                 ^^^^^^^^^^^^^^^^^^^^^ help: consider using `push` with a character literal: `prompt.push('\n')`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#single_char_add_str
    = note: `#[warn(clippy::single_char_add_str)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/services/ai_pattern_detector.rs:310:34
    |
310 |                 prompt.push_str(&format!("Code:\n```\n{}\n```\n\n", preview));
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
310 -                 prompt.push_str(&format!("Code:\n```\n{}\n```\n\n", preview));
310 +                 prompt.push_str(&format!("Code:\n```\n{preview}\n```\n\n"));
    |

warning: variables can be used directly in the `format!` string
  --> src/parser/language_validation_test.rs:52:22
   |
52 |             Ok(_) => println!("✓ Successfully created parser pool for {}", lang),
   |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
52 -             Ok(_) => println!("✓ Successfully created parser pool for {}", lang),
52 +             Ok(_) => println!("✓ Successfully created parser pool for {lang}"),
   |

warning: variables can be used directly in the `format!` string
  --> src/parser/language_validation_test.rs:53:23
   |
53 |             Err(e) => println!("⚠ Failed to create parser pool for {}: {:?}", lang, e),
   |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
53 -             Err(e) => println!("⚠ Failed to create parser pool for {}: {:?}", lang, e),
53 +             Err(e) => println!("⚠ Failed to create parser pool for {lang}: {e:?}"),
   |

warning: variables can be used directly in the `format!` string
  --> src/parser/language_validation_test.rs:78:9
   |
78 | /         assert_eq!(
79 | |             detected_lang, lang,
80 | |             "Language detection failed for {}",
81 | |             lang
82 | |         );
   | |_________^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/parser/language_validation_test.rs:157:17
    |
157 |                 println!("✓ {} parsing successful", lang);
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
157 -                 println!("✓ {} parsing successful", lang);
157 +                 println!("✓ {lang} parsing successful");
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/language_validation_test.rs:214:9
    |
214 | /         assert_eq!(
215 | |             detected_lang, expected_lang,
216 | |             "Language detection failed for {}, expected {}, got {}",
217 | |             filename, expected_lang, detected_lang
218 | |         );
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/parser/language_validation_test.rs:238:9
    |
238 |         eprintln!("Error setting language on parser: {:?}", e);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
238 -         eprintln!("Error setting language on parser: {:?}", e);
238 +         eprintln!("Error setting language on parser: {e:?}");
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/language_validation_test.rs:249:13
    |
249 |             eprintln!("Error creating pool for {}: {:?}", lang, e);
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
249 -             eprintln!("Error creating pool for {}: {:?}", lang, e);
249 +             eprintln!("Error creating pool for {lang}: {e:?}");
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/language_validation_test.rs:251:9
    |
251 |         assert!(pool_result.is_ok(), "Failed to create pool for {}", lang);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
251 -         assert!(pool_result.is_ok(), "Failed to create pool for {}", lang);
251 +         assert!(pool_result.is_ok(), "Failed to create pool for {lang}");
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/language_validation_test.rs:267:9
    |
267 |         assert!(parser_result.is_ok(), "Failed to get parser for {}", lang);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
267 -         assert!(parser_result.is_ok(), "Failed to get parser for {}", lang);
267 +         assert!(parser_result.is_ok(), "Failed to get parser for {lang}");
    |

warning: variables can be used directly in the `format!` string
   --> src/parser/language_validation_test.rs:274:5
    |
274 |     println!("Parser pools created for {} languages", pool_count);
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
274 -     println!("Parser pools created for {} languages", pool_count);
274 +     println!("Parser pools created for {pool_count} languages");
    |

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/file_processor.rs:258:9
    |
258 | /         debug_assert!(
259 | |             max_concurrency >= 1,
260 | |             "clamp bounds invalid: max_concurrency={}, min=1",
261 | |             max_concurrency
262 | |         );
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: `debug_assert!(true)` will be optimized out by the compiler
   --> src/services/analyzer/file_processor.rs:281:17
    |
281 |                 debug_assert!(2.0 >= 0.5, "clamp bounds invalid: max=2.0, min=0.5");
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: remove it
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#assertions_on_constants

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/performance.rs:160:17
    |
160 | /                 format!(
161 | |                     "Peak memory usage was {} MB, approaching system limits",
162 | |                     memory_usage_mb
163 | |                 ),
    | |_________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/performance.rs:170:17
    |
170 | /                 format!(
171 | |                     "Peak memory usage was {} MB, consider monitoring memory usage",
172 | |                     memory_usage_mb
173 | |                 ),
    | |_________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: `debug_assert!(true)` will be optimized out by the compiler
   --> src/services/analyzer/performance.rs:194:17
    |
194 |                 debug_assert!(2.0 >= 0.5, "clamp bounds invalid: max=2.0, min=0.5");
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: remove it
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#assertions_on_constants

warning: this `impl` can be derived
  --> src/services/analyzer/progress.rs:13:1
   |
13 | / impl Default for ProgressDetails {
14 | |     fn default() -> Self {
15 | |         Self {
16 | |             message: None,
...  |
21 | | }
   | |_^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#derivable_impls
help: replace the manual implementation with a derive attribute
   |
7  + #[derive(Default)]
8  ~ pub struct ProgressDetails {
   |

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/progress.rs:125:20
    |
125 |               stage: format!(
    |  ____________________^
126 | |                 "Parsed {}/{} files ({:.1}% success)",
127 | |                 files_processed, total_files, success_rate
128 | |             ),
    | |_____________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/repository.rs:102:21
    |
102 |                     format!("Failed to get remote commit hash: {}", e),
    |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
102 -                     format!("Failed to get remote commit hash: {}", e),
102 +                     format!("Failed to get remote commit hash: {e}"),
    |

warning: length comparison to zero
  --> src/services/analyzer/results.rs:99:12
   |
99 |         if failed_files.len() > 0 {
   |            ^^^^^^^^^^^^^^^^^^^^^^ help: using `!is_empty` is clearer and more explicit: `!failed_files.is_empty()`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#len_zero
   = note: `#[warn(clippy::len_zero)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/results.rs:104:21
    |
104 | /                     format!(
105 | |                         "High failure rate: {:.1}% of files failed to parse",
106 | |                         failure_rate
107 | |                     ),
    | |_____________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: using `clone` on type `AnalysisStatus` which implements the `Copy` trait
   --> src/services/analyzer/results.rs:193:21
    |
193 |             status: result.status.clone(),
    |                     ^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `result.status`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy

warning: `debug_assert!(true)` will be optimized out by the compiler
  --> src/services/analyzer/streaming_processor.rs:67:9
   |
67 |         debug_assert!(50 >= 1, "clamp bounds invalid: max=50, min=1");
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: remove it
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#assertions_on_constants

warning: you seem to use `.enumerate()` and immediately discard the index
  --> src/services/analyzer/streaming_processor.rs:72:36
   |
72 |         for (_batch_idx, batch) in files.chunks(batch_size).enumerate() {
   |                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unused_enumerate_index
   = note: `#[warn(clippy::unused_enumerate_index)]` on by default
help: remove the `.enumerate()` call
   |
72 -         for (_batch_idx, batch) in files.chunks(batch_size).enumerate() {
72 +         for batch in files.chunks(batch_size) {
   |

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/streaming_processor.rs:366:28
    |
366 |                 text: Some(format!("Large file with {} lines", line_count)),
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
366 -                 text: Some(format!("Large file with {} lines", line_count)),
366 +                 text: Some(format!("Large file with {line_count} lines")),
    |

warning: module has the same name as its containing module
   --> src/services/analyzer/pattern_optimization_tests.rs:5:1
    |
5   | / mod pattern_optimization_tests {
6   | |     use super::*;
7   | |
8   | |     #[test]
...   |
102 | | }
    | |_^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#module_inception
    = note: `#[warn(clippy::module_inception)]` on by default

warning: variables can be used directly in the `format!` string
  --> src/services/analyzer/pattern_optimization_tests.rs:40:13
   |
40 |             assert_eq!(original, optimized, "Behavior changed for value: {}", value);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
40 -             assert_eq!(original, optimized, "Behavior changed for value: {}", value);
40 +             assert_eq!(original, optimized, "Behavior changed for value: {value}");
   |

warning: variables can be used directly in the `format!` string
   --> src/services/code_quality_assessor.rs:436:30
    |
436 |             prompt.push_str(&format!("Technical debt: {} minutes\n", technical_debt));
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
436 -             prompt.push_str(&format!("Technical debt: {} minutes\n", technical_debt));
436 +             prompt.push_str(&format!("Technical debt: {technical_debt} minutes\n"));
    |

warning: calling `push_str()` using a single-character string literal
   --> src/services/code_quality_assessor.rs:457:17
    |
457 |                 prompt.push_str("\n");
    |                 ^^^^^^^^^^^^^^^^^^^^^ help: consider using `push` with a character literal: `prompt.push('\n')`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#single_char_add_str

warning: variables can be used directly in the `format!` string
   --> src/services/code_quality_assessor.rs:462:34
    |
462 |                 prompt.push_str(&format!("Code preview:\n```\n{}\n```\n\n", preview));
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
462 -                 prompt.push_str(&format!("Code preview:\n```\n{}\n```\n\n", preview));
462 +                 prompt.push_str(&format!("Code preview:\n```\n{preview}\n```\n\n"));
    |

warning: length comparison to zero
  --> src/services/analyzer/results.rs:99:12
   |
99 |         if failed_files.len() > 0 {
   |            ^^^^^^^^^^^^^^^^^^^^^^ help: using `!is_empty` is clearer and more explicit: `!failed_files.is_empty()`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#len_zero

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/results.rs:373:18
    |
373 |                 &format!("file{}.rs", i),
    |                  ^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
373 -                 &format!("file{}.rs", i),
373 +                 &format!("file{i}.rs"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/analyzer/results.rs:380:28
    |
380 |                 file_path: format!("error{}.rs", i),
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
380 -                 file_path: format!("error{}.rs", i),
380 +                 file_path: format!("error{i}.rs"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/embeddings_enhancement.rs:359:31
    |
359 |                     chunk_id: format!("chunk_{:016x}", i),
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
359 -                     chunk_id: format!("chunk_{:016x}", i),
359 +                     chunk_id: format!("chunk_{i:016x}"),
    |

warning: the loop variable `i` is used to index `vector`
   --> src/services/embeddings_enhancement.rs:401:18
    |
401 |         for i in 0..EMBEDDING_DIMENSION {
    |                  ^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_range_loop
    = note: `#[warn(clippy::needless_range_loop)]` on by default
help: consider using an iterator and enumerate()
    |
401 -         for i in 0..EMBEDDING_DIMENSION {
401 +         for (i, <item>) in vector.iter_mut().enumerate().take(EMBEDDING_DIMENSION) {
    |

warning: variables can be used directly in the `format!` string
   --> src/services/embeddings_enhancement.rs:406:23
    |
406 |             chunk_id: format!("fallback_chunk_{:016x}", idx),
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
406 -             chunk_id: format!("fallback_chunk_{:016x}", idx),
406 +             chunk_id: format!("fallback_chunk_{idx:016x}"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/embeddings_enhancement.rs:481:31
    |
481 |             content.push_str(&format!("\nCode:\n{}", preview));
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
481 -             content.push_str(&format!("\nCode:\n{}", preview));
481 +             content.push_str(&format!("\nCode:\n{preview}"));
    |

warning: variables can be used directly in the `format!` string
   --> src/services/intelligent_documentation.rs:725:26
    |
725 |         prompt.push_str(&format!("Repository URL: {}\n", repository_url));
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
725 -         prompt.push_str(&format!("Repository URL: {}\n", repository_url));
725 +         prompt.push_str(&format!("Repository URL: {repository_url}\n"));
    |

warning: variables can be used directly in the `format!` string
   --> src/services/intelligent_documentation.rs:726:26
    |
726 |         prompt.push_str(&format!("Primary language: {}\n", primary_language));
    |                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
726 -         prompt.push_str(&format!("Primary language: {}\n", primary_language));
726 +         prompt.push_str(&format!("Primary language: {primary_language}\n"));
    |

warning: called `Iterator::last` on a `DoubleEndedIterator`; this will needlessly iterate the entire iterator
   --> src/services/intelligent_documentation.rs:877:25
    |
877 |         let repo_name = repository_url.split('/').last().unwrap_or("Repository");
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^------
    |                                                   |
    |                                                   help: try: `next_back()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#double_ended_iterator_last
    = note: `#[warn(clippy::double_ended_iterator_last)]` on by default

warning: called `Iterator::last` on a `DoubleEndedIterator`; this will needlessly iterate the entire iterator
   --> src/services/intelligent_documentation.rs:888:23
    |
888 |                   name: analysis
    |  _______________________^
889 | |                     .path
890 | |                     .split('/')
891 | |                     .last()
    | |______________________-----^
    |                        |
    |                        help: try: `next_back()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#double_ended_iterator_last

warning: variables can be used directly in the `format!` string
   --> src/services/intelligent_documentation.rs:968:30
    |
968 |                 description: format!("{} repository written in {}", repo_name, primary_language),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
968 -                 description: format!("{} repository written in {}", repo_name, primary_language),
968 +                 description: format!("{repo_name} repository written in {primary_language}"),
    |

warning: you should consider adding a `Default` implementation for `LanguageDetector`
  --> src/services/language_detector.rs:10:5
   |
10 | /     pub fn new() -> Self {
11 | |         Self
12 | |     }
   | |_____^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
   |
9  + impl Default for LanguageDetector {
10 +     fn default() -> Self {
11 +         Self::new()
12 +     }
13 + }
   |

warning: iterating on a map's values
  --> src/services/language_detector.rs:23:34
   |
23 |         let total_lines: usize = languages.iter().map(|(_, stat)| stat.lines()).sum();
   |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `languages.values().map(|stat| stat.lines())`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#iter_kv_map
   = note: `#[warn(clippy::iter_kv_map)]` on by default

warning: you should consider adding a `Default` implementation for `PatternDetector`
  --> src/services/pattern_detector.rs:14:5
   |
14 | /     pub fn new() -> Self {
15 | |         Self {
16 | |             long_method_threshold: 50,
17 | |             complexity_threshold: 10,
18 | |         }
19 | |     }
   | |_____^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
   |
13 + impl Default for PatternDetector {
14 +     fn default() -> Self {
15 +         Self::new()
16 +     }
17 + }
   |

warning: this `map_or` can be simplified
   --> src/services/pattern_detector.rs:358:18
    |
358 |               ) && child.name.as_ref().map_or(false, |n| {
    |  __________________^
359 | |                 n.starts_with("create") || n.starts_with("build") || n.starts_with("make")
360 | |             })
    | |______________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or
help: use is_some_and instead
    |
358 -             ) && child.name.as_ref().map_or(false, |n| {
358 +             ) && child.name.as_ref().is_some_and(|n| {
    |

warning: parameter is only used in recursion
   --> src/services/pattern_detector.rs:391:30
    |
391 |     fn calculate_complexity(&self, node: &AstNode) -> usize {
    |                              ^^^^
    |
note: parameter used here
   --> src/services/pattern_detector.rs:417:27
    |
417 |             complexity += self.calculate_complexity(child);
    |                           ^^^^
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#only_used_in_recursion
    = note: `#[warn(clippy::only_used_in_recursion)]` on by default

warning: parameter is only used in recursion
   --> src/services/pattern_detector.rs:437:34
    |
437 |     fn has_string_concatenation(&self, node: &AstNode) -> bool {
    |                                  ^^^^
    |
note: parameter used here
   --> src/services/pattern_detector.rs:457:16
    |
457 |             if self.has_string_concatenation(child) {
    |                ^^^^
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#only_used_in_recursion

warning: useless use of `format!`
   --> src/services/repository_insights.rs:697:30
    |
697 |             prompt.push_str(&format!("\nQuality Assessment:\n"));
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using `.to_string()`: `"\nQuality Assessment:\n".to_string()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#useless_format
    = note: `#[warn(clippy::useless_format)]` on by default

warning: variables can be used directly in the `format!` string
   --> src/services/repository_insights.rs:716:34
    |
716 |                 prompt.push_str(&format!("Code sample:\n```\n{}\n```\n", preview));
    |                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
716 -                 prompt.push_str(&format!("Code sample:\n```\n{}\n```\n", preview));
716 +                 prompt.push_str(&format!("Code sample:\n```\n{preview}\n```\n"));
    |

warning: this expression creates a reference which is immediately dereferenced by the compiler
  --> src/services/security/compliance/checker.rs:42:58
   |
42 |                         if self.is_likely_false_positive(&rule, &line_content) {
   |                                                          ^^^^^ help: change this to: `rule`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrow
   = note: `#[warn(clippy::needless_borrow)]` on by default

warning: unnecessary `if let` since only the `Ok` variant of the iterator element is used
  --> src/services/security/dependency/parsers/dotnet.rs:30:21
   |
30 |                       for attr in e.attributes() {
   |                       ^           -------------- help: try: `e.attributes().flatten()`
   |  _____________________|
   | |
31 | |                         if let Ok(attr) = attr {
32 | |                             match attr.key.as_ref() {
33 | |                                 b"id" => name = String::from_utf8_lossy(&attr.value).to_string(),
...  |
44 | |                     }
   | |_____________________^
   |
help: ...and remove the `if let` statement in the for loop
  --> src/services/security/dependency/parsers/dotnet.rs:31:25
   |
31 | /                         if let Ok(attr) = attr {
32 | |                             match attr.key.as_ref() {
33 | |                                 b"id" => name = String::from_utf8_lossy(&attr.value).to_string(),
34 | |                                 b"version" => {
...  |
43 | |                         }
   | |_________________________^
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_flatten

warning: unnecessary `if let` since only the `Ok` variant of the iterator element is used
   --> src/services/security/dependency/parsers/dotnet.rs:105:21
    |
105 |                       for attr in e.attributes() {
    |                       ^           -------------- help: try: `e.attributes().flatten()`
    |  _____________________|
    | |
106 | |                         if let Ok(attr) = attr {
107 | |                             match attr.key.as_ref() {
108 | |                                 b"Include" => {
...   |
121 | |                     }
    | |_____________________^
    |
help: ...and remove the `if let` statement in the for loop
   --> src/services/security/dependency/parsers/dotnet.rs:106:25
    |
106 | /                         if let Ok(attr) = attr {
107 | |                             match attr.key.as_ref() {
108 | |                                 b"Include" => {
109 | |                                     name = String::from_utf8_lossy(&attr.value).to_string()
...   |
120 | |                         }
    | |_________________________^
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_flatten

warning: variables can be used directly in the `format!` string
  --> src/services/security/dependency/parsers/go.rs:69:42
   |
69 |                         current_version: format!("v{}", version),
   |                                          ^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
69 -                         current_version: format!("v{}", version),
69 +                         current_version: format!("v{version}"),
   |

warning: variables can be used directly in the `format!` string
  --> src/services/security/dependency/parsers/go.rs:96:42
   |
96 |                         current_version: format!("v{}", version),
   |                                          ^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
96 -                         current_version: format!("v{}", version),
96 +                         current_version: format!("v{version}"),
   |

warning: variables can be used directly in the `format!` string
   --> src/services/security/dependency/parsers/go.rs:146:27
    |
146 |                 let key = format!("{}@{}", module, version);
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
146 -                 let key = format!("{}@{}", module, version);
146 +                 let key = format!("{module}@{version}");
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/dependency/parsers/gradle.rs:115:28
    |
115 |                 let name = format!("{}:{}", group, artifact);
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
115 -                 let name = format!("{}:{}", group, artifact);
115 +                 let name = format!("{group}:{artifact}");
    |

warning: called `Iterator::last` on a `DoubleEndedIterator`; this will needlessly iterate the entire iterator
   --> src/services/security/dependency/parsers/npm.rs:183:28
    |
183 |                 let name = path.split('/').last().unwrap_or(path);
    |                            ^^^^^^^^^^^^^^^^------
    |                                            |
    |                                            help: try: `next_back()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#double_ended_iterator_last

warning: accessing first element with `arr.get(0)`
  --> src/services/security/dependency/parsers/php.rs:98:29
   |
98 | ...                   arr.get(0).and_then(|v| v.as_str()).map(|s| s.to_string())
   |                       ^^^^^^^^^^ help: try: `arr.first()`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#get_first
   = note: `#[warn(clippy::get_first)]` on by default

warning: accessing first element with `arr.get(0)`
   --> src/services/security/dependency/parsers/php.rs:140:29
    |
140 | ...                   arr.get(0).and_then(|v| v.as_str()).map(|s| s.to_string())
    |                       ^^^^^^^^^^ help: try: `arr.first()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#get_first

warning: variables can be used directly in the `format!` string
  --> src/services/security/dependency/parsers/python.rs:49:21
   |
49 |                     format!("{}{}", op, version)
   |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
49 -                     format!("{}{}", op, version)
49 +                     format!("{op}{version}")
   |

warning: this `map_or` can be simplified
  --> src/services/security/dependency/parsers/ruby.rs:56:30
   |
56 |                   let is_dev = current_group
   |  ______________________________^
57 | |                     .as_ref()
58 | |                     .map_or(false, |g| g == "development" || g == "test");
   | |_________________________________________________________________________^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or
help: use is_some_and instead
   |
58 -                     .map_or(false, |g| g == "development" || g == "test");
58 +                     .is_some_and(|g| g == "development" || g == "test");
   |

warning: using `clone` on type `SecuritySeverity` which implements the `Copy` trait
  --> src/services/security/dependency/scanner.rs:57:39
   |
57 | ...                   severity: vuln_info.severity.clone(),
   |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `vuln_info.severity`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy

warning: using `clone` on type `SecuritySeverity` which implements the `Copy` trait
  --> src/services/security/secrets/detector.rs:53:35
   |
53 |                         severity: pattern.severity.clone(),
   |                                   ^^^^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `pattern.severity`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy

warning: use of `or_insert_with` to construct default value
  --> src/services/security/threat/modeler.rs:80:36
   |
80 |             vuln_groups.entry(key).or_insert_with(Vec::new).push(vuln);
   |                                    ^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `or_default()`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unwrap_or_default

warning: variables can be used directly in the `format!` string
  --> src/services/security/threat/modeler.rs:98:30
   |
98 |                 threat_name: format!("{} Exploitation Threat", vuln_type),
   |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
98 -                 threat_name: format!("{} Exploitation Threat", vuln_type),
98 +                 threat_name: format!("{vuln_type} Exploitation Threat"),
   |

warning: use of `or_insert_with` to construct default value
   --> src/services/security/threat/modeler.rs:147:18
    |
147 |                 .or_insert_with(Vec::new)
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `or_default()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unwrap_or_default

warning: using `clone` on type `SecuritySeverity` which implements the `Copy` trait
   --> src/services/security/threat/modeler.rs:146:24
    |
146 |                 .entry(dep_vuln.severity.clone())
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `dep_vuln.severity`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy

warning: variables can be used directly in the `format!` string
   --> src/services/security/threat/modeler.rs:160:30
    |
160 |                 threat_name: format!("Supply Chain Attack via {} Dependencies", severity),
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
160 -                 threat_name: format!("Supply Chain Attack via {} Dependencies", severity),
160 +                 threat_name: format!("Supply Chain Attack via {severity} Dependencies"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/threat/modeler.rs:302:9
    |
302 | /         format!(
303 | |             "Identified {} instances of {} vulnerabilities that could be exploited by attackers to compromise the system",
304 | |             count, vuln_type
305 | |         )
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: writing `&mut Vec` instead of `&mut [_]` involves a new object where a slice will do
   --> src/services/security/threat/modeler.rs:383:43
    |
383 |     fn prioritize_threats(&self, threats: &mut Vec<ThreatModel>) {
    |                                           ^^^^^^^^^^^^^^^^^^^^^ help: change this to: `&mut [ThreatModel]`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#ptr_arg
    = note: `#[warn(clippy::ptr_arg)]` on by default

warning: variables can be used directly in the `format!` string
  --> src/services/security/types.rs:63:13
   |
63 |             format!("Failed to compile pattern '{}': {}", name, e)
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
63 -             format!("Failed to compile pattern '{}': {}", name, e)
63 +             format!("Failed to compile pattern '{name}': {e}")
   |

warning: using `clone` on type `SecuritySeverity` which implements the `Copy` trait
  --> src/services/security/vulnerability/detector.rs:79:35
   |
79 |                         severity: pattern.severity.clone(),
   |                                   ^^^^^^^^^^^^^^^^^^^^^^^^ help: try removing the `clone` call: `pattern.severity`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#clone_on_copy

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/detector.rs:144:38
    |
144 |                         description: format!("Potentially dangerous function call: {}", name),
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
144 -                         description: format!("Potentially dangerous function call: {}", name),
144 +                         description: format!("Potentially dangerous function call: {name}"),
    |

warning: called `Iterator::last` on a `DoubleEndedIterator`; this will needlessly iterate the entire iterator
   --> src/services/security/vulnerability/ml_classifier.rs:351:30
    |
351 |         let file_extension = file_analysis.path.split('.').last().unwrap_or("");
    |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^------
    |                                                            |
    |                                                            help: try: `next_back()`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#double_ended_iterator_last

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:449:38
    |
449 |                           description: format!(
    |  ______________________________________^
450 | |                             "{}: Potential SQL injection vulnerability",
451 | |                             description
452 | |                         ),
    | |_________________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:494:38
    |
494 |                         description: format!("{}: Potential XSS vulnerability", description),
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
494 -                         description: format!("{}: Potential XSS vulnerability", description),
494 +                         description: format!("{description}: Potential XSS vulnerability"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:536:38
    |
536 |                         description: format!("{}: Potential command injection", description),
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
536 -                         description: format!("{}: Potential command injection", description),
536 +                         description: format!("{description}: Potential command injection"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:574:38
    |
574 |                         description: format!("{}: Potential path traversal", description),
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
574 -                         description: format!("{}: Potential path traversal", description),
574 +                         description: format!("{description}: Potential path traversal"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:622:38
    |
622 |                         description: format!("{}: Insecure deserialization", description),
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
622 -                         description: format!("{}: Insecure deserialization", description),
622 +                         description: format!("{description}: Insecure deserialization"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:685:38
    |
685 |                         description: format!("{}: Weak cryptography", description),
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
685 -                         description: format!("{}: Weak cryptography", description),
685 +                         description: format!("{description}: Weak cryptography"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:728:38
    |
728 |                         description: format!("{}: Authentication issue", description),
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
728 -                         description: format!("{}: Authentication issue", description),
728 +                         description: format!("{description}: Authentication issue"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:780:38
    |
780 |                         description: format!("{}: Security misconfiguration", description),
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
780 -                         description: format!("{}: Security misconfiguration", description),
780 +                         description: format!("{description}: Security misconfiguration"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:847:38
    |
847 |                         description: format!("{}: Potential race condition", description),
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
847 -                         description: format!("{}: Potential race condition", description),
847 +                         description: format!("{description}: Potential race condition"),
    |

warning: variables can be used directly in the `format!` string
   --> src/services/security/vulnerability/ml_classifier.rs:885:38
    |
885 |                         description: format!("{}: Buffer overflow risk", description),
    |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
885 -                         description: format!("{}: Buffer overflow risk", description),
885 +                         description: format!("{description}: Buffer overflow risk"),
    |

warning: you should consider adding a `Default` implementation for `SecurityAnalyzer`
  --> src/services/security/mod.rs:46:5
   |
46 | /     pub fn new() -> Self {
47 | |         Self {
48 | |             vulnerability_detector: VulnerabilityDetector::new(),
49 | |             dependency_scanner: DependencyScanner::new(),
...  |
55 | |     }
   | |_____^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
   |
45 + impl Default for SecurityAnalyzer {
46 +     fn default() -> Self {
47 +         Self::new()
48 +     }
49 + }
   |

warning: this expression creates a reference which is immediately dereferenced by the compiler
   --> src/services/semantic_search.rs:241:37
    |
241 |             if !self.passes_filters(&query, &file_analysis) {
    |                                     ^^^^^^ help: change this to: `query`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrow

warning: this `else { if .. }` block can be collapsed
   --> src/services/semantic_search.rs:453:20
    |
453 |               } else {
    |  ____________________^
454 | |                 if let Some(analysis) = file_analysis {
455 | |                     if query.language_filters.contains(&analysis.language) {
456 | |                         1.0
...   |
463 | |             },
    | |_____________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#collapsible_else_if
    = note: `#[warn(clippy::collapsible_else_if)]` on by default
help: collapse nested if block
    |
453 ~             } else if let Some(analysis) = file_analysis {
454 +                 if query.language_filters.contains(&analysis.language) {
455 +                     1.0
456 +                 } else {
457 +                     0.5
458 +                 }
459 +             } else {
460 +                 0.5
461 ~             },
    |

warning: `debug_assert!(true)` will be optimized out by the compiler
   --> src/services/semantic_search.rs:607:9
    |
607 |         debug_assert!(1.0 >= 0.0, "clamp bounds invalid: max=1.0, min=0.0");
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: remove it
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#assertions_on_constants

warning: variables can be used directly in the `format!` string
   --> src/services/semantic_search.rs:613:9
    |
613 | ...   format!("This code is related to your query '{}' based on content analysis. The code contains relevant functionality that matches your search criteria.", query)
    |       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
613 -         format!("This code is related to your query '{}' based on content analysis. The code contains relevant functionality that matches your search criteria.", query)
613 +         format!("This code is related to your query '{query}' based on content analysis. The code contains relevant functionality that matches your search criteria.")
    |

warning: `assert!(true)` will be optimized out by the compiler
   --> src/services/security/dependency/parsers/go.rs:182:9
    |
182 |         assert!(true);
    |         ^^^^^^^^^^^^^
    |
    = help: remove it
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#assertions_on_constants

warning: variables can be used directly in the `format!` string
   --> src/storage/cache.rs:393:29
    |
393 |             let cache_key = format!("{}{}:main", ANALYSIS_CACHE_PREFIX, repo_url);
    |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
393 -             let cache_key = format!("{}{}:main", ANALYSIS_CACHE_PREFIX, repo_url);
393 +             let cache_key = format!("{ANALYSIS_CACHE_PREFIX}{repo_url}:main");
    |

warning: variables can be used directly in the `format!` string
  --> src/storage/pubsub.rs:37:30
   |
37 |             let topic_path = format!("projects/{}/topics/{}", project_id, topic_name);
   |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
37 -             let topic_path = format!("projects/{}/topics/{}", project_id, topic_name);
37 +             let topic_path = format!("projects/{project_id}/topics/{topic_name}");
   |

warning: variables can be used directly in the `format!` string
   --> src/storage/pubsub.rs:206:39
    |
206 |                           warnings.push(format!(
    |  _______________________________________^
207 | |                             "Cannot verify topic {} due to missing pubsub.topics.get permission",
208 | |                             name
209 | |                         ));
    | |_________________________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: variables can be used directly in the `format!` string
   --> src/services/semantic_search.rs:808:9
    |
808 | /         assert!(
809 | |             (similarity - 1.0).abs() < 1e-6,
810 | |             "Expected similarity close to 1.0, got {}",
811 | |             similarity
812 | |         );
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: module has the same name as its containing module
 --> src/storage/mod.rs:7:1
  |
7 | pub mod storage;
  | ^^^^^^^^^^^^^^^^
  |
  = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#module_inception
  = note: `#[warn(clippy::module_inception)]` on by default

warning: module has the same name as its containing module
 --> src/storage/mod.rs:7:1
  |
7 | pub mod storage;
  | ^^^^^^^^^^^^^^^^
  |
  = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#module_inception

warning: `analysis-engine` (lib) generated 147 warnings (run `cargo clippy --fix --lib -p analysis-engine` to apply 129 suggestions)
warning: `analysis-engine` (lib test) generated 171 warnings (145 duplicates) (run `cargo clippy --fix --lib -p analysis-engine --tests` to apply 19 suggestions)
warning: unused import: `uuid::Uuid`
  --> tests/comprehensive_test_framework.rs:20:5
   |
20 | use uuid::Uuid;
   |     ^^^^^^^^^^
   |
   = note: `#[warn(unused_imports)]` on by default

warning: unused import: `std::collections::HashMap`
 --> tests/test_utils.rs:3:5
  |
3 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0061]: this function takes 1 argument but 0 arguments were supplied
   --> benches/analysis_bench.rs:120:18
    |
120 |     let parser = TreeSitterParser::new().unwrap();
    |                  ^^^^^^^^^^^^^^^^^^^^^-- argument #1 of type `std::sync::Arc<analysis_engine::config::ServiceConfig>` is missing
    |
note: associated function defined here
   --> /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/src/parser/mod.rs:60:12
    |
60  |     pub fn new(config: Arc<ServiceConfig>) -> Result<Self> {
    |            ^^^
help: provide the argument
    |
120 |     let parser = TreeSitterParser::new(/* std::sync::Arc<analysis_engine::config::ServiceConfig> */).unwrap();
    |                                        ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

warning: variables can be used directly in the `format!` string
 --> src/bin/test_language_registry.rs:9:5
  |
9 |     println!("Languages: {:?}\n", languages);
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  |
  = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
  = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
  |
9 -     println!("Languages: {:?}\n", languages);
9 +     println!("Languages: {languages:?}\n");
  |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_language_registry.rs:18:17
   |
18 |                 println!("✓ {} - loaded successfully", lang_name);
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
18 -                 println!("✓ {} - loaded successfully", lang_name);
18 +                 println!("✓ {lang_name} - loaded successfully");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_language_registry.rs:22:17
   |
22 |                 println!("✗ {} - FAILED to load", lang_name);
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
22 -                 println!("✗ {} - FAILED to load", lang_name);
22 +                 println!("✗ {lang_name} - FAILED to load");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_language_registry.rs:31:9
   |
31 |         println!("Failed: {:?}", failed_languages);
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
31 -         println!("Failed: {:?}", failed_languages);
31 +         println!("Failed: {failed_languages:?}");
   |

warning: redundant pattern matching, consider using `is_some()`
  --> src/bin/test_language_registry.rs:39:16
   |
39 |         if let Some(_) = get_language(lang) {
   |         -------^^^^^^^--------------------- help: try: `if get_language(lang).is_some()`
   |
   = note: this will change drop order of the result, as well as all temporaries
   = note: add `#[allow(clippy::redundant_pattern_matching)]` if this is important
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#redundant_pattern_matching
   = note: `#[warn(clippy::redundant_pattern_matching)]` on by default

warning: variables can be used directly in the `format!` string
  --> src/bin/test_language_registry.rs:40:13
   |
40 |             println!("{}: Available ✓", lang);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
40 -             println!("{}: Available ✓", lang);
40 +             println!("{lang}: Available ✓");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_language_registry.rs:42:13
   |
42 |             println!("{}: Not available ✗", lang);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
42 -             println!("{}: Not available ✗", lang);
42 +             println!("{lang}: Not available ✗");
   |

error[E0061]: this function takes 1 argument but 0 arguments were supplied
   --> benches/analysis_bench.rs:175:18
    |
175 |     let parser = TreeSitterParser::new().unwrap();
    |                  ^^^^^^^^^^^^^^^^^^^^^-- argument #1 of type `std::sync::Arc<analysis_engine::config::ServiceConfig>` is missing
    |
note: associated function defined here
   --> /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/src/parser/mod.rs:60:12
    |
60  |     pub fn new(config: Arc<ServiceConfig>) -> Result<Self> {
    |            ^^^
help: provide the argument
    |
175 |     let parser = TreeSitterParser::new(/* std::sync::Arc<analysis_engine::config::ServiceConfig> */).unwrap();
    |                                        ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

error[E0061]: this function takes 1 argument but 0 arguments were supplied
   --> benches/analysis_bench.rs:239:18
    |
239 |     let parser = TreeSitterParser::new().unwrap();
    |                  ^^^^^^^^^^^^^^^^^^^^^-- argument #1 of type `std::sync::Arc<analysis_engine::config::ServiceConfig>` is missing
    |
note: associated function defined here
   --> /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/src/parser/mod.rs:60:12
    |
60  |     pub fn new(config: Arc<ServiceConfig>) -> Result<Self> {
    |            ^^^
help: provide the argument
    |
239 |     let parser = TreeSitterParser::new(/* std::sync::Arc<analysis_engine::config::ServiceConfig> */).unwrap();
    |                                        ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

warning: `analysis-engine` (bin "test_language_registry" test) generated 7 warnings (run `cargo clippy --fix --bin "test_language_registry" --tests` to apply 6 suggestions)
warning: variables can be used directly in the `format!` string
  --> src/bin/test_parsers.rs:14:13
   |
14 |             println!("Failed to create parser: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
14 -             println!("Failed to create parser: {}", e);
14 +             println!("Failed to create parser: {e}");
   |

warning: variables can be used directly in the `format!` string
   --> src/bin/test_parsers.rs:171:9
    |
171 |         print!("Testing {} parser... ", lang);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
171 -         print!("Testing {} parser... ", lang);
171 +         print!("Testing {lang} parser... ");
    |

warning: variables can be used directly in the `format!` string
   --> src/bin/test_parsers.rs:199:13
    |
199 |             println!("✗ FAILED - couldn't write temp file: {}", e);
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
199 -             println!("✗ FAILED - couldn't write temp file: {}", e);
199 +             println!("✗ FAILED - couldn't write temp file: {e}");
    |

warning: variables can be used directly in the `format!` string
   --> src/bin/test_parsers.rs:216:17
    |
216 |                 println!("✗ FAILED - {}", e);
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
216 -                 println!("✗ FAILED - {}", e);
216 +                 println!("✗ FAILED - {e}");
    |

error[E0432]: unresolved import `analysis_engine::services::parser`
 --> tests/edge_cases.rs:8:32
  |
8 | use analysis_engine::services::parser::{Parser, ParserConfig};
  |                                ^^^^^^ could not find `parser` in `services`

warning: unused imports: `AnalysisRequest` and `FileAnalysis`
 --> tests/edge_cases.rs:7:31
  |
7 | use analysis_engine::models::{AnalysisRequest, FileAnalysis};
  |                               ^^^^^^^^^^^^^^^  ^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

error[E0061]: this function takes 1 argument but 0 arguments were supplied
   --> benches/analysis_bench.rs:289:18
    |
289 |     let parser = TreeSitterParser::new().unwrap();
    |                  ^^^^^^^^^^^^^^^^^^^^^-- argument #1 of type `std::sync::Arc<analysis_engine::config::ServiceConfig>` is missing
    |
note: associated function defined here
   --> /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/src/parser/mod.rs:60:12
    |
60  |     pub fn new(config: Arc<ServiceConfig>) -> Result<Self> {
    |            ^^^
help: provide the argument
    |
289 |     let parser = TreeSitterParser::new(/* std::sync::Arc<analysis_engine::config::ServiceConfig> */).unwrap();
    |                                        ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

warning: `analysis-engine` (bin "test_parsers" test) generated 4 warnings (run `cargo clippy --fix --bin "test_parsers" --tests` to apply 4 suggestions)
For more information about this error, try `rustc --explain E0061`.
error: could not compile `analysis-engine` (bench "analysis_bench") due to 4 previous errors
warning: build failed, waiting for other jobs to finish...
warning: `analysis-engine` (bin "test_parsers") generated 4 warnings (4 duplicates)
warning: variables can be used directly in the `format!` string
  --> src/bin/test_ai_services.rs:54:13
   |
54 |             eprintln!("✗ Failed to initialize AI services: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
54 -             eprintln!("✗ Failed to initialize AI services: {}", e);
54 +             eprintln!("✗ Failed to initialize AI services: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_ai_services.rs:65:13
   |
65 |             println!("✗ AI Pattern Detection test failed: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
65 -             println!("✗ AI Pattern Detection test failed: {}", e);
65 +             println!("✗ AI Pattern Detection test failed: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_ai_services.rs:78:13
   |
78 |             println!("✗ Code Quality Assessment test failed: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
78 -             println!("✗ Code Quality Assessment test failed: {}", e);
78 +             println!("✗ Code Quality Assessment test failed: {e}");
   |

warning: variables can be used directly in the `format!` string
  --> src/bin/test_ai_services.rs:90:13
   |
90 |             println!("✗ Semantic Search test failed: {}", e);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
90 -             println!("✗ Semantic Search test failed: {}", e);
90 +             println!("✗ Semantic Search test failed: {e}");
   |

warning: variables can be used directly in the `format!` string
   --> src/bin/test_ai_services.rs:102:13
    |
102 |             println!("✗ Repository Insights test failed: {}", e);
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
102 -             println!("✗ Repository Insights test failed: {}", e);
102 +             println!("✗ Repository Insights test failed: {e}");
    |

warning: variables can be used directly in the `format!` string
  --> tests/integration_tests.rs:27:38
   |
27 |         let response = reqwest::get(&format!("{}/health", BASE_URL))
   |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
   = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
   |
27 -         let response = reqwest::get(&format!("{}/health", BASE_URL))
27 +         let response = reqwest::get(&format!("{BASE_URL}/health"))
   |

warning: variables can be used directly in the `format!` string
  --> tests/integration_tests.rs:41:38
   |
41 |         let response = reqwest::get(&format!("{}/api/v1/languages", BASE_URL))
   |                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
41 -         let response = reqwest::get(&format!("{}/api/v1/languages", BASE_URL))
41 +         let response = reqwest::get(&format!("{BASE_URL}/api/v1/languages"))
   |

warning: `analysis-engine` (bin "test_ai_services" test) generated 5 warnings (run `cargo clippy --fix --bin "test_ai_services" --tests` to apply 5 suggestions)
warning: the borrowed expression implements the required traits
  --> tests/integration_tests.rs:85:19
   |
85 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
   |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis", BASE_URL)`
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args
   = note: `#[warn(clippy::needless_borrows_for_generic_args)]` on by default

warning: variables can be used directly in the `format!` string
  --> tests/integration_tests.rs:85:20
   |
85 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
   |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
   |
85 -             .post(&format!("{}/api/v1/analysis", BASE_URL))
85 +             .post(&format!("{BASE_URL}/api/v1/analysis"))
   |

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:100:9
    |
100 |         println!("Created analysis: {}", analysis_id);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
100 -         println!("Created analysis: {}", analysis_id);
100 +         println!("Created analysis: {analysis_id}");
    |

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:141:9
    |
141 | /         assert!(
142 | |             successful_count >= 50,
143 | |             "Expected at least 50 successful requests, got {}",
144 | |             successful_count
145 | |         );
    | |_________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: unused import: `analysis_engine::config::ServiceConfig`
 --> src/bin/load_test.rs:7:5
  |
7 | use analysis_engine::config::ServiceConfig;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `std::sync::Arc`
 --> src/bin/load_test.rs:9:5
  |
9 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused imports: `api::AppState`, `config::ServiceConfig`, and `parser::TreeSitterParser`
  --> src/bin/../../tests/load_test.rs:7:5
   |
7  |     api::AppState,
   |     ^^^^^^^^^^^^^
8  |     config::ServiceConfig,
   |     ^^^^^^^^^^^^^^^^^^^^^
9  |     models::{AnalysisRequestV2 as AnalysisRequest, AnalysisResultV2 as AnalysisResult},
10 |     parser::TreeSitterParser,
   |     ^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: if this is a test module, consider adding a `#[cfg(test)]` to the containing module
  --> src/bin/load_test.rs:14:1
   |
14 | mod load_test;
   | ^^^^^^^^^^^^^^

warning: unused import: `error`
  --> src/bin/../../tests/load_test.rs:16:15
   |
16 | use tracing::{error, info, warn};
   |               ^^^^^
   |
help: if this is a test module, consider adding a `#[cfg(test)]` to the containing module
  --> src/bin/load_test.rs:14:1
   |
14 | mod load_test;
   | ^^^^^^^^^^^^^^

warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:191:19
    |
191 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis", BASE_URL)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:191:20
    |
191 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
191 -             .post(&format!("{}/api/v1/analysis", BASE_URL))
191 +             .post(&format!("{BASE_URL}/api/v1/analysis"))
    |

warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:205:18
    |
205 |             .get(&format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id))
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:205:19
    |
205 |             .get(&format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id))
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
205 -             .get(&format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id))
205 +             .get(&format!("{BASE_URL}/api/v1/analysis/{analysis_id}"))
    |

warning: `analysis-engine` (bin "test_language_registry") generated 7 warnings (7 duplicates)
warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:217:18
    |
217 |             .get(&format!("{}/api/v1/analyses", BASE_URL))
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analyses", BASE_URL)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:217:19
    |
217 |             .get(&format!("{}/api/v1/analyses", BASE_URL))
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
217 -             .get(&format!("{}/api/v1/analyses", BASE_URL))
217 +             .get(&format!("{BASE_URL}/api/v1/analyses"))
    |

warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:245:19
    |
245 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis", BASE_URL)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:245:20
    |
245 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
245 -             .post(&format!("{}/api/v1/analysis", BASE_URL))
245 +             .post(&format!("{BASE_URL}/api/v1/analysis"))
    |

warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:261:22
    |
261 |                 .get(&format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id))
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:261:23
    |
261 |                 .get(&format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id))
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
261 -                 .get(&format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id))
261 +                 .get(&format!("{BASE_URL}/api/v1/analysis/{analysis_id}"))
    |

warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:293:18
    |
293 |             .get(&format!("{}/api/v1/analyses", BASE_URL))
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analyses", BASE_URL)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:293:19
    |
293 |             .get(&format!("{}/api/v1/analyses", BASE_URL))
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
293 -             .get(&format!("{}/api/v1/analyses", BASE_URL))
293 +             .get(&format!("{BASE_URL}/api/v1/analyses"))
    |

warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:310:22
    |
310 |                 .get(&format!("{}/api/v1/languages", BASE_URL))
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/languages", BASE_URL)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:310:23
    |
310 |                 .get(&format!("{}/api/v1/languages", BASE_URL))
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
310 -                 .get(&format!("{}/api/v1/languages", BASE_URL))
310 +                 .get(&format!("{BASE_URL}/api/v1/languages"))
    |

warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:341:19
    |
341 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis", BASE_URL)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:341:20
    |
341 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
341 -             .post(&format!("{}/api/v1/analysis", BASE_URL))
341 +             .post(&format!("{BASE_URL}/api/v1/analysis"))
    |

warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:357:18
    |
357 |             .get(&format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id))
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:357:19
    |
357 |             .get(&format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id))
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
357 -             .get(&format!("{}/api/v1/analysis/{}", BASE_URL, analysis_id))
357 +             .get(&format!("{BASE_URL}/api/v1/analysis/{analysis_id}"))
    |

error[E0277]: the trait bound `analysis_engine::models::security::SecurityAnalysisRequest: std::default::Default` is not satisfied
   --> tests/edge_cases.rs:354:32
    |
354 |         let security_request = Default::default();
    |                                ^^^^^^^^^^^^^^^^^^ the trait `std::default::Default` is not implemented for `analysis_engine::models::security::SecurityAnalysisRequest`

warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:386:19
    |
386 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis", BASE_URL)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:386:20
    |
386 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
386 -             .post(&format!("{}/api/v1/analysis", BASE_URL))
386 +             .post(&format!("{BASE_URL}/api/v1/analysis"))
    |

warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:415:19
    |
415 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis", BASE_URL)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:415:20
    |
415 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
415 -             .post(&format!("{}/api/v1/analysis", BASE_URL))
415 +             .post(&format!("{BASE_URL}/api/v1/analysis"))
    |

error[E0277]: the trait bound `analysis_engine::models::security::SecurityAnalysisRequest: std::default::Default` is not satisfied
   --> tests/edge_cases.rs:379:32
    |
379 |         let security_request = Default::default();
    |                                ^^^^^^^^^^^^^^^^^^ the trait `std::default::Default` is not implemented for `analysis_engine::models::security::SecurityAnalysisRequest`

warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:444:19
    |
444 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis", BASE_URL)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:444:20
    |
444 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
444 -             .post(&format!("{}/api/v1/analysis", BASE_URL))
444 +             .post(&format!("{BASE_URL}/api/v1/analysis"))
    |

warning: the borrowed expression implements the required traits
   --> tests/integration_tests.rs:459:19
    |
459 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis", BASE_URL)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:459:20
    |
459 |             .post(&format!("{}/api/v1/analysis", BASE_URL))
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
459 -             .post(&format!("{}/api/v1/analysis", BASE_URL))
459 +             .post(&format!("{BASE_URL}/api/v1/analysis"))
    |

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:468:9
    |
468 |         println!("First analysis took: {:?}", duration1);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
468 -         println!("First analysis took: {:?}", duration1);
468 +         println!("First analysis took: {duration1:?}");
    |

warning: variables can be used directly in the `format!` string
   --> tests/integration_tests.rs:469:9
    |
469 |         println!("Second analysis took: {:?}", duration2);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
469 -         println!("Second analysis took: {:?}", duration2);
469 +         println!("Second analysis took: {duration2:?}");
    |

Some errors have detailed explanations: E0277, E0432.
For more information about an error, try `rustc --explain E0277`.
warning: unused variable: `config`
   --> src/bin/../../tests/load_test.rs:135:22
    |
135 |     pub async fn new(config: LoadTestConfig) -> anyhow::Result<Self> {
    |                      ^^^^^^ help: if this is intentional, prefix it with an underscore: `_config`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: `analysis-engine` (test "edge_cases") generated 1 warning
error: could not compile `analysis-engine` (test "edge_cases") due to 3 previous errors; 1 warning emitted
warning: `analysis-engine` (test "integration_tests") generated 34 warnings (run `cargo clippy --fix --test "integration_tests"` to apply 34 suggestions)
error: this comparison involving the minimum or maximum element for this type contains a case that is always true or always false
   --> tests/contract_validation_test.rs:284:13
    |
284 |     assert!(metrics_json["total_processing_ms"].as_u64().unwrap() >= 0);
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: because `0` is the minimum value for this type, this comparison is always true
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#absurd_extreme_comparisons
    = note: `#[deny(clippy::absurd_extreme_comparisons)]` on by default

error: this comparison involving the minimum or maximum element for this type contains a case that is always true or always false
   --> tests/contract_validation_test.rs:285:13
    |
285 |     assert!(metrics_json["memory_peak_mb"].as_u64().unwrap() >= 0);
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: because `0` is the minimum value for this type, this comparison is always true
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#absurd_extreme_comparisons

error[E0432]: unresolved import `analysis_engine::services::security_analyzer`
 --> tests/security_intelligence_tests.rs:3:32
  |
3 | use analysis_engine::services::security_analyzer::SecurityAnalyzer;
  |                                ^^^^^^^^^^^^^^^^^ could not find `security_analyzer` in `services`

warning: unused import: `chrono::Utc`
 --> tests/security_intelligence_tests.rs:4:5
  |
4 | use chrono::Utc;
  |     ^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `std::collections::HashMap`
 --> tests/security_intelligence_tests.rs:5:5
  |
5 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: comparison is useless due to type limits
   --> tests/contract_validation_test.rs:284:13
    |
284 |     assert!(metrics_json["total_processing_ms"].as_u64().unwrap() >= 0);
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = note: `#[warn(unused_comparisons)]` on by default

warning: comparison is useless due to type limits
   --> tests/contract_validation_test.rs:285:13
    |
285 |     assert!(metrics_json["memory_peak_mb"].as_u64().unwrap() >= 0);
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: `analysis-engine` (test "contract_validation_test") generated 2 warnings
error: could not compile `analysis-engine` (test "contract_validation_test") due to 2 previous errors; 2 warnings emitted
warning: unused variable: `service`
   --> src/bin/../../tests/load_test.rs:273:9
    |
273 |         service: Arc<AnalysisService>,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_service`

warning: unused variable: `request`
   --> src/bin/../../tests/load_test.rs:274:9
    |
274 |         request: AnalysisRequest,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_request`

warning: fields `metrics_collector`, `mock_services`, and `database_test_manager` are never read
  --> tests/comprehensive_test_framework.rs:50:5
   |
46 | pub struct TestFramework {
   |            ------------- fields in this struct
...
50 |     metrics_collector: Arc<analysis_engine::metrics::MetricsCollector>,
   |     ^^^^^^^^^^^^^^^^^
51 |     mock_services: Arc<mock_services::MockServices>,
   |     ^^^^^^^^^^^^^
52 |     database_test_manager: Arc<database_testing::DatabaseTestManager>,
   |     ^^^^^^^^^^^^^^^^^^^^^
   |
   = note: `#[warn(dead_code)]` on by default

warning: you should consider adding a `Default` implementation for `DatabaseTestManager`
  --> tests/database_testing.rs:8:5
   |
8  | /     pub fn new() -> Self {
9  | |         Self {}
10 | |     }
   | |_____^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
   = note: `#[warn(clippy::new_without_default)]` on by default
help: try adding this
   |
7  + impl Default for DatabaseTestManager {
8  +     fn default() -> Self {
9  +         Self::new()
10 +     }
11 + }
   |

warning: fields `name`, `expected_loc`, and `expected_files` are never read
  --> src/bin/../../tests/load_test.rs:36:9
   |
35 | pub struct TestRepository {
   |            -------------- fields in this struct
36 |     pub name: String,
   |         ^^^^
37 |     pub url: String,
38 |     pub expected_loc: usize,
   |         ^^^^^^^^^^^^
39 |     pub expected_files: usize,
   |         ^^^^^^^^^^^^^^
   |
   = note: `TestRepository` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis
   = note: `#[warn(dead_code)]` on by default

warning: you should consider adding a `Default` implementation for `MockServices`
  --> tests/mock_services.rs:8:5
   |
8  | /     pub fn new() -> Self {
9  | |         Self {}
10 | |     }
   | |_____^
   |
   = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
   |
7  + impl Default for MockServices {
8  +     fn default() -> Self {
9  +         Self::new()
10 +     }
11 + }
   |

For more information about this error, try `rustc --explain E0432`.
warning: `analysis-engine` (test "security_intelligence_tests") generated 2 warnings
error: could not compile `analysis-engine` (test "security_intelligence_tests") due to 1 previous error; 2 warnings emitted
warning: the borrowed expression implements the required traits
   --> tests/comprehensive_test_framework.rs:323:19
    |
323 |             .post(&format!("{}/api/v1/analysis", self.base_url))
    |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis", self.base_url)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args
    = note: `#[warn(clippy::needless_borrows_for_generic_args)]` on by default

warning: the borrowed expression implements the required traits
   --> tests/comprehensive_test_framework.rs:403:27
    |
403 |                     .post(&format!("{}/api/v1/analysis", base_url))
    |                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis", base_url)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> tests/comprehensive_test_framework.rs:403:28
    |
403 |                     .post(&format!("{}/api/v1/analysis", base_url))
    |                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
    = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
    |
403 -                     .post(&format!("{}/api/v1/analysis", base_url))
403 +                     .post(&format!("{base_url}/api/v1/analysis"))
    |

warning: variables can be used directly in the `format!` string
   --> tests/comprehensive_test_framework.rs:428:22
    |
428 |               details: format!(
    |  ______________________^
429 | |                 "Successfully handled {}/55 concurrent analyses",
430 | |                 successful_count
431 | |             ),
    | |_____________^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args

warning: the borrowed expression implements the required traits
   --> tests/comprehensive_test_framework.rs:470:23
    |
470 |                 .post(&format!("{}/api/v1/analysis", self.base_url))
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: change this to: `format!("{}/api/v1/analysis", self.base_url)`
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args

warning: variables can be used directly in the `format!` string
   --> src/bin/../../tests/load_test.rs:194:42
    |
194 |                         let error_type = format!("{:?}", e);
    |                                          ^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
    = note: `#[warn(clippy::uninlined_format_args)]` on by default
help: change this to
    |
194 -                         let error_type = format!("{:?}", e);
194 +                         let error_type = format!("{e:?}");
    |

warning: variables can be used directly in the `format!` string
   --> tests/comprehensive_test_framework.rs:644:22
    |
644 |             details: format!("{} test passed", name),
    |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
644 -             details: format!("{} test passed", name),
644 +             details: format!("{name} test passed"),
    |

warning: you should consider adding a `Default` implementation for `TestSuiteReport`
   --> tests/comprehensive_test_framework.rs:686:5
    |
686 | /     pub fn new() -> Self {
687 | |         Self {
688 | |             phases: Vec::new(),
689 | |             overall_passed: true,
...   |
693 | |     }
    | |_____^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
    |
685 + impl Default for TestSuiteReport {
686 +     fn default() -> Self {
687 +         Self::new()
688 +     }
689 + }
    |

warning: you should consider adding a `Default` implementation for `MemoryMonitor`
   --> tests/comprehensive_test_framework.rs:749:5
    |
749 | /     pub fn new() -> Self {
750 | |         Self {}
751 | |     }
    | |_____^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#new_without_default
help: try adding this
    |
748 + impl Default for MemoryMonitor {
749 +     fn default() -> Self {
750 +         Self::new()
751 +     }
752 + }
    |

warning: variables can be used directly in the `format!` string
   --> src/bin/../../tests/load_test.rs:440:35
    |
440 |             .map(|(error, count)| format!("- {}: {}", error, count))
    |                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = help: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args
help: change this to
    |
440 -             .map(|(error, count)| format!("- {}: {}", error, count))
440 +             .map(|(error, count)| format!("- {error}: {count}"))
    |

warning: `analysis-engine` (test "comprehensive_test_framework") generated 13 warnings (run `cargo clippy --fix --test "comprehensive_test_framework"` to apply 12 suggestions)
warning: `analysis-engine` (bin "load_test") generated 10 warnings (run `cargo clippy --fix --bin "load_test"` to apply 6 suggestions)
