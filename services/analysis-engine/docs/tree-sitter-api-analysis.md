# Tree-Sitter API Analysis Report

## Executive Summary

The tree-sitter language crates have inconsistent APIs depending on their version. This analysis documents the patterns found and provides a solution for creating a unified abstraction layer.

## Key Findings

### 1. API Patterns

There are two distinct API patterns used by tree-sitter language crates:

#### Pattern A: Safe `language()` Function (Older Crates < 0.20)
```rust
// Direct function call, no unsafe required
let language = tree_sitter_yaml::language();
```

Crates using this pattern:
- tree-sitter-yaml (0.6.1)
- tree-sitter-kotlin (0.3.8) 
- tree-sitter-erlang (0.7.0)
- tree-sitter-d (0.6.1)
- tree-sitter-lua (0.1.0)
- tree-sitter-dart (0.0.4)
- tree-sitter-html (0.20.4)
- tree-sitter-css (0.21.1)
- tree-sitter-json (0.21.0)

#### Pattern B: `LANGUAGE` Constant as `LanguageFn` (Newer Crates >= 0.20)
```rust
// LANGUAGE is a function pointer that must be called with unsafe
let language = unsafe { tree_sitter_rust::LANGUAGE() };
```

Crates using this pattern:
- tree-sitter-rust (0.24.0)
- tree-sitter-python (0.23.6)
- tree-sitter-javascript (0.23.1)
- tree-sitter-typescript (0.23.2) - uses `LANGUAGE_TYPESCRIPT` and `LANGUAGE_TSX`
- tree-sitter-go (0.23.4)
- tree-sitter-java (0.23.5)
- tree-sitter-c (0.24.1)
- tree-sitter-cpp (0.23.4)
- tree-sitter-ruby (0.23.1)
- tree-sitter-bash (0.23.3)
- tree-sitter-objc (3.0.2)
- tree-sitter-r (1.2.0)
- tree-sitter-julia (0.23.1)
- tree-sitter-scala (0.23.4)
- tree-sitter-zig (1.1.2)
- tree-sitter-php (0.23.11) - uses `LANGUAGE_PHP`
- tree-sitter-ocaml (0.24.2) - uses `LANGUAGE_OCAML` and `LANGUAGE_OCAML_INTERFACE`
- tree-sitter-haskell (0.15.0)
- tree-sitter-xml (0.7.0)
- tree-sitter-md (0.3.2)
- tree-sitter-swift (0.6.0)
- tree-sitter-elixir (0.3.4)
- tree-sitter-nix (0.0.2)

### 2. Special Cases

Some languages have multiple grammar variants:
- **TypeScript**: `LANGUAGE_TYPESCRIPT` and `LANGUAGE_TSX`
- **PHP**: `LANGUAGE_PHP` (instead of just `LANGUAGE`)
- **OCaml**: `LANGUAGE_OCAML` and `LANGUAGE_OCAML_INTERFACE`

### 3. Type Information

- `LanguageFn` is a type alias for `unsafe extern "C" fn() -> Language`
- This is a function pointer that must be called to get the actual `Language` struct
- The `unsafe` is required because it's calling an extern "C" function

## Solution: Unified Registry

The `language_registry.rs` file provides a unified abstraction that handles both patterns:

```rust
pub static LANGUAGE_REGISTRY: Lazy<HashMap<&'static str, TreeSitterLanguage>> = Lazy::new(|| {
    let mut registry = HashMap::new();

    // Pattern B: Newer crates with LANGUAGE constant
    registry.insert("rust", TreeSitterLanguage::new(unsafe { tree_sitter_rust::LANGUAGE() }));
    // ... more languages ...

    // Pattern A: Older crates with language() function  
    registry.insert("yaml", TreeSitterLanguage::new(tree_sitter_yaml::language()));
    // ... more languages ...

    registry
});
```

## Benefits of This Approach

1. **Type Safety**: Wraps raw `Language` in a newtype for better type safety
2. **Unified API**: Consumers don't need to know which pattern each language uses
3. **Lazy Loading**: Languages are only loaded when first accessed
4. **Thread Safety**: Uses `Lazy` for safe concurrent access
5. **Extensibility**: Easy to add new languages as they become available

## Usage

```rust
// Get a language by name
if let Some(language) = get_language("rust") {
    let mut parser = Parser::new();
    parser.set_language(&language)?;
    // ... use parser ...
}

// Check if a language is supported
if is_language_supported("python") {
    // ... language is available ...
}

// Get all supported languages
let languages = supported_languages();
```

## Maintenance Notes

When adding new tree-sitter language crates:

1. Check the crate version
2. For versions >= 0.20, use the `LANGUAGE` constant pattern with `unsafe`
3. For versions < 0.20, use the `language()` function pattern
4. Check the crate documentation for special constant names (e.g., `LANGUAGE_PHP`)
5. Test that the language loads correctly before committing

## Error Handling

Common compilation errors and their solutions:

- **"cannot find function `language`"**: The crate uses Pattern B, switch to `LANGUAGE` constant
- **"expected function, found `LanguageFn`"**: Missing parentheses to call the function pointer
- **"type `LanguageFn` cannot be dereferenced"**: Don't use `*`, just call it as a function

## Conclusion

The tree-sitter ecosystem's API inconsistency is manageable through a proper abstraction layer. The registry pattern provides a clean, maintainable solution that shields consumers from these implementation details while providing type safety and good performance.