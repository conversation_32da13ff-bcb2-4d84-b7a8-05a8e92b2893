# Redis Deployment Guide for Analysis Engine

This guide provides instructions for deploying Red<PERSON> (Google Memorystore) for the Analysis Engine service.

## Overview

The Analysis Engine uses Redis for:
- Caching analysis results
- Rate limiting
- Session management
- Temporary data storage

The service can run without Redis (graceful degradation), but performance will be significantly improved with caching enabled.

## Prerequisites

- Google Cloud Project: `vibe-match-463114`
- gcloud CLI installed and authenticated
- Appropriate permissions to create Memorystore instances

## Deployment Steps

### 1. Create Redis Instance

```bash
# Set variables
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
REDIS_INSTANCE_NAME="analysis-engine-cache"
REDIS_VERSION="redis_7_0"
MEMORY_SIZE_GB=4
NETWORK="default"

# Create the Redis instance
gcloud redis instances create ${REDIS_INSTANCE_NAME} \
    --size=${MEMORY_SIZE_GB} \
    --region=${REGION} \
    --redis-version=${REDIS_VERSION} \
    --network=${NETWORK} \
    --project=${PROJECT_ID}
```

This will take approximately 5-10 minutes to complete.

### 2. Get Redis Instance Details

```bash
# Get the Redis host IP
REDIS_HOST=$(gcloud redis instances describe ${REDIS_INSTANCE_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format="value(host)")

REDIS_PORT=$(gcloud redis instances describe ${REDIS_INSTANCE_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format="value(port)")

echo "Redis Host: ${REDIS_HOST}"
echo "Redis Port: ${REDIS_PORT}"
echo "Redis URL: redis://${REDIS_HOST}:${REDIS_PORT}"
```

### 3. Configure VPC Connector (Required for Cloud Run)

Cloud Run needs a VPC connector to access Redis:

```bash
# Create VPC connector
CONNECTOR_NAME="analysis-engine-connector"
SUBNET_NAME="analysis-engine-subnet"

# First, create a subnet for the connector
gcloud compute networks subnets create ${SUBNET_NAME} \
    --network=default \
    --region=${REGION} \
    --range=********/28 \
    --project=${PROJECT_ID}

# Create the VPC connector
gcloud compute networks vpc-access connectors create ${CONNECTOR_NAME} \
    --region=${REGION} \
    --subnet=${SUBNET_NAME} \
    --subnet-project=${PROJECT_ID} \
    --min-instances=2 \
    --max-instances=10 \
    --machine-type=e2-micro \
    --project=${PROJECT_ID}
```

### 4. Update Cloud Run Service

Update the Analysis Engine deployment to use Redis:

```bash
# Update the service with Redis URL and VPC connector
gcloud run services update analysis-engine \
    --region=${REGION} \
    --update-env-vars "REDIS_URL=redis://${REDIS_HOST}:${REDIS_PORT}" \
    --vpc-connector=${CONNECTOR_NAME} \
    --project=${PROJECT_ID}
```

### 5. Verify Redis Connection

Check the service logs to verify Redis connection:

```bash
# View recent logs
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=analysis-engine AND textPayload:Redis" \
    --limit=10 \
    --project=${PROJECT_ID} \
    --format="table(timestamp,textPayload)"
```

You should see: "Redis connection pool created successfully"

## Configuration Options

### Redis Configuration via Environment Variables

- `REDIS_URL`: Full Redis connection URL (e.g., `redis://**********:6379`)
- `REDIS_MAX_CONNECTIONS`: Maximum connections in pool (default: 10)
- `REDIS_CONNECTION_TIMEOUT`: Connection timeout in seconds (default: 5)
- `REDIS_POOL_TIMEOUT`: Pool checkout timeout in seconds (default: 5)

### Memory and Performance Tuning

For production workloads:
- 4GB Redis instance (handles ~100K cached analyses)
- Set maxmemory-policy to `allkeys-lru` for automatic eviction
- Enable Redis persistence for critical data

### Security Considerations

1. **Network Security**: Redis is only accessible within the VPC
2. **No Public IP**: Memorystore instances don't have public IPs
3. **IAM Permissions**: Service account needs `redis.instances.get` permission
4. **Connection Encryption**: Consider using Redis 6.0+ with TLS

## Monitoring

### Key Metrics to Monitor

1. **Cache Hit Rate**: Should be >80% for optimal performance
2. **Memory Usage**: Monitor to prevent evictions
3. **Connection Count**: Ensure within limits
4. **Command Latency**: Should be <5ms for most operations

### Setting up Monitoring

```bash
# Create alert for high memory usage
gcloud alpha monitoring policies create \
    --notification-channels=CHANNEL_ID \
    --display-name="Redis High Memory Usage" \
    --condition-display-name="Memory > 90%" \
    --condition="resource.type=\"redis.googleapis.com/Instance\" AND metric.type=\"redis.googleapis.com/stats/memory/usage_ratio\" AND metric.value > 0.9"
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Verify VPC connector is properly configured
   - Check Redis instance is in READY state
   - Ensure correct Redis host IP

2. **Timeout Errors**
   - Check network connectivity
   - Verify firewall rules allow traffic
   - Increase connection timeout if needed

3. **Authentication Errors**
   - Memorystore doesn't use auth by default
   - Remove any password from REDIS_URL

### Debug Commands

```bash
# Check Redis instance status
gcloud redis instances describe ${REDIS_INSTANCE_NAME} \
    --region=${REGION} \
    --project=${PROJECT_ID}

# Test connectivity from Cloud Shell
gcloud compute ssh --tunnel-through-iap INSTANCE_NAME -- \
    redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} ping

# View Cloud Run service configuration
gcloud run services describe analysis-engine \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format=export
```

## Cost Optimization

- **Basic Tier**: ~$0.049/GB/hour (no replication)
- **Standard Tier**: ~$0.098/GB/hour (with replication)
- **Auto-scaling**: Consider using smaller instance with good eviction policy
- **Reserved Capacity**: Save up to 50% with 1-year commitment

## Next Steps

1. Deploy Redis instance following the steps above
2. Update Analysis Engine with Redis connection details
3. Monitor cache performance and adjust size as needed
4. Set up alerts for critical metrics
5. Document Redis URL in secure location for future deployments