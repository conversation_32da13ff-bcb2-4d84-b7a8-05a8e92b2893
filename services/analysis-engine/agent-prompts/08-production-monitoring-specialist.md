# Agent Prompt 8: Production Monitoring Specialist

**Agent Type**: Production Monitoring Specialist  
**Mission**: Implement comprehensive monitoring and observability for production deployment  
**Priority**: MEDIUM  
**Context**: Production deployment requires proper monitoring, logging, and alerting systems.

---

## Agent Prompt

You are a production monitoring specialist. Your task is to implement comprehensive monitoring and observability for the analysis engine to ensure successful production deployment and operations.

### Critical Context

The analysis engine needs production-ready monitoring to:
- Track real-world performance vs. claimed metrics
- Detect issues before they impact customers
- Provide operational visibility for maintenance
- Enable data-driven performance optimization

### Implementation Areas

1. **Performance metrics collection**:
   - Throughput tracking (LOC/second)
   - Latency monitoring (request/response times)
   - Error rate monitoring
   - Resource utilization (CPU, memory, disk)

2. **Memory usage monitoring and alerting**:
   - Real-time memory usage tracking
   - Memory leak detection
   - Memory pressure alerts
   - Garbage collection monitoring

3. **Parse success rate tracking**:
   - Success/failure rates by language
   - Error pattern analysis
   - File-level parsing statistics
   - Quality metrics over time

4. **Request/response logging**:
   - Structured logging with correlation IDs
   - Request tracing and performance
   - Error logging with context
   - Audit trail for compliance

5. **Dashboard creation for operations**:
   - Real-time performance dashboards
   - Historical trend analysis
   - Alerting and notification systems
   - Operational runbooks

### Requirements

- **Implement Prometheus metrics** for monitoring
- **Add structured logging** with proper levels
- **Create health check monitoring** for dependencies
- **Set up alerting for critical metrics**
- **Test monitoring under load**

### Success Criteria

Production-ready monitoring with dashboards and alerts that provide complete operational visibility.

### Context Files to Review

- `services/analysis-engine/src/metrics/`
- `services/analysis-engine/src/api/handlers/health.rs`
- `services/analysis-engine/src/api/middleware/`
- `services/analysis-engine/src/services/analyzer/mod.rs`

### Monitoring Setup Commands

```bash
# Install monitoring dependencies
cargo add prometheus
cargo add tracing
cargo add tracing-subscriber
cargo add serde_json

# Test metrics collection
cargo test --lib test_metrics_collection -- --nocapture

# Test logging implementation
cargo test --lib test_structured_logging -- --nocapture

# Test health monitoring
cargo test --lib test_health_monitoring -- --nocapture
```

### Expected Deliverables

1. **Prometheus metrics implementation** with key performance indicators
2. **Structured logging system** with correlation IDs and context
3. **Health check monitoring** for all dependencies
4. **Alerting rules** for critical metrics and thresholds
5. **Monitoring dashboard** configuration for operations
6. **Documentation** for monitoring setup and maintenance

### Key Metrics to Implement

```rust
// Core performance metrics
use prometheus::{Counter, Histogram, Gauge, IntCounter, IntGauge};

lazy_static! {
    // Request metrics
    static ref REQUESTS_TOTAL: IntCounter = IntCounter::new(
        "analysis_requests_total", "Total number of analysis requests"
    ).unwrap();
    
    static ref REQUEST_DURATION: Histogram = Histogram::new(
        "analysis_request_duration_seconds", "Request duration in seconds"
    ).unwrap();
    
    // Processing metrics
    static ref LINES_PROCESSED: Counter = Counter::new(
        "analysis_lines_processed_total", "Total lines of code processed"
    ).unwrap();
    
    static ref PROCESSING_RATE: Gauge = Gauge::new(
        "analysis_processing_rate_loc_per_second", "Current processing rate in LOC/second"
    ).unwrap();
    
    // Parse metrics
    static ref PARSE_SUCCESS_RATE: Gauge = Gauge::new(
        "analysis_parse_success_rate", "Current parse success rate (0-1)"
    ).unwrap();
    
    static ref PARSE_ERRORS: IntCounter = IntCounter::new(
        "analysis_parse_errors_total", "Total number of parse errors"
    ).unwrap();
    
    // Resource metrics
    static ref MEMORY_USAGE: IntGauge = IntGauge::new(
        "analysis_memory_usage_bytes", "Current memory usage in bytes"
    ).unwrap();
    
    static ref ACTIVE_ANALYSES: IntGauge = IntGauge::new(
        "analysis_active_analyses", "Number of currently active analyses"
    ).unwrap();
}
```

### Structured Logging Implementation

```rust
use tracing::{info, warn, error, debug, span, Level};
use serde_json::json;

// Request logging with correlation ID
#[tracing::instrument(skip(analyzer))]
pub async fn analyze_repository(
    analyzer: &Analyzer,
    request: AnalysisRequest,
    correlation_id: String,
) -> Result<AnalysisResult, Error> {
    let _span = span!(Level::INFO, "analysis_request", 
        correlation_id = %correlation_id,
        repository_url = %request.repository_url
    );
    
    info!(
        correlation_id = %correlation_id,
        repository_url = %request.repository_url,
        "Starting repository analysis"
    );
    
    let start_time = std::time::Instant::now();
    
    match analyzer.analyze(&request).await {
        Ok(result) => {
            let duration = start_time.elapsed();
            info!(
                correlation_id = %correlation_id,
                duration_ms = duration.as_millis(),
                lines_processed = result.metrics.total_lines,
                parse_success_rate = result.metrics.parse_success_rate,
                "Analysis completed successfully"
            );
            
            // Update metrics
            REQUESTS_TOTAL.inc();
            REQUEST_DURATION.observe(duration.as_secs_f64());
            LINES_PROCESSED.inc_by(result.metrics.total_lines as f64);
            PROCESSING_RATE.set(result.metrics.total_lines as f64 / duration.as_secs_f64());
            PARSE_SUCCESS_RATE.set(result.metrics.parse_success_rate);
            
            Ok(result)
        }
        Err(e) => {
            error!(
                correlation_id = %correlation_id,
                error = %e,
                "Analysis failed"
            );
            
            REQUESTS_TOTAL.inc();
            PARSE_ERRORS.inc();
            
            Err(e)
        }
    }
}
```

### Health Check Monitoring

```rust
// Enhanced health checks with metrics
pub async fn detailed_health_with_metrics(
    State(state): State<Arc<AppState>>
) -> impl IntoResponse {
    let health_check_duration = std::time::Instant::now();
    
    // Check all dependencies
    let spanner_health = check_spanner_health(&state).await;
    let storage_health = check_storage_health(&state).await;
    let pubsub_health = check_pubsub_health(&state).await;
    let redis_health = check_redis_health(&state).await;
    
    // Collect current metrics
    let current_metrics = json!({
        "active_analyses": ACTIVE_ANALYSES.get(),
        "memory_usage_bytes": MEMORY_USAGE.get(),
        "requests_total": REQUESTS_TOTAL.get(),
        "parse_success_rate": PARSE_SUCCESS_RATE.get(),
        "processing_rate": PROCESSING_RATE.get(),
    });
    
    // System health assessment
    let overall_health = if spanner_health && storage_health && pubsub_health {
        "healthy"
    } else if storage_health && pubsub_health {
        "degraded"
    } else {
        "unhealthy"
    };
    
    let response = json!({
        "status": overall_health,
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "dependencies": {
            "spanner": spanner_health,
            "storage": storage_health,
            "pubsub": pubsub_health,
            "redis": redis_health,
        },
        "metrics": current_metrics,
        "health_check_duration_ms": health_check_duration.elapsed().as_millis(),
    });
    
    let status_code = match overall_health {
        "healthy" => StatusCode::OK,
        "degraded" => StatusCode::OK,
        "unhealthy" => StatusCode::SERVICE_UNAVAILABLE,
    };
    
    (status_code, Json(response))
}
```

### Alerting Rules

```yaml
# Example Prometheus alerting rules
groups:
  - name: analysis-engine
    rules:
      - alert: HighErrorRate
        expr: rate(analysis_parse_errors_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High parse error rate detected"
          description: "Parse error rate is {{ $value }} per second"
      
      - alert: MemoryUsageHigh
        expr: analysis_memory_usage_bytes > **********  # 3GB
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Memory usage approaching Cloud Run limit"
          description: "Memory usage is {{ $value }} bytes"
      
      - alert: ProcessingRateLow
        expr: analysis_processing_rate_loc_per_second < 5000
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Processing rate below expected threshold"
          description: "Current processing rate is {{ $value }} LOC/second"
```

### Dashboard Configuration

```json
// Grafana dashboard configuration
{
  "dashboard": {
    "title": "Analysis Engine Performance",
    "panels": [
      {
        "title": "Processing Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "analysis_processing_rate_loc_per_second",
            "legendFormat": "LOC/second"
          }
        ]
      },
      {
        "title": "Parse Success Rate",
        "type": "singlestat",
        "targets": [
          {
            "expr": "analysis_parse_success_rate * 100",
            "legendFormat": "Success Rate %"
          }
        ]
      },
      {
        "title": "Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "analysis_memory_usage_bytes / **********",
            "legendFormat": "Memory Usage (GB)"
          }
        ]
      }
    ]
  }
}
```

### Testing Framework

```bash
# Test metrics collection
cargo test --lib test_metrics_collection -- --nocapture

# Test logging functionality
cargo test --lib test_structured_logging -- --nocapture

# Test health monitoring
cargo test --lib test_health_monitoring -- --nocapture

# Test alerting conditions
cargo test --lib test_alerting_conditions -- --nocapture
```

### Performance Targets

- **Metrics overhead**: <5% performance impact
- **Logging overhead**: <2% performance impact
- **Health check response**: <100ms
- **Metrics collection**: <1% CPU overhead
- **Dashboard refresh**: <5 seconds

### Priority Order

1. **Implement core metrics** (throughput, latency, errors)
2. **Add structured logging** with correlation IDs
3. **Enhance health checks** with dependency monitoring
4. **Create alerting rules** for critical metrics
5. **Build operational dashboards**
6. **Test monitoring under load**

### Integration with Existing Code

- **Minimal code changes** to existing functionality
- **Async-compatible** monitoring implementation
- **Configurable monitoring** levels and targets
- **Graceful degradation** if monitoring fails

---

**Agent Deployment**: Use this prompt to deploy a production monitoring specialist focused on implementing comprehensive observability for successful production operations.