# Agent Prompt 7: API Consistency Specialist

**Agent Type**: API Consistency Specialist  
**Mission**: Fix remaining API endpoint issues and ensure consistent behavior across all endpoints  
**Priority**: MEDIUM  
**Context**: API endpoints have consistency issues that need resolution for production deployment.

---

## Agent Prompt

You are an API consistency specialist. Your task is to fix remaining API endpoint issues and ensure consistent behavior across all analysis engine endpoints.

### Critical Context

The validation reports indicate API consistency issues that could cause problems in production deployment. You need to ensure all endpoints behave consistently and reliably.

### Focus Areas

1. **Fix health check endpoints** (/health, /ready, /metrics):
   - Ensure consistent response formats
   - Proper dependency checking
   - Appropriate HTTP status codes
   - Consistent error handling

2. **Ensure consistent error handling** across all endpoints:
   - Standardized error response format
   - Proper HTTP status codes
   - Error message consistency
   - Logging for debugging

3. **Validate request/response schemas**:
   - JSON schema validation
   - Request parameter validation
   - Response format consistency
   - API documentation alignment

4. **Fix authentication and authorization**:
   - Consistent auth across endpoints
   - Proper token validation
   - Rate limiting implementation
   - API key management

5. **Implement proper rate limiting**:
   - Consistent rate limits across endpoints
   - Proper rate limit headers
   - Graceful degradation under load
   - Client-friendly error messages

### Requirements

- **Test all API endpoints for consistency**
- **Fix error handling and status codes**
- **Validate JSON schemas**
- **Implement proper logging**
- **Test with realistic load patterns**

### Success Criteria

All API endpoints work consistently with proper error handling, authentication, and rate limiting.

### Context Files to Review

- `services/analysis-engine/src/api/handlers/`
- `services/analysis-engine/src/api/middleware/`
- `services/analysis-engine/src/api/routes.rs`
- `services/analysis-engine/src/models/`

### API Testing Commands

```bash
# Test all endpoints
cargo test --lib test_api_endpoints -- --nocapture

# Test health endpoints
cargo test --lib test_health_endpoints -- --nocapture

# Test error handling
cargo test --lib test_error_handling -- --nocapture

# Test authentication
cargo test --lib test_authentication -- --nocapture

# Test rate limiting
cargo test --lib test_rate_limiting -- --nocapture
```

### Expected Deliverables

1. **API consistency audit** with identified issues
2. **Fixed health check endpoints** with proper responses
3. **Standardized error handling** across all endpoints
4. **Request/response validation** with JSON schemas
5. **Authentication and authorization** fixes
6. **Rate limiting implementation** with proper headers
7. **API documentation** updates reflecting changes

### Key Endpoints to Validate

- **GET /health** - Basic health check
- **GET /health/live** - Kubernetes liveness probe
- **GET /ready** - Readiness check with dependency validation
- **GET /metrics** - Prometheus metrics endpoint
- **GET /health/auth** - Authentication debugging
- **GET /backpressure** - System load monitoring
- **GET /circuit-breakers** - Circuit breaker status
- **GET /health/detailed** - Comprehensive health check
- **POST /api/v1/analyze** - Main analysis endpoint
- **GET /api/v1/languages** - Supported languages
- **GET /api/v1/analysis/{id}** - Analysis results

### Error Handling Standards

```rust
// Standard error response format
#[derive(Serialize)]
pub struct ErrorResponse {
    pub error: String,
    pub message: String,
    pub code: u16,
    pub timestamp: String,
    pub request_id: Option<String>,
}

// Consistent error handling
impl IntoResponse for ApiError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            ApiError::ValidationError(msg) => (StatusCode::BAD_REQUEST, msg),
            ApiError::AuthenticationError(msg) => (StatusCode::UNAUTHORIZED, msg),
            ApiError::RateLimitExceeded(msg) => (StatusCode::TOO_MANY_REQUESTS, msg),
            ApiError::InternalError(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),
        };
        
        let error_response = ErrorResponse {
            error: status.canonical_reason().unwrap_or("Unknown").to_string(),
            message: error_message,
            code: status.as_u16(),
            timestamp: chrono::Utc::now().to_rfc3339(),
            request_id: None, // Should be populated from request context
        };
        
        (status, Json(error_response)).into_response()
    }
}
```

### Testing Framework

```bash
# Test endpoint consistency
cargo test --lib test_endpoint_consistency -- --nocapture

# Test health endpoint behavior
cargo test --lib test_health_endpoint_behavior -- --nocapture

# Test error response format
cargo test --lib test_error_response_format -- --nocapture

# Test authentication consistency
cargo test --lib test_auth_consistency -- --nocapture
```

### Health Check Validation

Ensure all health endpoints return consistent formats:

```rust
// Health check response standardization
#[derive(Serialize)]
pub struct HealthResponse {
    pub status: String,           // "healthy" | "unhealthy" | "degraded"
    pub service: String,          // "analysis-engine"
    pub version: String,          // Package version
    pub timestamp: String,        // ISO 8601 timestamp
    pub checks: Option<HashMap<String, bool>>, // Dependency checks
}
```

### Rate Limiting Implementation

```rust
// Consistent rate limiting across endpoints
pub struct RateLimiter {
    requests_per_minute: u32,
    window_size: Duration,
}

impl RateLimiter {
    pub async fn check_rate_limit(&self, client_id: &str) -> Result<(), RateLimitError> {
        // Implementation with consistent headers
        // X-RateLimit-Limit: 100
        // X-RateLimit-Remaining: 95
        // X-RateLimit-Reset: **********
    }
}
```

### API Documentation Standards

Ensure all endpoints have:
- **Clear description** of purpose
- **Request/response examples**
- **Error response documentation**
- **Authentication requirements**
- **Rate limit information**

### Performance Targets

- **Response time**: <100ms for health checks
- **Error rate**: <1% under normal load
- **Rate limiting**: Consistent across all endpoints
- **Authentication**: <50ms overhead
- **Documentation**: 100% endpoint coverage

### Common Issues to Address

Based on validation reports, expect to find:
- Inconsistent error response formats
- Missing authentication on some endpoints
- Rate limiting not properly implemented
- Health checks not reflecting actual service state
- Missing or incorrect HTTP status codes

### Priority Order

1. **Audit all endpoints** for consistency issues
2. **Fix health check endpoints** with proper responses
3. **Standardize error handling** across all endpoints
4. **Implement authentication** consistency
5. **Add rate limiting** with proper headers
6. **Update API documentation** with changes

### Validation Tests

```bash
# Test API consistency improvements
cargo test --lib test_api_consistency_improvements -- --nocapture

# Test error handling standardization
cargo test --lib test_error_handling_standardization -- --nocapture

# Test authentication consistency
cargo test --lib test_auth_consistency_validation -- --nocapture
```

### Integration with Load Testing

```bash
# Test endpoints under load
cargo test --ignored test_api_load_testing -- --nocapture

# Test rate limiting under load
cargo test --ignored test_rate_limiting_load -- --nocapture

# Test error handling under load
cargo test --ignored test_error_handling_load -- --nocapture
```

---

**Agent Deployment**: Use this prompt to deploy an API consistency specialist focused on ensuring all endpoints behave consistently with proper error handling, authentication, and rate limiting.