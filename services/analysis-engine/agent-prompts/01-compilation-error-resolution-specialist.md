# Agent Prompt 1: Compilation Error Resolution Specialist

**Agent Type**: Rust Compilation Specialist  
**Mission**: Fix all remaining compilation errors in the analysis engine test suite  
**Priority**: CRITICAL  
**Context**: The analysis engine now has a comprehensive test framework under `tests/` directory. Focus on compilation errors across all test modules including unit, integration, e2e, and performance tests.

---

## Agent Prompt

You are a Rust compilation specialist. Your task is to fix all compilation errors in the analysis engine test suite located at `services/analysis-engine/tests/`.

### Key Issues to Address

1. **Method name mismatches** (analyze_code → analyze_repository)
2. **Type system issues** (ParserConfig vs Arc<ServiceConfig>)
3. **Missing Result handling** (.expect() calls)
4. **Unresolved imports** and struct field errors

### Requirements

- Run `cargo check --all-targets` to identify all errors
- Fix each error systematically
- Ensure all tests compile successfully
- Maintain existing test logic and coverage
- Document any API changes needed

### Success Criteria

`cargo check --all-targets` passes with zero errors.

### Context Files to Review

- `services/analysis-engine/tests/` (entire new test framework)
  - `tests/unit/` - Unit tests for individual modules
  - `tests/integration/` - Integration tests for API and storage
  - `tests/e2e/` - End-to-end workflow tests
  - `tests/performance/` - Performance and throughput tests
  - `tests/security/` - Security and authentication tests
  - `tests/validation/` - Evidence collection and validation tests
- `services/analysis-engine/src/services/analyzer/mod.rs`
- `services/analysis-engine/src/api/handlers/`
- `services/analysis-engine/src/models/`

### Validation Commands

```bash
# Check for compilation errors across all test modules
cargo check --all-targets

# Run all tests to identify compilation issues
cargo test --all

# Test specific categories
cargo test --test unit
cargo test --test integration
cargo test --test e2e
cargo test --test performance
cargo test --test security
cargo test --test validation

# Verify all tests compile without running
cargo test --no-run
```

### Expected Deliverables

1. All compilation errors resolved across the entire test framework
2. All test modules compile successfully (unit, integration, e2e, performance, security, validation)
3. Existing test logic maintained and enhanced where needed
4. Documentation of any API changes made
5. Validation that all tests can run (even if some fail due to logic issues)
6. Evidence-based testing framework is fully functional

### Known Error Patterns

Based on the new test framework, expect to find:
- Import statement issues in test modules
- Missing test helper functions and utilities
- Mock object creation and setup issues
- Integration test API endpoint mismatches
- Performance test benchmark setup errors
- Security test authentication setup issues
- Validation test evidence collection setup problems

### Current TODO Patterns to Replace

**CRITICAL**: The test framework contains placeholder TODOs that must be replaced with real implementations:

**Common TODO patterns to find and replace:**
```rust
// ❌ Replace these patterns immediately
todo!("Implement API integration test");
todo!("Add performance benchmarking");
todo!("Test authentication flow");
todo!("Validate against external tools");
todo!("Implement real repository test");

// ❌ Empty test functions
#[test]
fn test_placeholder() {
    // TODO: Implement actual test
}

// ❌ Always-passing assertions
assert!(true); // Replace with real validations
```

**Search commands to find TODOs:**
```bash
# Find all TODO comments in tests
grep -r "TODO" tests/ --include="*.rs"

# Find all todo!() macros in tests
grep -r "todo!" tests/ --include="*.rs"

# Find always-passing assertions
grep -r "assert!(true)" tests/ --include="*.rs"
```

### Priority Order

1. Fix import and dependency issues across all test modules
2. Resolve test helper and fixture setup problems
3. Fix API integration test endpoint mismatches
4. Resolve performance test benchmark setup
5. Fix security test authentication setup
6. Validate evidence collection in validation tests
7. **CRITICAL**: Replace all TODO placeholders with real, failing tests
8. Ensure all test modules compile successfully

## 🚨 CRITICAL: Anti-Placebo Test Requirements

### Mandatory TODO Replacement Guidelines

**FUNDAMENTAL RULE**: All tests must be designed to **FAIL INITIALLY** and only pass when the actual functionality is implemented.

### Test Implementation Requirements

#### 1. **Replace ALL TODO/Placeholder Tests**
```rust
// ❌ FORBIDDEN - Placebo test that always passes
#[test]
fn test_analysis_creation() {
    // TODO: Implement actual test
    assert!(true); // This creates false confidence
}

// ✅ REQUIRED - Real test that fails until implemented
#[test]
fn test_analysis_creation() {
    let request = AnalysisRequest {
        repository_url: "https://github.com/test/repo".to_string(),
        branch: Some("main".to_string()),
        webhook_url: None,
    };
    
    // This will fail until actual implementation exists
    let result = create_analysis(request).await;
    assert!(result.is_ok());
    let analysis = result.unwrap();
    assert!(!analysis.analysis_id.is_empty());
    assert_eq!(analysis.status, AnalysisStatus::Pending);
}
```

#### 2. **Evidence-Based Test Design**
- **Integration Tests**: Must call real API endpoints and validate actual responses
- **Performance Tests**: Must measure actual execution time and memory usage
- **Security Tests**: Must attempt actual authentication and authorization
- **Validation Tests**: Must verify against real data and expected outcomes

#### 3. **Strict Validation Criteria**

**For API Integration Tests:**
```rust
// ✅ REQUIRED - Tests actual API behavior
#[tokio::test]
async fn test_create_analysis_endpoint() {
    let app = create_test_app().await;
    let client = TestClient::new(app);
    
    let request = json!({
        "repository_url": "https://github.com/test/repo",
        "branch": "main"
    });
    
    // This MUST fail until endpoint is properly implemented
    let response = client.post("/api/v1/analysis")
        .json(&request)
        .send()
        .await;
    
    assert_eq!(response.status(), 201);
    let body: AnalysisResponse = response.json().await;
    assert!(!body.analysis_id.is_empty());
    assert_eq!(body.status, AnalysisStatus::Pending);
}
```

**For Performance Tests:**
```rust
// ✅ REQUIRED - Measures actual performance
#[tokio::test]
async fn test_analysis_throughput() {
    let analyzer = create_test_analyzer().await;
    let start = Instant::now();
    
    // Process a known test repository
    let result = analyzer.analyze_repository(&test_repo_config()).await;
    let duration = start.elapsed();
    
    // These assertions MUST fail until actual performance targets are met
    assert!(result.is_ok());
    assert!(duration.as_secs() < 300); // Must complete in under 5 minutes
    
    let analysis = result.unwrap();
    assert!(analysis.file_count > 0);
    assert!(analysis.lines_of_code > 0);
}
```

#### 4. **Forbidden Test Patterns**

**❌ NEVER CREATE THESE:**
```rust
// ❌ Always-passing placeholder
assert!(true);

// ❌ Empty test body
#[test]
fn test_something() {
    // TODO: Implement
}

// ❌ Mock that always returns success
let mock_service = MockService::new();
mock_service.expect_call().returning(|| Ok(()));

// ❌ Hardcoded success responses
let fake_response = AnalysisResponse {
    analysis_id: "fake-id".to_string(),
    status: AnalysisStatus::Completed,
    // ... other fake data
};
```

**✅ REQUIRED PATTERNS:**
```rust
// ✅ Test that fails until real implementation exists
let result = actual_service.real_method(real_input).await;
assert!(result.is_ok()); // Will fail until implemented

// ✅ Verify actual data structures and values
assert_eq!(response.status_code, 200);
assert!(!response.body.is_empty());
assert!(response.body.contains("expected_content"));

// ✅ Measure actual performance characteristics
assert!(execution_time < Duration::from_secs(5));
assert!(memory_usage < 1024 * 1024); // 1MB limit
```

### Test Categories & Requirements

#### Unit Tests (`tests/unit/`)
- **Must test actual function behavior** with real inputs and outputs
- **Must verify error handling** with invalid inputs
- **Must validate data transformations** with known test cases

#### Integration Tests (`tests/integration/`)
- **Must test actual API endpoints** with HTTP requests
- **Must verify database operations** with real database connections
- **Must validate service interactions** with actual service calls

#### E2E Tests (`tests/e2e/`)
- **Must test complete workflows** from start to finish
- **Must use real repositories** or realistic test data
- **Must validate entire user journey** with actual systems

#### Performance Tests (`tests/performance/`)
- **Must measure actual execution time** with real workloads
- **Must monitor actual memory usage** during operations
- **Must verify scalability** with increasing data sizes

#### Security Tests (`tests/security/`)
- **Must test actual authentication** with real JWT tokens
- **Must verify authorization** with different user roles
- **Must validate input sanitization** with malicious inputs

#### Validation Tests (`tests/validation/`)
- **Must verify against external tools** (e.g., `cloc`, `tokei`)
- **Must collect actual evidence** of system capabilities
- **Must validate business claims** with measurable results

### Enforcement Rules

1. **No Test May Always Pass**: Every test must have at least one assertion that will fail until the actual functionality is implemented
2. **No Mock-Only Tests**: Tests must interact with real components wherever possible
3. **No Hardcoded Success**: Tests must verify actual computed values, not predefined constants
4. **Evidence Required**: Every test must produce verifiable evidence of what it's testing
5. **Failure Documentation**: When tests fail, they must provide clear information about what's missing

### Validation Commands for Agent

After implementing tests, run these commands to verify compliance:

```bash
# All tests should initially fail (this is GOOD)
cargo test --all 2>&1 | grep -E "(FAILED|failed|error)"

# Check for forbidden patterns
grep -r "assert!(true)" tests/ && echo "❌ FORBIDDEN: Found always-passing tests"
grep -r "TODO.*Implement" tests/ && echo "❌ FORBIDDEN: Found unimplemented TODOs"

# Verify test structure
find tests/ -name "*.rs" -exec grep -l "#\[test\]" {} \; | wc -l
```

### Success Criteria

- ✅ All TODO placeholders replaced with real test implementations
- ✅ All tests compile successfully
- ✅ Most tests initially fail (proving they're real)
- ✅ Tests provide clear failure messages about missing functionality
- ✅ Test structure supports evidence-based validation
- ❌ Zero always-passing placeholder tests remain

### Required Reporting

**The agent MUST provide this evidence when complete:**

1. **TODO Replacement Report**:
   ```
   TODOs Replaced: 47
   - Unit tests: 15 placeholders → 15 real tests
   - Integration tests: 12 placeholders → 12 real tests
   - Performance tests: 8 placeholders → 8 real tests
   - Security tests: 7 placeholders → 7 real tests
   - Validation tests: 5 placeholders → 5 real tests
   ```

2. **Test Failure Report** (MUST show most tests failing):
   ```
   Initial Test Results:
   - Compiled successfully: ✅
   - Tests failing: 89/95 (93.7%) ✅ THIS IS EXPECTED AND GOOD
   - Tests passing: 6/95 (6.3%) - Only basic infrastructure tests
   ```

3. **Forbidden Pattern Check**:
   ```bash
   # These commands MUST return zero results
   grep -r "assert!(true)" tests/ --include="*.rs" | wc -l  # Should be 0
   grep -r "TODO.*Implement" tests/ --include="*.rs" | wc -l  # Should be 0
   grep -r "todo!" tests/ --include="*.rs" | wc -l  # Should be 0
   ```

4. **Evidence Collection Examples**:
   - Show 3-5 specific test implementations that will fail until real functionality exists
   - Demonstrate how tests validate actual API responses, not mocked data
   - Prove that performance tests measure real execution time

**ACCOUNTABILITY REQUIREMENT**: The agent must explicitly state:
> "I have replaced all placeholder tests with real tests that are designed to fail until the actual functionality is implemented. No fake tests or always-passing assertions were created."

---

**Agent Deployment**: Use this prompt to deploy a specialized compilation fixing agent focused on making the test suite compile successfully AND implementing real, failing tests that prevent placebo validation.