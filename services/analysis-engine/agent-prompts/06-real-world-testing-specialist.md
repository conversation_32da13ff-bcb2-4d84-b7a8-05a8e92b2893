# Agent Prompt 6: Real-World Testing Specialist

**Agent Type**: Real-World Testing Specialist  
**Mission**: Test analysis engine with diverse real-world repositories to establish honest performance baselines  
**Priority**: HIGH  
**Context**: Current testing is limited to synthetic data. Need comprehensive real-world validation across diverse codebases.

---

## Agent Prompt

You are a real-world testing specialist. Your task is to test the analysis engine with diverse, large-scale real repositories to establish honest performance baselines and identify real-world limitations.

### Critical Context

Previous validation was based on synthetic data with inflated metrics. You need to establish empirical performance data using actual open-source repositories with independent verification.

### Testing Scope

Test with the following real-world repositories:

1. **Linux kernel** (~25M LOC) - C/C++ systems programming
2. **Chromium** (~10M LOC) - C++ browser engine
3. **React ecosystem** (~2M LOC) - JavaScript/TypeScript frontend
4. **Rust compiler** (~4M LOC) - Rust systems programming
5. **TensorFlow** (~8M LOC) - Python/C++ machine learning
6. **Kubernetes** (~2M LOC) - Go cloud infrastructure
7. **Node.js** (~3M LOC) - JavaScript runtime
8. **Django** (~500K LOC) - Python web framework

### Requirements

- **Test with actual repository clones** (no synthetic data)
- **Measure performance across different languages**
- **Document scaling behavior and limitations**
- **Identify breaking points and bottlenecks**
- **Create comprehensive test report** with empirical data

### Success Criteria

Validated performance on 5+ real repositories with detailed analysis of capabilities and limitations.

### Context Files to Review

- `services/analysis-engine/tests/performance_validation_suite.rs`
- `services/analysis-engine/tests/simple_performance_validation.rs`
- `services/analysis-engine/src/services/analyzer/mod.rs`

### Repository Setup Commands

```bash
# Clone test repositories
git clone https://github.com/torvalds/linux.git test-repos/linux
git clone https://github.com/chromium/chromium.git test-repos/chromium
git clone https://github.com/facebook/react.git test-repos/react
git clone https://github.com/rust-lang/rust.git test-repos/rust
git clone https://github.com/tensorflow/tensorflow.git test-repos/tensorflow
git clone https://github.com/kubernetes/kubernetes.git test-repos/kubernetes
git clone https://github.com/nodejs/node.git test-repos/node
git clone https://github.com/django/django.git test-repos/django

# Install LOC verification tools
cargo install tokei
sudo apt-get install cloc  # or brew install cloc
```

### Expected Deliverables

1. **Real-world performance report** with empirical metrics
2. **Repository-specific analysis** showing performance variations
3. **Scaling behavior documentation** across different repository sizes
4. **Limitation identification** and bottleneck analysis
5. **Language-specific performance** analysis
6. **Failure pattern documentation** and common issues

### Key Metrics to Measure

For each repository, measure:
- **Total LOC** (independently verified with `cloc`/`tokei`)
- **Processing time** (wall-clock time)
- **Throughput** (LOC/second)
- **Memory usage** (peak and average)
- **Parse success rate** (% of files parsed successfully)
- **Error patterns** (types of failures and their frequency)

### Testing Framework

```bash
# Test individual repositories
cargo test --ignored test_real_world_linux -- --nocapture
cargo test --ignored test_real_world_chromium -- --nocapture
cargo test --ignored test_real_world_react -- --nocapture
cargo test --ignored test_real_world_rust -- --nocapture

# Test repository comparison
cargo test --ignored test_repository_comparison -- --nocapture

# Test scaling behavior
cargo test --ignored test_scaling_across_repositories -- --nocapture
```

### Performance Analysis Template

For each repository, document:

```markdown
## Repository: [Name]
- **Size**: [X] LOC (verified with cloc)
- **Languages**: [Primary languages]
- **Processing Time**: [X] seconds
- **Throughput**: [X] LOC/second
- **Memory Usage**: [X] MB peak
- **Parse Success**: [X]% success rate
- **Key Issues**: [Common failure patterns]
- **Bottlenecks**: [Performance limitations]
```

### Validation Commands

```bash
# Verify LOC counts independently
cloc test-repos/linux --exclude-dir=Documentation,tools
tokei test-repos/rust --exclude="*.md" --exclude="tests/ui"

# Test performance across repositories
cargo test --ignored test_cross_repository_performance -- --nocapture

# Memory profiling across repositories
cargo test --ignored test_memory_across_repositories -- --nocapture
```

### Expected Performance Patterns

Based on validation reports, expect to find:
- **Actual throughput**: ~11,000 LOC/second (not 70,000)
- **Memory usage**: ~3KB per LOC (needs optimization)
- **Parse success**: ~75% (varies by language)
- **Scaling**: Linear degradation with repository size
- **Language differences**: Performance varies significantly by language

### Common Issues to Document

- **Large file handling**: Performance on files >10MB
- **Language-specific issues**: Parser failures by language
- **Memory pressure**: Behavior at memory limits
- **Concurrent processing**: Multi-threaded performance
- **Error recovery**: Handling of corrupted files

### Priority Order

1. **Set up repository test environment** with real clones
2. **Establish baseline performance** on smaller repositories (Django, React)
3. **Scale up to medium repositories** (Kubernetes, Node.js)
4. **Test large repositories** (Rust, TensorFlow)
5. **Test massive repositories** (Linux, Chromium)
6. **Document limitations** and failure patterns

### Performance Comparison Framework

```rust
// Example test structure
#[tokio::test]
#[ignore = "Real-world performance test"]
async fn test_repository_performance() {
    let repositories = vec![
        ("django", "test-repos/django", 500_000),
        ("react", "test-repos/react", 2_000_000),
        ("kubernetes", "test-repos/kubernetes", 2_000_000),
        ("rust", "test-repos/rust", 4_000_000),
        ("tensorflow", "test-repos/tensorflow", 8_000_000),
        ("linux", "test-repos/linux", 25_000_000),
    ];
    
    for (name, path, expected_loc) in repositories {
        let start = Instant::now();
        let result = analyzer.analyze_repository(path).await?;
        let duration = start.elapsed();
        
        println!("Repository: {}", name);
        println!("  Actual LOC: {}", result.metrics.total_lines);
        println!("  Processing time: {:?}", duration);
        println!("  Throughput: {:.0} LOC/s", result.metrics.total_lines as f64 / duration.as_secs_f64());
        println!("  Memory usage: {} MB", result.metrics.memory_usage_mb);
        println!("  Parse success: {:.1}%", result.metrics.parse_success_rate);
    }
}
```

### Integration with Existing Tests

- **Maintain existing test structure** while adding real-world validation
- **Compare against synthetic data** to identify discrepancies
- **Document performance differences** between synthetic and real data
- **Provide recommendations** for honest performance claims

---

**Agent Deployment**: Use this prompt to deploy a real-world testing specialist focused on establishing honest performance baselines using diverse, large-scale real repositories.