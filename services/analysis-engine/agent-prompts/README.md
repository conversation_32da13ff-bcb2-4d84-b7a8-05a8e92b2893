# Analysis Engine Agent Prompts

This directory contains specialized AI agent prompts designed to address the technical issues identified in the analysis engine validation reports.

## Overview

The critical validation assessment revealed several key issues that need to be addressed for production readiness:

1. **Compilation errors** in test suites (41 errors)
2. **Performance validation** needs to be done with real data (not synthetic)
3. **Parse success rate** needs improvement from 75% to 85%+
4. **Concurrent processing** issues with TreeSitter initialization
5. **Memory optimization** required (current 3KB per LOC is too high)
6. **Real-world testing** needed with actual repositories
7. **API consistency** issues across endpoints
8. **Production monitoring** implementation required

## Agent Prompts

### 1. Compilation Error Resolution Specialist
**File**: `01-compilation-error-resolution-specialist.md`
**Priority**: CRITICAL
**Focus**: Fix 41 compilation errors in comprehensive_test_suite.rs

Deploy this agent first to get the basic test infrastructure working.

### 2. Performance Validation Specialist
**File**: `02-performance-validation-specialist.md`
**Priority**: CRITICAL
**Focus**: Establish honest performance baseline using real repository data

This agent will establish the actual capabilities (likely ~11,000 LOC/s) rather than inflated metrics.

### 3. Parse Success Rate Improvement Specialist
**File**: `03-parse-success-rate-improvement-specialist.md`
**Priority**: HIGH
**Focus**: Improve parse success rate from 75% to 85%+

Essential for production deployment where customers expect reliable analysis.

### 4. Concurrent Processing Specialist
**File**: `04-concurrent-processing-specialist.md`
**Priority**: HIGH
**Focus**: Fix TreeSitter initialization for concurrent processing

Required for horizontal scaling and multi-core utilization.

### 5. Memory Optimization Specialist
**File**: `05-memory-optimization-specialist.md`
**Priority**: HIGH
**Focus**: Reduce memory usage from 3KB to <1KB per LOC

Critical for Cloud Run deployment within 4GB limits.

### 6. Real-World Testing Specialist
**File**: `06-real-world-testing-specialist.md`
**Priority**: HIGH
**Focus**: Test with actual repositories (Linux, Chromium, React, etc.)

Establishes honest capabilities and identifies real-world limitations.

### 7. API Consistency Specialist
**File**: `07-api-consistency-specialist.md`
**Priority**: MEDIUM
**Focus**: Fix API endpoint consistency and error handling

Important for production deployment but not blocking.

### 8. Production Monitoring Specialist
**File**: `08-production-monitoring-specialist.md`
**Priority**: MEDIUM
**Focus**: Implement comprehensive monitoring and observability

Essential for production operations and performance tracking.

## Deployment Strategy

### Phase 1: Foundation (Deploy First)
1. **Agent 1**: Fix compilation errors - get tests working
2. **Agent 2**: Establish real performance baseline
3. **Agent 6**: Test with real repositories

### Phase 2: Optimization (Deploy Second)
4. **Agent 3**: Improve parse success rate
5. **Agent 4**: Fix concurrent processing
6. **Agent 5**: Optimize memory usage

### Phase 3: Production Ready (Deploy Third)
7. **Agent 7**: API consistency fixes
8. **Agent 8**: Production monitoring

## Usage Instructions

### For Each Agent:

1. **Copy the prompt** from the markdown file
2. **Deploy to your AI agent** (Claude, GPT, etc.)
3. **Let the agent work** on the specific issue
4. **Review the results** and iterate if needed
5. **Move to next agent** once issue is resolved

### Context Awareness:

Each agent prompt includes:
- **Clear mission statement**
- **Specific technical context**
- **Required files to review**
- **Success criteria**
- **Validation commands**
- **Expected deliverables**

### Integration:

- Agents can work **independently** on their specific issues
- Some agents may need **sequential deployment** (Agent 1 before others)
- **Real-world testing** (Agent 6) should validate improvements from other agents

## Expected Outcomes

After deploying all agents, the analysis engine should have:

✅ **Compilation**: All tests compile and run successfully
✅ **Performance**: Honest baseline established (~11,000 LOC/s)
✅ **Quality**: 85%+ parse success rate
✅ **Scalability**: Concurrent processing works
✅ **Efficiency**: Memory usage <1KB per LOC
✅ **Validation**: Tested on real repositories
✅ **Consistency**: API endpoints work reliably
✅ **Monitoring**: Production observability in place

## Key Principles

1. **Honesty over hype**: Focus on actual capabilities, not inflated metrics
2. **Real-world testing**: Use actual repositories, not synthetic data
3. **Production readiness**: Address all blocking issues for deployment
4. **Incremental improvement**: Build on what works, fix what doesn't
5. **Evidence-based**: Validate all claims with empirical data

## Success Metrics

- **Compilation**: 0 errors in `cargo check --all-targets`
- **Performance**: Validated on 5+ real repositories
- **Parse Rate**: 85%+ success across languages
- **Memory**: <1KB per LOC with streaming
- **Concurrency**: Linear scaling across cores
- **API**: Consistent behavior across endpoints
- **Monitoring**: Production-ready observability

## Notes

- These prompts are designed to be **deployment-ready**
- Each agent has **specific, measurable goals**
- The prompts include **technical context** and **validation methods**
- Focus is on **technical delivery** rather than process overhead
- **No legal reviews** - purely technical implementation

---

**Deploy these agents to systematically address all identified issues and achieve production readiness for the analysis engine.**