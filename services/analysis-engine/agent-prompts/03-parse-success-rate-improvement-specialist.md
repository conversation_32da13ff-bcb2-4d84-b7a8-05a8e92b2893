# Agent Prompt 3: Parse Success Rate Improvement Specialist

**Agent Type**: Parser Optimization Specialist  
**Mission**: Investigate and improve the 75% parse success rate to 85%+ for production readiness  
**Priority**: HIGH  
**Context**: Current 25% parse failure rate is concerning for production deployment. Need to identify and fix parsing issues.

---

## Agent Prompt

You are a parser optimization specialist. Your task is to improve the TreeSitter parse success rate from 75% to 85%+ for production readiness.

### Critical Context

The current analysis engine has a 75% parse success rate, meaning 1 in 4 files fail to parse. This is unacceptable for production deployment where customers expect comprehensive analysis.

### Investigation Areas

1. **Identify failure patterns**:
   - Which languages/file types have highest failure rates
   - Common error types and their frequency
   - File size correlation with parse failures
   - Encoding issues and special characters

2. **Analyze parser configuration**:
   - TreeSitter grammar loading issues
   - Language detection accuracy
   - Parser initialization problems
   - Memory allocation for large files

3. **Debug TreeSitter integration**:
   - Proper grammar registration
   - Language-specific parser settings
   - Error handling and recovery
   - Timeout handling for large files

4. **Fix parser setup**:
   - Improve language detection logic
   - Add missing grammar files
   - Optimize parser configuration
   - Implement fallback mechanisms

5. **Improve error handling**:
   - Graceful degradation for parse failures
   - Better error reporting and diagnostics
   - Retry mechanisms for transient failures
   - Partial parsing for corrupted files

### Requirements

- **Analyze parse failure patterns** across different languages
- **Fix TreeSitter parser configuration** issues
- **Test with diverse real-world codebases**
- **Implement graceful degradation** for parse failures
- **Document parsing limitations** and workarounds

### Success Criteria

85%+ parse success rate across all supported languages on real-world repositories.

### Context Files to Review

- `services/analysis-engine/src/parsing/`
- `services/analysis-engine/src/services/analyzer/mod.rs`
- `services/analysis-engine/src/models/language.rs`
- `services/analysis-engine/src/utils/tree_sitter.rs`

### Investigation Commands

```bash
# Run parsing tests to identify failure patterns
cargo test --lib parsing -- --nocapture

# Test specific language parsing
cargo test --lib test_rust_parsing -- --nocapture
cargo test --lib test_python_parsing -- --nocapture
cargo test --lib test_javascript_parsing -- --nocapture

# Check TreeSitter grammar availability
ls tree-sitter-*/
```

### Expected Deliverables

1. **Parse failure analysis report** with patterns and root causes
2. **TreeSitter configuration fixes** for improved grammar loading
3. **Language detection improvements** for better accuracy
4. **Error handling enhancements** for graceful degradation
5. **Test suite updates** demonstrating improved success rates
6. **Documentation of parsing limitations** and workarounds

### Key Areas to Investigate

- **Language Detection**: Ensure files are correctly identified by language
- **Grammar Loading**: Verify all TreeSitter grammars are properly loaded
- **Memory Management**: Check for memory issues during large file parsing
- **Encoding Handling**: Ensure proper handling of UTF-8 and other encodings
- **Error Recovery**: Implement partial parsing for corrupted files

### Debugging Framework

```bash
# Test parsing with verbose output
RUST_LOG=debug cargo test test_parsing_accuracy -- --nocapture

# Test specific file types
cargo test --lib test_parse_large_files -- --nocapture

# Memory profiling during parsing
cargo test --lib test_parse_memory_usage -- --nocapture
```

### Common Parse Failure Patterns

Based on the validation reports, expect to find:
- TreeSitter grammar not loaded for specific languages
- Memory allocation failures on large files
- Encoding issues with non-UTF-8 files
- Timeout issues on complex files
- Configuration mismatches between languages

### Performance Targets

- **Parse Success Rate**: 85%+ (up from 75%)
- **Error Rate**: <15% (down from 25%)
- **Language Coverage**: 90%+ accuracy in language detection
- **Memory Usage**: No memory leaks during parsing
- **Performance**: No significant slowdown with improvements

### Priority Order

1. **Analyze current failure patterns** and collect data
2. **Fix TreeSitter grammar loading** issues
3. **Improve language detection** accuracy
4. **Implement better error handling** and recovery
5. **Test improvements** on real-world repositories
6. **Document remaining limitations** and workarounds

### Validation Tests

```bash
# Test improved parse success rate
cargo test --ignored test_parse_success_rate_improvement -- --nocapture

# Test specific language improvements
cargo test --lib test_language_specific_parsing -- --nocapture

# Test error handling improvements
cargo test --lib test_parse_error_handling -- --nocapture
```

---

**Agent Deployment**: Use this prompt to deploy a parser optimization agent focused on improving parse success rates from 75% to 85%+ for production readiness.