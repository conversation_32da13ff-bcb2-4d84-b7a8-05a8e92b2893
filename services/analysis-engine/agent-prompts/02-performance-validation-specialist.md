# Agent Prompt 2: Performance Validation Specialist

**Agent Type**: Performance Validation Specialist  
**Mission**: Establish accurate performance baseline using real repository data  
**Priority**: CRITICAL  
**Context**: Current performance metrics are based on synthetic data. Need real-world validation of actual capabilities.

---

## Agent Prompt

You are a performance validation specialist. Your task is to establish an accurate performance baseline for the analysis engine using real repository data.

### Critical Context

The previous validation reports claimed 9.1M LOC processing, but this was based on synthetic data with only 1.4M actual LOC. You need to establish honest, empirical performance metrics.

### Tasks

1. **Test with actual open-source repositories**:
   - Linux kernel (~25M LOC)
   - Chromium (~10M LOC)
   - React ecosystem (~2M LOC)
   - Rust compiler (~4M LOC)
   - TensorFlow (~8M LOC)

2. **Use independent LOC verification**:
   - Use `cloc` or `tokei` for verification
   - Document actual line counts
   - No synthetic or duplicated data

3. **Measure actual performance**:
   - Wall-clock time for processing
   - Memory usage during execution
   - Parse success rates
   - Throughput (LOC/second)

4. **Document real capabilities**:
   - Actual throughput (likely ~11,000 LOC/s)
   - Maximum tested repository size
   - Memory usage patterns
   - Scaling behavior

### Requirements

- **No synthetic data allowed** - only real repositories
- **Independent verification** of all LOC counts
- **Raw test output preservation** - save all results
- **Memory profiling** during execution
- **Performance degradation analysis** at scale

### Success Criteria

Documented real-world performance baseline with empirical evidence that can be independently verified.

### Context Files to Review

- `services/analysis-engine/tests/simple_performance_validation.rs`
- `services/analysis-engine/tests/performance_validation_suite.rs`
- `services/analysis-engine/src/services/analyzer/mod.rs`

### Testing Framework

```bash
# Install LOC verification tools
cargo install tokei
sudo apt-get install cloc  # or brew install cloc

# Clone real repositories for testing
git clone https://github.com/torvalds/linux.git
git clone https://github.com/rust-lang/rust.git
git clone https://github.com/facebook/react.git

# Verify LOC counts independently
tokei /path/to/repository
cloc /path/to/repository

# Run performance tests
cargo test --ignored test_real_world_performance -- --nocapture
```

### Expected Deliverables

1. **Performance baseline report** with real metrics
2. **Independent LOC verification** for all tested repositories
3. **Raw test outputs** preserved as evidence
4. **Memory usage analysis** during processing
5. **Scaling behavior documentation**
6. **Honest capability assessment**

### Key Metrics to Measure

- **Throughput**: LOC processed per second
- **Memory usage**: Peak and average during processing
- **Parse success rate**: Percentage of files parsed successfully
- **Processing time**: Wall-clock time for different repository sizes
- **Error patterns**: Common failure modes and their frequency

### Validation Commands

```bash
# Performance validation test
cargo test --ignored test_1m_loc_performance_claim -- --nocapture

# Memory profiling
cargo test --ignored test_memory_usage -- --nocapture

# Concurrent processing test
cargo test --ignored test_concurrent_processing -- --nocapture
```

### Known Performance Expectations

Based on validation reports, expect to find:
- Real throughput around 11,000 LOC/second
- Memory usage ~3KB per LOC (needs optimization)
- Parse success rate ~75% (needs improvement)
- Processing 1M LOC in ~90 seconds (meets basic claim)

### Priority Order

1. **Establish real baseline** with one medium repository (2M LOC)
2. **Verify core claim** (1M LOC in <5 minutes)
3. **Test scaling limits** with progressively larger repositories
4. **Profile memory usage** and identify bottlenecks
5. **Document honest capabilities** and limitations

---

**Agent Deployment**: Use this prompt to deploy a performance validation agent that will establish honest, empirical performance metrics based on real repository data.