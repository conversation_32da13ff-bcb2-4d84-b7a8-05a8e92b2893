# 🚀 Pattern Mining Service - Production Deployment Guide

## Quick Start

### Prerequisites
- Google Cloud SDK installed
- Docker installed and running
- Service account key file: `vibe-match-463114-dbda8d8a6cb9.json` in project root
- Appropriate GCP permissions
- Git access to the repository

### Step 1: Setup GCP Resources (First Time Only)
```bash
cd services/pattern-mining

# The scripts will automatically use the service account key from the project root
# Make sure vibe-match-463114-dbda8d8a6cb9.json exists in the project root
./scripts/setup-gcp-resources.sh
```

This will create:
- Service account with required permissions
- Secrets in Secret Manager
- BigQuery dataset
- Cloud Storage bucket
- Enable required APIs

**Important**: Update the Gemini API key after running the setup:
```bash
echo -n 'YOUR_ACTUAL_GEMINI_API_KEY' | gcloud secrets versions add gemini-api-key --data-file=-
```

### Step 2: Validate Environment
```bash
./scripts/pre-deployment-validation.sh
```

All critical checks should pass before proceeding.

### Step 3: Deploy to Production
```bash
./scripts/deploy-production.sh
```

The deployment script will:
1. Build the Docker image
2. Push to Container Registry
3. Deploy to Cloud Run
4. Run smoke tests
5. Configure monitoring

### Step 4: Verify Deployment
```bash
# Get service URL
SERVICE_URL=$(gcloud run services describe pattern-mining \
    --region=us-central1 \
    --project=vibe-match-463114 \
    --format="value(status.url)")

# Check health
curl "$SERVICE_URL/health"
```

## Configuration

### Environment Variables
- Set in `deploy/production-config.yaml`
- Secrets managed via Google Secret Manager
- No hardcoded values in code or configs

### Security
- Service runs with minimal permissions
- All secrets rotated automatically
- Prompt injection protection enabled
- Rate limiting configured

### Monitoring
- Prometheus metrics at `/metrics`
- Health check at `/health`
- Ready check at `/ready`
- Security metrics at `/security/metrics`

## Rollback

If issues occur, rollback immediately:
```bash
# List revisions
gcloud run revisions list --service=pattern-mining --region=us-central1 --project=vibe-match-463114

# Rollback to previous
gcloud run services update-traffic pattern-mining \
    --to-revisions=PREVIOUS_REVISION=100 \
    --region=us-central1 \
    --project=vibe-match-463114
```

## Support

See [PRODUCTION_DEPLOYMENT_RUNBOOK.md](docs/PRODUCTION_DEPLOYMENT_RUNBOOK.md) for detailed procedures and troubleshooting.