# Cloud Build configuration for Pattern Mining Service
steps:
  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'us-central1-docker.pkg.dev/$PROJECT_ID/pattern-mining/pattern-mining:latest', '-f', 'Dockerfile.cloudrun', '.']
    
  # Push the Docker image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/pattern-mining/pattern-mining:latest']
    
  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'pattern-mining'
      - '--image'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/pattern-mining/pattern-mining:latest'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--service-account'
      - 'pattern-mining-sa@$PROJECT_ID.iam.gserviceaccount.com'
      - '--cpu'
      - '2'
      - '--memory'
      - '4Gi'
      - '--max-instances'
      - '100'
      - '--min-instances'
      - '1'
      - '--concurrency'
      - '1000'
      - '--timeout'
      - '60'
      - '--set-env-vars'
      - 'ENV=production,PROJECT_ID=$PROJECT_ID,LOG_LEVEL=INFO'
      - '--set-secrets'
      - 'GEMINI_API_KEY=pattern-mining-gemini-api-key:latest,JWT_SECRET=pattern-mining-jwt-secret:latest,DATABASE_PASSWORD=pattern-mining-database-password:latest,REDIS_PASSWORD=pattern-mining-redis-password:latest'

images:
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/pattern-mining/pattern-mining:latest'

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'