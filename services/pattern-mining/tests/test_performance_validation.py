#!/usr/bin/env python3
"""
Test runner for Wave 2 performance validation framework.

This test validates that all performance validation components work correctly:
- Performance benchmarking
- Load testing  
- Monitoring and alerting
- Validation and certification
"""

import asyncio
import sys
import logging
from pathlib import Path
import time
import json

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pattern_mining.performance.benchmark import PerformanceBenchmark
from pattern_mining.performance.load_testing import LoadTester
from pattern_mining.performance.monitoring import PerformanceMonitor
from pattern_mining.performance.validation import PerformanceValidator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockRedisClient:
    """Mock Redis client for testing."""
    
    def __init__(self):
        self.data = {}
    
    async def ping(self):
        return True
    
    async def set(self, key, value, ex=None):
        self.data[key] = value
        return True
    
    async def get(self, key):
        return self.data.get(key)
    
    async def sadd(self, key, *values):
        if key not in self.data:
            self.data[key] = set()
        self.data[key].update(values)
        return len(values)
    
    async def srem(self, key, *values):
        if key in self.data:
            self.data[key].difference_update(values)
        return len(values)


async def test_performance_benchmark():
    """Test performance benchmarking framework."""
    logger.info("🧪 Testing Performance Benchmark Framework")
    
    # Initialize benchmark with mock Redis
    mock_redis = MockRedisClient()
    benchmark = PerformanceBenchmark(mock_redis)
    
    # Test LOC processing benchmark
    logger.info("Testing LOC processing benchmark...")
    loc_result = await benchmark._benchmark_loc_processing()
    
    assert loc_result.test_name == "LOC Processing"
    assert loc_result.lines_processed > 0
    assert loc_result.loc_per_minute > 0
    assert loc_result.duration_seconds > 0
    
    logger.info(f"✅ LOC Benchmark: {loc_result.loc_per_minute:,.0f} LOC/min")
    
    # Test throughput benchmark
    logger.info("Testing throughput benchmark...")
    throughput_result = await benchmark._benchmark_throughput()
    
    assert throughput_result.test_name == "Throughput"
    assert throughput_result.throughput_rps > 0
    assert throughput_result.success_rate >= 0
    
    logger.info(f"✅ Throughput Benchmark: {throughput_result.throughput_rps:.1f} RPS")
    
    # Test memory efficiency benchmark
    logger.info("Testing memory efficiency benchmark...")
    memory_result = await benchmark._benchmark_memory_efficiency()
    
    assert memory_result.test_name == "Memory Efficiency"
    assert memory_result.memory_peak_mb >= 0
    
    logger.info(f"✅ Memory Benchmark: {memory_result.memory_peak_mb:.1f} MB peak")
    
    logger.info("✅ Performance Benchmark Framework: PASSED")
    return True


async def test_load_testing():
    """Test load testing framework."""
    logger.info("🧪 Testing Load Testing Framework")
    
    # Initialize load tester
    load_tester = LoadTester("http://httpbin.org")  # Use httpbin for testing
    
    # Test baseline load test
    logger.info("Testing baseline load test...")
    baseline_result = await load_tester._execute_load_test(
        test_name="Test Baseline",
        target_rps=5,
        duration_seconds=10,
        ramp_up_seconds=2
    )
    
    assert baseline_result.test_name == "Test Baseline"
    assert baseline_result.target_rps == 5
    assert baseline_result.duration_seconds > 0
    assert baseline_result.total_requests > 0
    
    logger.info(f"✅ Baseline Load Test: {baseline_result.actual_rps:.1f} RPS")
    
    # Test spike test
    logger.info("Testing spike test...")
    spike_result = await load_tester._execute_spike_test(
        test_name="Test Spike",
        base_rps=2,
        spike_rps=10,
        spike_duration=5
    )
    
    assert spike_result.test_name == "Test Spike"
    assert spike_result.total_requests > 0
    
    logger.info(f"✅ Spike Test: {spike_result.actual_rps:.1f} RPS")
    
    logger.info("✅ Load Testing Framework: PASSED")
    return True


async def test_performance_monitoring():
    """Test performance monitoring framework."""
    logger.info("🧪 Testing Performance Monitoring Framework")
    
    # Initialize monitor with mock Redis
    mock_redis = MockRedisClient()
    monitor = PerformanceMonitor(mock_redis)
    
    # Test metrics collection
    logger.info("Testing metrics collection...")
    
    # Simulate some requests
    monitor.metrics_collector.record_request(100.0, 200)  # 100ms, success
    monitor.metrics_collector.record_request(150.0, 200)  # 150ms, success
    monitor.metrics_collector.record_request(200.0, 500)  # 200ms, error
    
    # Simulate cache operations
    monitor.metrics_collector.record_cache_hit()
    monitor.metrics_collector.record_cache_hit()
    monitor.metrics_collector.record_cache_miss()
    
    # Get current metrics
    metrics = monitor.metrics_collector.get_current_metrics()
    
    assert metrics.request_count == 3
    assert metrics.response_time_ms > 0
    assert metrics.error_rate > 0
    assert metrics.cache_hit_rate > 0
    
    logger.info(f"✅ Metrics: {metrics.request_count} requests, {metrics.response_time_ms:.1f}ms avg")
    
    # Test alert rules
    logger.info("Testing alert rules...")
    
    # Test high response time alert
    high_response_rule = None
    for rule in monitor.alert_rules.values():
        if rule.name == "high_response_time":
            high_response_rule = rule
            break
    
    assert high_response_rule is not None
    assert high_response_rule.threshold == 500.0
    assert high_response_rule.metric == "response_time_ms"
    
    # Test alert evaluation
    violation = monitor._evaluate_rule(high_response_rule, 600.0)  # Over threshold
    assert violation is True
    
    no_violation = monitor._evaluate_rule(high_response_rule, 400.0)  # Under threshold
    assert no_violation is False
    
    logger.info("✅ Alert Rules: Working correctly")
    
    # Test performance summary
    summary = monitor.get_performance_summary()
    assert "current_rps" in summary
    assert "avg_response_time_ms" in summary
    
    logger.info("✅ Performance Monitoring Framework: PASSED")
    return True


async def test_performance_validation():
    """Test performance validation framework."""
    logger.info("🧪 Testing Performance Validation Framework")
    
    # Initialize validator with mock Redis
    mock_redis = MockRedisClient()
    validator = PerformanceValidator(mock_redis)
    
    # Test criterion evaluation
    logger.info("Testing criterion evaluation...")
    
    # Test LOC per minute criterion
    loc_pass = validator._evaluate_criterion("loc_per_minute", 1_500_000, 1_000_000)
    assert loc_pass is True
    
    loc_fail = validator._evaluate_criterion("loc_per_minute", 500_000, 1_000_000)
    assert loc_fail is False
    
    # Test latency criterion
    latency_pass = validator._evaluate_criterion("latency_p95_ms", 80.0, 100.0)
    assert latency_pass is True
    
    latency_fail = validator._evaluate_criterion("latency_p95_ms", 120.0, 100.0)
    assert latency_fail is False
    
    logger.info("✅ Criterion Evaluation: Working correctly")
    
    # Test score calculation
    logger.info("Testing score calculation...")
    
    # Test performance score (higher is better)
    score_100 = validator._calculate_criterion_score("throughput_rps", 1000, 1000)
    assert score_100 == 100
    
    score_50 = validator._calculate_criterion_score("throughput_rps", 500, 1000)
    assert score_50 == 50
    
    # Test latency score (lower is better)
    latency_score_100 = validator._calculate_criterion_score("latency_p95_ms", 50, 100)
    assert latency_score_100 == 100
    
    latency_score_50 = validator._calculate_criterion_score("latency_p95_ms", 150, 100)
    assert latency_score_50 == 50
    
    logger.info("✅ Score Calculation: Working correctly")
    
    # Test recommendation generation
    logger.info("Testing recommendation generation...")
    
    validation_results = {
        "throughput_rps": {"passed": False, "actual": 500, "threshold": 1000},
        "latency_p95_ms": {"passed": False, "actual": 150, "threshold": 100},
    }
    
    recommendations = validator._generate_recommendations(
        validation_results, 
        ["throughput_rps", "latency_p95_ms"]
    )
    
    assert len(recommendations) >= 2
    assert any("throughput" in rec.lower() for rec in recommendations)
    assert any("latency" in rec.lower() for rec in recommendations)
    
    logger.info("✅ Recommendation Generation: Working correctly")
    
    # Test certification level determination
    logger.info("Testing certification level determination...")
    
    prod_cert = validator._determine_certification_level(95.0, "pass")
    assert prod_cert == "production"
    
    staging_cert = validator._determine_certification_level(75.0, "warning")
    assert staging_cert == "staging"
    
    dev_cert = validator._determine_certification_level(55.0, "fail")
    assert dev_cert == "development"
    
    logger.info("✅ Certification Level: Working correctly")
    
    logger.info("✅ Performance Validation Framework: PASSED")
    return True


async def test_integration():
    """Test integration of all performance components."""
    logger.info("🧪 Testing Integration")
    
    # Initialize all components
    mock_redis = MockRedisClient()
    
    benchmark = PerformanceBenchmark(mock_redis)
    load_tester = LoadTester("http://httpbin.org")
    monitor = PerformanceMonitor(mock_redis)
    validator = PerformanceValidator(mock_redis)
    
    # Test that components can work together
    logger.info("Testing component integration...")
    
    # Simulate monitoring during a benchmark
    monitor.metrics_collector.record_request(100.0, 200)
    
    # Get current metrics
    metrics = monitor.metrics_collector.get_current_metrics()
    
    # Test that metrics can be used for validation
    extracted_metrics = {
        "throughput_rps": metrics.throughput_rps,
        "response_time_ms": metrics.response_time_ms,
        "error_rate": metrics.error_rate,
        "cache_hit_rate": metrics.cache_hit_rate,
    }
    
    assert all(isinstance(v, (int, float)) for v in extracted_metrics.values())
    
    logger.info("✅ Component Integration: Working correctly")
    
    # Test mock data storage
    logger.info("Testing data storage...")
    
    # Store test data
    await mock_redis.set("test_key", "test_value")
    stored_value = await mock_redis.get("test_key")
    assert stored_value == "test_value"
    
    # Store metrics
    await mock_redis.set("metrics:test", json.dumps(metrics.to_dict()))
    stored_metrics = await mock_redis.get("metrics:test")
    assert stored_metrics is not None
    
    logger.info("✅ Data Storage: Working correctly")
    
    logger.info("✅ Integration Test: PASSED")
    return True


async def run_performance_validation_tests():
    """Run all performance validation tests."""
    logger.info("🚀 Starting Wave 2 Performance Validation Tests")
    logger.info("=" * 60)
    
    start_time = time.time()
    
    tests = [
        ("Performance Benchmark", test_performance_benchmark),
        ("Load Testing", test_load_testing),
        ("Performance Monitoring", test_performance_monitoring),
        ("Performance Validation", test_performance_validation),
        ("Integration", test_integration),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            logger.info(f"\n📋 Running {test_name} Tests...")
            await test_func()
            passed += 1
            logger.info(f"✅ {test_name}: PASSED")
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test_name}: FAILED - {e}")
            import traceback
            traceback.print_exc()
    
    end_time = time.time()
    duration = end_time - start_time
    
    logger.info("=" * 60)
    logger.info("🏁 Wave 2 Performance Validation Test Results")
    logger.info("=" * 60)
    logger.info(f"✅ Tests Passed: {passed}")
    logger.info(f"❌ Tests Failed: {failed}")
    logger.info(f"⏱️ Duration: {duration:.2f} seconds")
    
    if failed == 0:
        logger.info("🎉 ALL TESTS PASSED - Wave 2 Performance Validation is ready!")
        return True
    else:
        logger.error(f"💥 {failed} tests failed - Wave 2 needs fixes")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_performance_validation_tests())
    sys.exit(0 if success else 1)