"""
Test SQL injection prevention in query builder.

This module tests the security enhancements to prevent SQL injection attacks.
"""

import pytest
from datetime import datetime

from pattern_mining.database.query_builder import BigQueryQueryBuilder
from pattern_mining.security.sql_security import SQLValidationError


class TestSQLInjectionPrevention:
    """Test SQL injection prevention measures."""
    
    def setup_method(self):
        """Set up test query builder."""
        self.builder = BigQueryQueryBuilder("test-project", "test-dataset")
    
    def test_valid_table_names(self):
        """Test valid table names are accepted."""
        # Simple table name
        query = self.builder.from_table("users").build()
        assert "FROM `test-project.test-dataset.users`" in query[0]
        
        # Table with alias
        self.builder.reset()
        query = self.builder.from_table("users", alias="u").build()
        assert "FROM `test-project.test-dataset.users` AS u" in query[0]
        
        # Fully qualified table name
        self.builder.reset()
        query = self.builder.from_table("project.dataset.table").build()
        assert "FROM `project.dataset.table`" in query[0]
    
    def test_invalid_table_names_rejected(self):
        """Test invalid table names are rejected."""
        # Table name with SQL injection attempt
        with pytest.raises(SQLValidationError):
            self.builder.from_table("users; DROP TABLE users;--")
        
        # Table name with special characters
        with pytest.raises(SQLValidationError):
            self.builder.from_table("users'")
        
        # Reserved word as table name
        with pytest.raises(SQLValidationError):
            self.builder.from_table("SELECT")
    
    def test_valid_aliases(self):
        """Test valid aliases are accepted."""
        query = self.builder.from_table("users", alias="u").build()
        assert "AS u" in query[0]
    
    def test_invalid_aliases_rejected(self):
        """Test invalid aliases are rejected."""
        # Alias with special characters
        with pytest.raises(SQLValidationError):
            self.builder.from_table("users", alias="u'")
        
        # Reserved word as alias
        with pytest.raises(SQLValidationError):
            self.builder.from_table("users", alias="WHERE")
    
    def test_limit_validation(self):
        """Test LIMIT value validation."""
        # Valid limit
        query = self.builder.from_table("users").limit(100).build()
        assert "LIMIT 100" in query[0]
        
        # Invalid limit (too high)
        with pytest.raises(SQLValidationError):
            self.builder.limit(10_000_000)
        
        # Invalid limit (negative)
        with pytest.raises(SQLValidationError):
            self.builder.limit(-1)
    
    def test_offset_validation(self):
        """Test OFFSET value validation."""
        # Valid offset
        query = self.builder.from_table("users").offset(50).build()
        assert "OFFSET 50" in query[0]
        
        # Invalid offset (too high)
        with pytest.raises(SQLValidationError):
            self.builder.offset(100_000_000)
        
        # Invalid offset (negative)
        with pytest.raises(SQLValidationError):
            self.builder.offset(-1)
    
    def test_parameterized_queries(self):
        """Test parameterized queries prevent injection."""
        # Using where_eq with parameter
        query, params = self.builder.from_table("users").where_eq("email", "<EMAIL>").build()
        assert "@param_0" in query
        assert params["param_0"] == "<EMAIL>"
        
        # Using where_in with parameters
        self.builder.reset()
        query, params = self.builder.from_table("users").where_in("status", ["active", "pending"]).build()
        assert "@param_0" in query
        assert params["param_0"] == ["active", "pending"]
    
    def test_join_validation(self):
        """Test JOIN clause validation."""
        # Valid join
        query = self.builder.from_table("users", "u").join(
            "orders", "u.id = orders.user_id", alias="o"
        ).build()
        assert "INNER JOIN `test-project.test-dataset.orders` AS o" in query[0]
        
        # Invalid table name in join
        with pytest.raises(SQLValidationError):
            self.builder.join("orders; DROP TABLE orders;", "u.id = o.user_id")
    
    def test_complex_query_with_security(self):
        """Test complex query with all security features."""
        query, params = (
            self.builder
            .select(["id", "email", "COUNT(*) as order_count"])
            .from_table("users", alias="u")
            .join("orders", "u.id = orders.user_id", alias="o")
            .where_eq("u.status", "active")
            .where_in("o.status", ["completed", "shipped"])
            .where_between("o.created_at", datetime(2024, 1, 1), datetime(2024, 12, 31))
            .group_by(["u.id", "u.email"])
            .having("COUNT(*) > 5")
            .order_by("order_count", desc=True)
            .limit(100)
            .offset(0)
            .build()
        )
        
        # Check query structure
        assert "SELECT id, email, COUNT(*) as order_count" in query
        assert "FROM `test-project.test-dataset.users` AS u" in query
        assert "INNER JOIN `test-project.test-dataset.orders` AS o" in query
        assert "WHERE u.status = @param_0" in query
        assert "AND o.status IN UNNEST(@param_1)" in query
        assert "AND o.created_at BETWEEN @param_2 AND @param_3" in query
        assert "GROUP BY u.id, u.email" in query
        assert "HAVING COUNT(*) > 5" in query
        assert "ORDER BY order_count DESC" in query
        assert "LIMIT 100" in query
        assert "OFFSET 0" in query
        
        # Check parameters
        assert params["param_0"] == "active"
        assert params["param_1"] == ["completed", "shipped"]
        assert params["param_2"] == datetime(2024, 1, 1)
        assert params["param_3"] == datetime(2024, 12, 31)
    
    def test_sql_injection_patterns_detected(self):
        """Test that common SQL injection patterns are detected."""
        # In SELECT fields (logged as warning)
        self.builder.select("id, (SELECT password FROM admin)")
        # Should log warning but still build
        
        # In WHERE conditions (should be parameterized)
        query, params = self.builder.from_table("users").where_eq("id", "1 OR 1=1").build()
        # The injection attempt is safely parameterized
        assert params["param_0"] == "1 OR 1=1"  # Stored as string value, not executed
    
    def test_edge_cases(self):
        """Test edge cases in validation."""
        # Empty table name
        with pytest.raises(SQLValidationError):
            self.builder.from_table("")
        
        # Very long table name
        with pytest.raises(SQLValidationError):
            self.builder.from_table("a" * 200)
        
        # Table name with numbers (valid)
        query = self.builder.from_table("users_2024").build()
        assert "users_2024" in query[0]
        
        # Table name starting with underscore (valid)
        self.builder.reset()
        query = self.builder.from_table("_internal_users").build()
        assert "_internal_users" in query[0]