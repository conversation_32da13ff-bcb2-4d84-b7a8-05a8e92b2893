"""
Ray cluster management and configuration for distributed pattern mining.

This module provides comprehensive Ray cluster management including:
- Cluster initialization and configuration with security
- Worker node management and monitoring
- Resource allocation and scheduling
- Fault tolerance and recovery
- Performance monitoring and optimization
- Mutual TLS authentication and secure communication
"""

import asyncio
import logging
import os
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import psutil
import ray
from ray.runtime_env import RuntimeEnv
from ray.cluster_utils import Cluster
from ray._private.ray_constants import AUTOSCALER_MAX_NUM_FAILURES
import yaml
import structlog

from ..security.ray_security import (
    RaySecurityConfig,
    RaySecurityManager,
    get_secure_ray_config
)

logger = structlog.get_logger()


class ClusterStatus(Enum):
    """Ray cluster status enumeration."""
    INITIALIZING = "initializing"
    RUNNING = "running"
    SCALING = "scaling"
    DEGRADED = "degraded"
    FAILED = "failed"
    SHUTDOWN = "shutdown"


@dataclass
class NodeConfig:
    """Configuration for Ray cluster nodes."""
    node_type: str
    min_workers: int = 0
    max_workers: int = 100
    instance_type: str = "m5.large"
    resources: Dict[str, float] = field(default_factory=dict)
    setup_commands: List[str] = field(default_factory=list)
    worker_start_ray_commands: List[str] = field(default_factory=list)
    idle_timeout_minutes: int = 5
    use_gpu: bool = False
    gpu_count: int = 0
    docker_image: Optional[str] = None


@dataclass
class ClusterConfig:
    """Ray cluster configuration."""
    cluster_name: str
    provider: str = "local"
    auth: Dict[str, Any] = field(default_factory=dict)
    head_node: NodeConfig = field(default_factory=lambda: NodeConfig("head"))
    worker_nodes: List[NodeConfig] = field(default_factory=list)
    max_workers: int = 100
    upscaling_speed: float = 1.0
    idle_timeout_minutes: int = 5
    docker_config: Dict[str, Any] = field(default_factory=dict)
    initialization_commands: List[str] = field(default_factory=list)
    setup_commands: List[str] = field(default_factory=list)
    head_setup_commands: List[str] = field(default_factory=list)
    worker_setup_commands: List[str] = field(default_factory=list)
    runtime_env: Dict[str, Any] = field(default_factory=dict)


class RayClusterManager:
    """
    Ray cluster manager for distributed pattern mining operations.
    
    Provides comprehensive cluster management including initialization,
    monitoring, scaling, and fault tolerance with enhanced security.
    """
    
    def __init__(self, config: ClusterConfig, security_config: Optional[RaySecurityConfig] = None):
        self.config = config
        self.security_config = security_config or get_secure_ray_config()
        self.security_manager = RaySecurityManager(self.security_config)
        self.cluster: Optional[Cluster] = None
        self.status = ClusterStatus.INITIALIZING
        self.nodes: Dict[str, Any] = {}
        self.resources: Dict[str, float] = {}
        self.metrics: Dict[str, Any] = {}
        self.last_health_check = time.time()
        self.failure_count = 0
        self.max_failures = AUTOSCALER_MAX_NUM_FAILURES
        self._security_setup: Optional[Dict[str, Any]] = None
        
        # Initialize runtime environment with security
        env_vars = self._get_env_vars()
        if self.security_config.enable_auth and self.security_config.auth_token:
            env_vars["RAY_AUTH_TOKEN"] = self.security_config.auth_token
        
        self.runtime_env = RuntimeEnv(
            pip=self._get_pip_requirements(),
            env_vars=env_vars,
            **config.runtime_env
        )
        
        logger.info(
            "Initialized Ray cluster manager",
            cluster_name=config.cluster_name,
            security_enabled=self.security_config.enable_tls
        )
    
    async def initialize_cluster(self) -> bool:
        """
        Initialize Ray cluster with configured settings and security.
        
        Returns:
            bool: True if cluster initialized successfully
        """
        try:
            logger.info("Initializing Ray cluster", cluster_name=self.config.cluster_name)
            
            # Setup security configuration first
            if self.security_config.enable_tls or self.security_config.enable_auth:
                logger.info("Setting up Ray cluster security")
                self._security_setup = await self.security_manager.setup_secure_cluster()
                logger.info("Ray security configured", dashboard_url=self._security_setup.get("dashboard_url"))
            
            # Get Ray initialization arguments with security
            ray_init_args = self.security_config.to_ray_init_args()
            
            # Add cluster-specific configuration
            ray_init_args.update({
                "address": self._get_ray_address(),
                "runtime_env": self.runtime_env,
                "namespace": self.config.cluster_name,
                "logging_level": logging.INFO,
                "log_to_driver": True,
            })
            
            # Initialize Ray with security configuration
            if not ray.is_initialized():
                # If TLS is enabled, set environment variables for Ray
                if self.security_config.enable_tls and self._security_setup:
                    certs = self._security_setup.get("certificates", {})
                    if certs:
                        os.environ["RAY_TLS_SERVER_CERT"] = certs.get("server_cert", "")
                        os.environ["RAY_TLS_SERVER_KEY"] = certs.get("server_key", "")
                        os.environ["RAY_TLS_CA_CERT"] = certs.get("ca_cert", "")
                        os.environ["RAY_USE_TLS"] = "1"
                        logger.info("Ray TLS environment variables configured")
                
                ray.init(**ray_init_args)
            
            # Wait for cluster to be ready
            await self._wait_for_cluster_ready()
            
            # Initialize cluster resources
            await self._initialize_cluster_resources()
            
            # Start monitoring
            await self._start_monitoring()
            
            self.status = ClusterStatus.RUNNING
            logger.info(
                "Ray cluster initialized successfully",
                cluster_name=self.config.cluster_name,
                tls_enabled=self.security_config.enable_tls,
                auth_enabled=self.security_config.enable_auth
            )
            
            return True
            
        except Exception as e:
            logger.error("Failed to initialize Ray cluster", error=str(e))
            self.status = ClusterStatus.FAILED
            self.failure_count += 1
            return False
    
    async def shutdown_cluster(self) -> bool:
        """
        Gracefully shutdown Ray cluster.
        
        Returns:
            bool: True if shutdown successful
        """
        try:
            logger.info(f"Shutting down Ray cluster: {self.config.cluster_name}")
            
            self.status = ClusterStatus.SHUTDOWN
            
            # Stop monitoring
            await self._stop_monitoring()
            
            # Shutdown Ray
            if ray.is_initialized():
                ray.shutdown()
            
            # Clean up cluster resources
            if self.cluster:
                self.cluster.shutdown()
                self.cluster = None
            
            logger.info(f"Ray cluster {self.config.cluster_name} shutdown successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to shutdown Ray cluster: {e}")
            return False
    
    async def scale_cluster(self, target_workers: int) -> bool:
        """
        Scale cluster to target number of workers.
        
        Args:
            target_workers: Target number of worker nodes
            
        Returns:
            bool: True if scaling successful
        """
        try:
            logger.info(f"Scaling cluster to {target_workers} workers")
            
            self.status = ClusterStatus.SCALING
            
            # Get current cluster state
            current_workers = len(self._get_worker_nodes())
            
            if target_workers > current_workers:
                # Scale up
                await self._scale_up(target_workers - current_workers)
            elif target_workers < current_workers:
                # Scale down
                await self._scale_down(current_workers - target_workers)
            
            # Wait for scaling to complete
            await self._wait_for_scaling_complete(target_workers)
            
            self.status = ClusterStatus.RUNNING
            logger.info(f"Cluster scaling completed: {target_workers} workers")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to scale cluster: {e}")
            self.status = ClusterStatus.DEGRADED
            return False
    
    async def get_cluster_status(self) -> Dict[str, Any]:
        """
        Get comprehensive cluster status information.
        
        Returns:
            Dict containing cluster status details
        """
        try:
            nodes = self._get_cluster_nodes()
            resources = self._get_cluster_resources()
            
            return {
                "cluster_name": self.config.cluster_name,
                "status": self.status.value,
                "nodes": {
                    "total": len(nodes),
                    "head": len([n for n in nodes if n.get("node_type") == "head"]),
                    "workers": len([n for n in nodes if n.get("node_type") == "worker"]),
                    "alive": len([n for n in nodes if n.get("is_alive", False)]),
                    "dead": len([n for n in nodes if not n.get("is_alive", True)])
                },
                "resources": resources,
                "metrics": self.metrics,
                "last_health_check": self.last_health_check,
                "failure_count": self.failure_count,
                "uptime": time.time() - self.last_health_check
            }
            
        except Exception as e:
            logger.error(f"Failed to get cluster status: {e}")
            return {"status": "error", "error": str(e)}
    
    async def health_check(self) -> bool:
        """
        Perform comprehensive cluster health check.
        
        Returns:
            bool: True if cluster is healthy
        """
        try:
            if not ray.is_initialized():
                return False
            
            # Check cluster connectivity
            if not await self._check_cluster_connectivity():
                return False
            
            # Check node health
            if not await self._check_node_health():
                return False
            
            # Check resource availability
            if not await self._check_resource_availability():
                return False
            
            # Update metrics
            await self._update_health_metrics()
            
            self.last_health_check = time.time()
            return True
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    async def recover_from_failure(self) -> bool:
        """
        Attempt to recover from cluster failures.
        
        Returns:
            bool: True if recovery successful
        """
        try:
            logger.info("Attempting cluster recovery from failure")
            
            # Check if recovery is possible
            if self.failure_count >= self.max_failures:
                logger.error("Max failures exceeded, cannot recover")
                return False
            
            # Restart failed components
            await self._restart_failed_components()
            
            # Verify cluster health
            if await self.health_check():
                logger.info("Cluster recovery successful")
                self.failure_count = 0
                self.status = ClusterStatus.RUNNING
                return True
            else:
                logger.error("Cluster recovery failed")
                self.failure_count += 1
                return False
                
        except Exception as e:
            logger.error(f"Recovery attempt failed: {e}")
            self.failure_count += 1
            return False
    
    def _get_ray_address(self) -> Optional[str]:
        """Get Ray cluster address from environment or config."""
        return os.getenv("RAY_ADDRESS", "auto")
    
    def _get_pip_requirements(self) -> List[str]:
        """Get pip requirements for runtime environment."""
        return [
            "ray[default,tune,serve]==2.47.0",
            "torch==2.4.0",
            "transformers==4.53.0",
            "scikit-learn==1.6.0",
            "numpy==2.1.0",
            "pandas==2.2.3"
        ]
    
    def _get_env_vars(self) -> Dict[str, str]:
        """Get environment variables for runtime environment."""
        return {
            "PYTHONPATH": os.getenv("PYTHONPATH", ""),
            "CUDA_VISIBLE_DEVICES": os.getenv("CUDA_VISIBLE_DEVICES", ""),
            "RAY_DISABLE_IMPORT_WARNING": "1",
            "RAY_DEDUP_LOGS": "0"
        }
    
    async def _wait_for_cluster_ready(self, timeout: int = 300) -> bool:
        """Wait for cluster to be ready."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check if Ray is connected
                if ray.is_initialized():
                    # Check if cluster has resources
                    resources = ray.cluster_resources()
                    if resources and "CPU" in resources:
                        return True
                
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.debug(f"Waiting for cluster ready: {e}")
                await asyncio.sleep(1)
        
        return False
    
    async def _initialize_cluster_resources(self):
        """Initialize cluster resources and capabilities."""
        try:
            # Get available resources
            self.resources = ray.cluster_resources()
            
            # Update node information
            nodes = ray.nodes()
            self.nodes = {node["NodeID"]: node for node in nodes}
            
            # Initialize metrics
            self.metrics = {
                "cpu_utilization": 0.0,
                "memory_utilization": 0.0,
                "gpu_utilization": 0.0,
                "network_io": 0.0,
                "disk_io": 0.0
            }
            
            logger.info(f"Initialized cluster resources: {self.resources}")
            
        except Exception as e:
            logger.error(f"Failed to initialize cluster resources: {e}")
            raise
    
    async def _start_monitoring(self):
        """Start cluster monitoring tasks."""
        try:
            # Start monitoring tasks
            asyncio.create_task(self._monitor_cluster_health())
            asyncio.create_task(self._monitor_resource_usage())
            asyncio.create_task(self._monitor_node_status())
            
            logger.info("Started cluster monitoring")
            
        except Exception as e:
            logger.error(f"Failed to start monitoring: {e}")
    
    async def _stop_monitoring(self):
        """Stop cluster monitoring tasks."""
        try:
            # Cancel monitoring tasks
            # Note: In production, maintain task references for proper cleanup
            
            logger.info("Stopped cluster monitoring")
            
        except Exception as e:
            logger.error(f"Failed to stop monitoring: {e}")
    
    async def _scale_up(self, num_workers: int):
        """Scale up cluster by adding workers."""
        try:
            # Implementation would depend on cluster provider
            # For local development, Ray handles this automatically
            
            logger.info(f"Scaling up by {num_workers} workers")
            
        except Exception as e:
            logger.error(f"Failed to scale up: {e}")
            raise
    
    async def _scale_down(self, num_workers: int):
        """Scale down cluster by removing workers."""
        try:
            # Implementation would depend on cluster provider
            # For local development, Ray handles this automatically
            
            logger.info(f"Scaling down by {num_workers} workers")
            
        except Exception as e:
            logger.error(f"Failed to scale down: {e}")
            raise
    
    async def _wait_for_scaling_complete(self, target_workers: int, timeout: int = 300):
        """Wait for scaling operation to complete."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                current_workers = len(self._get_worker_nodes())
                if current_workers == target_workers:
                    return True
                
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.debug(f"Waiting for scaling complete: {e}")
                await asyncio.sleep(5)
        
        return False
    
    def _get_cluster_nodes(self) -> List[Dict[str, Any]]:
        """Get list of all cluster nodes."""
        try:
            return ray.nodes()
        except Exception as e:
            logger.error(f"Failed to get cluster nodes: {e}")
            return []
    
    def _get_worker_nodes(self) -> List[Dict[str, Any]]:
        """Get list of worker nodes."""
        try:
            nodes = ray.nodes()
            return [node for node in nodes if not node.get("is_head_node", False)]
        except Exception as e:
            logger.error(f"Failed to get worker nodes: {e}")
            return []
    
    def _get_cluster_resources(self) -> Dict[str, float]:
        """Get cluster resource information."""
        try:
            return ray.cluster_resources()
        except Exception as e:
            logger.error(f"Failed to get cluster resources: {e}")
            return {}
    
    async def _check_cluster_connectivity(self) -> bool:
        """Check cluster connectivity with security validation."""
        try:
            # Test basic Ray operations
            @ray.remote
            def test_task():
                return "ok"
            
            result = await test_task.remote()
            success = result == "ok"
            
            # Log security event
            if self.security_config.enable_security_logging:
                client_ip = "127.0.0.1"  # In production, get actual client IP
                await self.security_manager.monitor_security_events(
                    "connectivity_check",
                    client_ip,
                    {"success": success, "timestamp": time.time()}
                )
            
            return success
            
        except Exception as e:
            logger.error("Cluster connectivity check failed", error=str(e))
            
            # Log security failure
            if self.security_config.enable_security_logging:
                await self.security_manager.monitor_security_events(
                    "connectivity_failure",
                    "127.0.0.1",
                    {"error": str(e), "timestamp": time.time()}
                )
            
            return False
    
    async def _check_node_health(self) -> bool:
        """Check health of all nodes."""
        try:
            nodes = self._get_cluster_nodes()
            alive_nodes = [node for node in nodes if node.get("is_alive", False)]
            
            # At least head node should be alive
            return len(alive_nodes) > 0
            
        except Exception as e:
            logger.error(f"Node health check failed: {e}")
            return False
    
    async def _check_resource_availability(self) -> bool:
        """Check resource availability."""
        try:
            resources = self._get_cluster_resources()
            
            # Check minimum resource requirements
            return (
                resources.get("CPU", 0) > 0 and
                resources.get("memory", 0) > **********  # 1GB minimum
            )
            
        except Exception as e:
            logger.error(f"Resource availability check failed: {e}")
            return False
    
    async def _update_health_metrics(self):
        """Update cluster health metrics."""
        try:
            # Get system metrics
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            
            # Update metrics
            self.metrics.update({
                "cpu_utilization": cpu_percent,
                "memory_utilization": memory_percent,
                "timestamp": time.time()
            })
            
        except Exception as e:
            logger.error(f"Failed to update health metrics: {e}")
    
    async def _restart_failed_components(self):
        """Restart failed cluster components."""
        try:
            # Check for failed nodes
            nodes = self._get_cluster_nodes()
            failed_nodes = [node for node in nodes if not node.get("is_alive", True)]
            
            if failed_nodes:
                logger.info(f"Restarting {len(failed_nodes)} failed nodes")
                # Implementation would depend on cluster provider
            
        except Exception as e:
            logger.error(f"Failed to restart components: {e}")
    
    async def _monitor_cluster_health(self):
        """Background task to monitor cluster health."""
        while self.status != ClusterStatus.SHUTDOWN:
            try:
                if not await self.health_check():
                    logger.warning("Cluster health check failed")
                    self.status = ClusterStatus.DEGRADED
                    
                    # Attempt recovery
                    if not await self.recover_from_failure():
                        logger.error("Cluster recovery failed")
                        self.status = ClusterStatus.FAILED
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Health monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _monitor_resource_usage(self):
        """Background task to monitor resource usage."""
        while self.status != ClusterStatus.SHUTDOWN:
            try:
                await self._update_health_metrics()
                await asyncio.sleep(60)  # Update every minute
                
            except Exception as e:
                logger.error(f"Resource monitoring error: {e}")
                await asyncio.sleep(120)
    
    async def _monitor_node_status(self):
        """Background task to monitor node status."""
        while self.status != ClusterStatus.SHUTDOWN:
            try:
                nodes = self._get_cluster_nodes()
                self.nodes = {node["NodeID"]: node for node in nodes}
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Node monitoring error: {e}")
                await asyncio.sleep(60)


def create_default_cluster_config(cluster_name: str) -> ClusterConfig:
    """Create default cluster configuration."""
    return ClusterConfig(
        cluster_name=cluster_name,
        head_node=NodeConfig(
            node_type="head",
            resources={"CPU": 2, "memory": 4000000000},
            setup_commands=[
                "pip install ray[default,tune,serve]==2.47.0",
                "pip install torch==2.4.0",
                "pip install transformers==4.53.0"
            ]
        ),
        worker_nodes=[
            NodeConfig(
                node_type="cpu_worker",
                min_workers=0,
                max_workers=10,
                resources={"CPU": 4, "memory": 8000000000},
                use_gpu=False
            ),
            NodeConfig(
                node_type="gpu_worker",
                min_workers=0,
                max_workers=4,
                resources={"CPU": 8, "memory": 16000000000, "GPU": 1},
                use_gpu=True,
                gpu_count=1
            )
        ],
        max_workers=20,
        upscaling_speed=1.0,
        idle_timeout_minutes=5
    )