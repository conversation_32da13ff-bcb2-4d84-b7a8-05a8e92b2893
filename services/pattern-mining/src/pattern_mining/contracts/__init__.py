"""
Pattern Mining Service - CCL Contract Compliance Module

This module implements contract-compliant data models and validation for seamless
integration with CCL service platform (Repository Analysis, Query Intelligence, Marketplace).

Based on contract schemas from /contracts/schemas/:
- pattern-input-v1.json
- pattern-output-v1.json
- error-response-v1.json

Wave 2.5: CCL Contract Compliance Implementation
"""

from .models import (
    # Input Models
    PatternInputV1,
    ASTDataV1,
    FileASTData,
    PatternASTNode,
    PatternSymbol,
    DetectionConfigV1,
    ContextV1,
    
    # Output Models
    PatternOutputV1,
    DetectedPatternV1,
    PatternLocation,
    CodeExample,
    PatternSummaryV1,
    PatternMetadataV1,
    
    # Common Models
    PatternRange,
    PatternImpact,
    PatternMetrics,
    DetectionMethod,
    PerformanceStats,
    
    # Error Models
    ErrorResponseV1,
)

from .validators import (
    PatternInputValidator,
    PatternOutputValidator,
    ContractSchemaValidator,
    validate_pattern_input,
    validate_pattern_output,
    validate_error_response,
)

__all__ = [
    # Input Models
    "PatternInputV1",
    "ASTDataV1", 
    "FileASTData",
    "PatternASTNode",
    "PatternSymbol",
    "DetectionConfigV1",
    "ContextV1",
    
    # Output Models
    "PatternOutputV1",
    "DetectedPatternV1",
    "PatternLocation",
    "CodeExample",
    "PatternSummaryV1",
    "PatternMetadataV1",
    
    # Common Models
    "PatternRange",
    "PatternImpact",
    "PatternMetrics",
    "DetectionMethod",
    "PerformanceStats",
    
    # Error Models
    "ErrorResponseV1",
    
    # Validators
    "PatternInputValidator",
    "PatternOutputValidator",
    "ContractSchemaValidator",
    "validate_pattern_input",
    "validate_pattern_output",
    "validate_error_response",
]

# Contract Version Information
CONTRACT_VERSION = "1.0.0"
SUPPORTED_PATTERN_TYPES = [
    "design_pattern",
    "anti_pattern",
    "security_vulnerability",
    "performance_issue",
    "code_smell",
    "architectural_pattern",
    "test_pattern",
    "concurrency_pattern",
]
SUPPORTED_SEVERITIES = ["info", "low", "medium", "high", "critical"]
SUPPORTED_DETECTORS = [
    "design_patterns",
    "anti_patterns",
    "security_vulnerabilities", 
    "performance_issues",
    "code_smells",
    "architectural_patterns",
    "test_patterns",
    "concurrency_patterns",
]