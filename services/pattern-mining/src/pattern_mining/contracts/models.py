"""
Pattern Mining Service - Contract-Compliant Data Models

Implements CCL contract-compliant data models based on:
- /contracts/schemas/pattern-input-v1.json
- /contracts/schemas/pattern-output-v1.json 
- /contracts/schemas/error-response-v1.json

Wave 2.5: CCL Contract Compliance Implementation
"""

import re
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from enum import Enum

from pydantic import BaseModel, Field, validator, root_validator


# ==================== COMMON MODELS ====================

class PatternRange(BaseModel):
    """Source code position range for pattern locations"""
    start_line: int = Field(..., ge=1, description="Starting line number")
    end_line: int = Field(..., ge=1, description="Ending line number")
    start_column: Optional[int] = Field(None, ge=0, description="Starting column position")
    end_column: Optional[int] = Field(None, ge=0, description="Ending column position")
    
    @validator('end_line')
    def validate_line_order(cls, v, values):
        if 'start_line' in values and v < values['start_line']:
            raise ValueError('end_line must be >= start_line')
        return v


class PatternImpact(BaseModel):
    """Pattern impact assessment on code quality dimensions"""
    maintainability: str = Field(..., regex=r"^(positive|neutral|negative)$")
    performance: str = Field(..., regex=r"^(positive|neutral|negative)$")
    security: str = Field(..., regex=r"^(positive|neutral|negative)$")
    readability: str = Field(..., regex=r"^(positive|neutral|negative)$")
    testability: str = Field(..., regex=r"^(positive|neutral|negative)$")


class PatternMetrics(BaseModel):
    """Pattern-specific metrics and measurements"""
    complexity_increase: Optional[float] = Field(None, description="Complexity impact")
    lines_affected: Optional[int] = Field(None, ge=0, description="Lines of code affected")
    files_affected: Optional[int] = Field(None, ge=0, description="Files affected")
    estimated_fix_time_minutes: Optional[int] = Field(None, ge=0, description="Estimated fix time")


class DetectionMethod(BaseModel):
    """Information about how the pattern was detected"""
    class Algorithm(str, Enum):
        RULE_BASED = "rule_based"
        ML_CLASSIFICATION = "ml_classification"
        ML_CLUSTERING = "ml_clustering"
        HYBRID = "hybrid"
    
    algorithm: Algorithm = Field(..., description="Detection algorithm used")
    model_name: Optional[str] = Field(None, description="ML model name if applicable")
    rule_set: Optional[str] = Field(None, description="Rule set name if applicable")
    features_used: Optional[List[str]] = Field(None, description="Features used for detection")


class PerformanceStats(BaseModel):
    """Performance statistics for pattern detection"""
    files_processed: int = Field(..., ge=0, description="Number of files processed")
    patterns_detected: int = Field(..., ge=0, description="Total patterns detected")
    avg_confidence: float = Field(..., ge=0, le=1, description="Average confidence score")
    memory_peak_mb: Optional[float] = Field(None, ge=0, description="Peak memory usage")
    cache_hit_ratio: Optional[float] = Field(None, ge=0, le=1, description="Cache hit ratio")


# ==================== INPUT MODELS ====================

class PatternASTNode(BaseModel):
    """AST node optimized for pattern detection"""
    id: str = Field(..., description="Unique node identifier within file")
    type: str = Field(..., description="AST node type")
    name: Optional[str] = Field(None, description="Node name/identifier")
    range: PatternRange = Field(..., description="Source code position range")
    parent_id: Optional[str] = Field(None, description="Parent node ID")
    children_ids: Optional[List[str]] = Field(None, description="Child node IDs")
    properties: Optional[Dict[str, Any]] = Field(None, description="Node-specific properties")
    text: Optional[str] = Field(None, max_length=10000, description="Source text for this node")
    annotations: Optional[List[str]] = Field(None, description="Code annotations/decorators")


class PatternSymbol(BaseModel):
    """Symbol information for pattern detection"""
    class SymbolType(str, Enum):
        FUNCTION = "function"
        METHOD = "method"
        CLASS = "class"
        INTERFACE = "interface"
        VARIABLE = "variable"
        CONSTANT = "constant"
        TYPE = "type"
        NAMESPACE = "namespace"
    
    class Visibility(str, Enum):
        PUBLIC = "public"
        PRIVATE = "private"
        PROTECTED = "protected"
        INTERNAL = "internal"
    
    class ReferenceType(str, Enum):
        CALL = "call"
        INHERITANCE = "inheritance"
        COMPOSITION = "composition"
        DEPENDENCY = "dependency"
    
    name: str = Field(..., description="Symbol name")
    type: SymbolType = Field(..., description="Symbol type")
    range: PatternRange = Field(..., description="Symbol location range")
    visibility: Optional[Visibility] = Field(None, description="Symbol visibility")
    modifiers: Optional[List[str]] = Field(None, description="Symbol modifiers")
    signature: Optional[str] = Field(None, description="Full symbol signature")
    parameters: Optional[List[Dict[str, str]]] = Field(None, description="Function/method parameters")
    return_type: Optional[str] = Field(None, description="Return type")
    complexity: Optional[int] = Field(None, ge=0, description="Symbol complexity score")
    references: Optional[List[Dict[str, Union[str, int]]]] = Field(None, description="Symbol references")
    documentation: Optional[str] = Field(None, description="Associated documentation")
    annotations: Optional[List[str]] = Field(None, description="Symbol annotations")


class FileASTData(BaseModel):
    """AST data for a single file optimized for pattern detection"""
    file_path: str = Field(..., description="Relative file path from repository root")
    language: str = Field(..., description="Programming language")
    content_hash: str = Field(..., regex=r"^[a-f0-9]{64}$", description="SHA-256 hash of file content")
    size_bytes: Optional[int] = Field(None, ge=0, description="File size in bytes")
    ast_nodes: List[PatternASTNode] = Field(..., description="Flattened AST nodes")
    
    # File-level metrics
    metrics: Dict[str, Union[int, float]] = Field(..., description="File-level metrics")
    
    # Extracted symbols
    symbols: Optional[List[PatternSymbol]] = Field(None, description="Extracted symbols")
    
    # Import information
    imports: Optional[List[Dict[str, str]]] = Field(None, description="Import/dependency information")
    
    # Code features for ML models
    code_features: Optional[Dict[str, Any]] = Field(None, description="Code features for ML")
    
    @validator('metrics')
    def validate_required_metrics(cls, v):
        required_metrics = ['lines_of_code', 'complexity', 'function_count']
        for metric in required_metrics:
            if metric not in v:
                raise ValueError(f"Required metric '{metric}' is missing")
        return v


class ASTDataV1(BaseModel):
    """AST data optimized for pattern detection"""
    files: List[FileASTData] = Field(..., max_items=10000, description="File-level AST data")
    repository_metrics: Dict[str, Union[int, float, Dict[str, Any]]] = Field(
        ..., description="Repository-level metrics"
    )
    
    @validator('repository_metrics')
    def validate_required_repo_metrics(cls, v):
        required_metrics = ['total_files', 'total_lines', 'languages']
        for metric in required_metrics:
            if metric not in v:
                raise ValueError(f"Required repository metric '{metric}' is missing")
        return v


class DetectionConfigV1(BaseModel):
    """Pattern detection configuration"""
    class PatternDetector(str, Enum):
        DESIGN_PATTERNS = "design_patterns"
        ANTI_PATTERNS = "anti_patterns"
        SECURITY_VULNERABILITIES = "security_vulnerabilities"
        PERFORMANCE_ISSUES = "performance_issues"
        CODE_SMELLS = "code_smells"
        ARCHITECTURAL_PATTERNS = "architectural_patterns"
        TEST_PATTERNS = "test_patterns"
        CONCURRENCY_PATTERNS = "concurrency_patterns"
    
    enabled_detectors: List[PatternDetector] = Field(..., min_items=1, description="Enabled detectors")
    confidence_threshold: float = Field(..., ge=0, le=1, description="Minimum confidence threshold")
    
    # Language-specific settings
    language_specific: Optional[Dict[str, Dict[str, Any]]] = Field(
        None, description="Language-specific detection settings"
    )
    
    # Performance limits
    performance_limits: Optional[Dict[str, int]] = Field(
        None, description="Performance and resource limits"
    )
    
    # Output preferences
    output_preferences: Optional[Dict[str, Union[bool, int]]] = Field(
        None, description="Output formatting preferences"
    )


class ContextV1(BaseModel):
    """Additional context for pattern detection"""
    class Priority(str, Enum):
        LOW = "low"
        NORMAL = "normal"
        HIGH = "high"
        URGENT = "urgent"
    
    user_id: Optional[str] = Field(None, description="User identifier")
    organization_id: Optional[str] = Field(None, description="Organization identifier")
    priority: Priority = Field(Priority.NORMAL, description="Request priority")
    callback_url: Optional[str] = Field(None, description="Webhook URL for completion")
    tags: Optional[List[str]] = Field(None, description="User-defined tags")


class PatternInputV1(BaseModel):
    """Contract-compliant pattern detection input"""
    repository_id: str = Field(..., regex=r"^repo_[a-zA-Z0-9]{16}$", description="Repository identifier")
    analysis_id: str = Field(..., regex=r"^analysis_[a-zA-Z0-9]{16}$", description="Analysis identifier")
    request_id: Optional[str] = Field(None, regex=r"^req_[a-zA-Z0-9]{16}$", description="Request identifier")
    ast_data: ASTDataV1 = Field(..., description="AST data for pattern detection")
    detection_config: DetectionConfigV1 = Field(..., description="Detection configuration")
    context: Optional[ContextV1] = Field(None, description="Additional context")
    
    @validator('request_id', always=True)
    def generate_request_id(cls, v):
        if v is None:
            return f"req_{uuid.uuid4().hex[:16]}"
        return v


# ==================== OUTPUT MODELS ====================

class PatternLocation(BaseModel):
    """Location of a pattern in the codebase"""
    file_path: str = Field(..., description="Relative file path from repository root")
    range: PatternRange = Field(..., description="Line range where pattern occurs")
    symbol_name: Optional[str] = Field(None, description="Primary symbol involved")
    context: Optional[Dict[str, str]] = Field(None, description="Additional location context")
    snippet: Optional[str] = Field(None, max_length=2000, description="Code snippet showing the pattern")


class CodeExample(BaseModel):
    """Code example demonstrating the pattern"""
    title: str = Field(..., description="Example title")
    description: Optional[str] = Field(None, description="Example description")
    code: str = Field(..., max_length=5000, description="Code snippet")
    language: str = Field(..., description="Programming language")
    file_path: Optional[str] = Field(None, description="Source file path")
    line_range: Optional[Dict[str, int]] = Field(None, description="Line range in source file")
    annotations: Optional[List[Dict[str, Union[int, str]]]] = Field(
        None, description="Code annotations"
    )


class DetectedPatternV1(BaseModel):
    """A detected code pattern"""
    class PatternType(str, Enum):
        DESIGN_PATTERN = "design_pattern"
        ANTI_PATTERN = "anti_pattern"
        SECURITY_VULNERABILITY = "security_vulnerability"
        PERFORMANCE_ISSUE = "performance_issue"
        CODE_SMELL = "code_smell"
        ARCHITECTURAL_PATTERN = "architectural_pattern"
        TEST_PATTERN = "test_pattern"
        CONCURRENCY_PATTERN = "concurrency_pattern"
    
    class Severity(str, Enum):
        INFO = "info"
        LOW = "low"
        MEDIUM = "medium"
        HIGH = "high"
        CRITICAL = "critical"
    
    id: str = Field(..., regex=r"^pattern_[a-zA-Z0-9]{16}$", description="Pattern instance ID")
    pattern_type: PatternType = Field(..., description="Type of pattern detected")
    pattern_name: str = Field(..., description="Human-readable pattern name")
    confidence: float = Field(..., ge=0, le=1, description="Detection confidence score")
    severity: Severity = Field(..., description="Pattern severity level")
    locations: List[PatternLocation] = Field(..., min_items=1, description="Code locations")
    description: Optional[str] = Field(None, description="Detailed pattern description")
    explanation: Optional[str] = Field(None, description="Why this pattern was detected")
    recommendations: Optional[List[str]] = Field(None, description="Improvement recommendations")
    examples: Optional[List[CodeExample]] = Field(None, description="Code examples")
    related_patterns: Optional[List[str]] = Field(None, description="Related pattern IDs")
    tags: Optional[List[str]] = Field(None, description="Semantic tags")
    impact: Optional[PatternImpact] = Field(None, description="Pattern impact assessment")
    metrics: Optional[PatternMetrics] = Field(None, description="Pattern-specific metrics")
    detection_method: Optional[DetectionMethod] = Field(None, description="Detection method info")
    
    @validator('id', always=True)
    def generate_pattern_id(cls, v):
        if v is None or not v.startswith('pattern_'):
            return f"pattern_{uuid.uuid4().hex[:16]}"
        return v


class PatternSummaryV1(BaseModel):
    """Summary of all detected patterns"""
    total_patterns: int = Field(..., ge=0, description="Total number of patterns detected")
    by_type: Dict[str, int] = Field(..., description="Pattern count by type")
    by_severity: Dict[str, int] = Field(..., description="Pattern count by severity")
    by_language: Optional[Dict[str, int]] = Field(None, description="Pattern count by language")
    
    # Top patterns
    top_patterns: Optional[List[Dict[str, Union[str, int, float]]]] = Field(
        None, max_items=10, description="Most frequently detected patterns"
    )
    
    # Quality scores
    quality_scores: Dict[str, float] = Field(..., description="Overall code quality scores")
    
    # Recommendations
    recommendations: Optional[List[Dict[str, Union[str, int, float]]]] = Field(
        None, description="Top-level improvement recommendations"
    )
    
    # Trends (if historical data available)
    trends: Optional[Dict[str, Union[List[str], int]]] = Field(
        None, description="Pattern trends"
    )
    
    @validator('quality_scores')
    def validate_quality_scores(cls, v):
        if 'overall_score' not in v:
            raise ValueError("overall_score is required in quality_scores")
        for score in v.values():
            if not (0 <= score <= 100):
                raise ValueError("Quality scores must be between 0 and 100")
        return v


class PatternMetadataV1(BaseModel):
    """Detection metadata and performance info"""
    version: str = Field(..., regex=r"^\d+\.\d+\.\d+$", description="Service version")
    timestamp: datetime = Field(..., description="Detection completion timestamp")
    processing_time_ms: int = Field(..., ge=0, description="Total processing time")
    model_versions: Optional[Dict[str, str]] = Field(None, description="ML model versions")
    performance_stats: Optional[PerformanceStats] = Field(None, description="Performance statistics")
    detection_config: Optional[Dict[str, Any]] = Field(None, description="Detection configuration used")


class PatternOutputV1(BaseModel):
    """Contract-compliant pattern detection output"""
    request_id: str = Field(..., regex=r"^req_[a-zA-Z0-9]{16}$", description="Request identifier")
    repository_id: str = Field(..., regex=r"^repo_[a-zA-Z0-9]{16}$", description="Repository identifier")
    analysis_id: Optional[str] = Field(None, regex=r"^analysis_[a-zA-Z0-9]{16}$", description="Analysis identifier")
    patterns: List[DetectedPatternV1] = Field(..., max_items=1000, description="Detected patterns")
    summary: PatternSummaryV1 = Field(..., description="Pattern summary")
    metadata: PatternMetadataV1 = Field(..., description="Detection metadata")


# ==================== ERROR MODELS ====================

class ErrorResponseV1(BaseModel):
    """Unified error response format"""
    class ErrorType(str, Enum):
        VALIDATION = "validation"
        TIMEOUT = "timeout"
        INTERNAL = "internal"
        EXTERNAL = "external"
    
    error_id: str = Field(..., description="Unique error identifier")
    service: str = Field(..., description="Service name")
    error_type: ErrorType = Field(..., description="Error type")
    message: str = Field(..., description="Human-readable error message")
    retryable: bool = Field(..., description="Whether the error is retryable")
    user_message: Optional[str] = Field(None, description="User-friendly message")
    correlation_id: Optional[str] = Field(None, description="Request correlation ID")
    timestamp: datetime = Field(..., description="Error timestamp")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional error context")
    
    @validator('error_id', always=True)
    def generate_error_id(cls, v):
        if v is None:
            return str(uuid.uuid4())
        return v
    
    @validator('timestamp', always=True)
    def set_timestamp(cls, v):
        if v is None:
            return datetime.utcnow()
        return v