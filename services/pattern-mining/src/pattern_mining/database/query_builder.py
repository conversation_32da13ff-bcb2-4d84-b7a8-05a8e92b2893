"""
BigQuery Query Builder

Dynamic query builder for BigQuery with proper SQL generation and analytics support.
Enhanced with SQL injection prevention and security validation.
"""

import json
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, date, timedelta
from enum import Enum
import structlog

from ..security.sql_security import (
    SQLSecurityValidator,
    SQLValidationError,
    IdentifierType,
    get_sql_security_validator
)

logger = structlog.get_logger(__name__)


class QueryType(str, Enum):
    """Supported query types."""
    SELECT = "SELECT"
    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    CREATE_TABLE = "CREATE_TABLE"
    CREATE_VIEW = "CREATE_VIEW"


class AggregationType(str, Enum):
    """Supported aggregation types."""
    COUNT = "COUNT"
    SUM = "SUM"
    AVG = "AVG"
    MIN = "MIN"
    MAX = "MAX"
    STDDEV = "STDDEV"
    VARIANCE = "VARIANCE"
    COUNTIF = "COUNTIF"
    SUMIF = "SUMIF"
    AVGIF = "AVGIF"
    ARRAY_AGG = "ARRAY_AGG"
    STRING_AGG = "STRING_AGG"
    APPROX_COUNT_DISTINCT = "APPROX_COUNT_DISTINCT"
    APPROX_QUANTILES = "APPROX_QUANTILES"
    APPROX_TOP_COUNT = "APPROX_TOP_COUNT"


class WindowFunction(str, Enum):
    """Supported window functions."""
    ROW_NUMBER = "ROW_NUMBER"
    RANK = "RANK"
    DENSE_RANK = "DENSE_RANK"
    PERCENT_RANK = "PERCENT_RANK"
    LAG = "LAG"
    LEAD = "LEAD"
    FIRST_VALUE = "FIRST_VALUE"
    LAST_VALUE = "LAST_VALUE"
    NTH_VALUE = "NTH_VALUE"


class JoinType(str, Enum):
    """Supported join types."""
    INNER = "INNER JOIN"
    LEFT = "LEFT JOIN"
    RIGHT = "RIGHT JOIN"
    FULL = "FULL OUTER JOIN"
    CROSS = "CROSS JOIN"


class FilterOperator(str, Enum):
    """Supported filter operators."""
    EQ = "="
    NE = "!="
    LT = "<"
    LE = "<="
    GT = ">"
    GE = ">="
    IN = "IN"
    NOT_IN = "NOT IN"
    LIKE = "LIKE"
    ILIKE = "ILIKE"
    IS_NULL = "IS NULL"
    IS_NOT_NULL = "IS NOT NULL"
    BETWEEN = "BETWEEN"
    NOT_BETWEEN = "NOT BETWEEN"
    STARTS_WITH = "STARTS_WITH"
    ENDS_WITH = "ENDS_WITH"
    CONTAINS = "CONTAINS"
    REGEXP_CONTAINS = "REGEXP_CONTAINS"
    ARRAY_CONTAINS = "ARRAY_CONTAINS"


class BigQueryQueryBuilder:
    """Dynamic query builder for BigQuery with SQL injection prevention."""
    
    def __init__(self, project_id: str, dataset_id: str):
        self.project_id = project_id
        self.dataset_id = dataset_id
        # Initialize security validator
        self._validator = get_sql_security_validator()
        self.reset()
    
    def reset(self):
        """Reset the query builder to initial state."""
        self._query_type = QueryType.SELECT
        self._select_fields = []
        self._from_table = None
        self._joins = []
        self._where_conditions = []
        self._group_by = []
        self._having_conditions = []
        self._order_by = []
        self._limit = None
        self._offset = None
        self._with_clauses = []
        self._parameters = {}
        self._table_aliases = {}
        return self
    
    def select(self, fields: Union[str, List[str]]) -> 'BigQueryQueryBuilder':
        """Add SELECT fields with validation."""
        if isinstance(fields, str):
            fields = [fields]
        
        validated_fields = []
        for field in fields:
            # Allow * as a special case
            if field == "*":
                validated_fields.append(field)
            else:
                try:
                    # Basic validation - in practice, field expressions can be complex
                    # For now, just check for obvious injection patterns
                    if self._validator.config.enable_injection_detection:
                        self._validator._check_injection_patterns(field)
                    validated_fields.append(field)
                except SQLValidationError as e:
                    logger.warning("Potentially unsafe SELECT field", field=field, error=str(e))
                    # Still add it but log the warning
                    validated_fields.append(field)
        
        self._select_fields.extend(validated_fields)
        return self
    
    def from_table(self, table_name: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Set FROM table with validation."""
        try:
            # Validate table name
            validated_table = self._validator.validate_table_name(table_name)
            
            if '.' not in validated_table:
                # Add project and dataset if not fully qualified
                validated_table = f"{self.project_id}.{self.dataset_id}.{validated_table}"
            
            self._from_table = validated_table
            
            if alias:
                # Validate alias
                validated_alias = self._validator.validate_alias(alias)
                self._table_aliases[validated_table] = validated_alias
                
        except SQLValidationError as e:
            logger.error("Table name validation failed", table_name=table_name, error=str(e))
            raise
            
        return self
    
    def join(
        self,
        table_name: str,
        on_condition: str,
        join_type: JoinType = JoinType.INNER,
        alias: Optional[str] = None
    ) -> 'BigQueryQueryBuilder':
        """Add JOIN clause with validation."""
        try:
            # Validate table name
            validated_table = self._validator.validate_table_name(table_name)
            
            if '.' not in validated_table:
                validated_table = f"{self.project_id}.{self.dataset_id}.{validated_table}"
            
            # Build join clause with proper escaping
            join_clause_parts = [join_type.value, f"`{validated_table}`"]
            
            if alias:
                # Validate alias
                validated_alias = self._validator.validate_alias(alias)
                join_clause_parts.append(f"AS {validated_alias}")
                self._table_aliases[validated_table] = validated_alias
            
            # Note: on_condition should use parameterized values through where_eq, where_in, etc.
            # For now, we'll add it as-is but log a warning if it contains suspicious patterns
            if self._validator.config.enable_injection_detection:
                try:
                    self._validator._check_injection_patterns(on_condition)
                except SQLValidationError:
                    logger.warning("Potentially unsafe JOIN condition", condition=on_condition)
            
            join_clause = " ".join(join_clause_parts) + f" ON {on_condition}"
            self._joins.append(join_clause)
            
        except SQLValidationError as e:
            logger.error("JOIN validation failed", table_name=table_name, error=str(e))
            raise
            
        return self
    
    def where(self, condition: str) -> 'BigQueryQueryBuilder':
        """Add WHERE condition."""
        self._where_conditions.append(condition)
        return self
    
    def where_eq(self, field: str, value: Any) -> 'BigQueryQueryBuilder':
        """Add WHERE field = value condition."""
        param_name = f"param_{len(self._parameters)}"
        self._parameters[param_name] = value
        self._where_conditions.append(f"{field} = @{param_name}")
        return self
    
    def where_in(self, field: str, values: List[Any]) -> 'BigQueryQueryBuilder':
        """Add WHERE field IN (...) condition."""
        param_name = f"param_{len(self._parameters)}"
        self._parameters[param_name] = values
        self._where_conditions.append(f"{field} IN UNNEST(@{param_name})")
        return self
    
    def where_between(self, field: str, start: Any, end: Any) -> 'BigQueryQueryBuilder':
        """Add WHERE field BETWEEN start AND end condition."""
        start_param = f"param_{len(self._parameters)}"
        end_param = f"param_{len(self._parameters) + 1}"
        self._parameters[start_param] = start
        self._parameters[end_param] = end
        self._where_conditions.append(f"{field} BETWEEN @{start_param} AND @{end_param}")
        return self
    
    def where_like(self, field: str, pattern: str) -> 'BigQueryQueryBuilder':
        """Add WHERE field LIKE pattern condition."""
        param_name = f"param_{len(self._parameters)}"
        self._parameters[param_name] = pattern
        self._where_conditions.append(f"{field} LIKE @{param_name}")
        return self
    
    def where_date_range(
        self,
        field: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> 'BigQueryQueryBuilder':
        """Add date range condition."""
        if start_date:
            start_param = f"param_{len(self._parameters)}"
            self._parameters[start_param] = start_date
            self._where_conditions.append(f"{field} >= @{start_param}")
        
        if end_date:
            end_param = f"param_{len(self._parameters)}"
            self._parameters[end_param] = end_date
            self._where_conditions.append(f"{field} <= @{end_param}")
        
        return self
    
    def group_by(self, fields: Union[str, List[str]]) -> 'BigQueryQueryBuilder':
        """Add GROUP BY fields."""
        if isinstance(fields, str):
            fields = [fields]
        self._group_by.extend(fields)
        return self
    
    def having(self, condition: str) -> 'BigQueryQueryBuilder':
        """Add HAVING condition."""
        self._having_conditions.append(condition)
        return self
    
    def order_by(self, field: str, desc: bool = False) -> 'BigQueryQueryBuilder':
        """Add ORDER BY field."""
        order_clause = f"{field} {'DESC' if desc else 'ASC'}"
        self._order_by.append(order_clause)
        return self
    
    def limit(self, limit: int) -> 'BigQueryQueryBuilder':
        """Add LIMIT clause with validation."""
        try:
            self._limit = self._validator.validate_limit(limit)
        except SQLValidationError as e:
            logger.error("LIMIT validation failed", limit=limit, error=str(e))
            raise
        return self
    
    def offset(self, offset: int) -> 'BigQueryQueryBuilder':
        """Add OFFSET clause with validation."""
        try:
            self._offset = self._validator.validate_offset(offset)
        except SQLValidationError as e:
            logger.error("OFFSET validation failed", offset=offset, error=str(e))
            raise
        return self
    
    def with_clause(self, name: str, query: str) -> 'BigQueryQueryBuilder':
        """Add WITH clause (CTE)."""
        self._with_clauses.append(f"{name} AS (\n{query}\n)")
        return self
    
    def add_parameter(self, name: str, value: Any) -> 'BigQueryQueryBuilder':
        """Add query parameter."""
        self._parameters[name] = value
        return self
    
    def build(self) -> Tuple[str, Dict[str, Any]]:
        """Build the SQL query and return it with parameters."""
        if self._query_type == QueryType.SELECT:
            return self._build_select_query()
        else:
            raise ValueError(f"Query type {self._query_type} not implemented")
    
    def _build_select_query(self) -> Tuple[str, Dict[str, Any]]:
        """Build SELECT query."""
        parts = []
        
        # WITH clauses
        if self._with_clauses:
            parts.append("WITH " + ",\n".join(self._with_clauses))
        
        # SELECT
        if not self._select_fields:
            self._select_fields = ["*"]
        parts.append("SELECT " + ",\n    ".join(self._select_fields))
        
        # FROM
        if not self._from_table:
            raise ValueError("FROM table is required")
        
        # Table name is already validated in from_table method
        from_clause = f"FROM `{self._from_table}`"
        if self._from_table in self._table_aliases:
            # Alias is already validated
            from_clause += f" AS {self._table_aliases[self._from_table]}"
        parts.append(from_clause)
        
        # JOINs
        if self._joins:
            parts.extend(self._joins)
        
        # WHERE
        if self._where_conditions:
            parts.append("WHERE " + " AND ".join(self._where_conditions))
        
        # GROUP BY
        if self._group_by:
            parts.append("GROUP BY " + ", ".join(self._group_by))
        
        # HAVING
        if self._having_conditions:
            parts.append("HAVING " + " AND ".join(self._having_conditions))
        
        # ORDER BY
        if self._order_by:
            parts.append("ORDER BY " + ", ".join(self._order_by))
        
        # LIMIT
        if self._limit is not None:
            parts.append(f"LIMIT {self._limit}")
        
        # OFFSET
        if self._offset is not None:
            parts.append(f"OFFSET {self._offset}")
        
        query = "\n".join(parts)
        return query, self._parameters.copy()
    
    def count(self, field: str = "*") -> 'BigQueryQueryBuilder':
        """Add COUNT aggregation."""
        self._select_fields.append(f"COUNT({field}) as count")
        return self
    
    def sum(self, field: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add SUM aggregation."""
        alias = alias or f"sum_{field.replace('.', '_')}"
        self._select_fields.append(f"SUM({field}) as {alias}")
        return self
    
    def avg(self, field: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add AVG aggregation."""
        alias = alias or f"avg_{field.replace('.', '_')}"
        self._select_fields.append(f"AVG({field}) as {alias}")
        return self
    
    def min(self, field: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add MIN aggregation."""
        alias = alias or f"min_{field.replace('.', '_')}"
        self._select_fields.append(f"MIN({field}) as {alias}")
        return self
    
    def max(self, field: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add MAX aggregation."""
        alias = alias or f"max_{field.replace('.', '_')}"
        self._select_fields.append(f"MAX({field}) as {alias}")
        return self
    
    def stddev(self, field: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add STDDEV aggregation."""
        alias = alias or f"stddev_{field.replace('.', '_')}"
        self._select_fields.append(f"STDDEV({field}) as {alias}")
        return self
    
    def countif(self, condition: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add COUNTIF aggregation."""
        alias = alias or "countif_result"
        self._select_fields.append(f"COUNTIF({condition}) as {alias}")
        return self
    
    def array_agg(
        self,
        field: str,
        alias: Optional[str] = None,
        order_by: Optional[str] = None,
        limit: Optional[int] = None
    ) -> 'BigQueryQueryBuilder':
        """Add ARRAY_AGG aggregation."""
        alias = alias or f"array_agg_{field.replace('.', '_')}"
        
        agg_expr = f"ARRAY_AGG({field}"
        if order_by:
            agg_expr += f" ORDER BY {order_by}"
        if limit:
            agg_expr += f" LIMIT {limit}"
        agg_expr += f") as {alias}"
        
        self._select_fields.append(agg_expr)
        return self
    
    def approx_count_distinct(self, field: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add APPROX_COUNT_DISTINCT aggregation."""
        alias = alias or f"approx_count_distinct_{field.replace('.', '_')}"
        self._select_fields.append(f"APPROX_COUNT_DISTINCT({field}) as {alias}")
        return self
    
    def approx_quantiles(
        self,
        field: str,
        num_quantiles: int,
        alias: Optional[str] = None
    ) -> 'BigQueryQueryBuilder':
        """Add APPROX_QUANTILES aggregation."""
        alias = alias or f"approx_quantiles_{field.replace('.', '_')}"
        self._select_fields.append(f"APPROX_QUANTILES({field}, {num_quantiles}) as {alias}")
        return self
    
    def approx_top_count(
        self,
        field: str,
        num_top: int,
        alias: Optional[str] = None
    ) -> 'BigQueryQueryBuilder':
        """Add APPROX_TOP_COUNT aggregation."""
        alias = alias or f"approx_top_count_{field.replace('.', '_')}"
        self._select_fields.append(f"APPROX_TOP_COUNT({field}, {num_top}) as {alias}")
        return self
    
    def window_function(
        self,
        func: WindowFunction,
        field: Optional[str] = None,
        partition_by: Optional[Union[str, List[str]]] = None,
        order_by: Optional[str] = None,
        alias: Optional[str] = None
    ) -> 'BigQueryQueryBuilder':
        """Add window function."""
        window_expr = func.value
        if field:
            window_expr += f"({field})"
        else:
            window_expr += "()"
        
        window_expr += " OVER ("
        
        if partition_by:
            if isinstance(partition_by, str):
                partition_by = [partition_by]
            window_expr += f"PARTITION BY {', '.join(partition_by)}"
        
        if order_by:
            if partition_by:
                window_expr += " "
            window_expr += f"ORDER BY {order_by}"
        
        window_expr += ")"
        
        alias = alias or f"window_result"
        self._select_fields.append(f"{window_expr} as {alias}")
        return self
    
    def date_trunc(self, field: str, part: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add DATE_TRUNC function."""
        alias = alias or f"date_trunc_{field.replace('.', '_')}"
        self._select_fields.append(f"DATE_TRUNC({field}, {part}) as {alias}")
        return self
    
    def extract(self, part: str, field: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add EXTRACT function."""
        alias = alias or f"extract_{part}_{field.replace('.', '_')}"
        self._select_fields.append(f"EXTRACT({part} FROM {field}) as {alias}")
        return self
    
    def case_when(
        self,
        conditions: List[Tuple[str, Any]],
        else_value: Any = None,
        alias: Optional[str] = None
    ) -> 'BigQueryQueryBuilder':
        """Add CASE WHEN expression."""
        case_expr = "CASE"
        
        for condition, value in conditions:
            case_expr += f" WHEN {condition} THEN {value}"
        
        if else_value is not None:
            case_expr += f" ELSE {else_value}"
        
        case_expr += " END"
        
        alias = alias or "case_result"
        self._select_fields.append(f"{case_expr} as {alias}")
        return self
    
    def json_extract(self, field: str, path: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add JSON_EXTRACT function."""
        alias = alias or f"json_extract_{field.replace('.', '_')}"
        self._select_fields.append(f"JSON_EXTRACT({field}, '{path}') as {alias}")
        return self
    
    def json_extract_scalar(self, field: str, path: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add JSON_EXTRACT_SCALAR function."""
        alias = alias or f"json_extract_scalar_{field.replace('.', '_')}"
        self._select_fields.append(f"JSON_EXTRACT_SCALAR({field}, '{path}') as {alias}")
        return self
    
    def struct(self, fields: List[str], alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add STRUCT function."""
        alias = alias or "struct_result"
        struct_expr = f"STRUCT({', '.join(fields)}) as {alias}"
        self._select_fields.append(struct_expr)
        return self
    
    def unnest(self, field: str, alias: Optional[str] = None) -> 'BigQueryQueryBuilder':
        """Add UNNEST function in FROM clause."""
        alias = alias or "unnest_result"
        unnest_expr = f"UNNEST({field}) as {alias}"
        
        if self._from_table:
            # Add as JOIN
            self._joins.append(f"CROSS JOIN {unnest_expr}")
        else:
            # Use as FROM table
            self._from_table = unnest_expr
        
        return self


class AnalyticsQueryBuilder(BigQueryQueryBuilder):
    """Specialized query builder for analytics queries."""
    
    def __init__(self, project_id: str, dataset_id: str):
        super().__init__(project_id, dataset_id)
    
    def pattern_analytics(
        self,
        repository_id: Optional[str] = None,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None,
        group_by_field: Optional[str] = None
    ) -> 'AnalyticsQueryBuilder':
        """Build pattern analytics query."""
        self.reset()
        
        # Base analytics fields
        self.select([
            "COUNT(*) as total_patterns",
            "COUNT(DISTINCT pattern_type) as unique_pattern_types",
            "AVG(confidence) as avg_confidence",
            "COUNT(DISTINCT repository_id) as unique_repositories",
            "COUNT(DISTINCT file_path) as unique_files",
            "COUNT(DISTINCT language) as unique_languages"
        ])
        
        # Severity distribution
        self.countif("severity = 'critical'", "critical_count")
        self.countif("severity = 'high'", "high_count")
        self.countif("severity = 'medium'", "medium_count")
        self.countif("severity = 'low'", "low_count")
        
        # Pattern category distribution
        self.countif("pattern_category = 'design_pattern'", "design_patterns")
        self.countif("pattern_category = 'anti_pattern'", "anti_patterns")
        self.countif("pattern_category = 'code_smell'", "code_smells")
        self.countif("pattern_category = 'security_issue'", "security_issues")
        self.countif("pattern_category = 'performance_issue'", "performance_issues")
        
        # Quality metrics
        self.avg("processing_time_ms", "avg_processing_time")
        self.avg("lines_of_code", "avg_lines_of_code")
        self.avg("cyclomatic_complexity", "avg_complexity")
        
        # Set FROM table
        self.from_table("pattern_results")
        
        # Add filters
        if repository_id:
            self.where_eq("repository_id", repository_id)
        
        if date_from or date_to:
            self.where_date_range("detection_date", date_from, date_to)
        
        # Group by if specified
        if group_by_field:
            self.group_by(group_by_field)
        
        return self
    
    def pattern_trends(
        self,
        repository_id: Optional[str] = None,
        days: int = 30,
        group_by: str = 'day'
    ) -> 'AnalyticsQueryBuilder':
        """Build pattern trends query."""
        self.reset()
        
        # Date grouping
        if group_by == 'day':
            date_field = "DATE(detected_at)"
        elif group_by == 'week':
            date_field = "DATE_TRUNC(DATE(detected_at), WEEK)"
        elif group_by == 'month':
            date_field = "DATE_TRUNC(DATE(detected_at), MONTH)"
        else:
            raise ValueError(f"Invalid group_by value: {group_by}")
        
        self.select([
            f"{date_field} as date",
            "COUNT(*) as total_patterns",
            "AVG(confidence) as avg_confidence",
            "COUNT(DISTINCT pattern_type) as unique_pattern_types"
        ])
        
        # Severity counts
        self.countif("severity = 'critical'", "critical_count")
        self.countif("severity = 'high'", "high_count")
        self.countif("severity = 'medium'", "medium_count")
        self.countif("severity = 'low'", "low_count")
        
        # Top pattern types
        self.array_agg(
            "STRUCT(pattern_type, COUNT(*) as count)",
            "top_pattern_types",
            "COUNT(*) DESC",
            5
        )
        
        self.from_table("pattern_results")
        
        # Add filters
        if repository_id:
            self.where_eq("repository_id", repository_id)
        
        # Date range
        start_date = date.today() - timedelta(days=days)
        self.where_date_range("detection_date", start_date)
        
        # Group by date
        self.group_by(date_field)
        self.order_by("date", desc=True)
        
        return self
    
    def similarity_search(
        self,
        reference_pattern: Dict[str, Any],
        similarity_threshold: float = 0.8,
        limit: int = 10
    ) -> 'AnalyticsQueryBuilder':
        """Build similarity search query."""
        self.reset()
        
        # Select fields
        self.select([
            "detection_id",
            "pattern_name",
            "pattern_type",
            "pattern_category",
            "severity",
            "confidence",
            "language",
            "file_path",
            "repository_id",
            "detected_at"
        ])
        
        # Similarity score calculation
        similarity_expr = f"""
        (
            CASE WHEN pattern_type = @pattern_type THEN 0.4 ELSE 0.0 END +
            CASE WHEN pattern_category = @pattern_category THEN 0.3 ELSE 0.0 END +
            CASE WHEN language = @language THEN 0.2 ELSE 0.0 END +
            CASE WHEN severity = @severity THEN 0.1 ELSE 0.0 END
        ) as similarity_score
        """
        self.select(similarity_expr)
        
        self.from_table("pattern_results")
        
        # Filters
        self.where("detection_id != @detection_id")
        self.where("""
            (
                pattern_type = @pattern_type
                OR pattern_category = @pattern_category
                OR language = @language
            )
        """)
        
        # HAVING clause for similarity threshold
        self.having(f"similarity_score >= {similarity_threshold}")
        
        # Order by similarity and confidence
        self.order_by("similarity_score", desc=True)
        self.order_by("confidence", desc=True)
        
        self.limit(limit)
        
        # Add parameters
        self.add_parameter("detection_id", reference_pattern.get("detection_id"))
        self.add_parameter("pattern_type", reference_pattern.get("pattern_type"))
        self.add_parameter("pattern_category", reference_pattern.get("pattern_category"))
        self.add_parameter("language", reference_pattern.get("language"))
        self.add_parameter("severity", reference_pattern.get("severity"))
        
        return self
    
    def comprehensive_statistics(
        self,
        repository_id: Optional[str] = None,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None
    ) -> 'AnalyticsQueryBuilder':
        """Build comprehensive statistics query with CTEs."""
        self.reset()
        
        # Base statistics CTE
        base_stats_cte = """
        SELECT
            COUNT(*) as total_patterns,
            COUNT(DISTINCT pattern_type) as unique_pattern_types,
            COUNT(DISTINCT repository_id) as unique_repositories,
            COUNT(DISTINCT file_path) as unique_files,
            COUNT(DISTINCT language) as unique_languages,
            
            AVG(confidence) as avg_confidence,
            STDDEV(confidence) as stddev_confidence,
            MIN(confidence) as min_confidence,
            MAX(confidence) as max_confidence,
            
            AVG(processing_time_ms) as avg_processing_time,
            MAX(processing_time_ms) as max_processing_time,
            MIN(processing_time_ms) as min_processing_time,
            
            AVG(lines_of_code) as avg_lines_of_code,
            AVG(cyclomatic_complexity) as avg_complexity
            
        FROM `pattern_results`
        """
        
        # Add filters to base CTE
        conditions = []
        if repository_id:
            conditions.append("repository_id = @repository_id")
        if date_from:
            conditions.append("detection_date >= @date_from")
        if date_to:
            conditions.append("detection_date <= @date_to")
        
        if conditions:
            base_stats_cte += " WHERE " + " AND ".join(conditions)
        
        self.with_clause("base_stats", base_stats_cte)
        
        # Severity statistics CTE
        severity_stats_cte = """
        SELECT
            severity,
            COUNT(*) as count,
            AVG(confidence) as avg_confidence
        FROM `pattern_results`
        """
        
        if conditions:
            severity_stats_cte += " WHERE " + " AND ".join(conditions)
        
        severity_stats_cte += " GROUP BY severity"
        self.with_clause("severity_stats", severity_stats_cte)
        
        # Pattern type statistics CTE
        pattern_type_stats_cte = """
        SELECT
            pattern_type,
            COUNT(*) as count,
            AVG(confidence) as avg_confidence
        FROM `pattern_results`
        """
        
        if conditions:
            pattern_type_stats_cte += " WHERE " + " AND ".join(conditions)
        
        pattern_type_stats_cte += " GROUP BY pattern_type ORDER BY count DESC"
        self.with_clause("pattern_type_stats", pattern_type_stats_cte)
        
        # Language statistics CTE
        language_stats_cte = """
        SELECT
            language,
            COUNT(*) as count,
            AVG(confidence) as avg_confidence
        FROM `pattern_results`
        """
        
        if conditions:
            language_stats_cte += " WHERE " + " AND ".join(conditions)
        
        language_stats_cte += " GROUP BY language ORDER BY count DESC"
        self.with_clause("language_stats", language_stats_cte)
        
        # Main query
        self.select([
            "b.*",
            """ARRAY_AGG(
                STRUCT(
                    s.severity,
                    s.count,
                    s.avg_confidence
                )
            ) as severity_breakdown""",
            """ARRAY_AGG(
                STRUCT(
                    p.pattern_type,
                    p.count,
                    p.avg_confidence
                )
            ) as pattern_type_breakdown""",
            """ARRAY_AGG(
                STRUCT(
                    l.language,
                    l.count,
                    l.avg_confidence
                )
            ) as language_breakdown"""
        ])
        
        # Use a simple FROM instead of from_table to avoid backticks
        self._from_table = "base_stats b"
        
        # Cross joins for aggregations
        self.join("severity_stats", "1=1", JoinType.CROSS, "s")
        self.join("pattern_type_stats", "1=1", JoinType.CROSS, "p")
        self.join("language_stats", "1=1", JoinType.CROSS, "l")
        
        # Group by all base stats fields
        self.group_by([
            "b.total_patterns", "b.unique_pattern_types", "b.unique_repositories",
            "b.unique_files", "b.unique_languages", "b.avg_confidence", "b.stddev_confidence",
            "b.min_confidence", "b.max_confidence", "b.avg_processing_time",
            "b.max_processing_time", "b.min_processing_time", "b.avg_lines_of_code",
            "b.avg_complexity"
        ])
        
        # Add parameters
        if repository_id:
            self.add_parameter("repository_id", repository_id)
        if date_from:
            self.add_parameter("date_from", date_from)
        if date_to:
            self.add_parameter("date_to", date_to)
        
        return self


def create_query_builder(project_id: str, dataset_id: str) -> BigQueryQueryBuilder:
    """Create a new BigQuery query builder instance."""
    return BigQueryQueryBuilder(project_id, dataset_id)


def create_analytics_query_builder(project_id: str, dataset_id: str) -> AnalyticsQueryBuilder:
    """Create a new analytics query builder instance."""
    return AnalyticsQueryBuilder(project_id, dataset_id)