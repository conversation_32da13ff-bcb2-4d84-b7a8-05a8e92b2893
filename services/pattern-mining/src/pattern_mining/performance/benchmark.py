"""
Performance benchmarking framework for pattern mining service.

Validates claimed performance metrics including:
- 1M+ LOC/minute processing capability
- Memory efficiency under load
- Throughput validation
- Latency measurements
"""

import asyncio
import time
import statistics
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import json
import logging
from datetime import datetime

from ..core.patterns import PatternMiner
from ..utils.metrics import MetricsCollector
from ..cache.redis_client import RedisClient
from ..storage.file_handler import FileHandler


@dataclass
class BenchmarkResult:
    """Result of a performance benchmark test."""
    test_name: str
    duration_seconds: float
    lines_processed: int
    loc_per_minute: float
    memory_peak_mb: float
    memory_avg_mb: float
    throughput_rps: float
    latency_p50_ms: float
    latency_p95_ms: float
    latency_p99_ms: float
    success_rate: float
    error_count: int
    patterns_found: int
    cache_hit_rate: float
    timestamp: datetime
    metadata: Dict[str, Any]


class PerformanceBenchmark:
    """
    Comprehensive performance benchmarking framework.
    
    Validates the claimed 1M+ LOC/minute processing capability and provides
    detailed performance metrics for production validation.
    """
    
    def __init__(self, redis_client: Optional[RedisClient] = None):
        self.logger = logging.getLogger(__name__)
        self.redis_client = redis_client
        self.pattern_miner = PatternMiner()
        self.metrics_collector = MetricsCollector()
        self.file_handler = FileHandler()
        
        # Performance targets
        self.target_loc_per_minute = 1_000_000  # 1M LOC/minute
        self.target_throughput_rps = 1000  # 1000 RPS
        self.target_latency_p95_ms = 100  # 100ms P95
        self.target_success_rate = 0.99  # 99% success rate
        
        # Test data directory
        self.test_data_dir = Path("tests/performance/data")
        self.results_dir = Path("tests/performance/results")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Memory tracking
        self.memory_samples = []
        self.latency_samples = []
        
    async def run_comprehensive_benchmark(self) -> Dict[str, BenchmarkResult]:
        """
        Run comprehensive performance benchmarks.
        
        Returns:
            Dict[str, BenchmarkResult]: Results for each benchmark test
        """
        self.logger.info("Starting comprehensive performance benchmark suite")
        
        results = {}
        
        # 1. LOC Processing Benchmark
        self.logger.info("Running LOC processing benchmark...")
        results["loc_processing"] = await self._benchmark_loc_processing()
        
        # 2. Throughput Benchmark
        self.logger.info("Running throughput benchmark...")
        results["throughput"] = await self._benchmark_throughput()
        
        # 3. Memory Efficiency Benchmark
        self.logger.info("Running memory efficiency benchmark...")
        results["memory_efficiency"] = await self._benchmark_memory_efficiency()
        
        # 4. Latency Benchmark
        self.logger.info("Running latency benchmark...")
        results["latency"] = await self._benchmark_latency()
        
        # 5. Cache Performance Benchmark
        self.logger.info("Running cache performance benchmark...")
        results["cache_performance"] = await self._benchmark_cache_performance()
        
        # 6. Concurrent Processing Benchmark
        self.logger.info("Running concurrent processing benchmark...")
        results["concurrent_processing"] = await self._benchmark_concurrent_processing()
        
        # Save results
        await self._save_benchmark_results(results)
        
        # Generate performance report
        self._generate_performance_report(results)
        
        return results
    
    async def _benchmark_loc_processing(self) -> BenchmarkResult:
        """
        Benchmark LOC processing speed to validate 1M+ LOC/minute claim.
        
        Returns:
            BenchmarkResult: LOC processing benchmark results
        """
        start_time = time.time()
        total_lines = 0
        errors = 0
        patterns_found = 0
        
        # Generate test files with known LOC counts
        test_files = await self._generate_test_files()
        
        # Track memory usage
        memory_tracker = asyncio.create_task(self._track_memory_usage())
        
        try:
            for file_path, expected_lines in test_files:
                try:
                    # Process file and count lines
                    content = await self.file_handler.read_file(file_path)
                    lines = content.count('\n') + 1
                    total_lines += lines
                    
                    # Run pattern mining
                    patterns = await self.pattern_miner.extract_patterns(content)
                    patterns_found += len(patterns)
                    
                except Exception as e:
                    self.logger.error(f"Error processing {file_path}: {e}")
                    errors += 1
        
        finally:
            memory_tracker.cancel()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Calculate performance metrics
        loc_per_minute = (total_lines / duration) * 60
        
        return BenchmarkResult(
            test_name="LOC Processing",
            duration_seconds=duration,
            lines_processed=total_lines,
            loc_per_minute=loc_per_minute,
            memory_peak_mb=max(self.memory_samples) if self.memory_samples else 0,
            memory_avg_mb=statistics.mean(self.memory_samples) if self.memory_samples else 0,
            throughput_rps=len(test_files) / duration,
            latency_p50_ms=0,  # Not applicable for this test
            latency_p95_ms=0,  # Not applicable for this test
            latency_p99_ms=0,  # Not applicable for this test
            success_rate=(len(test_files) - errors) / len(test_files) if test_files else 0,
            error_count=errors,
            patterns_found=patterns_found,
            cache_hit_rate=0,  # Not applicable for this test
            timestamp=datetime.now(),
            metadata={
                "test_files_count": len(test_files),
                "target_loc_per_minute": self.target_loc_per_minute,
                "performance_met": loc_per_minute >= self.target_loc_per_minute,
                "performance_ratio": loc_per_minute / self.target_loc_per_minute,
            }
        )
    
    async def _benchmark_throughput(self) -> BenchmarkResult:
        """
        Benchmark throughput to validate 1000 RPS capability.
        
        Returns:
            BenchmarkResult: Throughput benchmark results
        """
        test_duration = 60  # 1 minute test
        request_count = 0
        success_count = 0
        start_time = time.time()
        
        # Generate test requests
        test_requests = await self._generate_test_requests(1000)
        
        # Track latency
        latency_tracker = asyncio.create_task(self._track_latency())
        
        try:
            # Execute requests as fast as possible
            tasks = []
            for request in test_requests:
                if time.time() - start_time >= test_duration:
                    break
                    
                task = asyncio.create_task(self._process_request(request))
                tasks.append(task)
                request_count += 1
            
            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Count successes
            success_count = sum(1 for r in results if not isinstance(r, Exception))
        
        finally:
            latency_tracker.cancel()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Calculate performance metrics
        throughput_rps = request_count / duration
        success_rate = success_count / request_count if request_count > 0 else 0
        
        return BenchmarkResult(
            test_name="Throughput",
            duration_seconds=duration,
            lines_processed=0,  # Not applicable
            loc_per_minute=0,  # Not applicable
            memory_peak_mb=max(self.memory_samples) if self.memory_samples else 0,
            memory_avg_mb=statistics.mean(self.memory_samples) if self.memory_samples else 0,
            throughput_rps=throughput_rps,
            latency_p50_ms=statistics.median(self.latency_samples) if self.latency_samples else 0,
            latency_p95_ms=statistics.quantiles(self.latency_samples, n=20)[18] if len(self.latency_samples) >= 20 else 0,
            latency_p99_ms=statistics.quantiles(self.latency_samples, n=100)[98] if len(self.latency_samples) >= 100 else 0,
            success_rate=success_rate,
            error_count=request_count - success_count,
            patterns_found=0,  # Not applicable
            cache_hit_rate=0,  # Not applicable
            timestamp=datetime.now(),
            metadata={
                "target_throughput_rps": self.target_throughput_rps,
                "performance_met": throughput_rps >= self.target_throughput_rps,
                "performance_ratio": throughput_rps / self.target_throughput_rps,
                "test_duration": test_duration,
                "total_requests": request_count,
            }
        )
    
    async def _benchmark_memory_efficiency(self) -> BenchmarkResult:
        """
        Benchmark memory efficiency under load.
        
        Returns:
            BenchmarkResult: Memory efficiency benchmark results
        """
        start_time = time.time()
        
        # Clear memory samples
        self.memory_samples = []
        
        # Start memory tracking
        memory_tracker = asyncio.create_task(self._track_memory_usage())
        
        try:
            # Process large files to test memory efficiency
            large_files = await self._generate_large_test_files()
            
            for file_path in large_files:
                content = await self.file_handler.read_file(file_path)
                await self.pattern_miner.extract_patterns(content)
                
                # Force garbage collection
                import gc
                gc.collect()
        
        finally:
            memory_tracker.cancel()
        
        end_time = time.time()
        duration = end_time - start_time
        
        return BenchmarkResult(
            test_name="Memory Efficiency",
            duration_seconds=duration,
            lines_processed=0,  # Not measured in this test
            loc_per_minute=0,  # Not applicable
            memory_peak_mb=max(self.memory_samples) if self.memory_samples else 0,
            memory_avg_mb=statistics.mean(self.memory_samples) if self.memory_samples else 0,
            throughput_rps=0,  # Not applicable
            latency_p50_ms=0,  # Not applicable
            latency_p95_ms=0,  # Not applicable
            latency_p99_ms=0,  # Not applicable
            success_rate=1.0,  # Assume success if no exceptions
            error_count=0,
            patterns_found=0,  # Not measured in this test
            cache_hit_rate=0,  # Not applicable
            timestamp=datetime.now(),
            metadata={
                "memory_samples_count": len(self.memory_samples),
                "memory_trend": "increasing" if self.memory_samples and self.memory_samples[-1] > self.memory_samples[0] else "stable",
                "test_files_count": len(large_files),
            }
        )
    
    async def _benchmark_latency(self) -> BenchmarkResult:
        """
        Benchmark response latency.
        
        Returns:
            BenchmarkResult: Latency benchmark results
        """
        start_time = time.time()
        
        # Clear latency samples
        self.latency_samples = []
        
        # Generate test requests
        test_requests = await self._generate_test_requests(1000)
        
        # Execute requests and measure latency
        for request in test_requests:
            request_start = time.time()
            try:
                await self._process_request(request)
                request_end = time.time()
                latency_ms = (request_end - request_start) * 1000
                self.latency_samples.append(latency_ms)
            except Exception as e:
                self.logger.error(f"Request failed: {e}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Calculate percentiles
        latency_p50 = statistics.median(self.latency_samples) if self.latency_samples else 0
        latency_p95 = statistics.quantiles(self.latency_samples, n=20)[18] if len(self.latency_samples) >= 20 else 0
        latency_p99 = statistics.quantiles(self.latency_samples, n=100)[98] if len(self.latency_samples) >= 100 else 0
        
        return BenchmarkResult(
            test_name="Latency",
            duration_seconds=duration,
            lines_processed=0,  # Not applicable
            loc_per_minute=0,  # Not applicable
            memory_peak_mb=0,  # Not measured in this test
            memory_avg_mb=0,  # Not measured in this test
            throughput_rps=len(test_requests) / duration,
            latency_p50_ms=latency_p50,
            latency_p95_ms=latency_p95,
            latency_p99_ms=latency_p99,
            success_rate=len(self.latency_samples) / len(test_requests) if test_requests else 0,
            error_count=len(test_requests) - len(self.latency_samples),
            patterns_found=0,  # Not applicable
            cache_hit_rate=0,  # Not applicable
            timestamp=datetime.now(),
            metadata={
                "target_latency_p95_ms": self.target_latency_p95_ms,
                "performance_met": latency_p95 <= self.target_latency_p95_ms,
                "total_requests": len(test_requests),
            }
        )
    
    async def _benchmark_cache_performance(self) -> BenchmarkResult:
        """
        Benchmark cache performance and hit rates.
        
        Returns:
            BenchmarkResult: Cache performance benchmark results
        """
        if not self.redis_client:
            return BenchmarkResult(
                test_name="Cache Performance",
                duration_seconds=0,
                lines_processed=0,
                loc_per_minute=0,
                memory_peak_mb=0,
                memory_avg_mb=0,
                throughput_rps=0,
                latency_p50_ms=0,
                latency_p95_ms=0,
                latency_p99_ms=0,
                success_rate=0,
                error_count=1,
                patterns_found=0,
                cache_hit_rate=0,
                timestamp=datetime.now(),
                metadata={"error": "Redis client not available"}
            )
        
        start_time = time.time()
        
        # Test cache performance
        cache_hits = 0
        cache_misses = 0
        
        # Generate test keys and values
        test_data = [(f"key_{i}", f"value_{i}") for i in range(1000)]
        
        # Write to cache
        for key, value in test_data:
            await self.redis_client.set(key, value)
        
        # Read from cache (should be hits)
        for key, expected_value in test_data:
            cached_value = await self.redis_client.get(key)
            if cached_value == expected_value:
                cache_hits += 1
            else:
                cache_misses += 1
        
        # Test with non-existent keys (should be misses)
        for i in range(100):
            cached_value = await self.redis_client.get(f"nonexistent_{i}")
            if cached_value is None:
                cache_misses += 1
            else:
                cache_hits += 1  # Unexpected hit
        
        end_time = time.time()
        duration = end_time - start_time
        
        total_operations = cache_hits + cache_misses
        cache_hit_rate = cache_hits / total_operations if total_operations > 0 else 0
        
        return BenchmarkResult(
            test_name="Cache Performance",
            duration_seconds=duration,
            lines_processed=0,  # Not applicable
            loc_per_minute=0,  # Not applicable
            memory_peak_mb=0,  # Not measured in this test
            memory_avg_mb=0,  # Not measured in this test
            throughput_rps=total_operations / duration,
            latency_p50_ms=0,  # Not measured in this test
            latency_p95_ms=0,  # Not measured in this test
            latency_p99_ms=0,  # Not measured in this test
            success_rate=1.0,  # Assume success if no exceptions
            error_count=0,
            patterns_found=0,  # Not applicable
            cache_hit_rate=cache_hit_rate,
            timestamp=datetime.now(),
            metadata={
                "cache_hits": cache_hits,
                "cache_misses": cache_misses,
                "total_operations": total_operations,
                "expected_cache_hit_rate": 0.909,  # 1000/1100
            }
        )
    
    async def _benchmark_concurrent_processing(self) -> BenchmarkResult:
        """
        Benchmark concurrent processing capabilities.
        
        Returns:
            BenchmarkResult: Concurrent processing benchmark results
        """
        start_time = time.time()
        
        # Generate test tasks
        num_tasks = 100
        tasks = []
        
        for i in range(num_tasks):
            task = asyncio.create_task(self._process_concurrent_task(i))
            tasks.append(task)
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Count successes
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        
        return BenchmarkResult(
            test_name="Concurrent Processing",
            duration_seconds=duration,
            lines_processed=0,  # Not measured in this test
            loc_per_minute=0,  # Not applicable
            memory_peak_mb=0,  # Not measured in this test
            memory_avg_mb=0,  # Not measured in this test
            throughput_rps=num_tasks / duration,
            latency_p50_ms=0,  # Not measured in this test
            latency_p95_ms=0,  # Not measured in this test
            latency_p99_ms=0,  # Not measured in this test
            success_rate=success_count / num_tasks,
            error_count=num_tasks - success_count,
            patterns_found=0,  # Not measured in this test
            cache_hit_rate=0,  # Not applicable
            timestamp=datetime.now(),
            metadata={
                "concurrent_tasks": num_tasks,
                "performance_met": success_count / num_tasks >= 0.95,
            }
        )
    
    async def _track_memory_usage(self):
        """Track memory usage during benchmark execution."""
        import psutil
        
        while True:
            try:
                process = psutil.Process()
                memory_mb = process.memory_info().rss / 1024 / 1024
                self.memory_samples.append(memory_mb)
                await asyncio.sleep(1)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error tracking memory: {e}")
                await asyncio.sleep(1)
    
    async def _track_latency(self):
        """Track latency during benchmark execution."""
        # This would be implemented to collect latency metrics
        # from actual request processing
        pass
    
    async def _generate_test_files(self) -> List[Tuple[Path, int]]:
        """Generate test files with known line counts."""
        test_files = []
        
        # Generate files of different sizes
        sizes = [1000, 5000, 10000, 50000, 100000]  # Lines per file
        
        for i, size in enumerate(sizes):
            file_path = self.test_data_dir / f"test_file_{i}_{size}.py"
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Generate Python code with patterns
            content = self._generate_test_code(size)
            
            with open(file_path, 'w') as f:
                f.write(content)
            
            test_files.append((file_path, size))
        
        return test_files
    
    async def _generate_large_test_files(self) -> List[Path]:
        """Generate large test files for memory efficiency testing."""
        large_files = []
        
        # Generate large files (1MB each)
        for i in range(10):
            file_path = self.test_data_dir / f"large_file_{i}.py"
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Generate 1MB of Python code
            content = self._generate_test_code(50000)  # ~1MB
            
            with open(file_path, 'w') as f:
                f.write(content)
            
            large_files.append(file_path)
        
        return large_files
    
    def _generate_test_code(self, num_lines: int) -> str:
        """Generate Python code with common patterns."""
        import random
        
        patterns = [
            "def function_{}(param1, param2):\n    return param1 + param2\n",
            "class Class{}:\n    def __init__(self):\n        self.value = {}\n",
            "if condition_{}:\n    result = process_data({})\n    return result\n",
            "for item in collection_{}:\n    item.process()\n    yield item\n",
            "try:\n    operation_{}()\nexcept Exception as e:\n    handle_error(e)\n",
            "with open('file_{}.txt') as f:\n    content = f.read()\n    return content\n",
            "import module_{}\nfrom package_{} import Component\n",
            "# Comment for function {}\n@decorator\ndef decorated_function_{}():\n    pass\n",
        ]
        
        lines = []
        for i in range(num_lines):
            pattern = random.choice(patterns)
            line = pattern.format(i, random.randint(1, 100))
            lines.append(line)
        
        return "\n".join(lines)
    
    async def _generate_test_requests(self, count: int) -> List[Dict[str, Any]]:
        """Generate test requests for throughput testing."""
        requests = []
        
        for i in range(count):
            request = {
                "id": i,
                "code": self._generate_test_code(100),
                "language": "python",
                "patterns": ["function", "class", "import"],
            }
            requests.append(request)
        
        return requests
    
    async def _process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single test request."""
        # Simulate request processing
        patterns = await self.pattern_miner.extract_patterns(request["code"])
        
        return {
            "id": request["id"],
            "patterns": patterns,
            "status": "success",
        }
    
    async def _process_concurrent_task(self, task_id: int) -> Dict[str, Any]:
        """Process a concurrent task."""
        # Simulate some work
        await asyncio.sleep(0.1)
        
        # Generate some test code and process it
        code = self._generate_test_code(100)
        patterns = await self.pattern_miner.extract_patterns(code)
        
        return {
            "task_id": task_id,
            "patterns": len(patterns),
            "status": "completed",
        }
    
    async def _save_benchmark_results(self, results: Dict[str, BenchmarkResult]):
        """Save benchmark results to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = self.results_dir / f"benchmark_results_{timestamp}.json"
        
        # Convert results to JSON-serializable format
        json_results = {}
        for test_name, result in results.items():
            json_results[test_name] = {
                "test_name": result.test_name,
                "duration_seconds": result.duration_seconds,
                "lines_processed": result.lines_processed,
                "loc_per_minute": result.loc_per_minute,
                "memory_peak_mb": result.memory_peak_mb,
                "memory_avg_mb": result.memory_avg_mb,
                "throughput_rps": result.throughput_rps,
                "latency_p50_ms": result.latency_p50_ms,
                "latency_p95_ms": result.latency_p95_ms,
                "latency_p99_ms": result.latency_p99_ms,
                "success_rate": result.success_rate,
                "error_count": result.error_count,
                "patterns_found": result.patterns_found,
                "cache_hit_rate": result.cache_hit_rate,
                "timestamp": result.timestamp.isoformat(),
                "metadata": result.metadata,
            }
        
        with open(results_file, 'w') as f:
            json.dump(json_results, f, indent=2)
        
        self.logger.info(f"Benchmark results saved to {results_file}")
    
    def _generate_performance_report(self, results: Dict[str, BenchmarkResult]):
        """Generate a comprehensive performance report."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = self.results_dir / f"performance_report_{timestamp}.md"
        
        report = []
        report.append("# Pattern Mining Service Performance Report")
        report.append(f"\n**Generated**: {datetime.now().isoformat()}")
        report.append(f"\n**Target Performance**:")
        report.append(f"- LOC/minute: {self.target_loc_per_minute:,}")
        report.append(f"- Throughput: {self.target_throughput_rps:,} RPS")
        report.append(f"- Latency P95: {self.target_latency_p95_ms}ms")
        report.append(f"- Success Rate: {self.target_success_rate:.1%}")
        
        report.append("\n## Benchmark Results")
        
        for test_name, result in results.items():
            report.append(f"\n### {result.test_name}")
            report.append(f"- Duration: {result.duration_seconds:.2f}s")
            
            if result.lines_processed > 0:
                report.append(f"- Lines Processed: {result.lines_processed:,}")
                report.append(f"- LOC/minute: {result.loc_per_minute:,.0f}")
                performance_met = result.loc_per_minute >= self.target_loc_per_minute
                report.append(f"- Target Met: {'✅' if performance_met else '❌'}")
            
            if result.throughput_rps > 0:
                report.append(f"- Throughput: {result.throughput_rps:.1f} RPS")
                throughput_met = result.throughput_rps >= self.target_throughput_rps
                report.append(f"- Target Met: {'✅' if throughput_met else '❌'}")
            
            if result.latency_p95_ms > 0:
                report.append(f"- Latency P50: {result.latency_p50_ms:.1f}ms")
                report.append(f"- Latency P95: {result.latency_p95_ms:.1f}ms")
                report.append(f"- Latency P99: {result.latency_p99_ms:.1f}ms")
                latency_met = result.latency_p95_ms <= self.target_latency_p95_ms
                report.append(f"- Target Met: {'✅' if latency_met else '❌'}")
            
            if result.memory_peak_mb > 0:
                report.append(f"- Memory Peak: {result.memory_peak_mb:.1f}MB")
                report.append(f"- Memory Avg: {result.memory_avg_mb:.1f}MB")
            
            report.append(f"- Success Rate: {result.success_rate:.1%}")
            success_met = result.success_rate >= self.target_success_rate
            report.append(f"- Target Met: {'✅' if success_met else '❌'}")
            
            if result.error_count > 0:
                report.append(f"- Errors: {result.error_count}")
            
            if result.cache_hit_rate > 0:
                report.append(f"- Cache Hit Rate: {result.cache_hit_rate:.1%}")
        
        # Overall assessment
        report.append("\n## Overall Assessment")
        
        loc_result = results.get("loc_processing")
        if loc_result:
            loc_met = loc_result.loc_per_minute >= self.target_loc_per_minute
            report.append(f"- **LOC Processing**: {'✅ PASSED' if loc_met else '❌ FAILED'}")
        
        throughput_result = results.get("throughput")
        if throughput_result:
            throughput_met = throughput_result.throughput_rps >= self.target_throughput_rps
            report.append(f"- **Throughput**: {'✅ PASSED' if throughput_met else '❌ FAILED'}")
        
        latency_result = results.get("latency")
        if latency_result:
            latency_met = latency_result.latency_p95_ms <= self.target_latency_p95_ms
            report.append(f"- **Latency**: {'✅ PASSED' if latency_met else '❌ FAILED'}")
        
        with open(report_file, 'w') as f:
            f.write("\n".join(report))
        
        self.logger.info(f"Performance report saved to {report_file}")