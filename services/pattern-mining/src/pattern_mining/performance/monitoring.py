"""
Production monitoring and alerting framework for pattern mining service.

Provides real-time monitoring of performance metrics including:
- Request throughput and latency
- Resource utilization (CPU, memory, I/O)
- Error rates and patterns
- Performance degradation detection
- Automated alerting
"""

import asyncio
import time
import logging
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from collections import deque
import json
import statistics
from pathlib import Path

try:
    import psutil
except ImportError:
    psutil = None

from ..utils.metrics import MetricsCollector
from ..cache.redis_client import RedisClient


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    timestamp: datetime
    request_count: int
    response_time_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float
    error_rate: float
    cache_hit_rate: float
    active_connections: int
    queue_size: int
    throughput_rps: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "timestamp": self.timestamp.isoformat(),
            "request_count": self.request_count,
            "response_time_ms": self.response_time_ms,
            "memory_usage_mb": self.memory_usage_mb,
            "cpu_usage_percent": self.cpu_usage_percent,
            "error_rate": self.error_rate,
            "cache_hit_rate": self.cache_hit_rate,
            "active_connections": self.active_connections,
            "queue_size": self.queue_size,
            "throughput_rps": self.throughput_rps,
        }


@dataclass
class AlertRule:
    """Alert rule configuration."""
    name: str
    metric: str
    operator: str  # >, <, >=, <=, ==
    threshold: float
    duration_seconds: int
    severity: str  # critical, warning, info
    callback: Optional[Callable] = None
    enabled: bool = True
    
    def __post_init__(self):
        if self.operator not in [">", "<", ">=", "<=", "=="]:
            raise ValueError(f"Invalid operator: {self.operator}")
        if self.severity not in ["critical", "warning", "info"]:
            raise ValueError(f"Invalid severity: {self.severity}")


@dataclass
class AlertState:
    """Alert state tracking."""
    rule: AlertRule
    triggered_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    is_active: bool = False
    trigger_count: int = 0
    violation_start: Optional[datetime] = None
    recent_values: deque = field(default_factory=lambda: deque(maxlen=100))


class MetricsCollector:
    """Collects and aggregates performance metrics."""
    
    def __init__(self, redis_client: Optional[RedisClient] = None):
        self.redis_client = redis_client
        self.logger = logging.getLogger(__name__)
        
        # Metrics storage
        self.metrics_history = deque(maxlen=1000)  # Keep last 1000 metrics
        self.request_times = deque(maxlen=10000)  # Keep last 10k request times
        
        # Counters
        self.request_count = 0
        self.error_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Performance tracking
        self.last_request_time = time.time()
        self.start_time = time.time()
        
    def record_request(self, response_time_ms: float, status_code: int):
        """Record a request with response time and status."""
        self.request_count += 1
        self.request_times.append(response_time_ms)
        
        if status_code >= 400:
            self.error_count += 1
            
        self.last_request_time = time.time()
    
    def record_cache_hit(self):
        """Record a cache hit."""
        self.cache_hits += 1
    
    def record_cache_miss(self):
        """Record a cache miss."""
        self.cache_misses += 1
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics."""
        now = datetime.now()
        
        # Calculate throughput
        uptime = time.time() - self.start_time
        throughput = self.request_count / uptime if uptime > 0 else 0
        
        # Calculate response time
        avg_response_time = statistics.mean(self.request_times) if self.request_times else 0
        
        # Calculate error rate
        error_rate = self.error_count / self.request_count if self.request_count > 0 else 0
        
        # Calculate cache hit rate
        total_cache_ops = self.cache_hits + self.cache_misses
        cache_hit_rate = self.cache_hits / total_cache_ops if total_cache_ops > 0 else 0
        
        # Get system metrics
        memory_usage = 0
        cpu_usage = 0
        
        if psutil:
            try:
                process = psutil.Process()
                memory_usage = process.memory_info().rss / 1024 / 1024  # MB
                cpu_usage = process.cpu_percent()
            except Exception as e:
                self.logger.warning(f"Error getting system metrics: {e}")
        
        return PerformanceMetrics(
            timestamp=now,
            request_count=self.request_count,
            response_time_ms=avg_response_time,
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage,
            error_rate=error_rate,
            cache_hit_rate=cache_hit_rate,
            active_connections=0,  # Would need to track this separately
            queue_size=0,  # Would need to track this separately
            throughput_rps=throughput,
        )
    
    def reset_counters(self):
        """Reset all counters."""
        self.request_count = 0
        self.error_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.start_time = time.time()


class PerformanceMonitor:
    """
    Real-time performance monitoring and alerting system.
    
    Monitors key performance metrics and triggers alerts when
    thresholds are exceeded.
    """
    
    def __init__(self, redis_client: Optional[RedisClient] = None):
        self.redis_client = redis_client
        self.logger = logging.getLogger(__name__)
        self.metrics_collector = MetricsCollector(redis_client)
        
        # Alert configuration
        self.alert_rules = {}
        self.alert_states = {}
        self.alert_callbacks = []
        
        # Monitoring configuration
        self.monitoring_interval = 30  # seconds
        self.metrics_retention_hours = 24
        
        # Storage
        self.metrics_history = deque(maxlen=2880)  # 24 hours at 30s intervals
        self.monitoring_task = None
        
        # Default alert rules
        self._setup_default_alert_rules()
    
    def _setup_default_alert_rules(self):
        """Set up default alert rules."""
        # High response time
        self.add_alert_rule(AlertRule(
            name="high_response_time",
            metric="response_time_ms",
            operator=">",
            threshold=500.0,  # 500ms
            duration_seconds=60,
            severity="warning"
        ))
        
        # Critical response time
        self.add_alert_rule(AlertRule(
            name="critical_response_time",
            metric="response_time_ms",
            operator=">",
            threshold=1000.0,  # 1000ms
            duration_seconds=30,
            severity="critical"
        ))
        
        # High error rate
        self.add_alert_rule(AlertRule(
            name="high_error_rate",
            metric="error_rate",
            operator=">",
            threshold=0.05,  # 5%
            duration_seconds=120,
            severity="warning"
        ))
        
        # Critical error rate
        self.add_alert_rule(AlertRule(
            name="critical_error_rate",
            metric="error_rate",
            operator=">",
            threshold=0.10,  # 10%
            duration_seconds=60,
            severity="critical"
        ))
        
        # Low throughput
        self.add_alert_rule(AlertRule(
            name="low_throughput",
            metric="throughput_rps",
            operator="<",
            threshold=100.0,  # 100 RPS
            duration_seconds=300,
            severity="warning"
        ))
        
        # High memory usage
        self.add_alert_rule(AlertRule(
            name="high_memory_usage",
            metric="memory_usage_mb",
            operator=">",
            threshold=2000.0,  # 2GB
            duration_seconds=180,
            severity="warning"
        ))
        
        # Critical memory usage
        self.add_alert_rule(AlertRule(
            name="critical_memory_usage",
            metric="memory_usage_mb",
            operator=">",
            threshold=3500.0,  # 3.5GB
            duration_seconds=60,
            severity="critical"
        ))
        
        # High CPU usage
        self.add_alert_rule(AlertRule(
            name="high_cpu_usage",
            metric="cpu_usage_percent",
            operator=">",
            threshold=80.0,  # 80%
            duration_seconds=300,
            severity="warning"
        ))
        
        # Low cache hit rate
        self.add_alert_rule(AlertRule(
            name="low_cache_hit_rate",
            metric="cache_hit_rate",
            operator="<",
            threshold=0.7,  # 70%
            duration_seconds=600,
            severity="warning"
        ))
    
    def add_alert_rule(self, rule: AlertRule):
        """Add an alert rule."""
        self.alert_rules[rule.name] = rule
        self.alert_states[rule.name] = AlertState(rule=rule)
        self.logger.info(f"Added alert rule: {rule.name}")
    
    def remove_alert_rule(self, name: str):
        """Remove an alert rule."""
        if name in self.alert_rules:
            del self.alert_rules[name]
            del self.alert_states[name]
            self.logger.info(f"Removed alert rule: {name}")
    
    def add_alert_callback(self, callback: Callable):
        """Add an alert callback function."""
        self.alert_callbacks.append(callback)
    
    async def start_monitoring(self):
        """Start the monitoring loop."""
        if self.monitoring_task is None:
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            self.logger.info("Performance monitoring started")
    
    async def stop_monitoring(self):
        """Stop the monitoring loop."""
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
            self.monitoring_task = None
            self.logger.info("Performance monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop."""
        while True:
            try:
                # Collect current metrics
                metrics = self.metrics_collector.get_current_metrics()
                
                # Store metrics
                self.metrics_history.append(metrics)
                
                # Check alert rules
                await self._check_alert_rules(metrics)
                
                # Store metrics in Redis if available
                if self.redis_client:
                    await self._store_metrics_in_redis(metrics)
                
                # Log metrics periodically
                if len(self.metrics_history) % 10 == 0:  # Every 5 minutes
                    self._log_metrics_summary(metrics)
                
                await asyncio.sleep(self.monitoring_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _check_alert_rules(self, metrics: PerformanceMetrics):
        """Check all alert rules against current metrics."""
        for rule_name, rule in self.alert_rules.items():
            if not rule.enabled:
                continue
                
            state = self.alert_states[rule_name]
            
            # Get metric value
            metric_value = getattr(metrics, rule.metric, None)
            if metric_value is None:
                continue
            
            # Add to recent values
            state.recent_values.append((metrics.timestamp, metric_value))
            
            # Check if rule is violated
            violated = self._evaluate_rule(rule, metric_value)
            
            if violated:
                if state.violation_start is None:
                    state.violation_start = metrics.timestamp
                
                # Check if violation duration exceeded
                violation_duration = (metrics.timestamp - state.violation_start).total_seconds()
                
                if violation_duration >= rule.duration_seconds and not state.is_active:
                    # Trigger alert
                    await self._trigger_alert(rule, state, metrics)
                    
            else:
                # Reset violation tracking
                state.violation_start = None
                
                # Resolve alert if active
                if state.is_active:
                    await self._resolve_alert(rule, state, metrics)
    
    def _evaluate_rule(self, rule: AlertRule, value: float) -> bool:
        """Evaluate if a rule is violated."""
        if rule.operator == ">":
            return value > rule.threshold
        elif rule.operator == "<":
            return value < rule.threshold
        elif rule.operator == ">=":
            return value >= rule.threshold
        elif rule.operator == "<=":
            return value <= rule.threshold
        elif rule.operator == "==":
            return value == rule.threshold
        else:
            return False
    
    async def _trigger_alert(self, rule: AlertRule, state: AlertState, metrics: PerformanceMetrics):
        """Trigger an alert."""
        state.is_active = True
        state.triggered_at = metrics.timestamp
        state.trigger_count += 1
        
        alert_data = {
            "rule_name": rule.name,
            "severity": rule.severity,
            "metric": rule.metric,
            "threshold": rule.threshold,
            "current_value": getattr(metrics, rule.metric),
            "triggered_at": metrics.timestamp.isoformat(),
            "message": f"Alert: {rule.name} - {rule.metric} {rule.operator} {rule.threshold}",
        }
        
        self.logger.warning(f"ALERT TRIGGERED: {alert_data['message']}")
        
        # Call rule-specific callback
        if rule.callback:
            try:
                await rule.callback(alert_data)
            except Exception as e:
                self.logger.error(f"Error in alert callback: {e}")
        
        # Call global callbacks
        for callback in self.alert_callbacks:
            try:
                await callback(alert_data)
            except Exception as e:
                self.logger.error(f"Error in alert callback: {e}")
        
        # Store alert in Redis
        if self.redis_client:
            await self._store_alert_in_redis(alert_data)
    
    async def _resolve_alert(self, rule: AlertRule, state: AlertState, metrics: PerformanceMetrics):
        """Resolve an alert."""
        state.is_active = False
        state.resolved_at = metrics.timestamp
        
        alert_data = {
            "rule_name": rule.name,
            "severity": rule.severity,
            "metric": rule.metric,
            "resolved_at": metrics.timestamp.isoformat(),
            "message": f"Alert resolved: {rule.name}",
        }
        
        self.logger.info(f"ALERT RESOLVED: {alert_data['message']}")
        
        # Store resolution in Redis
        if self.redis_client:
            await self._store_alert_in_redis(alert_data)
    
    async def _store_metrics_in_redis(self, metrics: PerformanceMetrics):
        """Store metrics in Redis."""
        try:
            key = f"metrics:{metrics.timestamp.strftime('%Y%m%d_%H%M%S')}"
            await self.redis_client.set(key, json.dumps(metrics.to_dict()), ex=86400)  # 24 hours
            
            # Update latest metrics
            await self.redis_client.set("metrics:latest", json.dumps(metrics.to_dict()), ex=300)  # 5 minutes
            
        except Exception as e:
            self.logger.error(f"Error storing metrics in Redis: {e}")
    
    async def _store_alert_in_redis(self, alert_data: Dict[str, Any]):
        """Store alert in Redis."""
        try:
            key = f"alerts:{alert_data['rule_name']}:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            await self.redis_client.set(key, json.dumps(alert_data), ex=604800)  # 7 days
            
            # Update active alerts
            if alert_data.get("resolved_at"):
                await self.redis_client.srem("alerts:active", alert_data["rule_name"])
            else:
                await self.redis_client.sadd("alerts:active", alert_data["rule_name"])
                
        except Exception as e:
            self.logger.error(f"Error storing alert in Redis: {e}")
    
    def _log_metrics_summary(self, metrics: PerformanceMetrics):
        """Log a summary of current metrics."""
        self.logger.info(
            f"Metrics Summary - "
            f"RPS: {metrics.throughput_rps:.1f}, "
            f"Latency: {metrics.response_time_ms:.1f}ms, "
            f"Memory: {metrics.memory_usage_mb:.1f}MB, "
            f"CPU: {metrics.cpu_usage_percent:.1f}%, "
            f"Error Rate: {metrics.error_rate:.1%}, "
            f"Cache Hit: {metrics.cache_hit_rate:.1%}"
        )
    
    def get_metrics_history(self, hours: int = 1) -> List[PerformanceMetrics]:
        """Get metrics history for the specified number of hours."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [m for m in self.metrics_history if m.timestamp >= cutoff_time]
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get all active alerts."""
        active_alerts = []
        
        for rule_name, state in self.alert_states.items():
            if state.is_active:
                active_alerts.append({
                    "rule_name": rule_name,
                    "severity": state.rule.severity,
                    "metric": state.rule.metric,
                    "threshold": state.rule.threshold,
                    "triggered_at": state.triggered_at.isoformat() if state.triggered_at else None,
                    "trigger_count": state.trigger_count,
                })
        
        return active_alerts
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get a summary of recent performance."""
        if not self.metrics_history:
            return {}
        
        recent_metrics = list(self.metrics_history)[-10:]  # Last 10 samples
        
        return {
            "current_rps": recent_metrics[-1].throughput_rps,
            "avg_response_time_ms": statistics.mean(m.response_time_ms for m in recent_metrics),
            "current_memory_mb": recent_metrics[-1].memory_usage_mb,
            "current_cpu_percent": recent_metrics[-1].cpu_usage_percent,
            "current_error_rate": recent_metrics[-1].error_rate,
            "current_cache_hit_rate": recent_metrics[-1].cache_hit_rate,
            "active_alerts": len(self.get_active_alerts()),
            "last_updated": recent_metrics[-1].timestamp.isoformat(),
        }
    
    async def export_metrics(self, filepath: str, hours: int = 24):
        """Export metrics to a file."""
        metrics = self.get_metrics_history(hours)
        
        data = {
            "export_time": datetime.now().isoformat(),
            "metrics_count": len(metrics),
            "hours": hours,
            "metrics": [m.to_dict() for m in metrics],
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
        
        self.logger.info(f"Exported {len(metrics)} metrics to {filepath}")


# Alert callback examples
async def email_alert_callback(alert_data: Dict[str, Any]):
    """Example email alert callback."""
    # This would integrate with an email service
    logging.getLogger(__name__).info(f"EMAIL ALERT: {alert_data['message']}")


async def slack_alert_callback(alert_data: Dict[str, Any]):
    """Example Slack alert callback."""
    # This would integrate with Slack API
    logging.getLogger(__name__).info(f"SLACK ALERT: {alert_data['message']}")


async def pagerduty_alert_callback(alert_data: Dict[str, Any]):
    """Example PagerDuty alert callback."""
    # This would integrate with PagerDuty API
    if alert_data["severity"] == "critical":
        logging.getLogger(__name__).info(f"PAGERDUTY ALERT: {alert_data['message']}")


# Health check endpoint integration
class HealthCheck:
    """Health check endpoint for monitoring."""
    
    def __init__(self, monitor: PerformanceMonitor):
        self.monitor = monitor
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get health status for monitoring endpoints."""
        active_alerts = self.monitor.get_active_alerts()
        performance = self.monitor.get_performance_summary()
        
        # Determine overall health
        critical_alerts = [a for a in active_alerts if a["severity"] == "critical"]
        warning_alerts = [a for a in active_alerts if a["severity"] == "warning"]
        
        if critical_alerts:
            status = "critical"
        elif warning_alerts:
            status = "warning"
        else:
            status = "healthy"
        
        return {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "performance": performance,
            "active_alerts": len(active_alerts),
            "critical_alerts": len(critical_alerts),
            "warning_alerts": len(warning_alerts),
        }