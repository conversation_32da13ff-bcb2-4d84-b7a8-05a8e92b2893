"""
Load testing framework for pattern mining service.

Provides comprehensive load testing capabilities including:
- 1000 RPS throughput validation
- Stress testing under extreme load
- Endurance testing for sustained performance
- Resource utilization monitoring
"""

import asyncio
import aiohttp
import time
import statistics
import logging
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
from pathlib import Path
import json
import random
import string

from ..utils.metrics import MetricsCollector


@dataclass
class LoadTestResult:
    """Result of a load test execution."""
    test_name: str
    duration_seconds: float
    target_rps: int
    actual_rps: float
    total_requests: int
    successful_requests: int
    failed_requests: int
    success_rate: float
    response_times: List[float]
    latency_p50_ms: float
    latency_p95_ms: float
    latency_p99_ms: float
    errors_by_status: Dict[int, int]
    throughput_over_time: List[Tuple[float, float]]  # (timestamp, rps)
    memory_usage_mb: List[float]
    cpu_usage_percent: List[float]
    timestamp: datetime
    metadata: Dict[str, Any]


class LoadTester:
    """
    Comprehensive load testing framework.
    
    Validates service performance under various load conditions including
    the target 1000 RPS capability and stress testing beyond limits.
    """
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.logger = logging.getLogger(__name__)
        self.metrics_collector = MetricsCollector()
        
        # Load test configuration
        self.default_timeout = 30  # seconds
        self.max_concurrent_requests = 1000
        self.default_test_duration = 300  # 5 minutes
        
        # Results storage
        self.results_dir = Path("tests/performance/load_results")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Test data
        self.test_payloads = self._generate_test_payloads()
        
    async def run_load_test_suite(self) -> Dict[str, LoadTestResult]:
        """
        Run comprehensive load testing suite.
        
        Returns:
            Dict[str, LoadTestResult]: Results for each load test
        """
        self.logger.info("Starting comprehensive load testing suite")
        
        results = {}
        
        # 1. Baseline Performance Test
        self.logger.info("Running baseline performance test...")
        results["baseline"] = await self._run_baseline_test()
        
        # 2. Target RPS Test (1000 RPS)
        self.logger.info("Running target RPS test (1000 RPS)...")
        results["target_rps"] = await self._run_target_rps_test()
        
        # 3. Stress Test (Beyond Capacity)
        self.logger.info("Running stress test...")
        results["stress"] = await self._run_stress_test()
        
        # 4. Endurance Test (Sustained Load)
        self.logger.info("Running endurance test...")
        results["endurance"] = await self._run_endurance_test()
        
        # 5. Spike Test (Sudden Load Increase)
        self.logger.info("Running spike test...")
        results["spike"] = await self._run_spike_test()
        
        # 6. Volume Test (Large Payloads)
        self.logger.info("Running volume test...")
        results["volume"] = await self._run_volume_test()
        
        # Save results
        await self._save_load_test_results(results)
        
        # Generate load test report
        self._generate_load_test_report(results)
        
        return results
    
    async def _run_baseline_test(self) -> LoadTestResult:
        """
        Run baseline performance test with low load.
        
        Returns:
            LoadTestResult: Baseline test results
        """
        return await self._execute_load_test(
            test_name="Baseline Performance",
            target_rps=10,
            duration_seconds=60,
            ramp_up_seconds=10
        )
    
    async def _run_target_rps_test(self) -> LoadTestResult:
        """
        Run target RPS test to validate 1000 RPS capability.
        
        Returns:
            LoadTestResult: Target RPS test results
        """
        return await self._execute_load_test(
            test_name="Target RPS (1000)",
            target_rps=1000,
            duration_seconds=300,  # 5 minutes
            ramp_up_seconds=30
        )
    
    async def _run_stress_test(self) -> LoadTestResult:
        """
        Run stress test beyond normal capacity.
        
        Returns:
            LoadTestResult: Stress test results
        """
        return await self._execute_load_test(
            test_name="Stress Test",
            target_rps=2000,  # 2x target capacity
            duration_seconds=180,  # 3 minutes
            ramp_up_seconds=20
        )
    
    async def _run_endurance_test(self) -> LoadTestResult:
        """
        Run endurance test with sustained load.
        
        Returns:
            LoadTestResult: Endurance test results
        """
        return await self._execute_load_test(
            test_name="Endurance Test",
            target_rps=500,  # 50% of target capacity
            duration_seconds=1800,  # 30 minutes
            ramp_up_seconds=60
        )
    
    async def _run_spike_test(self) -> LoadTestResult:
        """
        Run spike test with sudden load increases.
        
        Returns:
            LoadTestResult: Spike test results
        """
        return await self._execute_spike_test(
            test_name="Spike Test",
            base_rps=100,
            spike_rps=1500,
            spike_duration=60
        )
    
    async def _run_volume_test(self) -> LoadTestResult:
        """
        Run volume test with large payloads.
        
        Returns:
            LoadTestResult: Volume test results
        """
        return await self._execute_load_test(
            test_name="Volume Test",
            target_rps=200,  # Lower RPS due to large payloads
            duration_seconds=300,
            ramp_up_seconds=30,
            use_large_payloads=True
        )
    
    async def _execute_load_test(
        self,
        test_name: str,
        target_rps: int,
        duration_seconds: int,
        ramp_up_seconds: int = 30,
        use_large_payloads: bool = False
    ) -> LoadTestResult:
        """
        Execute a load test with specified parameters.
        
        Args:
            test_name: Name of the test
            target_rps: Target requests per second
            duration_seconds: Test duration
            ramp_up_seconds: Ramp-up time to reach target RPS
            use_large_payloads: Whether to use large test payloads
            
        Returns:
            LoadTestResult: Test execution results
        """
        self.logger.info(f"Starting load test: {test_name}")
        self.logger.info(f"Target RPS: {target_rps}, Duration: {duration_seconds}s")
        
        # Test tracking
        start_time = time.time()
        response_times = []
        errors_by_status = {}
        throughput_over_time = []
        memory_usage = []
        cpu_usage = []
        
        # Request tracking
        total_requests = 0
        successful_requests = 0
        failed_requests = 0
        
        # Start monitoring
        monitoring_task = asyncio.create_task(
            self._monitor_system_resources(memory_usage, cpu_usage)
        )
        
        try:
            # Create HTTP session
            timeout = aiohttp.ClientTimeout(total=self.default_timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                
                # Calculate request schedule
                total_requests_planned = target_rps * duration_seconds
                
                # Execute requests
                tasks = []
                test_start = time.time()
                
                for i in range(total_requests_planned):
                    # Calculate when this request should be sent
                    if i < ramp_up_seconds * target_rps:
                        # Ramp-up phase
                        current_rps = (i / ramp_up_seconds) if ramp_up_seconds > 0 else target_rps
                        target_time = test_start + (i / current_rps)
                    else:
                        # Steady state
                        steady_start = test_start + ramp_up_seconds
                        steady_index = i - (ramp_up_seconds * target_rps)
                        target_time = steady_start + (steady_index / target_rps)
                    
                    # Check if test should continue
                    if time.time() - test_start >= duration_seconds:
                        break
                    
                    # Wait until it's time to send this request
                    current_time = time.time()
                    if target_time > current_time:
                        await asyncio.sleep(target_time - current_time)
                    
                    # Create request task
                    payload = self._get_test_payload(use_large_payloads)
                    task = asyncio.create_task(
                        self._send_request(session, payload, i)
                    )
                    tasks.append(task)
                    total_requests += 1
                    
                    # Track throughput periodically
                    if i % (target_rps // 4) == 0:  # 4 times per second
                        current_rps = total_requests / (time.time() - test_start)
                        throughput_over_time.append((time.time() - test_start, current_rps))
                
                # Wait for all requests to complete
                self.logger.info(f"Waiting for {len(tasks)} requests to complete...")
                
                # Process results as they complete
                for task in asyncio.as_completed(tasks):
                    try:
                        result = await task
                        if result["success"]:
                            successful_requests += 1
                        else:
                            failed_requests += 1
                            status_code = result.get("status_code", 0)
                            errors_by_status[status_code] = errors_by_status.get(status_code, 0) + 1
                        
                        response_times.append(result["response_time"])
                        
                    except Exception as e:
                        self.logger.error(f"Request failed: {e}")
                        failed_requests += 1
                        errors_by_status[0] = errors_by_status.get(0, 0) + 1
        
        finally:
            monitoring_task.cancel()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Calculate metrics
        actual_rps = total_requests / duration
        success_rate = successful_requests / total_requests if total_requests > 0 else 0
        
        # Calculate percentiles
        latency_p50 = statistics.median(response_times) if response_times else 0
        latency_p95 = statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else 0
        latency_p99 = statistics.quantiles(response_times, n=100)[98] if len(response_times) >= 100 else 0
        
        result = LoadTestResult(
            test_name=test_name,
            duration_seconds=duration,
            target_rps=target_rps,
            actual_rps=actual_rps,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            success_rate=success_rate,
            response_times=response_times,
            latency_p50_ms=latency_p50,
            latency_p95_ms=latency_p95,
            latency_p99_ms=latency_p99,
            errors_by_status=errors_by_status,
            throughput_over_time=throughput_over_time,
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage,
            timestamp=datetime.now(),
            metadata={
                "ramp_up_seconds": ramp_up_seconds,
                "use_large_payloads": use_large_payloads,
                "target_achieved": actual_rps >= target_rps * 0.95,  # 95% of target
                "performance_ratio": actual_rps / target_rps,
            }
        )
        
        self.logger.info(f"Load test completed: {test_name}")
        self.logger.info(f"Actual RPS: {actual_rps:.1f}/{target_rps}")
        self.logger.info(f"Success rate: {success_rate:.1%}")
        
        return result
    
    async def _execute_spike_test(
        self,
        test_name: str,
        base_rps: int,
        spike_rps: int,
        spike_duration: int
    ) -> LoadTestResult:
        """
        Execute a spike test with sudden load increases.
        
        Args:
            test_name: Name of the test
            base_rps: Base load RPS
            spike_rps: Spike load RPS
            spike_duration: Duration of spike in seconds
            
        Returns:
            LoadTestResult: Spike test results
        """
        self.logger.info(f"Starting spike test: {test_name}")
        self.logger.info(f"Base RPS: {base_rps}, Spike RPS: {spike_rps}")
        
        # Test phases
        phases = [
            ("baseline", base_rps, 60),    # 1 minute baseline
            ("spike", spike_rps, spike_duration),  # Spike phase
            ("recovery", base_rps, 60),    # 1 minute recovery
        ]
        
        # Aggregate results
        all_response_times = []
        all_errors = {}
        total_requests = 0
        successful_requests = 0
        failed_requests = 0
        
        start_time = time.time()
        
        for phase_name, rps, duration in phases:
            self.logger.info(f"Starting phase: {phase_name} ({rps} RPS for {duration}s)")
            
            result = await self._execute_load_test(
                test_name=f"{test_name} - {phase_name}",
                target_rps=rps,
                duration_seconds=duration,
                ramp_up_seconds=5  # Quick ramp-up for spike
            )
            
            # Aggregate results
            all_response_times.extend(result.response_times)
            total_requests += result.total_requests
            successful_requests += result.successful_requests
            failed_requests += result.failed_requests
            
            # Merge error counts
            for status_code, count in result.errors_by_status.items():
                all_errors[status_code] = all_errors.get(status_code, 0) + count
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Calculate overall metrics
        actual_rps = total_requests / duration
        success_rate = successful_requests / total_requests if total_requests > 0 else 0
        
        # Calculate percentiles
        latency_p50 = statistics.median(all_response_times) if all_response_times else 0
        latency_p95 = statistics.quantiles(all_response_times, n=20)[18] if len(all_response_times) >= 20 else 0
        latency_p99 = statistics.quantiles(all_response_times, n=100)[98] if len(all_response_times) >= 100 else 0
        
        return LoadTestResult(
            test_name=test_name,
            duration_seconds=duration,
            target_rps=spike_rps,  # Use spike RPS as target
            actual_rps=actual_rps,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            success_rate=success_rate,
            response_times=all_response_times,
            latency_p50_ms=latency_p50,
            latency_p95_ms=latency_p95,
            latency_p99_ms=latency_p99,
            errors_by_status=all_errors,
            throughput_over_time=[],  # Not tracked in spike test
            memory_usage_mb=[],
            cpu_usage_percent=[],
            timestamp=datetime.now(),
            metadata={
                "base_rps": base_rps,
                "spike_rps": spike_rps,
                "spike_duration": spike_duration,
                "test_type": "spike",
            }
        )
    
    async def _send_request(
        self,
        session: aiohttp.ClientSession,
        payload: Dict[str, Any],
        request_id: int
    ) -> Dict[str, Any]:
        """
        Send a single HTTP request.
        
        Args:
            session: HTTP session
            payload: Request payload
            request_id: Request identifier
            
        Returns:
            Dict[str, Any]: Request result
        """
        start_time = time.time()
        
        try:
            async with session.post(
                f"{self.base_url}/api/v1/patterns/extract",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                response_time = (time.time() - start_time) * 1000  # Convert to ms
                
                # Read response
                response_data = await response.text()
                
                return {
                    "request_id": request_id,
                    "success": response.status == 200,
                    "status_code": response.status,
                    "response_time": response_time,
                    "response_size": len(response_data),
                }
        
        except asyncio.TimeoutError:
            response_time = (time.time() - start_time) * 1000
            return {
                "request_id": request_id,
                "success": False,
                "status_code": 408,  # Request Timeout
                "response_time": response_time,
                "error": "timeout",
            }
        
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return {
                "request_id": request_id,
                "success": False,
                "status_code": 0,
                "response_time": response_time,
                "error": str(e),
            }
    
    async def _monitor_system_resources(
        self,
        memory_usage: List[float],
        cpu_usage: List[float]
    ):
        """Monitor system resources during load test."""
        try:
            import psutil
            
            while True:
                # Memory usage
                memory_info = psutil.virtual_memory()
                memory_usage.append(memory_info.percent)
                
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=1)
                cpu_usage.append(cpu_percent)
                
                await asyncio.sleep(5)  # Monitor every 5 seconds
        
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"Error monitoring resources: {e}")
    
    def _generate_test_payloads(self) -> List[Dict[str, Any]]:
        """Generate test payloads for load testing."""
        payloads = []
        
        # Small payloads
        for i in range(100):
            payload = {
                "code": self._generate_code_sample("python", 50),
                "language": "python",
                "options": {
                    "extract_functions": True,
                    "extract_classes": True,
                    "extract_imports": True,
                }
            }
            payloads.append(payload)
        
        # Medium payloads
        for i in range(50):
            payload = {
                "code": self._generate_code_sample("python", 500),
                "language": "python",
                "options": {
                    "extract_functions": True,
                    "extract_classes": True,
                    "extract_imports": True,
                    "extract_comments": True,
                }
            }
            payloads.append(payload)
        
        # Large payloads
        for i in range(10):
            payload = {
                "code": self._generate_code_sample("python", 2000),
                "language": "python",
                "options": {
                    "extract_functions": True,
                    "extract_classes": True,
                    "extract_imports": True,
                    "extract_comments": True,
                    "extract_patterns": True,
                }
            }
            payloads.append(payload)
        
        return payloads
    
    def _get_test_payload(self, use_large_payloads: bool = False) -> Dict[str, Any]:
        """Get a test payload for load testing."""
        if use_large_payloads:
            # Return large payloads for volume testing
            return {
                "code": self._generate_code_sample("python", 5000),
                "language": "python",
                "options": {
                    "extract_functions": True,
                    "extract_classes": True,
                    "extract_imports": True,
                    "extract_comments": True,
                    "extract_patterns": True,
                    "extract_metrics": True,
                }
            }
        else:
            # Return random payload from test set
            return random.choice(self.test_payloads)
    
    def _generate_code_sample(self, language: str, num_lines: int) -> str:
        """Generate a code sample for testing."""
        if language == "python":
            return self._generate_python_code(num_lines)
        else:
            return self._generate_generic_code(num_lines)
    
    def _generate_python_code(self, num_lines: int) -> str:
        """Generate Python code sample."""
        patterns = [
            "def {func_name}({params}):\n    \"\"\"{docstring}\"\"\"\n    return {return_value}\n",
            "class {class_name}:\n    def __init__(self, {params}):\n        self.{attr} = {value}\n",
            "if {condition}:\n    {action}\nelse:\n    {alt_action}\n",
            "for {var} in {iterable}:\n    {action}\n",
            "try:\n    {action}\nexcept {exception}:\n    {handler}\n",
            "with {context} as {var}:\n    {action}\n",
            "import {module}\nfrom {package} import {item}\n",
            "# {comment}\n@{decorator}\ndef {func_name}():\n    pass\n",
        ]
        
        lines = []
        for i in range(num_lines):
            pattern = random.choice(patterns)
            line = pattern.format(
                func_name=f"function_{i}",
                class_name=f"Class{i}",
                params="param1, param2",
                docstring=f"Function {i} documentation",
                return_value=f"result_{i}",
                attr=f"attr_{i}",
                value=f"value_{i}",
                condition=f"condition_{i}",
                action=f"action_{i}()",
                alt_action=f"alt_action_{i}()",
                var=f"var_{i}",
                iterable=f"iterable_{i}",
                exception=f"Exception",
                handler=f"handle_error_{i}()",
                context=f"context_{i}",
                module=f"module_{i}",
                package=f"package_{i}",
                item=f"Item{i}",
                comment=f"Comment for line {i}",
                decorator=f"decorator_{i}",
            )
            lines.append(line)
        
        return "\n".join(lines)
    
    def _generate_generic_code(self, num_lines: int) -> str:
        """Generate generic code sample."""
        lines = []
        for i in range(num_lines):
            line = f"// Line {i}: " + "".join(random.choices(string.ascii_letters + string.digits, k=50))
            lines.append(line)
        return "\n".join(lines)
    
    async def _save_load_test_results(self, results: Dict[str, LoadTestResult]):
        """Save load test results to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = self.results_dir / f"load_test_results_{timestamp}.json"
        
        # Convert results to JSON-serializable format
        json_results = {}
        for test_name, result in results.items():
            json_results[test_name] = {
                "test_name": result.test_name,
                "duration_seconds": result.duration_seconds,
                "target_rps": result.target_rps,
                "actual_rps": result.actual_rps,
                "total_requests": result.total_requests,
                "successful_requests": result.successful_requests,
                "failed_requests": result.failed_requests,
                "success_rate": result.success_rate,
                "latency_p50_ms": result.latency_p50_ms,
                "latency_p95_ms": result.latency_p95_ms,
                "latency_p99_ms": result.latency_p99_ms,
                "errors_by_status": result.errors_by_status,
                "timestamp": result.timestamp.isoformat(),
                "metadata": result.metadata,
            }
        
        with open(results_file, 'w') as f:
            json.dump(json_results, f, indent=2)
        
        self.logger.info(f"Load test results saved to {results_file}")
    
    def _generate_load_test_report(self, results: Dict[str, LoadTestResult]):
        """Generate a comprehensive load test report."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = self.results_dir / f"load_test_report_{timestamp}.md"
        
        report = []
        report.append("# Pattern Mining Service Load Test Report")
        report.append(f"\n**Generated**: {datetime.now().isoformat()}")
        report.append(f"\n**Target Performance**: 1000 RPS")
        
        report.append("\n## Test Results Summary")
        
        # Results table
        report.append("\n| Test | Target RPS | Actual RPS | Success Rate | P95 Latency | Status |")
        report.append("|------|------------|------------|--------------|-------------|---------|")
        
        for test_name, result in results.items():
            status = "✅ PASS" if result.actual_rps >= result.target_rps * 0.95 else "❌ FAIL"
            report.append(f"| {result.test_name} | {result.target_rps} | {result.actual_rps:.1f} | {result.success_rate:.1%} | {result.latency_p95_ms:.1f}ms | {status} |")
        
        # Detailed results
        report.append("\n## Detailed Results")
        
        for test_name, result in results.items():
            report.append(f"\n### {result.test_name}")
            report.append(f"- **Duration**: {result.duration_seconds:.1f}s")
            report.append(f"- **Target RPS**: {result.target_rps}")
            report.append(f"- **Actual RPS**: {result.actual_rps:.1f}")
            report.append(f"- **Performance Ratio**: {result.actual_rps/result.target_rps:.1%}")
            report.append(f"- **Total Requests**: {result.total_requests:,}")
            report.append(f"- **Successful**: {result.successful_requests:,}")
            report.append(f"- **Failed**: {result.failed_requests:,}")
            report.append(f"- **Success Rate**: {result.success_rate:.1%}")
            report.append(f"- **Latency P50**: {result.latency_p50_ms:.1f}ms")
            report.append(f"- **Latency P95**: {result.latency_p95_ms:.1f}ms")
            report.append(f"- **Latency P99**: {result.latency_p99_ms:.1f}ms")
            
            if result.errors_by_status:
                report.append("- **Errors by Status**:")
                for status, count in result.errors_by_status.items():
                    report.append(f"  - {status}: {count}")
        
        # Overall assessment
        report.append("\n## Overall Assessment")
        
        target_test = results.get("target_rps")
        if target_test:
            target_met = target_test.actual_rps >= 1000 * 0.95
            report.append(f"- **1000 RPS Target**: {'✅ ACHIEVED' if target_met else '❌ NOT ACHIEVED'}")
            report.append(f"- **Actual Performance**: {target_test.actual_rps:.1f} RPS")
        
        stress_test = results.get("stress")
        if stress_test:
            report.append(f"- **Stress Test**: {stress_test.actual_rps:.1f} RPS at 2000 RPS target")
        
        # Recommendations
        report.append("\n## Recommendations")
        
        if target_test and target_test.actual_rps < 1000:
            report.append("- **Performance Optimization**: Service did not meet 1000 RPS target")
            report.append("  - Review bottlenecks in request processing")
            report.append("  - Optimize database queries and caching")
            report.append("  - Consider horizontal scaling")
        
        if any(result.success_rate < 0.99 for result in results.values()):
            report.append("- **Reliability Improvement**: Success rate below 99%")
            report.append("  - Investigate error causes")
            report.append("  - Implement better error handling")
            report.append("  - Add circuit breakers and retries")
        
        with open(report_file, 'w') as f:
            f.write("\n".join(report))
        
        self.logger.info(f"Load test report saved to {report_file}")