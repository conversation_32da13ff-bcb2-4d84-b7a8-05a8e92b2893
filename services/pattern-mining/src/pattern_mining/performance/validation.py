"""
Performance validation framework for pattern mining service.

Orchestrates comprehensive performance validation including:
- Production readiness assessment
- Performance regression detection
- Continuous validation loops
- Performance certification
"""

import asyncio
import logging
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path
import json

from .benchmark import PerformanceBenchmark, BenchmarkResult
from .load_testing import LoadTester, LoadTestResult
from .monitoring import PerformanceMonitor, PerformanceMetrics
from ..cache.redis_client import RedisClient


@dataclass
class ValidationResult:
    """Result of performance validation."""
    test_suite: str
    timestamp: datetime
    overall_status: str  # pass, fail, warning
    performance_score: float  # 0-100
    benchmark_results: Dict[str, BenchmarkResult]
    load_test_results: Dict[str, LoadTestResult]
    validation_criteria: Dict[str, Any]
    recommendations: List[str]
    certification_level: str  # production, staging, development
    regression_detected: bool
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "test_suite": self.test_suite,
            "timestamp": self.timestamp.isoformat(),
            "overall_status": self.overall_status,
            "performance_score": self.performance_score,
            "validation_criteria": self.validation_criteria,
            "recommendations": self.recommendations,
            "certification_level": self.certification_level,
            "regression_detected": self.regression_detected,
        }


class PerformanceValidator:
    """
    Comprehensive performance validation framework.
    
    Validates service performance against production requirements and
    provides certification for deployment readiness.
    """
    
    def __init__(self, redis_client: Optional[RedisClient] = None):
        self.redis_client = redis_client
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.benchmark = PerformanceBenchmark(redis_client)
        self.load_tester = LoadTester()
        self.monitor = PerformanceMonitor(redis_client)
        
        # CCL Contract-specific validation criteria
        self.production_criteria = {
            # CCL Contract Requirements
            "processing_time_seconds": 30,  # 30-second processing budget
            "files_per_second": 50,  # 50 files/second processing rate
            "patterns_per_second": 100,  # 100 patterns/second detection rate
            "integration_latency_p95_ms": 100,  # Integration SLA: p95 < 100ms
            "integration_throughput_rps": 20,  # Integration SLA: 20 RPS sustained
            
            # Service-level Requirements
            "loc_per_minute": 1_000_000,  # 1M LOC/minute (backward compatibility)
            "throughput_rps": 1000,  # Overall service RPS
            "success_rate": 0.99,  # 99% success rate
            "memory_limit_mb": 4000,  # 4GB memory limit
            "cpu_limit_percent": 80,  # 80% CPU limit
            "error_rate": 0.01,  # 1% error rate
            "cache_hit_rate": 0.80,  # 80% cache hit rate
        }
        
        self.staging_criteria = {
            # CCL Contract Requirements (relaxed for staging)
            "processing_time_seconds": 45,  # 45-second processing budget
            "files_per_second": 30,  # 30 files/second processing rate
            "patterns_per_second": 60,  # 60 patterns/second detection rate
            "integration_latency_p95_ms": 150,  # Integration SLA: p95 < 150ms
            "integration_throughput_rps": 15,  # Integration SLA: 15 RPS sustained
            
            # Service-level Requirements
            "loc_per_minute": 500_000,  # 500K LOC/minute
            "throughput_rps": 500,  # 500 RPS
            "success_rate": 0.95,  # 95% success rate
            "memory_limit_mb": 2000,  # 2GB memory limit
            "cpu_limit_percent": 70,  # 70% CPU limit
            "error_rate": 0.05,  # 5% error rate
            "cache_hit_rate": 0.70,  # 70% cache hit rate
        }
        
        self.development_criteria = {
            # CCL Contract Requirements (minimal for development)
            "processing_time_seconds": 60,  # 60-second processing budget
            "files_per_second": 10,  # 10 files/second processing rate
            "patterns_per_second": 20,  # 20 patterns/second detection rate
            "integration_latency_p95_ms": 500,  # Integration SLA: p95 < 500ms
            "integration_throughput_rps": 5,  # Integration SLA: 5 RPS sustained
            
            # Service-level Requirements
            "loc_per_minute": 100_000,  # 100K LOC/minute
            "throughput_rps": 100,  # 100 RPS
            "success_rate": 0.90,  # 90% success rate
            "memory_limit_mb": 1000,  # 1GB memory limit
            "cpu_limit_percent": 60,  # 60% CPU limit
            "error_rate": 0.10,  # 10% error rate
            "cache_hit_rate": 0.60,  # 60% cache hit rate
        }
        
        # Results storage
        self.results_dir = Path("tests/performance/validation_results")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Historical results for regression detection
        self.historical_results = []
        
    async def validate_production_readiness(self) -> ValidationResult:
        """
        Validate production readiness with comprehensive testing.
        
        Returns:
            ValidationResult: Validation results with certification level
        """
        self.logger.info("Starting production readiness validation")
        
        # Run comprehensive benchmarks
        self.logger.info("Running performance benchmarks...")
        benchmark_results = await self.benchmark.run_comprehensive_benchmark()
        
        # Run load tests
        self.logger.info("Running load tests...")
        load_test_results = await self.load_tester.run_load_test_suite()
        
        # Validate against production criteria
        validation_result = self._validate_against_criteria(
            benchmark_results=benchmark_results,
            load_test_results=load_test_results,
            criteria=self.production_criteria,
            target_level="production"
        )
        
        # Save results
        await self._save_validation_results(validation_result)
        
        # Generate certification report
        await self._generate_certification_report(validation_result)
        
        return validation_result
    
    async def validate_staging_readiness(self) -> ValidationResult:
        """
        Validate staging readiness with moderate testing.
        
        Returns:
            ValidationResult: Validation results for staging
        """
        self.logger.info("Starting staging readiness validation")
        
        # Run key benchmarks
        benchmark_results = {}
        benchmark_results["loc_processing"] = await self.benchmark._benchmark_loc_processing()
        benchmark_results["throughput"] = await self.benchmark._benchmark_throughput()
        benchmark_results["latency"] = await self.benchmark._benchmark_latency()
        
        # Run key load tests
        load_test_results = {}
        load_test_results["baseline"] = await self.load_tester._run_baseline_test()
        load_test_results["target_rps"] = await self.load_tester._run_target_rps_test()
        
        # Validate against staging criteria
        validation_result = self._validate_against_criteria(
            benchmark_results=benchmark_results,
            load_test_results=load_test_results,
            criteria=self.staging_criteria,
            target_level="staging"
        )
        
        return validation_result
    
    async def validate_development_readiness(self) -> ValidationResult:
        """
        Validate development readiness with basic testing.
        
        Returns:
            ValidationResult: Validation results for development
        """
        self.logger.info("Starting development readiness validation")
        
        # Run basic benchmarks
        benchmark_results = {}
        benchmark_results["loc_processing"] = await self.benchmark._benchmark_loc_processing()
        benchmark_results["memory_efficiency"] = await self.benchmark._benchmark_memory_efficiency()
        
        # Run basic load tests
        load_test_results = {}
        load_test_results["baseline"] = await self.load_tester._run_baseline_test()
        
        # Validate against development criteria
        validation_result = self._validate_against_criteria(
            benchmark_results=benchmark_results,
            load_test_results=load_test_results,
            criteria=self.development_criteria,
            target_level="development"
        )
        
        return validation_result
    
    def _validate_against_criteria(
        self,
        benchmark_results: Dict[str, BenchmarkResult],
        load_test_results: Dict[str, LoadTestResult],
        criteria: Dict[str, Any],
        target_level: str
    ) -> ValidationResult:
        """
        Validate results against specified criteria.
        
        Args:
            benchmark_results: Benchmark test results
            load_test_results: Load test results
            criteria: Validation criteria
            target_level: Target certification level
            
        Returns:
            ValidationResult: Validation results
        """
        # Extract key metrics
        metrics = self._extract_key_metrics(benchmark_results, load_test_results)
        
        # Validate each criterion
        validation_results = {}
        total_score = 0
        max_score = 0
        
        for criterion, threshold in criteria.items():
            if criterion in metrics:
                actual_value = metrics[criterion]
                passed = self._evaluate_criterion(criterion, actual_value, threshold)
                
                # Calculate score (0-100 for each criterion)
                score = self._calculate_criterion_score(criterion, actual_value, threshold)
                validation_results[criterion] = {
                    "passed": passed,
                    "actual": actual_value,
                    "threshold": threshold,
                    "score": score,
                }
                
                total_score += score
                max_score += 100
        
        # Calculate overall score
        overall_score = (total_score / max_score * 100) if max_score > 0 else 0
        
        # Determine overall status
        failed_criteria = [c for c, r in validation_results.items() if not r["passed"]]
        if not failed_criteria:
            overall_status = "pass"
        elif len(failed_criteria) <= 2:
            overall_status = "warning"
        else:
            overall_status = "fail"
        
        # Generate recommendations
        recommendations = self._generate_recommendations(validation_results, failed_criteria)
        
        # Determine certification level
        certification_level = self._determine_certification_level(overall_score, overall_status)
        
        # Check for regression
        regression_detected = self._detect_regression(overall_score, target_level)
        
        return ValidationResult(
            test_suite=f"{target_level}_validation",
            timestamp=datetime.now(),
            overall_status=overall_status,
            performance_score=overall_score,
            benchmark_results=benchmark_results,
            load_test_results=load_test_results,
            validation_criteria=validation_results,
            recommendations=recommendations,
            certification_level=certification_level,
            regression_detected=regression_detected,
        )
    
    def _extract_key_metrics(
        self,
        benchmark_results: Dict[str, BenchmarkResult],
        load_test_results: Dict[str, LoadTestResult]
    ) -> Dict[str, float]:
        """Extract key metrics from results."""
        metrics = {}
        
        # Extract from benchmark results
        if "loc_processing" in benchmark_results:
            result = benchmark_results["loc_processing"]
            metrics["loc_per_minute"] = result.loc_per_minute
            metrics["memory_limit_mb"] = result.memory_peak_mb
        
        if "throughput" in benchmark_results:
            result = benchmark_results["throughput"]
            metrics["throughput_rps"] = result.throughput_rps
            metrics["latency_p95_ms"] = result.latency_p95_ms
            metrics["success_rate"] = result.success_rate
        
        if "latency" in benchmark_results:
            result = benchmark_results["latency"]
            metrics["latency_p95_ms"] = result.latency_p95_ms
        
        if "cache_performance" in benchmark_results:
            result = benchmark_results["cache_performance"]
            metrics["cache_hit_rate"] = result.cache_hit_rate
        
        # Extract from load test results
        if "target_rps" in load_test_results:
            result = load_test_results["target_rps"]
            metrics["throughput_rps"] = result.actual_rps
            metrics["latency_p95_ms"] = result.latency_p95_ms
            metrics["success_rate"] = result.success_rate
            metrics["error_rate"] = 1 - result.success_rate
        
        if "baseline" in load_test_results:
            result = load_test_results["baseline"]
            if "success_rate" not in metrics:
                metrics["success_rate"] = result.success_rate
            if "error_rate" not in metrics:
                metrics["error_rate"] = 1 - result.success_rate
        
        return metrics
    
    def _evaluate_criterion(self, criterion: str, actual: float, threshold: float) -> bool:
        """Evaluate if a criterion passes."""
        # Define comparison operators for each criterion
        comparison_operators = {
            # CCL Contract criteria
            "processing_time_seconds": "<=",
            "files_per_second": ">=",
            "patterns_per_second": ">=",
            "integration_latency_p95_ms": "<=",
            "integration_throughput_rps": ">=",
            
            # Service-level criteria
            "loc_per_minute": ">=",
            "throughput_rps": ">=",
            "latency_p95_ms": "<=",
            "success_rate": ">=",
            "memory_limit_mb": "<=",
            "cpu_limit_percent": "<=",
            "error_rate": "<=",
            "cache_hit_rate": ">=",
        }
        
        operator = comparison_operators.get(criterion, ">=")
        
        if operator == ">=":
            return actual >= threshold
        elif operator == "<=":
            return actual <= threshold
        elif operator == ">":
            return actual > threshold
        elif operator == "<":
            return actual < threshold
        else:
            return False
    
    def _calculate_criterion_score(self, criterion: str, actual: float, threshold: float) -> float:
        """Calculate score (0-100) for a criterion."""
        # Define scoring logic for each criterion
        if criterion in [
            "loc_per_minute", "throughput_rps", "success_rate", "cache_hit_rate",
            "files_per_second", "patterns_per_second", "integration_throughput_rps"
        ]:
            # Higher is better
            if actual >= threshold:
                return 100
            else:
                return max(0, (actual / threshold) * 100)
        
        elif criterion in [
            "latency_p95_ms", "memory_limit_mb", "cpu_limit_percent", "error_rate",
            "processing_time_seconds", "integration_latency_p95_ms"
        ]:
            # Lower is better
            if actual <= threshold:
                return 100
            else:
                return max(0, 100 - ((actual - threshold) / threshold) * 100)
        
        else:
            return 50  # Default score for unknown criteria
    
    def _generate_recommendations(
        self,
        validation_results: Dict[str, Dict[str, Any]],
        failed_criteria: List[str]
    ) -> List[str]:
        """Generate recommendations based on validation results."""
        recommendations = []
        
        for criterion in failed_criteria:
            result = validation_results[criterion]
            actual = result["actual"]
            threshold = result["threshold"]
            
            if criterion == "loc_per_minute":
                recommendations.append(
                    f"Performance: LOC processing is {actual:,.0f}/min, target is {threshold:,.0f}/min. "
                    f"Consider optimizing parsing algorithms or adding parallel processing."
                )
            
            elif criterion == "throughput_rps":
                recommendations.append(
                    f"Throughput: Actual RPS is {actual:.1f}, target is {threshold:.1f}. "
                    f"Consider optimizing request handling, adding caching, or scaling horizontally."
                )
            
            elif criterion == "latency_p95_ms":
                recommendations.append(
                    f"Latency: P95 latency is {actual:.1f}ms, target is {threshold:.1f}ms. "
                    f"Consider optimizing database queries, improving caching, or reducing processing time."
                )
            
            elif criterion == "success_rate":
                recommendations.append(
                    f"Reliability: Success rate is {actual:.1%}, target is {threshold:.1%}. "
                    f"Investigate error causes and improve error handling."
                )
            
            elif criterion == "memory_limit_mb":
                recommendations.append(
                    f"Memory: Peak memory usage is {actual:.1f}MB, limit is {threshold:.1f}MB. "
                    f"Consider memory optimization, garbage collection tuning, or increasing memory limits."
                )
            
            elif criterion == "error_rate":
                recommendations.append(
                    f"Errors: Error rate is {actual:.1%}, target is {threshold:.1%}. "
                    f"Investigate error patterns and implement better error handling."
                )
            
            elif criterion == "cache_hit_rate":
                recommendations.append(
                    f"Caching: Cache hit rate is {actual:.1%}, target is {threshold:.1%}. "
                    f"Consider improving cache strategy, increasing cache size, or optimizing cache keys."
                )
            
            # CCL Contract-specific criteria
            elif criterion == "processing_time_seconds":
                recommendations.append(
                    f"Processing Time: Actual processing time is {actual:.1f}s, target is {threshold:.1f}s. "
                    f"Consider optimizing AST processing, parallel file processing, or caching patterns."
                )
            
            elif criterion == "files_per_second":
                recommendations.append(
                    f"File Processing Rate: Processing {actual:.1f} files/s, target is {threshold:.1f} files/s. "
                    f"Consider parallel processing, optimizing feature extraction, or reducing AST parsing overhead."
                )
            
            elif criterion == "patterns_per_second":
                recommendations.append(
                    f"Pattern Detection Rate: Detecting {actual:.1f} patterns/s, target is {threshold:.1f} patterns/s. "
                    f"Consider optimizing pattern detection algorithms, using pattern caching, or parallel detection."
                )
            
            elif criterion == "integration_latency_p95_ms":
                recommendations.append(
                    f"Integration Latency: P95 latency is {actual:.1f}ms, SLA is {threshold:.1f}ms. "
                    f"Consider optimizing API serialization, reducing middleware overhead, or improving Redis access."
                )
            
            elif criterion == "integration_throughput_rps":
                recommendations.append(
                    f"Integration Throughput: Sustaining {actual:.1f} RPS, SLA is {threshold:.1f} RPS. "
                    f"Consider request batching, connection pooling, or horizontal scaling."
                )
        
        # General recommendations
        if len(failed_criteria) > 3:
            recommendations.append(
                "Multiple performance issues detected. Consider comprehensive performance review "
                "and optimization before deployment."
            )
        
        return recommendations
    
    def _determine_certification_level(self, score: float, status: str) -> str:
        """Determine certification level based on score and status."""
        if score >= 90 and status == "pass":
            return "production"
        elif score >= 70 and status in ["pass", "warning"]:
            return "staging"
        elif score >= 50:
            return "development"
        else:
            return "not_certified"
    
    def _detect_regression(self, current_score: float, target_level: str) -> bool:
        """Detect performance regression based on historical data."""
        if not self.historical_results:
            return False
        
        # Get recent results for the same target level
        recent_results = [
            r for r in self.historical_results[-10:]  # Last 10 results
            if r.get("target_level") == target_level
        ]
        
        if len(recent_results) < 3:
            return False
        
        # Calculate average score of recent results
        avg_score = sum(r["performance_score"] for r in recent_results) / len(recent_results)
        
        # Detect regression if current score is significantly lower
        regression_threshold = 0.1  # 10% degradation
        return current_score < avg_score * (1 - regression_threshold)
    
    async def _save_validation_results(self, result: ValidationResult):
        """Save validation results to file and Redis."""
        # Save to file
        timestamp = result.timestamp.strftime("%Y%m%d_%H%M%S")
        results_file = self.results_dir / f"validation_{result.test_suite}_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(result.to_dict(), f, indent=2)
        
        # Save to Redis if available
        if self.redis_client:
            key = f"validation:{result.test_suite}:{timestamp}"
            await self.redis_client.set(key, json.dumps(result.to_dict()), ex=604800)  # 7 days
            
            # Update latest validation
            await self.redis_client.set(
                f"validation:latest:{result.test_suite}",
                json.dumps(result.to_dict()),
                ex=86400  # 24 hours
            )
        
        # Add to historical results
        self.historical_results.append({
            "timestamp": result.timestamp.isoformat(),
            "performance_score": result.performance_score,
            "target_level": result.test_suite.replace("_validation", ""),
            "overall_status": result.overall_status,
        })
        
        # Keep only last 100 results
        if len(self.historical_results) > 100:
            self.historical_results = self.historical_results[-100:]
        
        self.logger.info(f"Validation results saved to {results_file}")
    
    async def _generate_certification_report(self, result: ValidationResult):
        """Generate a comprehensive certification report."""
        timestamp = result.timestamp.strftime("%Y%m%d_%H%M%S")
        report_file = self.results_dir / f"certification_{result.test_suite}_{timestamp}.md"
        
        report = []
        report.append("# Performance Certification Report")
        report.append(f"\n**Service**: Pattern Mining Service")
        report.append(f"**Test Suite**: {result.test_suite}")
        report.append(f"**Timestamp**: {result.timestamp.isoformat()}")
        report.append(f"**Overall Status**: {result.overall_status.upper()}")
        report.append(f"**Performance Score**: {result.performance_score:.1f}/100")
        report.append(f"**Certification Level**: {result.certification_level.upper()}")
        
        if result.regression_detected:
            report.append(f"**⚠️ REGRESSION DETECTED**: Performance degradation from previous tests")
        
        report.append("\n## Validation Criteria Results")
        
        # Criteria table
        report.append("\n| Criterion | Status | Actual | Threshold | Score |")
        report.append("|-----------|---------|--------|-----------|-------|")
        
        for criterion, data in result.validation_criteria.items():
            status = "✅ PASS" if data["passed"] else "❌ FAIL"
            report.append(f"| {criterion} | {status} | {data['actual']} | {data['threshold']} | {data['score']:.1f} |")
        
        # Recommendations
        if result.recommendations:
            report.append("\n## Recommendations")
            for i, rec in enumerate(result.recommendations, 1):
                report.append(f"{i}. {rec}")
        
        # Certification decision
        report.append("\n## Certification Decision")
        
        if result.certification_level == "production":
            report.append("✅ **CERTIFIED FOR PRODUCTION DEPLOYMENT**")
            report.append("- All performance criteria met")
            report.append("- Service is ready for production traffic")
        
        elif result.certification_level == "staging":
            report.append("⚠️ **CERTIFIED FOR STAGING DEPLOYMENT**")
            report.append("- Most performance criteria met")
            report.append("- Additional optimization recommended before production")
        
        elif result.certification_level == "development":
            report.append("⚠️ **CERTIFIED FOR DEVELOPMENT ONLY**")
            report.append("- Basic performance criteria met")
            report.append("- Significant optimization required for higher environments")
        
        else:
            report.append("❌ **NOT CERTIFIED**")
            report.append("- Performance criteria not met")
            report.append("- Requires performance improvements before deployment")
        
        # Next steps
        report.append("\n## Next Steps")
        
        if result.certification_level == "production":
            report.append("1. Deploy to production with monitoring")
            report.append("2. Set up performance alerts")
            report.append("3. Schedule regular performance validation")
        
        elif result.certification_level in ["staging", "development"]:
            report.append("1. Address performance recommendations")
            report.append("2. Re-run validation after optimization")
            report.append("3. Consider gradual rollout with monitoring")
        
        else:
            report.append("1. Review and implement all recommendations")
            report.append("2. Run development validation first")
            report.append("3. Gradually progress through certification levels")
        
        with open(report_file, 'w') as f:
            f.write("\n".join(report))
        
        self.logger.info(f"Certification report saved to {report_file}")
    
    async def run_continuous_validation(self, interval_hours: int = 24):
        """Run continuous validation loop."""
        self.logger.info(f"Starting continuous validation (every {interval_hours} hours)")
        
        while True:
            try:
                # Run staging validation
                result = await self.validate_staging_readiness()
                
                # Alert if regression detected
                if result.regression_detected:
                    self.logger.warning("Performance regression detected!")
                
                # Alert if certification level drops
                if result.certification_level == "not_certified":
                    self.logger.error("Service no longer certified!")
                
                # Wait for next validation
                await asyncio.sleep(interval_hours * 3600)
                
            except Exception as e:
                self.logger.error(f"Error in continuous validation: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour before retry