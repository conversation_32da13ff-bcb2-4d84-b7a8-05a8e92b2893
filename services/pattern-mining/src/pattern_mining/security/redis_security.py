"""
Redis Security Configuration

Provides secure Redis connection with TLS/SSL encryption, certificate management,
and connection security best practices.
"""

import os
import ssl
import asyncio
from typing import Optional, Dict, Any
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import secrets
import logging

import structlog
from cryptography import x509
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa

from ..config.settings import get_settings

logger = structlog.get_logger()


@dataclass
class RedisSecurityConfig:
    """Redis security configuration with TLS/SSL support."""
    
    # TLS/SSL Configuration
    enable_tls: bool = True
    tls_ca_cert_path: Optional[str] = None
    tls_cert_path: Optional[str] = None
    tls_key_path: Optional[str] = None
    tls_key_password: Optional[str] = None
    tls_verify_mode: ssl.VerifyMode = ssl.CERT_REQUIRED
    tls_check_hostname: bool = True
    tls_ciphers: str = "ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS"
    tls_min_version: ssl.TLSVersion = ssl.TLSVersion.TLSv1_2
    
    # Certificate Management
    cert_rotation_days: int = 30
    cert_expiry_warning_days: int = 7
    auto_generate_certs: bool = False
    cert_storage_path: str = "/etc/redis/certs"
    
    # Connection Security
    require_auth: bool = True
    auth_password: Optional[str] = None
    auth_username: Optional[str] = "default"
    acl_enabled: bool = True
    max_connection_attempts: int = 3
    connection_timeout_ms: int = 5000
    
    # Security Monitoring
    enable_security_logging: bool = True
    log_connection_attempts: bool = True
    log_auth_failures: bool = True
    alert_on_suspicious_activity: bool = True
    
    # Additional Security Features
    enable_command_filtering: bool = True
    allowed_commands: list = field(default_factory=lambda: [
        "GET", "SET", "DEL", "EXISTS", "EXPIRE", "TTL",
        "HGET", "HSET", "HGETALL", "HMSET",
        "SADD", "SMEMBERS", "SISMEMBER",
        "LPUSH", "RPUSH", "LPOP", "RPOP", "LRANGE",
        "INCR", "DECR", "INCRBY", "DECRBY",
        "MGET", "MSET", "PING", "AUTH", "SELECT"
    ])
    
    def to_ssl_context(self) -> ssl.SSLContext:
        """Create SSL context from configuration."""
        context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
        
        # Set minimum TLS version
        context.minimum_version = self.tls_min_version
        
        # Set ciphers
        context.set_ciphers(self.tls_ciphers)
        
        # Load certificates
        if self.tls_ca_cert_path:
            context.load_verify_locations(cafile=self.tls_ca_cert_path)
        
        if self.tls_cert_path and self.tls_key_path:
            context.load_cert_chain(
                certfile=self.tls_cert_path,
                keyfile=self.tls_key_path,
                password=self.tls_key_password
            )
        
        # Set verification mode
        context.verify_mode = self.tls_verify_mode
        context.check_hostname = self.tls_check_hostname
        
        return context


class RedisCertificateManager:
    """Manages Redis TLS certificates with rotation and validation."""
    
    def __init__(self, config: RedisSecurityConfig):
        self.config = config
        self.cert_path = Path(config.cert_storage_path)
        self.cert_path.mkdir(parents=True, exist_ok=True)
        
    async def ensure_certificates(self) -> Dict[str, str]:
        """Ensure valid certificates exist, generate if needed."""
        cert_files = {
            "ca_cert": self.cert_path / "ca.crt",
            "server_cert": self.cert_path / "server.crt",
            "server_key": self.cert_path / "server.key",
            "client_cert": self.cert_path / "client.crt",
            "client_key": self.cert_path / "client.key"
        }
        
        # Check if certificates exist and are valid
        if await self._certificates_valid(cert_files):
            logger.info("Redis certificates are valid")
            return {k: str(v) for k, v in cert_files.items()}
        
        if self.config.auto_generate_certs:
            logger.info("Generating new Redis certificates")
            return await self._generate_certificates()
        else:
            raise ValueError("Redis certificates not found or invalid, and auto-generation is disabled")
    
    async def _certificates_valid(self, cert_files: Dict[str, Path]) -> bool:
        """Check if certificates exist and are valid."""
        try:
            # Check if all files exist
            for cert_file in cert_files.values():
                if not cert_file.exists():
                    return False
            
            # Check certificate expiration
            with open(cert_files["server_cert"], "rb") as f:
                cert_data = f.read()
                cert = x509.load_pem_x509_certificate(cert_data)
                
                # Check if certificate is expired or will expire soon
                days_until_expiry = (cert.not_valid_after - datetime.utcnow()).days
                if days_until_expiry < self.config.cert_expiry_warning_days:
                    logger.warning(
                        "Redis certificate expiring soon",
                        days_until_expiry=days_until_expiry
                    )
                    if days_until_expiry <= 0:
                        return False
            
            return True
            
        except Exception as e:
            logger.error("Error checking certificate validity", error=str(e))
            return False
    
    async def _generate_certificates(self) -> Dict[str, str]:
        """Generate self-signed certificates for Redis TLS."""
        # Generate CA key and certificate
        ca_key = rsa.generate_private_key(public_exponent=65537, key_size=4096)
        ca_cert = self._create_ca_certificate(ca_key)
        
        # Generate server key and certificate
        server_key = rsa.generate_private_key(public_exponent=65537, key_size=4096)
        server_cert = self._create_server_certificate(server_key, ca_key, ca_cert)
        
        # Generate client key and certificate
        client_key = rsa.generate_private_key(public_exponent=65537, key_size=4096)
        client_cert = self._create_client_certificate(client_key, ca_key, ca_cert)
        
        # Save certificates
        cert_files = {
            "ca_cert": self._save_certificate(ca_cert, "ca.crt"),
            "server_cert": self._save_certificate(server_cert, "server.crt"),
            "server_key": self._save_private_key(server_key, "server.key"),
            "client_cert": self._save_certificate(client_cert, "client.crt"),
            "client_key": self._save_private_key(client_key, "client.key")
        }
        
        logger.info("Generated new Redis certificates", cert_files=cert_files)
        return cert_files
    
    def _create_ca_certificate(self, key: rsa.RSAPrivateKey) -> x509.Certificate:
        """Create CA certificate."""
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "California"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Pattern Mining Service"),
            x509.NameAttribute(NameOID.COMMON_NAME, "Pattern Mining CA"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=365 * 10)  # 10 years for CA
        ).add_extension(
            x509.BasicConstraints(ca=True, path_length=0),
            critical=True,
        ).add_extension(
            x509.KeyUsage(
                digital_signature=True,
                key_cert_sign=True,
                crl_sign=True,
                key_encipherment=False,
                content_commitment=False,
                data_encipherment=False,
                key_agreement=False,
                encipher_only=False,
                decipher_only=False,
            ),
            critical=True,
        ).sign(key, hashes.SHA256())
        
        return cert
    
    def _create_server_certificate(
        self,
        key: rsa.RSAPrivateKey,
        ca_key: rsa.RSAPrivateKey,
        ca_cert: x509.Certificate
    ) -> x509.Certificate:
        """Create server certificate signed by CA."""
        subject = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "California"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Pattern Mining Service"),
            x509.NameAttribute(NameOID.COMMON_NAME, "redis-server"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            ca_cert.issuer
        ).public_key(
            key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=self.config.cert_rotation_days)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.DNSName("redis"),
                x509.DNSName("redis-server"),
                x509.IPAddress("127.0.0.1"),
            ]),
            critical=False,
        ).add_extension(
            x509.BasicConstraints(ca=False, path_length=None),
            critical=True,
        ).add_extension(
            x509.KeyUsage(
                digital_signature=True,
                key_encipherment=True,
                key_cert_sign=False,
                crl_sign=False,
                content_commitment=False,
                data_encipherment=False,
                key_agreement=False,
                encipher_only=False,
                decipher_only=False,
            ),
            critical=True,
        ).sign(ca_key, hashes.SHA256())
        
        return cert
    
    def _create_client_certificate(
        self,
        key: rsa.RSAPrivateKey,
        ca_key: rsa.RSAPrivateKey,
        ca_cert: x509.Certificate
    ) -> x509.Certificate:
        """Create client certificate signed by CA."""
        subject = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "California"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Pattern Mining Service"),
            x509.NameAttribute(NameOID.COMMON_NAME, "redis-client"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            ca_cert.issuer
        ).public_key(
            key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=self.config.cert_rotation_days)
        ).add_extension(
            x509.BasicConstraints(ca=False, path_length=None),
            critical=True,
        ).add_extension(
            x509.KeyUsage(
                digital_signature=True,
                key_encipherment=True,
                key_cert_sign=False,
                crl_sign=False,
                content_commitment=False,
                data_encipherment=False,
                key_agreement=False,
                encipher_only=False,
                decipher_only=False,
            ),
            critical=True,
        ).add_extension(
            x509.ExtendedKeyUsage([x509.oid.ExtensionOID.CLIENT_AUTH]),
            critical=True,
        ).sign(ca_key, hashes.SHA256())
        
        return cert
    
    def _save_certificate(self, cert: x509.Certificate, filename: str) -> str:
        """Save certificate to file."""
        cert_path = self.cert_path / filename
        with open(cert_path, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        cert_path.chmod(0o644)
        return str(cert_path)
    
    def _save_private_key(self, key: rsa.RSAPrivateKey, filename: str) -> str:
        """Save private key to file."""
        key_path = self.cert_path / filename
        with open(key_path, "wb") as f:
            f.write(key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.TraditionalOpenSSL,
                encryption_algorithm=serialization.NoEncryption()
            ))
        key_path.chmod(0o600)
        return str(key_path)
    
    async def rotate_certificates(self) -> Dict[str, str]:
        """Rotate certificates if needed."""
        logger.info("Starting certificate rotation")
        
        # Generate new certificates
        new_certs = await self._generate_certificates()
        
        # TODO: Implement graceful rotation with zero downtime
        # 1. Generate new certificates
        # 2. Update Redis configuration
        # 3. Reload Redis without dropping connections
        # 4. Clean up old certificates
        
        logger.info("Certificate rotation completed")
        return new_certs


class RedisSecurityMonitor:
    """Monitors Redis security events and alerts on suspicious activity."""
    
    def __init__(self, config: RedisSecurityConfig):
        self.config = config
        self.failed_auth_attempts: Dict[str, int] = {}
        self.suspicious_commands: Dict[str, int] = {}
        self.last_alert_time: Dict[str, datetime] = {}
        self.alert_cooldown = timedelta(minutes=5)
        
    async def log_connection_attempt(self, client_ip: str, success: bool):
        """Log connection attempt."""
        if not self.config.log_connection_attempts:
            return
            
        if success:
            logger.info("Redis connection successful", client_ip=client_ip)
            # Reset failed attempts on success
            self.failed_auth_attempts.pop(client_ip, None)
        else:
            logger.warning("Redis connection failed", client_ip=client_ip)
            self.failed_auth_attempts[client_ip] = self.failed_auth_attempts.get(client_ip, 0) + 1
            
            # Alert on multiple failed attempts
            if self.failed_auth_attempts[client_ip] >= 3:
                await self._alert_suspicious_activity(
                    f"Multiple failed Redis auth attempts from {client_ip}"
                )
    
    async def log_command(self, client_ip: str, command: str):
        """Log Redis command execution."""
        if not self.config.enable_security_logging:
            return
            
        # Check if command is allowed
        if self.config.enable_command_filtering:
            command_name = command.split()[0].upper()
            if command_name not in self.config.allowed_commands:
                logger.warning(
                    "Blocked Redis command",
                    client_ip=client_ip,
                    command=command_name
                )
                self.suspicious_commands[client_ip] = self.suspicious_commands.get(client_ip, 0) + 1
                
                if self.suspicious_commands[client_ip] >= 5:
                    await self._alert_suspicious_activity(
                        f"Multiple blocked Redis commands from {client_ip}"
                    )
    
    async def _alert_suspicious_activity(self, message: str):
        """Alert on suspicious activity."""
        if not self.config.alert_on_suspicious_activity:
            return
            
        # Check cooldown
        now = datetime.utcnow()
        if message in self.last_alert_time:
            if now - self.last_alert_time[message] < self.alert_cooldown:
                return
        
        logger.error("SECURITY ALERT", message=message)
        self.last_alert_time[message] = now
        
        # TODO: Integrate with alerting system (email, Slack, PagerDuty)
        # For now, just log the alert


def get_secure_redis_config() -> RedisSecurityConfig:
    """Get secure Redis configuration from settings."""
    settings = get_settings()
    
    # Generate secure password if not provided
    auth_password = settings.redis_password
    if not auth_password and settings.environment == "production":
        auth_password = secrets.token_urlsafe(32)
        logger.warning("Generated random Redis password for production")
    
    return RedisSecurityConfig(
        enable_tls=True,  # Force TLS in production
        tls_ca_cert_path=os.getenv("REDIS_TLS_CA_CERT", settings.redis_ssl_ca_certs),
        tls_cert_path=os.getenv("REDIS_TLS_CERT"),
        tls_key_path=os.getenv("REDIS_TLS_KEY"),
        tls_key_password=os.getenv("REDIS_TLS_KEY_PASSWORD"),
        auth_password=auth_password,
        auth_username=os.getenv("REDIS_USERNAME", "default"),
        cert_storage_path=os.getenv("REDIS_CERT_PATH", "/etc/redis/certs"),
        auto_generate_certs=settings.environment == "development",
        enable_security_logging=settings.environment == "production",
        alert_on_suspicious_activity=settings.environment == "production"
    )