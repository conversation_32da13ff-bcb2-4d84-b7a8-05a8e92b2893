"""
SQL Security Module

Provides SQL injection prevention through input validation, sanitization,
and secure query construction for BigQuery.
"""

import re
from typing import Any, List, Optional, Union, Dict
from dataclasses import dataclass
from enum import Enum
import structlog

logger = structlog.get_logger()


class SQLValidationError(Exception):
    """Raised when SQL validation fails."""
    pass


class IdentifierType(Enum):
    """Types of SQL identifiers."""
    TABLE = "table"
    COLUMN = "column"
    ALIAS = "alias"
    DATASET = "dataset"
    PROJECT = "project"


@dataclass
class SQLSecurityConfig:
    """SQL security configuration."""
    
    # Validation settings
    allow_fully_qualified_names: bool = True
    max_identifier_length: int = 128
    max_query_length: int = 1_000_000  # 1MB
    
    # Allowed patterns
    allowed_table_pattern: str = r"^[a-zA-Z_][a-zA-Z0-9_]*$"
    allowed_column_pattern: str = r"^[a-zA-Z_][a-zA-Z0-9_]*$"
    allowed_alias_pattern: str = r"^[a-zA-Z_][a-zA-Z0-9_]*$"
    
    # Reserved words that cannot be used as identifiers
    reserved_words: List[str] = None
    
    # Injection pattern detection
    enable_injection_detection: bool = True
    log_suspicious_queries: bool = True
    
    def __post_init__(self):
        if self.reserved_words is None:
            self.reserved_words = [
                "SELECT", "FROM", "WHERE", "AND", "OR", "NOT", "INSERT", "UPDATE",
                "DELETE", "DROP", "CREATE", "ALTER", "TRUNCATE", "EXEC", "EXECUTE",
                "UNION", "INTERSECT", "EXCEPT", "ORDER", "GROUP", "HAVING", "BY",
                "ASC", "DESC", "LIMIT", "OFFSET", "JOIN", "INNER", "LEFT", "RIGHT",
                "OUTER", "CROSS", "ON", "AS", "WITH", "CASE", "WHEN", "THEN", "ELSE",
                "END", "EXISTS", "IN", "LIKE", "BETWEEN", "IS", "NULL", "DISTINCT",
                "ALL", "ANY", "SOME", "TABLE", "VIEW", "INDEX", "PROCEDURE", "FUNCTION"
            ]


class SQLSecurityValidator:
    """Validates and sanitizes SQL inputs to prevent injection attacks."""
    
    def __init__(self, config: Optional[SQLSecurityConfig] = None):
        self.config = config or SQLSecurityConfig()
        
        # Compile regex patterns for performance
        self.table_pattern = re.compile(self.config.allowed_table_pattern)
        self.column_pattern = re.compile(self.config.allowed_column_pattern)
        self.alias_pattern = re.compile(self.config.allowed_alias_pattern)
        
        # SQL injection patterns
        self.injection_patterns = [
            re.compile(r"(;|--|\*/|/\*|\bxp_|\bsp_)", re.IGNORECASE),  # Common injection markers
            re.compile(r"(\bunion\b.*\bselect\b|\bselect\b.*\bunion\b)", re.IGNORECASE),  # UNION attacks
            re.compile(r"(\bor\b\s*\d+\s*=\s*\d+|\band\b\s*\d+\s*=\s*\d+)", re.IGNORECASE),  # Always true conditions
            re.compile(r"(\bexec\b|\bexecute\b|\bxp_cmdshell\b)", re.IGNORECASE),  # Command execution
            re.compile(r"(\bdrop\b.*\btable\b|\btruncate\b.*\btable\b)", re.IGNORECASE),  # Destructive operations
            re.compile(r"(\binto\b\s*@|\bdeclare\b\s*@)", re.IGNORECASE),  # Variable declaration
            re.compile(r"(\bwaitfor\b\s*delay\b|\bsleep\b)", re.IGNORECASE),  # Time-based attacks
        ]
    
    def validate_identifier(self, identifier: str, identifier_type: IdentifierType) -> str:
        """
        Validate and return a safe identifier.
        
        Args:
            identifier: The identifier to validate
            identifier_type: Type of identifier (table, column, alias, etc.)
            
        Returns:
            Validated identifier
            
        Raises:
            SQLValidationError: If identifier is invalid
        """
        if not identifier:
            raise SQLValidationError(f"Empty {identifier_type.value} identifier")
        
        # Check length
        if len(identifier) > self.config.max_identifier_length:
            raise SQLValidationError(
                f"{identifier_type.value} identifier too long: {len(identifier)} > {self.config.max_identifier_length}"
            )
        
        # Handle fully qualified names (project.dataset.table)
        parts = identifier.split(".")
        if len(parts) > 3:
            raise SQLValidationError(f"Invalid fully qualified name: {identifier}")
        
        if len(parts) == 3 and not self.config.allow_fully_qualified_names:
            raise SQLValidationError(f"Fully qualified names not allowed: {identifier}")
        
        # Validate each part
        for i, part in enumerate(parts):
            part_type = identifier_type
            if len(parts) == 3:
                if i == 0:
                    part_type = IdentifierType.PROJECT
                elif i == 1:
                    part_type = IdentifierType.DATASET
            elif len(parts) == 2 and i == 0:
                part_type = IdentifierType.DATASET
            
            self._validate_identifier_part(part, part_type)
        
        return identifier
    
    def _validate_identifier_part(self, part: str, identifier_type: IdentifierType):
        """Validate a single part of an identifier."""
        # Check against reserved words
        if part.upper() in self.config.reserved_words:
            raise SQLValidationError(f"Reserved word used as {identifier_type.value}: {part}")
        
        # Check pattern based on type
        if identifier_type in [IdentifierType.TABLE, IdentifierType.DATASET, IdentifierType.PROJECT]:
            pattern = self.table_pattern
        elif identifier_type == IdentifierType.COLUMN:
            pattern = self.column_pattern
        elif identifier_type == IdentifierType.ALIAS:
            pattern = self.alias_pattern
        else:
            pattern = self.table_pattern
        
        if not pattern.match(part):
            raise SQLValidationError(
                f"Invalid {identifier_type.value} identifier: {part}. "
                f"Must match pattern: {pattern.pattern}"
            )
    
    def validate_table_name(self, table_name: str) -> str:
        """Validate a table name."""
        return self.validate_identifier(table_name, IdentifierType.TABLE)
    
    def validate_column_name(self, column_name: str) -> str:
        """Validate a column name."""
        return self.validate_identifier(column_name, IdentifierType.COLUMN)
    
    def validate_alias(self, alias: str) -> str:
        """Validate an alias."""
        return self.validate_identifier(alias, IdentifierType.ALIAS)
    
    def validate_numeric_value(self, value: Union[int, float], min_val: Optional[float] = None, max_val: Optional[float] = None) -> Union[int, float]:
        """
        Validate a numeric value.
        
        Args:
            value: The value to validate
            min_val: Minimum allowed value
            max_val: Maximum allowed value
            
        Returns:
            Validated value
            
        Raises:
            SQLValidationError: If value is invalid
        """
        if not isinstance(value, (int, float)):
            raise SQLValidationError(f"Invalid numeric value: {value}")
        
        if min_val is not None and value < min_val:
            raise SQLValidationError(f"Value {value} is less than minimum {min_val}")
        
        if max_val is not None and value > max_val:
            raise SQLValidationError(f"Value {value} is greater than maximum {max_val}")
        
        return value
    
    def validate_limit(self, limit: int) -> int:
        """Validate a LIMIT value."""
        return int(self.validate_numeric_value(limit, min_val=1, max_val=1_000_000))
    
    def validate_offset(self, offset: int) -> int:
        """Validate an OFFSET value."""
        return int(self.validate_numeric_value(offset, min_val=0, max_val=10_000_000))
    
    def escape_string_value(self, value: str) -> str:
        """
        Escape a string value for safe inclusion in SQL.
        Note: This is for cases where parameterization is not possible.
        Always prefer parameterized queries!
        
        Args:
            value: String value to escape
            
        Returns:
            Escaped string value
        """
        if value is None:
            return "NULL"
        
        # BigQuery uses standard SQL escaping
        # Replace single quotes with two single quotes
        escaped = value.replace("'", "''")
        
        # Replace backslashes with double backslashes
        escaped = escaped.replace("\\", "\\\\")
        
        # Check for potential injection patterns if enabled
        if self.config.enable_injection_detection:
            self._check_injection_patterns(escaped)
        
        return escaped
    
    def _check_injection_patterns(self, value: str):
        """Check for potential SQL injection patterns."""
        for pattern in self.injection_patterns:
            if pattern.search(value):
                if self.config.log_suspicious_queries:
                    logger.warning(
                        "Potential SQL injection pattern detected",
                        pattern=pattern.pattern,
                        value=value[:100]  # Log only first 100 chars
                    )
                raise SQLValidationError(f"Suspicious SQL pattern detected in value")
    
    def validate_query_length(self, query: str):
        """Validate query length."""
        if len(query) > self.config.max_query_length:
            raise SQLValidationError(
                f"Query too long: {len(query)} > {self.config.max_query_length}"
            )
    
    def sanitize_order_by_field(self, field: str, allowed_fields: List[str]) -> str:
        """
        Sanitize an ORDER BY field against a whitelist.
        
        Args:
            field: Field to order by
            allowed_fields: List of allowed field names
            
        Returns:
            Sanitized field name
            
        Raises:
            SQLValidationError: If field is not in allowed list
        """
        # Remove any direction suffix (ASC/DESC)
        field_name = field.upper().replace(" ASC", "").replace(" DESC", "").strip()
        
        # Check if field is in allowed list (case insensitive)
        allowed_upper = [f.upper() for f in allowed_fields]
        if field_name not in allowed_upper:
            raise SQLValidationError(f"Invalid ORDER BY field: {field}")
        
        # Return the original allowed field (preserving case)
        idx = allowed_upper.index(field_name)
        return allowed_fields[idx]
    
    def create_safe_in_clause(self, values: List[Any]) -> str:
        """
        Create a safe IN clause with escaped values.
        Note: This is for cases where parameterization is not possible.
        Always prefer parameterized queries!
        
        Args:
            values: List of values for IN clause
            
        Returns:
            Safe IN clause string
        """
        if not values:
            raise SQLValidationError("Empty values for IN clause")
        
        safe_values = []
        for value in values:
            if isinstance(value, str):
                safe_values.append(f"'{self.escape_string_value(value)}'")
            elif isinstance(value, (int, float)):
                safe_values.append(str(self.validate_numeric_value(value)))
            elif value is None:
                safe_values.append("NULL")
            else:
                raise SQLValidationError(f"Unsupported value type for IN clause: {type(value)}")
        
        return f"({', '.join(safe_values)})"


def get_sql_security_validator() -> SQLSecurityValidator:
    """Get a configured SQL security validator."""
    return SQLSecurityValidator()