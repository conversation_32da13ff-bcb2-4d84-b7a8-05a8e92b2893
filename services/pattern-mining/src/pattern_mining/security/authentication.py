"""
Authentication System for Pattern Mining Service

Provides comprehensive authentication including:
- JWT token authentication
- OAuth2 integration
- Service account authentication
- Multi-factor authentication
- Session management
"""

import time
import json
import hashlib
import secrets
import pyotp
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Union
from dataclasses import dataclass, asdict
from enum import Enum
import jwt
from jwt.exceptions import InvalidTokenError, ExpiredSignatureError
from passlib.context import CryptContext
from fastapi import HTTPException, Request
from fastapi.security import HTT<PERSON><PERSON><PERSON>er, HTTPAuthorizationCredentials
from google.auth.transport import requests
from google.oauth2 import service_account
from google.oauth2.credentials import Credentials
import redis.asyncio as redis
import structlog
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

logger = structlog.get_logger()

# Security configuration
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()


class AuthenticationError(Exception):
    """Base authentication error."""
    pass


class TokenError(AuthenticationError):
    """Token-related errors."""
    pass


class MFAError(AuthenticationError):
    """Multi-factor authentication errors."""
    pass


class SessionError(AuthenticationError):
    """Session management errors."""
    pass


class TokenType(str, Enum):
    """Token types."""
    ACCESS = "access"
    REFRESH = "refresh"
    API_KEY = "api_key"
    SERVICE_ACCOUNT = "service_account"


class UserRole(str, Enum):
    """User roles."""
    ADMIN = "admin"
    USER = "user"
    SERVICE = "service"
    READONLY = "readonly"


@dataclass
class User:
    """User model for authentication."""
    id: str
    email: str
    username: str
    role: UserRole
    permissions: List[str]
    is_active: bool = True
    mfa_enabled: bool = False
    mfa_secret: Optional[str] = None
    created_at: datetime = datetime.utcnow()
    last_login: Optional[datetime] = None
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None


@dataclass
class Token:
    """Token model."""
    token: str
    token_type: TokenType
    expires_at: datetime
    user_id: str
    scope: List[str]
    created_at: datetime = datetime.utcnow()


@dataclass
class Session:
    """Session model."""
    session_id: str
    user_id: str
    created_at: datetime
    expires_at: datetime
    last_activity: datetime
    ip_address: str
    user_agent: str
    is_active: bool = True


class JWTAuthenticator:
    """JWT-based authentication service."""
    
    def __init__(
        self,
        secret_key: str,
        algorithm: str = "HS256",
        access_token_expire_minutes: int = 15,
        refresh_token_expire_days: int = 7,
        redis_client: Optional[redis.Redis] = None
    ):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire_minutes = access_token_expire_minutes
        self.refresh_token_expire_days = refresh_token_expire_days
        self.redis_client = redis_client
        
    async def create_access_token(self, user: User, expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token."""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        payload = {
            "sub": user.id,
            "email": user.email,
            "username": user.username,
            "role": user.role.value,
            "permissions": user.permissions,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": TokenType.ACCESS.value,
            "jti": secrets.token_hex(16)  # JWT ID for revocation
        }
        
        try:
            token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            
            # Store token in Redis for revocation checking
            if self.redis_client:
                token_key = f"auth:token:{payload['jti']}"
                await self.redis_client.setex(
                    token_key,
                    int((expire - datetime.utcnow()).total_seconds()),
                    json.dumps({"user_id": user.id, "type": TokenType.ACCESS.value})
                )
            
            logger.info(
                "Access token created",
                user_id=user.id,
                username=user.username,
                expires_at=expire.isoformat(),
                jti=payload['jti']
            )
            
            return token
            
        except Exception as e:
            logger.error("Failed to create access token", error=str(e), user_id=user.id)
            raise TokenError(f"Failed to create access token: {str(e)}")
    
    async def create_refresh_token(self, user: User) -> str:
        """Create JWT refresh token."""
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        
        payload = {
            "sub": user.id,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": TokenType.REFRESH.value,
            "jti": secrets.token_hex(16)
        }
        
        try:
            token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            
            # Store refresh token in Redis
            if self.redis_client:
                token_key = f"auth:refresh:{payload['jti']}"
                await self.redis_client.setex(
                    token_key,
                    int((expire - datetime.utcnow()).total_seconds()),
                    json.dumps({"user_id": user.id, "type": TokenType.REFRESH.value})
                )
            
            logger.info(
                "Refresh token created",
                user_id=user.id,
                expires_at=expire.isoformat(),
                jti=payload['jti']
            )
            
            return token
            
        except Exception as e:
            logger.error("Failed to create refresh token", error=str(e), user_id=user.id)
            raise TokenError(f"Failed to create refresh token: {str(e)}")
    
    async def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify JWT token."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Check if token is revoked
            if self.redis_client and "jti" in payload:
                token_key = f"auth:token:{payload['jti']}"
                if payload.get("type") == TokenType.REFRESH.value:
                    token_key = f"auth:refresh:{payload['jti']}"
                
                stored_token = await self.redis_client.get(token_key)
                if not stored_token:
                    raise TokenError("Token has been revoked")
            
            return payload
            
        except ExpiredSignatureError:
            raise TokenError("Token has expired")
        except InvalidTokenError as e:
            raise TokenError(f"Invalid token: {str(e)}")
        except Exception as e:
            logger.error("Token verification failed", error=str(e))
            raise TokenError(f"Token verification failed: {str(e)}")
    
    async def revoke_token(self, token: str) -> None:
        """Revoke a token."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            if self.redis_client and "jti" in payload:
                token_key = f"auth:token:{payload['jti']}"
                if payload.get("type") == TokenType.REFRESH.value:
                    token_key = f"auth:refresh:{payload['jti']}"
                
                await self.redis_client.delete(token_key)
                
                logger.info(
                    "Token revoked",
                    user_id=payload.get("sub"),
                    jti=payload.get("jti"),
                    type=payload.get("type")
                )
            
        except Exception as e:
            logger.error("Token revocation failed", error=str(e))
            raise TokenError(f"Token revocation failed: {str(e)}")
    
    async def refresh_access_token(self, refresh_token: str) -> str:
        """Refresh access token using refresh token."""
        try:
            payload = await self.verify_token(refresh_token)
            
            if payload.get("type") != TokenType.REFRESH.value:
                raise TokenError("Invalid refresh token")
            
            # Here you would typically load the user from database
            # For now, we'll create a minimal user object
            user = User(
                id=payload["sub"],
                email="",  # Would be loaded from database
                username="",  # Would be loaded from database
                role=UserRole.USER,  # Would be loaded from database
                permissions=[]  # Would be loaded from database
            )
            
            new_access_token = self.create_access_token(user)
            
            logger.info(
                "Access token refreshed",
                user_id=user.id,
                refresh_jti=payload.get("jti")
            )
            
            return new_access_token
            
        except Exception as e:
            logger.error("Token refresh failed", error=str(e))
            raise TokenError(f"Token refresh failed: {str(e)}")


class OAuth2Authenticator:
    """OAuth2 authentication service."""
    
    def __init__(
        self,
        client_id: str,
        client_secret: str,
        redirect_uri: str,
        scopes: List[str],
        redis_client: Optional[redis.Redis] = None
    ):
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri
        self.scopes = scopes
        self.redis_client = redis_client
        
    async def get_authorization_url(self, state: Optional[str] = None) -> str:
        """Get OAuth2 authorization URL."""
        if not state:
            state = secrets.token_urlsafe(32)
        
        # Store state in Redis for verification
        if self.redis_client:
            state_key = f"oauth2:state:{state}"
            await self.redis_client.setex(state_key, 600, "valid")  # 10 minutes
        
        params = {
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri,
            "scope": " ".join(self.scopes),
            "response_type": "code",
            "state": state
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"https://accounts.google.com/o/oauth2/v2/auth?{query_string}"
    
    async def exchange_code_for_token(self, code: str, state: str) -> Dict[str, Any]:
        """Exchange authorization code for access token."""
        # Verify state
        if self.redis_client:
            state_key = f"oauth2:state:{state}"
            stored_state = await self.redis_client.get(state_key)
            if not stored_state:
                raise AuthenticationError("Invalid or expired state parameter")
            await self.redis_client.delete(state_key)
        
        # Exchange code for token
        token_url = "https://oauth2.googleapis.com/token"
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": self.redirect_uri
        }
        
        # Here you would make HTTP request to get token
        # Implementation depends on your HTTP client
        # For now, returning a placeholder
        return {
            "access_token": "oauth2_access_token",
            "refresh_token": "oauth2_refresh_token",
            "expires_in": 3600,
            "token_type": "Bearer"
        }


class ServiceAccountAuthenticator:
    """Service account authentication."""
    
    def __init__(
        self,
        service_account_key: Dict[str, Any],
        scopes: List[str],
        redis_client: Optional[redis.Redis] = None
    ):
        self.service_account_key = service_account_key
        self.scopes = scopes
        self.redis_client = redis_client
        
    def authenticate(self) -> service_account.Credentials:
        """Authenticate service account."""
        try:
            credentials = service_account.Credentials.from_service_account_info(
                self.service_account_key,
                scopes=self.scopes
            )
            
            logger.info(
                "Service account authenticated",
                service_account_email=self.service_account_key.get("client_email"),
                project_id=self.service_account_key.get("project_id")
            )
            
            return credentials
            
        except Exception as e:
            logger.error("Service account authentication failed", error=str(e))
            raise AuthenticationError(f"Service account authentication failed: {str(e)}")


class MFAAuthenticator:
    """Multi-factor authentication service."""
    
    def __init__(self, app_name: str = "Pattern Mining Service"):
        self.app_name = app_name
        
    def generate_secret(self) -> str:
        """Generate MFA secret."""
        return pyotp.random_base32()
    
    def generate_qr_code_url(self, user_email: str, secret: str) -> str:
        """Generate QR code URL for MFA setup."""
        totp = pyotp.TOTP(secret)
        return totp.provisioning_uri(
            user_email,
            issuer_name=self.app_name
        )
    
    def verify_totp(self, secret: str, token: str) -> bool:
        """Verify TOTP token."""
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(token, valid_window=1)
        except Exception as e:
            logger.error("TOTP verification failed", error=str(e))
            return False
    
    def generate_backup_codes(self, count: int = 8) -> List[str]:
        """Generate backup codes for MFA."""
        return [secrets.token_hex(4) for _ in range(count)]


class SessionManager:
    """Session management service."""
    
    def __init__(
        self,
        redis_client: redis.Redis,
        session_timeout_minutes: int = 30,
        max_sessions_per_user: int = 5
    ):
        self.redis_client = redis_client
        self.session_timeout_minutes = session_timeout_minutes
        self.max_sessions_per_user = max_sessions_per_user
        
    async def create_session(
        self,
        user_id: str,
        ip_address: str,
        user_agent: str
    ) -> Session:
        """Create a new session."""
        session_id = secrets.token_urlsafe(32)
        now = datetime.utcnow()
        expires_at = now + timedelta(minutes=self.session_timeout_minutes)
        
        session = Session(
            session_id=session_id,
            user_id=user_id,
            created_at=now,
            expires_at=expires_at,
            last_activity=now,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        # Store session in Redis
        session_key = f"session:{session_id}"
        await self.redis_client.setex(
            session_key,
            int(timedelta(minutes=self.session_timeout_minutes).total_seconds()),
            json.dumps(asdict(session), default=str)
        )
        
        # Track user sessions
        user_sessions_key = f"user_sessions:{user_id}"
        await self.redis_client.sadd(user_sessions_key, session_id)
        await self.redis_client.expire(user_sessions_key, self.session_timeout_minutes * 60)
        
        # Enforce max sessions per user
        await self._enforce_max_sessions(user_id)
        
        logger.info(
            "Session created",
            user_id=user_id,
            session_id=session_id,
            ip_address=ip_address,
            expires_at=expires_at.isoformat()
        )
        
        return session
    
    async def get_session(self, session_id: str) -> Optional[Session]:
        """Get session by ID."""
        session_key = f"session:{session_id}"
        session_data = await self.redis_client.get(session_key)
        
        if not session_data:
            return None
        
        try:
            data = json.loads(session_data)
            return Session(**data)
        except Exception as e:
            logger.error("Failed to deserialize session", error=str(e), session_id=session_id)
            return None
    
    async def update_session_activity(self, session_id: str) -> None:
        """Update session last activity."""
        session = await self.get_session(session_id)
        if not session:
            raise SessionError("Session not found")
        
        session.last_activity = datetime.utcnow()
        
        # Update session in Redis
        session_key = f"session:{session_id}"
        await self.redis_client.setex(
            session_key,
            int(timedelta(minutes=self.session_timeout_minutes).total_seconds()),
            json.dumps(asdict(session), default=str)
        )
    
    async def revoke_session(self, session_id: str) -> None:
        """Revoke a session."""
        session = await self.get_session(session_id)
        if session:
            # Remove from user sessions
            user_sessions_key = f"user_sessions:{session.user_id}"
            await self.redis_client.srem(user_sessions_key, session_id)
            
            # Remove session
            session_key = f"session:{session_id}"
            await self.redis_client.delete(session_key)
            
            logger.info(
                "Session revoked",
                user_id=session.user_id,
                session_id=session_id
            )
    
    async def revoke_all_user_sessions(self, user_id: str) -> None:
        """Revoke all sessions for a user."""
        user_sessions_key = f"user_sessions:{user_id}"
        session_ids = await self.redis_client.smembers(user_sessions_key)
        
        for session_id in session_ids:
            await self.revoke_session(session_id)
        
        await self.redis_client.delete(user_sessions_key)
        
        logger.info(
            "All user sessions revoked",
            user_id=user_id,
            session_count=len(session_ids)
        )
    
    async def _enforce_max_sessions(self, user_id: str) -> None:
        """Enforce maximum sessions per user."""
        user_sessions_key = f"user_sessions:{user_id}"
        session_ids = await self.redis_client.smembers(user_sessions_key)
        
        if len(session_ids) > self.max_sessions_per_user:
            # Remove oldest sessions
            sessions_to_remove = len(session_ids) - self.max_sessions_per_user
            oldest_sessions = list(session_ids)[:sessions_to_remove]
            
            for session_id in oldest_sessions:
                await self.revoke_session(session_id)
            
            logger.info(
                "Enforced max sessions per user",
                user_id=user_id,
                removed_sessions=sessions_to_remove
            )