# Pattern Mining Service Secure Multi-Stage Dockerfile
# Enhanced with security hardening best practices
# Build targets: development, production, production-gpu

ARG PYTHON_VERSION=3.13
ARG CUDA_VERSION=12.3

# =============================================================================
# Base stage with common dependencies and security hardening
# =============================================================================
FROM python:${PYTHON_VERSION}-slim-bookworm AS base

# Add security labels
LABEL maintainer="Pattern Mining Team"
LABEL security.scan="required"
LABEL security.vulnerabilities="none"

# Set secure environment variables
ENV PYTHONPATH=/app/src \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive \
    # Security: Disable pip version check to avoid external calls
    PIP_NO_COLOR=1 \
    # Security: Force UTF-8 encoding
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8

# Create app user first (before installing packages)
RUN groupadd --gid 1000 app \
    && useradd --uid 1000 --gid app --shell /usr/sbin/nologin --create-home app

# Install only essential system dependencies with security updates
RUN apt-get update \
    && apt-get upgrade -y \
    && apt-get install -y --no-install-recommends \
        gcc \
        g++ \
        git \
        curl \
        ca-certificates \
        # Remove after build
        && apt-get clean \
        && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* \
        && rm -rf /root/.cache /root/.cargo

# Create app directory with proper permissions
WORKDIR /app
RUN chown -R app:app /app

# =============================================================================
# Development stage (less restrictive for development)
# =============================================================================
FROM base AS development

# Install development dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    vim \
    htop \
    iputils-ping \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Switch to app user for pip installs
USER app

# Copy requirements files with proper ownership
COPY --chown=app:app requirements*.txt ./

# Install all dependencies including dev
RUN pip install --user --upgrade pip setuptools wheel \
    && pip install --user -r requirements.txt \
    && if [ -f requirements-dev.txt ]; then pip install --user -r requirements-dev.txt; fi

# Copy application code
COPY --chown=app:app . .

# Install application in editable mode
RUN pip install --user -e .

EXPOSE 8000

# Development command with auto-reload
CMD ["python", "-m", "uvicorn", "pattern_mining.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# =============================================================================
# Builder stage for production (minimize attack surface)
# =============================================================================
FROM base AS builder

# Copy only requirements (minimize build context)
COPY --chown=app:app requirements.txt .

# Build wheels as root (needed for compilation)
RUN pip install --upgrade pip setuptools wheel \
    && pip wheel --no-cache-dir --no-deps --wheel-dir /app/wheels -r requirements.txt

# =============================================================================
# Security scanner stage
# =============================================================================
FROM aquasec/trivy:latest AS security-scanner
COPY --from=builder /app/wheels /wheels
RUN trivy fs --no-progress --security-checks vuln /wheels

# =============================================================================
# Production stage with maximum security
# =============================================================================
FROM gcr.io/distroless/python3-debian12 AS production

# Copy Python packages from builder
COPY --from=builder /app/wheels /wheels
COPY --from=builder /usr/local/lib/python*/site-packages /usr/local/lib/python3.13/site-packages

# Copy application code with minimal permissions
WORKDIR /app
COPY --chmod=550 src/ src/
COPY --chmod=440 pyproject.toml .
COPY --chmod=440 README.md .

# Install application (this is tricky with distroless, might need adjustment)
# For distroless, we need to pre-install in builder stage

# Create necessary directories with restricted permissions
USER 1000:1000

# Environment variables for production
ENV PYTHONPATH=/app/src \
    PYTHONUNBUFFERED=1 \
    # Security: Don't write bytecode
    PYTHONDONTWRITEBYTECODE=1 \
    # Security: Use random hash seed
    PYTHONHASHSEED=random

# Expose port (documentation only in distroless)
EXPOSE 8000

# Note: distroless doesn't have shell, so no HEALTHCHECK
# Health checks should be done by orchestrator (Kubernetes/Cloud Run)

# Production command
ENTRYPOINT ["python", "-m", "uvicorn", "pattern_mining.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]

# =============================================================================
# Alternative: Hardened production with minimal Alpine
# =============================================================================
FROM python:${PYTHON_VERSION}-alpine AS production-alpine

# Install security updates
RUN apk update && apk upgrade

# Install only runtime dependencies
RUN apk add --no-cache \
    libstdc++ \
    libgcc \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Create app user with no shell
RUN addgroup -g 1000 -S app \
    && adduser -u 1000 -S -G app -s /sbin/nologin -h /app app

WORKDIR /app

# Copy wheels from builder
COPY --from=builder --chown=app:app /app/wheels /wheels

# Install pre-built wheels as app user
USER app
RUN pip install --user --no-cache /wheels/*

# Copy application code with restricted permissions
COPY --chown=app:app --chmod=550 src/ src/
COPY --chown=app:app --chmod=440 pyproject.toml .
COPY --chown=app:app --chmod=440 README.md .

# Install application
RUN pip install --user .

# Remove pip to reduce attack surface
USER root
RUN apk del py3-pip && rm -rf /usr/lib/python*/ensurepip

USER app

# Security: Set read-only root filesystem
# (Application should write to specific mounted volumes only)
# This is set at runtime: docker run --read-only

# Environment variables
ENV PYTHONPATH=/app/src \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONHASHSEED=random \
    PATH=/app/.local/bin:$PATH

# Create writable directories that the app needs
# These should be mounted as volumes in production
RUN mkdir -p /app/tmp /app/logs

# Expose port
EXPOSE 8000

# Health check without curl (use Python)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import urllib.request; urllib.request.urlopen('http://localhost:8000/health').read()"

# Production command with security options
CMD ["python", "-m", "uvicorn", "pattern_mining.api.main:app", \
     "--host", "0.0.0.0", \
     "--port", "8000", \
     "--workers", "1", \
     "--access-log", \
     "--use-colors", "false", \
     "--loop", "uvloop"]

# =============================================================================
# GPU Production stage with security
# =============================================================================
FROM nvidia/cuda:${CUDA_VERSION}-runtime-ubuntu22.04 AS production-gpu

ARG PYTHON_VERSION

# Security updates first
RUN apt-get update && apt-get upgrade -y

# Install Python and minimal dependencies
RUN apt-get install -y --no-install-recommends \
    python${PYTHON_VERSION} \
    python${PYTHON_VERSION}-distutils \
    python3-pip \
    ca-certificates \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* \
    && ln -s /usr/bin/python${PYTHON_VERSION} /usr/bin/python

# Create app user with no shell
RUN groupadd --gid 1000 app \
    && useradd --uid 1000 --gid app --shell /usr/sbin/nologin --create-home app

WORKDIR /app

# Copy from builder
COPY --from=builder --chown=app:app /app/wheels /wheels

# Switch to app user
USER app

# Install dependencies
RUN pip install --user --no-cache /wheels/*

# Copy application with restricted permissions
COPY --chown=app:app --chmod=550 src/ src/
COPY --chown=app:app --chmod=440 pyproject.toml .
COPY --chown=app:app --chmod=440 README.md .

# Install application
RUN pip install --user .

# Install GPU-specific requirements
COPY --chown=app:app --chmod=440 requirements-gpu.txt .
RUN pip install --user -r requirements-gpu.txt

# Remove pip from user path
RUN rm -rf ~/.cache/pip

# Environment variables
ENV PYTHONPATH=/app/src \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONHASHSEED=random \
    PATH=/app/.local/bin:$PATH

EXPOSE 8000

# Health check using Python
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import urllib.request; urllib.request.urlopen('http://localhost:8000/health').read()"

CMD ["python", "-m", "uvicorn", "pattern_mining.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]

# =============================================================================
# Security scanning stage (run separately)
# =============================================================================
FROM aquasec/trivy:latest AS final-scan
COPY --from=production-alpine /app /app
RUN trivy fs --exit-code 1 --no-progress --security-checks vuln,config /app