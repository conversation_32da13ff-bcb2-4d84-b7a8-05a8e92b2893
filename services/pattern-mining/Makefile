# Pattern Mining Service Makefile
# Wave 2.5: CCL Contract Compliance Implementation

.PHONY: help install test test-unit test-integration test-contract test-performance \
        test-all coverage lint format clean docker-build docker-run validate-contract \
        validate-performance validate-all dev run

# Default target
.DEFAULT_GOAL := help

# Variables
PYTHON := python3
PIP := pip3
PROJECT_NAME := pattern-mining
SRC_DIR := src/pattern_mining
TEST_DIR := tests
DOCKER_IMAGE := episteme/pattern-mining:latest
PORT := 8001

# Help target
help:
	@echo "Pattern Mining Service - Available Commands"
	@echo "=========================================="
	@echo ""
	@echo "Development:"
	@echo "  make install          Install dependencies"
	@echo "  make dev              Run development server"
	@echo "  make run              Run production server"
	@echo ""
	@echo "Testing:"
	@echo "  make test             Run all tests"
	@echo "  make test-unit        Run unit tests"
	@echo "  make test-integration Run integration tests"
	@echo "  make test-contract    Run contract compliance tests"
	@echo "  make test-performance Run performance tests"
	@echo "  make coverage         Generate coverage report"
	@echo ""
	@echo "Validation:"
	@echo "  make validate-contract    Run contract validation"
	@echo "  make validate-performance Run performance validation"
	@echo "  make validate-all         Run all validations"
	@echo ""
	@echo "Code Quality:"
	@echo "  make lint             Run linters"
	@echo "  make format           Format code"
	@echo ""
	@echo "Docker:"
	@echo "  make docker-build     Build Docker image"
	@echo "  make docker-run       Run Docker container"
	@echo ""
	@echo "Utilities:"
	@echo "  make clean            Clean generated files"

# Installation
install:
	@echo "Installing dependencies..."
	$(PIP) install -e ".[dev,test]"
	@echo "Dependencies installed successfully!"

# Development server
dev:
	@echo "Starting development server..."
	cd src && $(PYTHON) -m pattern_mining.main

# Production server
run:
	@echo "Starting production server..."
	cd src && uvicorn pattern_mining.api.main:app --host 0.0.0.0 --port $(PORT) --workers 4

# Testing targets
test:
	@echo "Running all tests..."
	pytest $(TEST_DIR) -v

test-unit:
	@echo "Running unit tests..."
	pytest $(TEST_DIR)/unit -v -m "unit"

test-integration:
	@echo "Running integration tests..."
	pytest $(TEST_DIR)/integration -v -m "integration"

test-contract:
	@echo "Running contract compliance tests..."
	pytest $(TEST_DIR)/contract -v -m "contract"

test-performance:
	@echo "Running performance tests..."
	pytest $(TEST_DIR)/performance -v -m "performance" --tb=short

test-all: test-unit test-integration test-contract test-performance
	@echo "All test suites completed!"

# Coverage
coverage:
	@echo "Generating coverage report..."
	pytest $(TEST_DIR) --cov=$(SRC_DIR) --cov-report=term-missing --cov-report=html
	@echo "Coverage report generated in htmlcov/index.html"

# Contract validation
validate-contract:
	@echo "Running CCL contract validation..."
	./scripts/validate_contract_performance.py

validate-performance:
	@echo "Running performance validation..."
	./scripts/validate_all_performance.py

validate-all: validate-contract validate-performance
	@echo "All validations completed!"

# Run complete contract test suite
test-contract-full:
	@echo "Running complete contract test suite..."
	./scripts/run_contract_tests.sh

# Code quality
lint:
	@echo "Running linters..."
	ruff check $(SRC_DIR) $(TEST_DIR)
	mypy $(SRC_DIR) --ignore-missing-imports

format:
	@echo "Formatting code..."
	black $(SRC_DIR) $(TEST_DIR)
	ruff check --fix $(SRC_DIR) $(TEST_DIR)

# Docker
docker-build:
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE) .

docker-run:
	@echo "Running Docker container..."
	docker run -p $(PORT):$(PORT) \
		-e ENVIRONMENT=production \
		-e REDIS_URL=redis://redis:6379 \
		$(DOCKER_IMAGE)

# Clean
clean:
	@echo "Cleaning generated files..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.coverage" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".mypy_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "htmlcov" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".ruff_cache" -exec rm -rf {} + 2>/dev/null || true
	@echo "Cleanup complete!"

# Development shortcuts
.PHONY: t tc tp tv tca

# Quick test shortcuts
t: test
tc: test-contract
tp: test-performance
tv: validate-all
tca: test-contract-full

# CI/CD targets
.PHONY: ci cd

ci: lint test coverage
	@echo "CI checks completed!"

cd: docker-build
	@echo "CD build completed!"

# Wave-specific targets
.PHONY: wave2 wave25 wave3

wave2:
	@echo "Running Wave 2 validation..."
	$(PYTHON) scripts/validate_performance.py

wave25:
	@echo "Running Wave 2.5 contract compliance..."
	make test-contract-full

wave3:
	@echo "Wave 3: Context Engineering Alignment"
	@echo "Not yet implemented"

# Database migrations (if needed)
.PHONY: db-upgrade db-downgrade

db-upgrade:
	@echo "Running database migrations..."
	alembic upgrade head

db-downgrade:
	@echo "Rolling back database migration..."
	alembic downgrade -1

# Performance benchmarks
.PHONY: benchmark

benchmark:
	@echo "Running performance benchmarks..."
	$(PYTHON) -m pattern_mining.performance.benchmark

# Documentation
.PHONY: docs

docs:
	@echo "Building documentation..."
	mkdocs build

# Environment setup
.PHONY: setup-env

setup-env:
	@echo "Setting up development environment..."
	$(PYTHON) -m venv venv
	./venv/bin/pip install --upgrade pip
	./venv/bin/pip install -e ".[dev,test,gpu]"
	@echo "Environment setup complete! Activate with: source venv/bin/activate"