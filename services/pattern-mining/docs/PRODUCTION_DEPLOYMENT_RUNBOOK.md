# 🚀 Pattern Mining Service - Production Deployment Runbook

**Service**: Pattern Mining (ML/AI Microservice)  
**Version**: 2.0.0  
**Environment**: Production  
**Last Updated**: July 2025  

## 📋 Table of Contents

1. [Pre-Deployment Checklist](#pre-deployment-checklist)
2. [Deployment Process](#deployment-process)
3. [Post-Deployment Validation](#post-deployment-validation)
4. [Rollback Procedures](#rollback-procedures)
5. [Monitoring & Alerts](#monitoring--alerts)
6. [Troubleshooting](#troubleshooting)
7. [Emergency Contacts](#emergency-contacts)

---

## 📝 Pre-Deployment Checklist

### 1. Environment Prerequisites

- [ ] **GCP Project**: Verify correct project (`episteme-prod`)
- [ ] **Authentication**: Logged in with appropriate GCP account
- [ ] **APIs Enabled**:
  - [ ] Cloud Run API
  - [ ] Container Registry API
  - [ ] Secret Manager API
  - [ ] BigQuery API
  - [ ] Cloud Storage API
  - [ ] Monitoring API

### 2. Security Requirements

- [ ] **Secrets Created** in Secret Manager:
  - [ ] `gemini-api-key` - Gemini API key with 24-hour rotation
  - [ ] `jwt-secret` - JWT signing secret
  - [ ] `database-password` - Database password
  - [ ] `redis-password` - Redis password
- [ ] **Service Account**: `<EMAIL>`
- [ ] **IAM Roles** assigned to service account:
  - [ ] `roles/bigquery.dataEditor`
  - [ ] `roles/bigquery.jobUser`
  - [ ] `roles/storage.objectViewer`
  - [ ] `roles/secretmanager.secretAccessor`
  - [ ] `roles/monitoring.metricWriter`

### 3. Code & Configuration

- [ ] **Branch**: Deploy from `main` or release branch
- [ ] **Tests Passed**: All unit, integration, and contract tests
- [ ] **Security Scan**: No critical vulnerabilities
- [ ] **Code Review**: Approved by team lead
- [ ] **Configuration**: Production config reviewed

### 4. Infrastructure

- [ ] **VPC Connector**: `pattern-mining-connector` (optional)
- [ ] **Database**: BigQuery datasets created
- [ ] **Redis**: Instance available and accessible
- [ ] **Monitoring**: Dashboards and alerts configured

---

## 🚀 Deployment Process

### Step 1: Run Pre-Deployment Validation

```bash
cd services/pattern-mining
./scripts/pre-deployment-validation.sh
```

✅ **Expected Result**: All checks pass or only warnings

### Step 2: Build and Deploy

```bash
# Set environment variables
export PROJECT_ID=episteme-prod
export REGION=us-central1

# Run deployment script
./scripts/deploy-production.sh
```

The script will:
1. Validate prerequisites
2. Build Docker image
3. Push to Container Registry
4. Deploy to Cloud Run
5. Run smoke tests
6. Configure monitoring

### Step 3: Verify Deployment

```bash
# Get service URL
SERVICE_URL=$(gcloud run services describe pattern-mining \
    --region=us-central1 \
    --format="value(status.url)")

# Check health
curl -s "$SERVICE_URL/health" | jq .

# Check metrics
curl -s "$SERVICE_URL/metrics"
```

### Step 4: Traffic Migration

For canary deployment:

```bash
# Start with 10% traffic
gcloud run services update-traffic pattern-mining \
    --to-revisions=LATEST=10 \
    --region=us-central1

# Monitor for 15 minutes, then increase
gcloud run services update-traffic pattern-mining \
    --to-revisions=LATEST=25 \
    --region=us-central1

# Continue increasing: 50%, 75%, 100%
```

---

## ✅ Post-Deployment Validation

### 1. Health Checks

```bash
# Service health
curl -s "$SERVICE_URL/health" | jq .

# Ready check
curl -s "$SERVICE_URL/ready" | jq .

# Version info
curl -s "$SERVICE_URL/version" | jq .
```

### 2. Functional Tests

```bash
# Test pattern detection endpoint
curl -X POST "$SERVICE_URL/api/v1/patterns/detect" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -d @test-payload.json

# Test ML inference
curl -X POST "$SERVICE_URL/api/v1/ml/analyze" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -d @ml-test-payload.json
```

### 3. Performance Tests

```bash
# Run performance validation
cd services/pattern-mining
python scripts/validate_contract_performance.py

# Check latency metrics
curl -s "$SERVICE_URL/metrics" | grep "http_request_duration"
```

### 4. Security Validation

```bash
# Check security metrics
curl -s "$SERVICE_URL/security/metrics" | jq .

# Verify prompt injection protection
curl -X POST "$SERVICE_URL/api/v1/patterns/detect" \
    -H "Content-Type: application/json" \
    -d '{"code": "'; DROP TABLE users; --"}'
```

---

## 🔄 Rollback Procedures

### Immediate Rollback (< 5 minutes)

```bash
# List revisions
gcloud run revisions list \
    --service=pattern-mining \
    --region=us-central1

# Rollback to previous revision
gcloud run services update-traffic pattern-mining \
    --to-revisions=pattern-mining-00002-abc=100 \
    --region=us-central1
```

### Full Rollback

```bash
# Deploy previous version
gcloud run deploy pattern-mining \
    --image=gcr.io/episteme-prod/pattern-mining:v1.9.0 \
    --region=us-central1
```

### Database Rollback

If schema changes were made:

```bash
# Connect to BigQuery
bq query --use_legacy_sql=false \
    "SELECT * FROM `episteme-prod.pattern_mining.migrations` ORDER BY applied_at DESC LIMIT 5"

# Apply rollback migrations
python -m alembic downgrade -1
```

---

## 📊 Monitoring & Alerts

### Key Metrics to Monitor

1. **Availability**
   - Target: 99.9%
   - Alert: < 99.5% over 10 minutes

2. **Latency**
   - P50: < 100ms
   - P95: < 500ms
   - P99: < 1000ms
   - Alert: P95 > 1000ms for 5 minutes

3. **Error Rate**
   - Target: < 1%
   - Alert: > 5% for 5 minutes

4. **Resource Usage**
   - CPU: < 70%
   - Memory: < 80%
   - Alert: > 80% for 5 minutes

### Dashboards

- **Service Health**: https://console.cloud.google.com/monitoring/dashboards/custom/pattern-mining-health
- **Performance**: https://console.cloud.google.com/monitoring/dashboards/custom/pattern-mining-performance
- **Security**: https://console.cloud.google.com/monitoring/dashboards/custom/pattern-mining-security

### Log Queries

```bash
# Recent errors
gcloud logging read 'resource.type="cloud_run_revision" 
    resource.labels.service_name="pattern-mining"
    severity>=ERROR' --limit=50

# Slow requests
gcloud logging read 'resource.type="cloud_run_revision"
    resource.labels.service_name="pattern-mining"
    httpRequest.latency>1s' --limit=20

# Security events
gcloud logging read 'resource.type="cloud_run_revision"
    resource.labels.service_name="pattern-mining"
    jsonPayload.security_event=true' --limit=50
```

---

## 🔧 Troubleshooting

### Common Issues

#### 1. Service Not Starting

```bash
# Check logs
gcloud logging read 'resource.type="cloud_run_revision"
    resource.labels.service_name="pattern-mining"' --limit=100

# Common causes:
# - Missing secrets
# - Invalid environment variables
# - Database connection issues
```

#### 2. High Latency

```bash
# Check resource usage
gcloud run services describe pattern-mining --region=us-central1

# Scale up if needed
gcloud run services update pattern-mining \
    --max-instances=100 \
    --region=us-central1
```

#### 3. Authentication Errors

```bash
# Verify service account
gcloud iam service-accounts get-iam-policy \
    <EMAIL>

# Check JWT secret
gcloud secrets versions access latest --secret=jwt-secret
```

#### 4. Database Connection Issues

```bash
# Test BigQuery connection
bq ls --project_id=episteme-prod

# Check database logs
gcloud logging read 'resource.type="bigquery_resource"
    protoPayload.serviceName="bigquery.googleapis.com"' --limit=50
```

### Debug Mode

For detailed debugging:

```bash
# Deploy with debug logging
gcloud run deploy pattern-mining \
    --set-env-vars="LOG_LEVEL=DEBUG,DEBUG=true" \
    --region=us-central1

# Remember to disable after debugging!
```

---

## 📞 Emergency Contacts

### On-Call Rotation

- **Primary**: Check PagerDuty schedule
- **Secondary**: Team lead
- **Escalation**: Platform team

### Key Personnel

| Role | Contact | Responsibility |
|------|---------|----------------|
| Service Owner | TBD | Overall service health |
| Platform Lead | TBD | Infrastructure issues |
| Security Lead | TBD | Security incidents |
| ML Lead | TBD | Model/algorithm issues |

### Incident Response

1. **Acknowledge** alert in PagerDuty
2. **Assess** severity and impact
3. **Communicate** in #incidents channel
4. **Execute** rollback if needed
5. **Document** in incident report

### External Dependencies

| Service | Status Page | Support |
|---------|------------|---------|
| Google Cloud | https://status.cloud.google.com | GCP Support |
| Gemini API | Check API console | API Support |
| BigQuery | GCP Status | GCP Support |

---

## 📚 Additional Resources

- [Security Configuration Guide](./security-configuration.md)
- [API Documentation](./api/openapi-contract.yaml)
- [Architecture Overview](./architecture.md)
- [Performance Tuning Guide](./performance-tuning.md)
- [Disaster Recovery Plan](./disaster-recovery.md)

---

**Remember**: Always follow the deployment checklist and never skip validation steps. When in doubt, ask for help!