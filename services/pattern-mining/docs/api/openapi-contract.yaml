openapi: 3.0.3
info:
  title: Pattern Mining Service - CCL Contract API
  description: |
    Pattern Mining Service API compliant with CCL v1.0.0 contracts.
    
    This service processes AST data from the Repository Analysis service and detects various patterns including:
    - Design patterns
    - Anti-patterns
    - Security vulnerabilities
    - Performance issues
    - Code smells
    - Architectural patterns
    - Test patterns
    - Concurrency patterns
  version: 1.0.0
  contact:
    name: Pattern Mining Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://pattern-mining.episteme.com
    description: Production server
  - url: https://staging-pattern-mining.episteme.com
    description: Staging server
  - url: http://localhost:8001
    description: Local development

tags:
  - name: patterns
    description: Pattern detection operations
  - name: health
    description: Health and readiness checks

paths:
  /api/v1/patterns/detect:
    post:
      tags:
        - patterns
      summary: Detect patterns in AST data
      description: |
        Processes AST data from Repository Analysis service and detects patterns.
        Compliant with CCL contract schemas pattern-input-v1 and pattern-output-v1.
      operationId: detectPatterns
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatternInputV1'
            examples:
              basicRequest:
                summary: Basic pattern detection request
                value:
                  repository_id: "repo_1234567890abcdef"
                  analysis_id: "analysis_123456789012"
                  ast_data:
                    repository_id: "repo_1234567890abcdef"
                    commit_hash: "abc123def456"
                    files:
                      - path: "src/main.py"
                        language: "python"
                        ast_nodes: []
                        symbols: []
                        imports: []
                        metrics:
                          lines_of_code: 100
                          cyclomatic_complexity: 10
                    metadata:
                      total_files: 1
                      languages:
                        python: 1
                  detection_config:
                    pattern_types: ["all"]
                    min_confidence: 0.7
      responses:
        '200':
          description: Patterns detected successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PatternOutputV1'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseV1'
        '408':
          description: Request timeout
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseV1'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponseV1'

  /api/v1/health:
    get:
      tags:
        - health
      summary: Health check endpoint
      description: Returns service health status
      operationId: getHealth
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum: [healthy]
                  service:
                    type: string
                    example: pattern-mining
                  version:
                    type: string
                    example: 1.0.0
                  timestamp:
                    type: string
                    format: date-time

  /api/v1/ready:
    get:
      tags:
        - health
      summary: Readiness check endpoint
      description: Returns service readiness status
      operationId: getReady
      responses:
        '200':
          description: Service is ready
          content:
            application/json:
              schema:
                type: object
                properties:
                  ready:
                    type: boolean
                    example: true
                  checks:
                    type: object
                    properties:
                      database:
                        type: boolean
                      redis:
                        type: boolean
                      model:
                        type: boolean
        '503':
          description: Service not ready
          content:
            application/json:
              schema:
                type: object
                properties:
                  ready:
                    type: boolean
                    example: false
                  checks:
                    type: object

components:
  schemas:
    PatternInputV1:
      type: object
      required:
        - repository_id
        - analysis_id
        - ast_data
        - detection_config
      properties:
        repository_id:
          type: string
          pattern: '^repo_[a-zA-Z0-9]{16}$'
          description: Repository identifier
          example: "repo_1234567890abcdef"
        analysis_id:
          type: string
          pattern: '^analysis_[a-zA-Z0-9]{12}$'
          description: Analysis identifier from Repository Analysis service
          example: "analysis_123456789012"
        request_id:
          type: string
          pattern: '^req_[a-zA-Z0-9]{16}$'
          description: Optional request identifier for tracking
          example: "req_abcdef0123456789"
        ast_data:
          $ref: '#/components/schemas/ASTDataV1'
        detection_config:
          $ref: '#/components/schemas/DetectionConfigV1'

    PatternOutputV1:
      type: object
      required:
        - repository_id
        - analysis_id
        - patterns
        - summary
        - processing_time_ms
        - model_version
        - timestamp
      properties:
        repository_id:
          type: string
          pattern: '^repo_[a-zA-Z0-9]{16}$'
        analysis_id:
          type: string
          pattern: '^analysis_[a-zA-Z0-9]{12}$'
        request_id:
          type: string
          pattern: '^req_[a-zA-Z0-9]{16}$'
        patterns:
          type: array
          items:
            $ref: '#/components/schemas/DetectedPatternV1'
        summary:
          $ref: '#/components/schemas/PatternSummaryV1'
        processing_time_ms:
          type: number
          format: float
          minimum: 0
          description: Processing time in milliseconds
        model_version:
          type: string
          description: Pattern detection model version
          example: "1.0.0"
        timestamp:
          type: string
          format: date-time
          description: ISO 8601 timestamp

    ASTDataV1:
      type: object
      required:
        - repository_id
        - commit_hash
        - files
        - metadata
      properties:
        repository_id:
          type: string
          pattern: '^repo_[a-zA-Z0-9]{16}$'
        commit_hash:
          type: string
          minLength: 6
          maxLength: 40
        files:
          type: array
          items:
            $ref: '#/components/schemas/FileASTDataV1'
        metadata:
          type: object
          properties:
            total_files:
              type: integer
              minimum: 0
            languages:
              type: object
              additionalProperties:
                type: integer
            analysis_timestamp:
              type: string
              format: date-time

    FileASTDataV1:
      type: object
      required:
        - path
        - language
        - ast_nodes
        - symbols
        - imports
        - metrics
      properties:
        path:
          type: string
          description: Relative file path
        language:
          type: string
          enum: [python, javascript, typescript, java, go, rust, cpp, csharp]
        ast_nodes:
          type: array
          items:
            type: object
            description: AST node structure
        symbols:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              type:
                type: string
              line:
                type: integer
              scope:
                type: string
        imports:
          type: array
          items:
            type: object
            properties:
              module:
                type: string
              names:
                type: array
                items:
                  type: string
              line:
                type: integer
        metrics:
          type: object
          properties:
            lines_of_code:
              type: integer
              minimum: 0
            cyclomatic_complexity:
              type: integer
              minimum: 0
            cognitive_complexity:
              type: integer
              minimum: 0
            max_nesting_depth:
              type: integer
              minimum: 0

    DetectionConfigV1:
      type: object
      required:
        - pattern_types
        - min_confidence
      properties:
        pattern_types:
          type: array
          items:
            type: string
            enum: [all, design_pattern, anti_pattern, security_vulnerability, performance_issue, code_smell, architectural_pattern, test_pattern, concurrency_pattern]
          minItems: 1
        min_confidence:
          type: number
          format: float
          minimum: 0.0
          maximum: 1.0
          default: 0.7
        max_patterns_per_file:
          type: integer
          minimum: 1
          maximum: 1000
          default: 100
        include_metrics:
          type: boolean
          default: true
        include_recommendations:
          type: boolean
          default: true
        timeout_seconds:
          type: integer
          minimum: 1
          maximum: 300
          default: 30
        parallel_processing:
          type: boolean
          default: true

    DetectedPatternV1:
      type: object
      required:
        - pattern_id
        - pattern_type
        - pattern_name
        - confidence
        - severity
        - file_path
        - line_start
        - line_end
        - description
      properties:
        pattern_id:
          type: string
          pattern: '^pat_[a-zA-Z0-9]{16}$'
          description: Unique pattern identifier
        pattern_type:
          $ref: '#/components/schemas/PatternTypeV1'
        pattern_name:
          type: string
          description: Human-readable pattern name
        confidence:
          type: number
          format: float
          minimum: 0.0
          maximum: 1.0
        severity:
          $ref: '#/components/schemas/SeverityLevelV1'
        file_path:
          type: string
        line_start:
          type: integer
          minimum: 1
        line_end:
          type: integer
          minimum: 1
        column_start:
          type: integer
          minimum: 0
        column_end:
          type: integer
          minimum: 0
        description:
          type: string
        recommendation:
          type: string
        tags:
          type: array
          items:
            type: string
        metadata:
          type: object
          additionalProperties: true

    PatternSummaryV1:
      type: object
      required:
        - total_patterns
        - patterns_by_type
        - patterns_by_severity
        - patterns_by_file
        - quality_score
        - confidence_stats
      properties:
        total_patterns:
          type: integer
          minimum: 0
        patterns_by_type:
          type: object
          additionalProperties:
            type: integer
        patterns_by_severity:
          type: object
          additionalProperties:
            type: integer
        patterns_by_file:
          type: object
          additionalProperties:
            type: integer
        quality_score:
          type: number
          format: float
          minimum: 0.0
          maximum: 100.0
        confidence_stats:
          type: object
          properties:
            mean:
              type: number
              format: float
            median:
              type: number
              format: float
            min:
              type: number
              format: float
            max:
              type: number
              format: float

    ErrorResponseV1:
      type: object
      required:
        - error_id
        - service
        - error_type
        - message
        - timestamp
        - retryable
      properties:
        error_id:
          type: string
          pattern: '^err_[a-zA-Z0-9]{16}$'
        service:
          type: string
          enum: [pattern-mining]
        error_type:
          type: string
          enum: [validation_error, processing_error, timeout_error, internal_error]
        message:
          type: string
        details:
          type: object
          additionalProperties: true
        timestamp:
          type: string
          format: date-time
        retryable:
          type: boolean
        retry_after_seconds:
          type: integer
          minimum: 1

    PatternTypeV1:
      type: string
      enum:
        - design_pattern
        - anti_pattern
        - security_vulnerability
        - performance_issue
        - code_smell
        - architectural_pattern
        - test_pattern
        - concurrency_pattern

    SeverityLevelV1:
      type: string
      enum:
        - critical
        - high
        - medium
        - low
        - info

security:
  - ApiKeyAuth: []
  - BearerAuth: []

externalDocs:
  description: CCL Contract Documentation
  url: https://docs.episteme.com/ccl/contracts/pattern-mining