# CCL Integration Guide - Pattern Mining Service

**Service**: Pattern Mining Service v1.0.0  
**Contract Version**: CCL v1.0.0  
**Last Updated**: 2025-01-18

## Overview

This guide provides comprehensive instructions for integrating with the Pattern Mining Service as part of the CCL (Context-aware Code Learning) ecosystem. The service processes AST data from the Repository Analysis service and detects various code patterns.

## Table of Contents

1. [Service Architecture](#service-architecture)
2. [API Contract](#api-contract)
3. [Integration Requirements](#integration-requirements)
4. [Authentication](#authentication)
5. [API Endpoints](#api-endpoints)
6. [Data Flow](#data-flow)
7. [Error Handling](#error-handling)
8. [Performance Considerations](#performance-considerations)
9. [Monitoring & Observability](#monitoring--observability)
10. [Examples](#examples)
11. [Troubleshooting](#troubleshooting)

## Service Architecture

### Overview
```
Repository Analysis Service
         |
         v
  [AST Data (FileASTData)]
         |
         v
Pattern Mining Service
         |
         v
  [Detected Patterns]
         |
         v
   Downstream Services
```

### Key Components
- **AST Processor**: Handles FileASTData from Repository Analysis
- **Feature Extractor**: Extracts ML-ready features from AST
- **Pattern Detector**: ML-based pattern detection engine
- **API Gateway**: FastAPI-based REST interface

## API Contract

### Contract Files
- Input Schema: `contracts/pattern-input-v1.json`
- Output Schema: `contracts/pattern-output-v1.json`
- OpenAPI Spec: `docs/api/openapi-contract.yaml`

### ID Format Requirements
All IDs must follow CCL standard formats:

| ID Type | Format | Example |
|---------|--------|---------|
| repository_id | `repo_[16 alphanumeric]` | `repo_1234567890abcdef` |
| analysis_id | `analysis_[12 alphanumeric]` | `analysis_123456789012` |
| request_id | `req_[16 alphanumeric]` | `req_abcdef0123456789` |
| pattern_id | `pat_[16 alphanumeric]` | `pat_1234567890abcdef` |
| error_id | `err_[16 alphanumeric]` | `err_1234567890abcdef` |

## Integration Requirements

### Prerequisites
1. **Service Registration**: Register your service with CCL service registry
2. **API Key**: Obtain API key for authentication
3. **Network Access**: Ensure network connectivity to Pattern Mining Service
4. **Contract Version**: Ensure compatibility with CCL v1.0.0

### Dependencies
- Repository Analysis Service output (AST data)
- Redis for caching (optional but recommended)
- Monitoring infrastructure (Prometheus/Grafana)

## Authentication

### API Key Authentication
```bash
curl -X POST https://pattern-mining.episteme.com/api/v1/patterns/detect \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d @request.json
```

### Bearer Token Authentication
```bash
curl -X POST https://pattern-mining.episteme.com/api/v1/patterns/detect \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d @request.json
```

## API Endpoints

### Pattern Detection
**Endpoint**: `POST /api/v1/patterns/detect`

**Request Body**:
```json
{
  "repository_id": "repo_1234567890abcdef",
  "analysis_id": "analysis_123456789012",
  "request_id": "req_abcdef0123456789",  // Optional
  "ast_data": {
    "repository_id": "repo_1234567890abcdef",
    "commit_hash": "abc123def456",
    "files": [
      {
        "path": "src/main.py",
        "language": "python",
        "ast_nodes": [...],
        "symbols": [...],
        "imports": [...],
        "metrics": {
          "lines_of_code": 150,
          "cyclomatic_complexity": 12
        }
      }
    ],
    "metadata": {
      "total_files": 1,
      "languages": {"python": 1}
    }
  },
  "detection_config": {
    "pattern_types": ["all"],
    "min_confidence": 0.7,
    "max_patterns_per_file": 100,
    "include_metrics": true,
    "include_recommendations": true,
    "timeout_seconds": 30,
    "parallel_processing": true
  }
}
```

**Response**:
```json
{
  "repository_id": "repo_1234567890abcdef",
  "analysis_id": "analysis_123456789012",
  "request_id": "req_abcdef0123456789",
  "patterns": [
    {
      "pattern_id": "pat_security12345678",
      "pattern_type": "security_vulnerability",
      "pattern_name": "SQL Injection",
      "confidence": 0.95,
      "severity": "critical",
      "file_path": "src/main.py",
      "line_start": 45,
      "line_end": 47,
      "description": "Potential SQL injection vulnerability",
      "recommendation": "Use parameterized queries",
      "tags": ["security", "sql", "injection"],
      "metadata": {
        "vulnerability_type": "sql_injection",
        "cwe_id": "CWE-89"
      }
    }
  ],
  "summary": {
    "total_patterns": 1,
    "patterns_by_type": {
      "security_vulnerability": 1
    },
    "patterns_by_severity": {
      "critical": 1
    },
    "patterns_by_file": {
      "src/main.py": 1
    },
    "quality_score": 75.5,
    "confidence_stats": {
      "mean": 0.95,
      "median": 0.95,
      "min": 0.95,
      "max": 0.95
    }
  },
  "processing_time_ms": 450.5,
  "model_version": "1.0.0",
  "timestamp": "2025-01-18T10:30:45.123Z"
}
```

### Health Check
**Endpoint**: `GET /api/v1/health`

**Response**:
```json
{
  "status": "healthy",
  "service": "pattern-mining",
  "version": "1.0.0",
  "timestamp": "2025-01-18T10:30:45.123Z"
}
```

### Readiness Check
**Endpoint**: `GET /api/v1/ready`

**Response**:
```json
{
  "ready": true,
  "checks": {
    "database": true,
    "redis": true,
    "model": true
  }
}
```

## Data Flow

### Integration Pattern
```python
import httpx
import asyncio
from typing import Dict, Any

class PatternMiningClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.headers = {
            "X-API-Key": api_key,
            "Content-Type": "application/json"
        }
        
    async def detect_patterns(
        self,
        repository_id: str,
        analysis_id: str,
        ast_data: Dict[str, Any],
        config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Detect patterns in AST data."""
        
        if config is None:
            config = {
                "pattern_types": ["all"],
                "min_confidence": 0.7
            }
            
        request_data = {
            "repository_id": repository_id,
            "analysis_id": analysis_id,
            "ast_data": ast_data,
            "detection_config": config
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/patterns/detect",
                json=request_data,
                headers=self.headers,
                timeout=60.0
            )
            response.raise_for_status()
            return response.json()

# Example usage
async def main():
    client = PatternMiningClient(
        base_url="https://pattern-mining.episteme.com",
        api_key="your-api-key"
    )
    
    # Get AST data from Repository Analysis service
    ast_data = await get_ast_data_from_repository_analysis()
    
    # Detect patterns
    result = await client.detect_patterns(
        repository_id="repo_1234567890abcdef",
        analysis_id="analysis_123456789012",
        ast_data=ast_data,
        config={
            "pattern_types": ["security_vulnerability", "performance_issue"],
            "min_confidence": 0.8
        }
    )
    
    print(f"Detected {result['summary']['total_patterns']} patterns")
```

## Error Handling

### Error Response Format
```json
{
  "error_id": "err_1234567890abcdef",
  "service": "pattern-mining",
  "error_type": "validation_error",
  "message": "Invalid repository_id format",
  "details": {
    "field": "repository_id",
    "provided": "invalid-id",
    "expected_pattern": "^repo_[a-zA-Z0-9]{16}$"
  },
  "timestamp": "2025-01-18T10:30:45.123Z",
  "retryable": false
}
```

### Error Types
- `validation_error`: Invalid request format or parameters
- `processing_error`: Error during pattern detection
- `timeout_error`: Request exceeded timeout limit
- `internal_error`: Internal service error

### Retry Strategy
```python
async def detect_patterns_with_retry(
    client: PatternMiningClient,
    max_retries: int = 3,
    **kwargs
) -> Dict[str, Any]:
    """Detect patterns with exponential backoff retry."""
    
    for attempt in range(max_retries):
        try:
            return await client.detect_patterns(**kwargs)
        except httpx.HTTPStatusError as e:
            if e.response.status_code >= 500:
                # Server error - retry with exponential backoff
                wait_time = 2 ** attempt
                await asyncio.sleep(wait_time)
                continue
            else:
                # Client error - don't retry
                raise
    
    raise Exception(f"Failed after {max_retries} attempts")
```

## Performance Considerations

### Service Limits
- **Processing Budget**: 30 seconds per request
- **File Processing Rate**: 50+ files/second
- **Pattern Detection Rate**: 100+ patterns/second
- **API Latency (p95)**: < 100ms
- **Throughput**: 20+ RPS sustained

### Optimization Tips
1. **Batch Requests**: Group multiple files in single request
2. **Use Caching**: Include request_id for cache hits
3. **Filter Pattern Types**: Only request needed patterns
4. **Adjust Confidence**: Higher threshold = fewer patterns
5. **Enable Parallel Processing**: For large repositories

### Request Size Limits
- Maximum files per request: 1000
- Maximum AST nodes per file: 50000
- Maximum request size: 50MB

## Monitoring & Observability

### Prometheus Metrics
Key metrics exposed at `/metrics`:

```
# Request metrics
ccl_pattern_requests_total
ccl_pattern_request_duration_seconds

# Processing metrics
ccl_files_processed_total
ccl_patterns_detected_total
ccl_processing_time_seconds

# Performance metrics
ccl_file_processing_rate_per_second
ccl_pattern_detection_rate_per_second
ccl_api_latency_p95_milliseconds
ccl_throughput_requests_per_second

# Error metrics
ccl_errors_total
ccl_error_rate_percentage
```

### Grafana Dashboard
Import dashboard from: `monitoring/grafana/dashboards/ccl-contract-compliance.json`

### Logging
Structured JSON logs with correlation IDs:

```json
{
  "timestamp": "2025-01-18T10:30:45.123Z",
  "level": "INFO",
  "service": "pattern-mining",
  "request_id": "req_abcdef0123456789",
  "repository_id": "repo_1234567890abcdef",
  "message": "Pattern detection completed",
  "duration_ms": 450.5,
  "patterns_detected": 15
}
```

## Examples

### Python Integration
```python
# Full example available at: examples/python/pattern_mining_client.py
```

### Go Integration
```go
// Full example available at: examples/go/pattern_mining_client.go
```

### Node.js Integration
```javascript
// Full example available at: examples/nodejs/pattern_mining_client.js
```

## Troubleshooting

### Common Issues

#### 1. ID Format Errors
**Problem**: `Invalid repository_id format`

**Solution**: Ensure all IDs follow CCL format requirements:
```python
import re

def validate_repository_id(repo_id: str) -> bool:
    return bool(re.match(r'^repo_[a-zA-Z0-9]{16}$', repo_id))
```

#### 2. Timeout Errors
**Problem**: `Request timeout after 30 seconds`

**Solution**: 
- Reduce number of files per request
- Increase timeout in detection_config
- Enable caching with request_id

#### 3. High Latency
**Problem**: API responses slower than expected

**Solution**:
- Check network connectivity
- Use geographically closer endpoints
- Enable Redis caching
- Monitor service metrics

#### 4. Missing Patterns
**Problem**: Expected patterns not detected

**Solution**:
- Lower min_confidence threshold
- Check pattern_types filter
- Verify AST data completeness
- Review pattern detection logs

### Debug Mode
Enable debug logging with request header:
```bash
-H "X-Debug-Mode: true"
```

### Support

For additional support:
- Documentation: https://docs.episteme.com/ccl/pattern-mining
- Issues: https://github.com/episteme/pattern-mining/issues
- Email: <EMAIL>

---

**Version History**
- v1.0.0 (2025-01-18): Initial CCL contract compliance release