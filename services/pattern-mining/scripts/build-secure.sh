#!/bin/bash
# Secure Docker Build Script for Pattern Mining Service
# Builds the hardened container with security best practices

set -euo pipefail

# Configuration
SERVICE_NAME="pattern-mining"
DOCKERFILE="Dockerfile.secure"
BUILD_CONTEXT="."
PYTHON_VERSION="${PYTHON_VERSION:-3.13}"
TARGET="${1:-production-alpine}"

# Build timestamp
BUILD_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Image tags
IMAGE_BASE="${SERVICE_NAME}"
IMAGE_TAG_LATEST="${IMAGE_BASE}:secure-latest"
IMAGE_TAG_VERSION="${IMAGE_BASE}:secure-${BUILD_TIMESTAMP}-${GIT_COMMIT}"

echo "=========================================="
echo "Secure Docker Build for Pattern Mining"
echo "=========================================="
echo "Target: ${TARGET}"
echo "Python Version: ${PYTHON_VERSION}"
echo "Git Commit: ${GIT_COMMIT}"
echo "Image Tags:"
echo "  - ${IMAGE_TAG_LATEST}"
echo "  - ${IMAGE_TAG_VERSION}"
echo ""

# Pre-build security checks
echo "1. Pre-build Security Checks"
echo "----------------------------"

# Check for secrets in build context
echo -n "Checking for secrets in build context... "
SECRET_FILES=$(find . -type f \( -name "*.key" -o -name "*.pem" -o -name "*.p12" -o -name "*secret*" -o -name "*password*" \) -not -path "./scripts/*" -not -path "./.git/*" 2>/dev/null | wc -l)
if [ "${SECRET_FILES}" -eq 0 ]; then
    echo "✓ No secret files found"
else
    echo "⚠ Warning: ${SECRET_FILES} potential secret files found in build context"
    echo "  Consider adding them to .dockerignore"
fi

# Verify .dockerignore exists
if [ ! -f ".dockerignore" ]; then
    echo "⚠ Warning: No .dockerignore file found"
    echo "Creating default .dockerignore..."
    cat > .dockerignore << EOF
# Security: Exclude sensitive files
*.key
*.pem
*.p12
*secret*
*password*
.env*
.git/
.gitignore
.dockerignore

# Development files
*.pyc
__pycache__/
.pytest_cache/
.coverage
.mypy_cache/
.ruff_cache/
*.egg-info/
dist/
build/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Documentation
docs/
*.md
!README.md

# Test files
tests/
test_*.py
*_test.py

# Build artifacts
security-scan-results/
*.log
EOF
fi

echo ""

# Build the secure image
echo "2. Building Secure Docker Image"
echo "-------------------------------"
echo "Building with Docker BuildKit for enhanced security..."

# Enable BuildKit for better caching and security features
export DOCKER_BUILDKIT=1

# Build with security labels
docker build \
    --target "${TARGET}" \
    --build-arg PYTHON_VERSION="${PYTHON_VERSION}" \
    --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
    --build-arg VCS_REF="${GIT_COMMIT}" \
    --label "org.opencontainers.image.created=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
    --label "org.opencontainers.image.revision=${GIT_COMMIT}" \
    --label "org.opencontainers.image.version=${BUILD_TIMESTAMP}" \
    --label "security.scan=required" \
    --tag "${IMAGE_TAG_LATEST}" \
    --tag "${IMAGE_TAG_VERSION}" \
    --file "${DOCKERFILE}" \
    "${BUILD_CONTEXT}"

echo ""
echo "✓ Build complete"
echo ""

# Post-build security scan
echo "3. Post-build Security Scan"
echo "---------------------------"

# Run security scan if script exists
if [ -x "./scripts/security-scan.sh" ]; then
    echo "Running security scan on built image..."
    ./scripts/security-scan.sh "${IMAGE_TAG_LATEST}"
else
    echo "⚠ Security scan script not found or not executable"
fi

echo ""

# Image inspection
echo "4. Image Inspection"
echo "-------------------"

# Check image size
IMAGE_SIZE=$(docker images --format "{{.Size}}" "${IMAGE_TAG_LATEST}")
echo "Image size: ${IMAGE_SIZE}"

# Check image layers
LAYER_COUNT=$(docker history "${IMAGE_TAG_LATEST}" | wc -l)
echo "Layer count: ${LAYER_COUNT}"

# Check for running user
echo -n "Default user: "
docker inspect "${IMAGE_TAG_LATEST}" | jq -r '.[0].Config.User' || echo "not set"

# List exposed ports
echo -n "Exposed ports: "
docker inspect "${IMAGE_TAG_LATEST}" | jq -r '.[0].Config.ExposedPorts | keys[]' 2>/dev/null || echo "none"

echo ""

# Test run with security options
echo "5. Security Test Run"
echo "--------------------"
echo "Testing container with security restrictions..."

# Try to run with security options
docker run --rm \
    --name "${SERVICE_NAME}-security-test" \
    --read-only \
    --cap-drop ALL \
    --security-opt no-new-privileges:true \
    --user 1000:1000 \
    --tmpfs /tmp:rw,noexec,nosuid,size=100m \
    --tmpfs /app/tmp:rw,noexec,nosuid,size=100m \
    "${IMAGE_TAG_LATEST}" \
    python -c "print('Security test passed')" && echo "✓ Container runs with security restrictions" || echo "✗ Container failed with security restrictions"

echo ""

# Generate build report
echo "6. Build Report"
echo "---------------"
BUILD_REPORT="security-scan-results/build-report-${BUILD_TIMESTAMP}.json"
mkdir -p security-scan-results

cat > "${BUILD_REPORT}" << EOF
{
  "build_info": {
    "timestamp": "${BUILD_TIMESTAMP}",
    "git_commit": "${GIT_COMMIT}",
    "target": "${TARGET}",
    "python_version": "${PYTHON_VERSION}",
    "image_tags": [
      "${IMAGE_TAG_LATEST}",
      "${IMAGE_TAG_VERSION}"
    ]
  },
  "image_info": {
    "size": "${IMAGE_SIZE}",
    "layers": ${LAYER_COUNT}
  },
  "security_checks": {
    "secret_files_in_context": ${SECRET_FILES},
    "dockerignore_exists": $([ -f .dockerignore ] && echo "true" || echo "false"),
    "runs_as_non_root": $(docker inspect "${IMAGE_TAG_LATEST}" | jq -r '.[0].Config.User != "" and .[0].Config.User != "root"')
  }
}
EOF

echo "Build report saved to: ${BUILD_REPORT}"
echo ""

echo "=========================================="
echo "Secure Build Complete!"
echo "=========================================="
echo "Images built:"
echo "  - ${IMAGE_TAG_LATEST}"
echo "  - ${IMAGE_TAG_VERSION}"
echo ""
echo "Next steps:"
echo "1. Review security scan results"
echo "2. Test the container in a secure environment"
echo "3. Push to secure registry with signature"
echo "4. Deploy with security policies enabled"
echo ""
echo "To push with signature:"
echo "  export DOCKER_CONTENT_TRUST=1"
echo "  docker push ${IMAGE_TAG_VERSION}"