#!/bin/bash
# Container Security Scanning Script for Pattern Mining Service
# Performs comprehensive security checks on Docker images and running containers

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="${1:-pattern-mining:secure}"
SCAN_RESULTS_DIR="./security-scan-results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Create results directory
mkdir -p "${SCAN_RESULTS_DIR}"

echo "=========================================="
echo "Container Security Scan for Pattern Mining"
echo "=========================================="
echo "Image: ${IMAGE_NAME}"
echo "Timestamp: ${TIMESTAMP}"
echo ""

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓${NC} $2"
    else
        echo -e "${RED}✗${NC} $2"
        return 1
    fi
}

# 1. Vulnerability Scanning with Trivy
echo "1. Vulnerability Scanning"
echo "-------------------------"
if command_exists trivy; then
    echo "Running Trivy vulnerability scan..."
    trivy image --severity CRITICAL,HIGH,MEDIUM \
        --format json \
        --output "${SCAN_RESULTS_DIR}/trivy-scan-${TIMESTAMP}.json" \
        "${IMAGE_NAME}"
    
    # Also generate human-readable report
    trivy image --severity CRITICAL,HIGH,MEDIUM \
        --format table \
        --output "${SCAN_RESULTS_DIR}/trivy-scan-${TIMESTAMP}.txt" \
        "${IMAGE_NAME}"
    
    # Check for critical vulnerabilities
    CRIT_VULNS=$(trivy image --severity CRITICAL --format json "${IMAGE_NAME}" | jq '.Results[].Vulnerabilities | length' | awk '{sum+=$1} END {print sum}')
    print_status $([ "${CRIT_VULNS}" -eq 0 ] && echo 0 || echo 1) "Critical vulnerabilities: ${CRIT_VULNS}"
else
    echo -e "${YELLOW}⚠${NC} Trivy not installed. Install with: brew install aquasecurity/trivy/trivy"
fi
echo ""

# 2. Docker Bench Security
echo "2. Docker Bench Security Checks"
echo "--------------------------------"
if [ -f "docker-bench-security.sh" ]; then
    echo "Running Docker Bench Security..."
    ./docker-bench-security.sh -c container_images,container_runtime > "${SCAN_RESULTS_DIR}/docker-bench-${TIMESTAMP}.txt"
else
    echo "Downloading Docker Bench Security..."
    docker run --rm --net host --pid host --userns host --cap-add audit_control \
        -e DOCKER_CONTENT_TRUST=$DOCKER_CONTENT_TRUST \
        -v /var/lib:/var/lib:ro \
        -v /var/run/docker.sock:/var/run/docker.sock:ro \
        -v /etc:/etc:ro \
        docker/docker-bench-security > "${SCAN_RESULTS_DIR}/docker-bench-${TIMESTAMP}.txt"
fi
echo ""

# 3. Image Layer Analysis
echo "3. Image Layer Analysis"
echo "-----------------------"
if command_exists dive; then
    echo "Running Dive analysis..."
    dive "${IMAGE_NAME}" --json "${SCAN_RESULTS_DIR}/dive-analysis-${TIMESTAMP}.json"
else
    echo -e "${YELLOW}⚠${NC} Dive not installed. Install with: brew install dive"
fi

# Using Docker history as alternative
echo "Docker image history:"
docker history --no-trunc "${IMAGE_NAME}" > "${SCAN_RESULTS_DIR}/docker-history-${TIMESTAMP}.txt"
echo ""

# 4. Security Configuration Checks
echo "4. Security Configuration Checks"
echo "--------------------------------"

# Check if running as non-root
echo -n "Checking non-root user... "
USER_ID=$(docker run --rm --entrypoint="" "${IMAGE_NAME}" id -u 2>/dev/null || echo "root")
if [ "${USER_ID}" != "0" ] && [ "${USER_ID}" != "root" ]; then
    print_status 0 "Running as user ${USER_ID}"
else
    print_status 1 "Running as root!"
fi

# Check for SUID binaries
echo -n "Checking for SUID binaries... "
SUID_COUNT=$(docker run --rm --entrypoint="" "${IMAGE_NAME}" find / -perm +6000 -type f 2>/dev/null | wc -l || echo "0")
print_status $([ "${SUID_COUNT}" -eq 0 ] && echo 0 || echo 1) "SUID binaries found: ${SUID_COUNT}"

# Check for shell access
echo -n "Checking shell availability... "
SHELL_AVAIL=$(docker run --rm --entrypoint="" "${IMAGE_NAME}" which sh 2>/dev/null || echo "none")
if [ "${SHELL_AVAIL}" = "none" ]; then
    print_status 0 "No shell available (good for security)"
else
    print_status 1 "Shell available at: ${SHELL_AVAIL}"
fi

# Check secrets in environment
echo -n "Checking for secrets in environment... "
SECRET_PATTERNS="password|secret|key|token|api_key|private"
SECRET_COUNT=$(docker inspect "${IMAGE_NAME}" | jq -r '.[0].Config.Env[]' | grep -iE "${SECRET_PATTERNS}" | wc -l || echo "0")
print_status $([ "${SECRET_COUNT}" -eq 0 ] && echo 0 || echo 1) "Potential secrets in ENV: ${SECRET_COUNT}"

echo ""

# 5. Runtime Security Checks
echo "5. Runtime Security Checks"
echo "--------------------------"

# Test container with security options
echo "Testing container with security options..."
CONTAINER_ID=$(docker run -d --rm \
    --name pattern-mining-security-test \
    --read-only \
    --cap-drop ALL \
    --security-opt no-new-privileges:true \
    --user 1000:1000 \
    "${IMAGE_NAME}" 2>&1 || echo "failed")

if [ "${CONTAINER_ID}" != "failed" ]; then
    sleep 5
    
    # Check if container is still running
    if docker ps | grep -q pattern-mining-security-test; then
        print_status 0 "Container runs with security restrictions"
        docker stop pattern-mining-security-test >/dev/null 2>&1
    else
        print_status 1 "Container failed with security restrictions"
        docker logs pattern-mining-security-test 2>&1 | tail -10
    fi
else
    print_status 1 "Failed to start container with security options"
fi
echo ""

# 6. Dockerfile Analysis
echo "6. Dockerfile Security Analysis"
echo "-------------------------------"
if [ -f "Dockerfile.secure" ]; then
    echo "Analyzing Dockerfile..."
    
    # Check for USER instruction
    grep -q "^USER" Dockerfile.secure && \
        print_status 0 "USER instruction found" || \
        print_status 1 "No USER instruction found"
    
    # Check for HEALTHCHECK
    grep -q "^HEALTHCHECK" Dockerfile.secure && \
        print_status 0 "HEALTHCHECK defined" || \
        print_status 1 "No HEALTHCHECK defined"
    
    # Check for latest tags
    grep -q ":latest" Dockerfile.secure && \
        print_status 1 "Uses :latest tag (not recommended)" || \
        print_status 0 "No :latest tags found"
    
    # Check for apt-get upgrade
    grep -q "apt-get upgrade" Dockerfile.secure && \
        print_status 0 "Security updates applied" || \
        print_status 1 "No security updates in Dockerfile"
fi
echo ""

# 7. Generate Security Report
echo "7. Security Report Generation"
echo "-----------------------------"
REPORT_FILE="${SCAN_RESULTS_DIR}/security-report-${TIMESTAMP}.md"

cat > "${REPORT_FILE}" << EOF
# Security Scan Report for Pattern Mining Service

**Date**: $(date)  
**Image**: ${IMAGE_NAME}

## Executive Summary

This report contains the security scan results for the Pattern Mining service container.

## Vulnerability Summary

### Trivy Scan Results
$(if [ -f "${SCAN_RESULTS_DIR}/trivy-scan-${TIMESTAMP}.txt" ]; then
    echo '```'
    head -20 "${SCAN_RESULTS_DIR}/trivy-scan-${TIMESTAMP}.txt"
    echo '```'
else
    echo "Trivy scan not performed"
fi)

## Configuration Security

- User ID: ${USER_ID}
- SUID Binaries: ${SUID_COUNT}
- Shell Available: ${SHELL_AVAIL}
- Potential Secrets in ENV: ${SECRET_COUNT}

## Recommendations

1. Address any critical or high vulnerabilities identified
2. Ensure all secrets are managed through proper secret management
3. Regularly update base images and dependencies
4. Continue using security best practices in Dockerfile

## Detailed Results

Full scan results are available in: ${SCAN_RESULTS_DIR}
EOF

echo "Security report generated: ${REPORT_FILE}"
echo ""

# 8. Summary
echo "=========================================="
echo "Security Scan Complete"
echo "=========================================="
echo "Results saved to: ${SCAN_RESULTS_DIR}"
echo ""
echo "Key Findings:"
[ -n "${CRIT_VULNS}" ] && echo "- Critical vulnerabilities: ${CRIT_VULNS}"
echo "- Running as user: ${USER_ID}"
echo "- SUID binaries: ${SUID_COUNT}"
echo "- Shell availability: ${SHELL_AVAIL}"
echo "- Potential secrets: ${SECRET_COUNT}"
echo ""
echo "Next steps:"
echo "1. Review the detailed reports in ${SCAN_RESULTS_DIR}"
echo "2. Address any critical vulnerabilities"
echo "3. Implement recommended security improvements"
echo "4. Re-run scan after fixes"