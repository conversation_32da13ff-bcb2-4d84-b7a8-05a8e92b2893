#!/bin/bash
# Performance Validation Script for Pattern Mining Service
# Orchestrates comprehensive performance testing and validation

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="pattern-mining"
VALIDATION_TYPE="${1:-production}"  # production, staging, development
RESULTS_DIR="./tests/performance/validation_results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Ensure results directory exists
mkdir -p "${RESULTS_DIR}"

echo -e "${BLUE}==========================================${NC}"
echo -e "${BLUE}Pattern Mining Service Performance Validation${NC}"
echo -e "${BLUE}==========================================${NC}"
echo -e "Validation Type: ${VALIDATION_TYPE}"
echo -e "Timestamp: ${TIMESTAMP}"
echo -e "Results Directory: ${RESULTS_DIR}"
echo ""

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓${NC} $2"
    else
        echo -e "${RED}✗${NC} $2"
        return 1
    fi
}

# Function to print info
print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Function to print warning
print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Check prerequisites
echo -e "${BLUE}1. Checking Prerequisites${NC}"
echo "------------------------"

# Check if service is running
echo -n "Checking if service is running... "
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health | grep -q "200"; then
    print_status 0 "Service is running"
else
    print_status 1 "Service is not running"
    echo ""
    echo "Please start the service first:"
    echo "  docker-compose up -d"
    echo "  # OR"
    echo "  python -m uvicorn pattern_mining.api.main:app --host 0.0.0.0 --port 8000"
    exit 1
fi

# Check Python dependencies
echo -n "Checking Python dependencies... "
if python -c "import aiohttp, psutil, statistics" 2>/dev/null; then
    print_status 0 "Dependencies available"
else
    print_status 1 "Missing dependencies"
    echo ""
    echo "Please install required dependencies:"
    echo "  pip install aiohttp psutil"
    exit 1
fi

# Check Redis connection (optional)
echo -n "Checking Redis connection... "
if python -c "import redis; r = redis.Redis(host='localhost', port=6379); r.ping()" 2>/dev/null; then
    print_status 0 "Redis connected"
    REDIS_AVAILABLE=true
else
    print_warning "Redis not available (caching features disabled)"
    REDIS_AVAILABLE=false
fi

echo ""

# Pre-validation service health check
echo -e "${BLUE}2. Pre-Validation Health Check${NC}"
echo "-------------------------------"

# Check service health
echo -n "Service health check... "
HEALTH_RESPONSE=$(curl -s http://localhost:8000/health || echo "ERROR")
if echo "$HEALTH_RESPONSE" | grep -q "healthy\|ok"; then
    print_status 0 "Service is healthy"
else
    print_status 1 "Service health check failed"
    echo "Response: $HEALTH_RESPONSE"
    exit 1
fi

# Check service version
echo -n "Service version... "
VERSION_RESPONSE=$(curl -s http://localhost:8000/version || echo "ERROR")
if [ "$VERSION_RESPONSE" != "ERROR" ]; then
    print_status 0 "Version: $VERSION_RESPONSE"
else
    print_warning "Version endpoint not available"
fi

echo ""

# Run performance validation
echo -e "${BLUE}3. Performance Validation${NC}"
echo "-------------------------"

# Create validation script
VALIDATION_SCRIPT="run_validation_${TIMESTAMP}.py"
cat > "${VALIDATION_SCRIPT}" << 'EOF'
import asyncio
import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from pattern_mining.performance.validation import PerformanceValidator
from pattern_mining.cache.redis_client import RedisClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'validation_{sys.argv[1]}_{sys.argv[2]}.log'),
        logging.StreamHandler()
    ]
)

async def main():
    validation_type = sys.argv[1] if len(sys.argv) > 1 else "production"
    redis_available = sys.argv[2] == "true" if len(sys.argv) > 2 else False
    
    # Initialize Redis client if available
    redis_client = None
    if redis_available:
        try:
            redis_client = RedisClient()
            await redis_client.ping()
            print("✓ Redis client initialized")
        except Exception as e:
            print(f"⚠ Redis client failed: {e}")
            redis_client = None
    
    # Initialize validator
    validator = PerformanceValidator(redis_client)
    
    # Run appropriate validation
    if validation_type == "production":
        print("Running production readiness validation...")
        result = await validator.validate_production_readiness()
    elif validation_type == "staging":
        print("Running staging readiness validation...")
        result = await validator.validate_staging_readiness()
    elif validation_type == "development":
        print("Running development readiness validation...")
        result = await validator.validate_development_readiness()
    else:
        print(f"Unknown validation type: {validation_type}")
        sys.exit(1)
    
    # Print results
    print(f"\n{'='*50}")
    print(f"VALIDATION RESULTS")
    print(f"{'='*50}")
    print(f"Overall Status: {result.overall_status.upper()}")
    print(f"Performance Score: {result.performance_score:.1f}/100")
    print(f"Certification Level: {result.certification_level.upper()}")
    print(f"Regression Detected: {result.regression_detected}")
    
    # Print failed criteria
    failed_criteria = [
        criterion for criterion, data in result.validation_criteria.items()
        if not data["passed"]
    ]
    
    if failed_criteria:
        print(f"\nFailed Criteria ({len(failed_criteria)}):")
        for criterion in failed_criteria:
            data = result.validation_criteria[criterion]
            print(f"  - {criterion}: {data['actual']} (threshold: {data['threshold']})")
    
    # Print recommendations
    if result.recommendations:
        print(f"\nRecommendations:")
        for i, rec in enumerate(result.recommendations, 1):
            print(f"  {i}. {rec}")
    
    # Exit with appropriate code
    if result.overall_status == "pass":
        print(f"\n✅ VALIDATION PASSED")
        sys.exit(0)
    elif result.overall_status == "warning":
        print(f"\n⚠️ VALIDATION PASSED WITH WARNINGS")
        sys.exit(0)
    else:
        print(f"\n❌ VALIDATION FAILED")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
EOF

# Run validation
print_info "Running ${VALIDATION_TYPE} validation..."
echo "This may take several minutes..."
echo ""

# Execute validation with timeout
VALIDATION_LOG="${RESULTS_DIR}/validation_${VALIDATION_TYPE}_${TIMESTAMP}.log"
if timeout 1800 python "${VALIDATION_SCRIPT}" "${VALIDATION_TYPE}" "${REDIS_AVAILABLE}" > "${VALIDATION_LOG}" 2>&1; then
    VALIDATION_EXIT_CODE=0
else
    VALIDATION_EXIT_CODE=$?
fi

# Show validation output
echo ""
echo -e "${BLUE}Validation Output:${NC}"
echo "-------------------"
tail -50 "${VALIDATION_LOG}"

# Clean up temporary script
rm -f "${VALIDATION_SCRIPT}"

echo ""

# Analyze results
echo -e "${BLUE}4. Results Analysis${NC}"
echo "-------------------"

# Find latest results files
LATEST_VALIDATION=$(find "${RESULTS_DIR}" -name "validation_${VALIDATION_TYPE}_*.json" -type f | sort | tail -1)
LATEST_CERTIFICATION=$(find "${RESULTS_DIR}" -name "certification_${VALIDATION_TYPE}_*.md" -type f | sort | tail -1)

if [ -n "$LATEST_VALIDATION" ] && [ -f "$LATEST_VALIDATION" ]; then
    print_status 0 "Validation results: $LATEST_VALIDATION"
    
    # Extract key metrics
    PERFORMANCE_SCORE=$(python -c "import json; data=json.load(open('$LATEST_VALIDATION')); print(f'{data[\"performance_score\"]:.1f}')")
    OVERALL_STATUS=$(python -c "import json; data=json.load(open('$LATEST_VALIDATION')); print(data['overall_status'])")
    CERTIFICATION_LEVEL=$(python -c "import json; data=json.load(open('$LATEST_VALIDATION')); print(data['certification_level'])")
    
    echo "  Performance Score: ${PERFORMANCE_SCORE}/100"
    echo "  Overall Status: ${OVERALL_STATUS}"
    echo "  Certification Level: ${CERTIFICATION_LEVEL}"
    
else
    print_status 1 "No validation results found"
fi

if [ -n "$LATEST_CERTIFICATION" ] && [ -f "$LATEST_CERTIFICATION" ]; then
    print_status 0 "Certification report: $LATEST_CERTIFICATION"
else
    print_warning "No certification report found"
fi

echo ""

# Generate summary report
echo -e "${BLUE}5. Summary Report${NC}"
echo "-----------------"

SUMMARY_REPORT="${RESULTS_DIR}/validation_summary_${VALIDATION_TYPE}_${TIMESTAMP}.md"

cat > "${SUMMARY_REPORT}" << EOF
# Performance Validation Summary

**Service**: Pattern Mining Service  
**Validation Type**: ${VALIDATION_TYPE}  
**Timestamp**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")  
**Duration**: $(date -d "@$(($(date +%s) - $(date -d "$TIMESTAMP" +%s)))" -u +"%H:%M:%S")

## Results

EOF

if [ $VALIDATION_EXIT_CODE -eq 0 ]; then
    echo "**Status**: ✅ PASSED" >> "${SUMMARY_REPORT}"
    echo "**Performance Score**: ${PERFORMANCE_SCORE:-N/A}/100" >> "${SUMMARY_REPORT}"
    echo "**Certification Level**: ${CERTIFICATION_LEVEL:-N/A}" >> "${SUMMARY_REPORT}"
else
    echo "**Status**: ❌ FAILED" >> "${SUMMARY_REPORT}"
    echo "**Exit Code**: ${VALIDATION_EXIT_CODE}" >> "${SUMMARY_REPORT}"
fi

cat >> "${SUMMARY_REPORT}" << EOF

## Files Generated

- Validation Log: \`validation_${VALIDATION_TYPE}_${TIMESTAMP}.log\`
- Validation Results: \`$(basename "${LATEST_VALIDATION:-N/A}")\`
- Certification Report: \`$(basename "${LATEST_CERTIFICATION:-N/A}")\`
- Summary Report: \`$(basename "${SUMMARY_REPORT}")\`

## Next Steps

EOF

if [ $VALIDATION_EXIT_CODE -eq 0 ]; then
    if [ "${CERTIFICATION_LEVEL:-not_certified}" = "production" ]; then
        echo "1. ✅ Service is certified for production deployment" >> "${SUMMARY_REPORT}"
        echo "2. Set up production monitoring and alerting" >> "${SUMMARY_REPORT}"
        echo "3. Schedule regular performance validation" >> "${SUMMARY_REPORT}"
    elif [ "${CERTIFICATION_LEVEL:-not_certified}" = "staging" ]; then
        echo "1. ⚠️ Service is certified for staging deployment" >> "${SUMMARY_REPORT}"
        echo "2. Address performance recommendations" >> "${SUMMARY_REPORT}"
        echo "3. Re-run validation after optimization" >> "${SUMMARY_REPORT}"
    else
        echo "1. ⚠️ Service requires additional optimization" >> "${SUMMARY_REPORT}"
        echo "2. Review certification report for recommendations" >> "${SUMMARY_REPORT}"
        echo "3. Re-run validation after improvements" >> "${SUMMARY_REPORT}"
    fi
else
    echo "1. ❌ Review validation log for errors" >> "${SUMMARY_REPORT}"
    echo "2. Fix identified issues" >> "${SUMMARY_REPORT}"
    echo "3. Re-run validation" >> "${SUMMARY_REPORT}"
fi

print_status 0 "Summary report: ${SUMMARY_REPORT}"

echo ""

# Final status
echo -e "${BLUE}==========================================${NC}"
echo -e "${BLUE}Validation Complete${NC}"
echo -e "${BLUE}==========================================${NC}"

if [ $VALIDATION_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ VALIDATION PASSED${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Review certification report: ${LATEST_CERTIFICATION:-N/A}"
    echo "2. Check validation results: ${LATEST_VALIDATION:-N/A}"
    echo "3. Follow deployment recommendations"
else
    echo -e "${RED}❌ VALIDATION FAILED${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Review validation log: ${VALIDATION_LOG}"
    echo "2. Address identified issues"
    echo "3. Re-run validation"
fi

echo ""
echo "Command to re-run validation:"
echo "  $0 ${VALIDATION_TYPE}"

# Exit with validation result
exit $VALIDATION_EXIT_CODE