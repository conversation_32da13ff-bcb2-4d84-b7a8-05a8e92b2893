version: '3.8'

# Security-hardened Docker Compose configuration
# For production deployment of pattern-mining service

services:
  pattern-mining:
    build:
      context: .
      dockerfile: Dockerfile.secure
      target: production-alpine
      args:
        PYTHON_VERSION: 3.13
    image: pattern-mining:secure
    container_name: pattern-mining-secure
    
    # Security: Run as non-root user
    user: "1000:1000"
    
    # Security: Read-only root filesystem
    read_only: true
    
    # Security: Drop all capabilities and add only required ones
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE  # Only if binding to port < 1024
    
    # Security: Disable privilege escalation
    security_opt:
      - no-new-privileges:true
      - seccomp:unconfined  # Use custom seccomp profile in production
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health').read()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Environment variables (use secrets in production)
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - PYTHONHASHSEED=random
      - REDIS_URL=rediss://redis:6379  # TLS-enabled Redis
      - RAY_ADDRESS=ray://ray-head:10001
      # Use Docker secrets for sensitive values
      - GCP_PROJECT_ID_FILE=/run/secrets/gcp_project_id
      - BIGQUERY_DATASET_ID_FILE=/run/secrets/bigquery_dataset_id
      - JWT_SECRET_KEY_FILE=/run/secrets/jwt_secret
      - REDIS_PASSWORD_FILE=/run/secrets/redis_password
      - RAY_AUTH_TOKEN_FILE=/run/secrets/ray_auth_token
    
    # Volumes for writable directories
    volumes:
      # Application temporary files
      - type: tmpfs
        target: /app/tmp
        tmpfs:
          size: 100M
      # Application logs (consider using logging driver instead)
      - type: volume
        source: app-logs
        target: /app/logs
        read_only: false
      # Redis TLS certificates
      - type: volume
        source: redis-certs
        target: /etc/redis/certs
        read_only: true
      # Ray TLS certificates  
      - type: volume
        source: ray-certs
        target: /etc/ray/certs
        read_only: true
      # Google Cloud credentials (mounted read-only)
      - type: bind
        source: /path/to/gcp-credentials.json
        target: /app/gcp-credentials.json
        read_only: true
    
    # Network configuration
    networks:
      - internal
      - redis-net
      - ray-net
    
    # Port mapping (only expose what's necessary)
    ports:
      - "8000:8000"
    
    # Secrets (defined below)
    secrets:
      - gcp_project_id
      - bigquery_dataset_id
      - jwt_secret
      - redis_password
      - ray_auth_token
    
    # Restart policy
    restart: unless-stopped
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=pattern-mining"
    
    # Dependencies
    depends_on:
      redis:
        condition: service_healthy
      ray-head:
        condition: service_healthy

  # Redis with TLS/SSL enabled
  redis:
    image: redis:7-alpine
    container_name: pattern-mining-redis
    command: >
      redis-server
      --requirepass $${REDIS_PASSWORD}
      --tls-port 6379
      --port 0
      --tls-cert-file /tls/server.crt
      --tls-key-file /tls/server.key
      --tls-ca-cert-file /tls/ca.crt
      --tls-dh-params-file /tls/dhparam.pem
      --tls-auth-clients yes
      --tls-protocols "TLSv1.2 TLSv1.3"
      --tls-ciphers "HIGH:!aNULL:!MD5"
      --tls-ciphersuites "TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256"
      --maxmemory 2gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    
    # Security settings
    user: "999:999"
    read_only: true
    cap_drop:
      - ALL
    cap_add:
      - SETUID
      - SETGID
    security_opt:
      - no-new-privileges:true
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
    
    # Volumes
    volumes:
      - type: volume
        source: redis-data
        target: /data
      - type: volume
        source: redis-certs
        target: /tls
        read_only: true
      - type: tmpfs
        target: /tmp
    
    # Health check
    healthcheck:
      test: ["CMD", "redis-cli", "--tls", "--cert", "/tls/client.crt", "--key", "/tls/client.key", "--cacert", "/tls/ca.crt", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    
    networks:
      - redis-net
    
    restart: unless-stopped
    
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Ray head node with security
  ray-head:
    image: rayproject/ray:latest-py313
    container_name: pattern-mining-ray-head
    command: >
      ray start
      --head
      --port=6379
      --dashboard-host=0.0.0.0
      --dashboard-port=8265
      --ray-client-server-port=10001
      --redis-password=$${RAY_REDIS_PASSWORD}
      --temp-dir=/tmp/ray
      --plasma-directory=/tmp/plasma
      --raylet-socket-name=/tmp/ray/raylet
    
    environment:
      - RAY_USE_TLS=1
      - RAY_TLS_SERVER_CERT=/tls/server.crt
      - RAY_TLS_SERVER_KEY=/tls/server.key
      - RAY_TLS_CA_CERT=/tls/ca.crt
    
    # Security settings
    user: "1000:1000"
    cap_drop:
      - ALL
    security_opt:
      - no-new-privileges:true
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
    
    # Volumes
    volumes:
      - type: volume
        source: ray-certs
        target: /tls
        read_only: true
      - type: tmpfs
        target: /tmp
        tmpfs:
          size: 2G
    
    # Health check
    healthcheck:
      test: ["CMD", "ray", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    networks:
      - ray-net
    
    ports:
      - "8265:8265"  # Dashboard (protect with reverse proxy)
      - "10001:10001"  # Ray client
    
    restart: unless-stopped

# Networks with encryption
networks:
  internal:
    driver: bridge
    driver_opts:
      encrypted: "true"
  redis-net:
    driver: bridge
    driver_opts:
      encrypted: "true"
  ray-net:
    driver: bridge
    driver_opts:
      encrypted: "true"

# Volumes
volumes:
  app-logs:
    driver: local
  redis-data:
    driver: local
  redis-certs:
    driver: local
  ray-certs:
    driver: local

# Secrets (use external secrets in production)
secrets:
  gcp_project_id:
    external: true
  bigquery_dataset_id:
    external: true
  jwt_secret:
    external: true
  redis_password:
    external: true
  ray_auth_token:
    external: true