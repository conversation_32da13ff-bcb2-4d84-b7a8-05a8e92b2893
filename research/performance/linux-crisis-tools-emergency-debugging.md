# Linux Crisis Tools for Emergency Performance Debugging

## Overview

A comprehensive guide to essential Linux performance tools that should be pre-installed for emergency debugging during performance incidents.

## Why Pre-Install Crisis Tools?

During performance outages, installing tools can be problematic due to:
- Slow or overloaded package managers
- Network connectivity issues
- Firewall restrictions blocking repositories
- Permission/approval delays
- Immutable file systems
- Package dependency conflicts

**Key Principle**: "Linux distros [should] add these crisis tools to their enterprise Linux variants, so that companies large and small can hit the ground running when performance outages occur."

## Essential Crisis Tool Packages

### 1. procps - Basic System Statistics
Fundamental tools for initial system assessment:
- **ps**: Process status and listings
- **vmstat**: Virtual memory statistics
- **uptime**: System load averages
- **top**: Real-time process monitoring

### 2. util-linux - System Information
Core utilities for system inspection:
- **dmesg**: Kernel message buffer
- **lsblk**: Block device information
- **lscpu**: CPU architecture details
- **lsmem**: Memory configuration

### 3. sysstat - Device Statistics
Advanced monitoring for devices:
- **iostat**: I/O statistics for devices
- **mpstat**: Per-CPU statistics
- **pidstat**: Per-process statistics
- **sar**: System activity reporter

### 4. iproute2 - Network Tools
Modern networking diagnostics:
- **ip**: Network interface configuration
- **ss**: Socket statistics (replaces netstat)
- **tc**: Traffic control
- **bridge**: Ethernet bridge management

### 5. bpfcc-tools - eBPF Performance Tools
Comprehensive suite of ~70 tools including:
- **opensnoop**: Trace file opens
- **execsnoop**: Trace new processes
- **tcplife**: TCP connection lifespan
- **biosnoop**: Block I/O tracing
- **cachestat**: Page cache statistics
- **runqlat**: Run queue latency

### 6. bpftrace - eBPF Scripting
High-level tracing language for:
- Custom one-liners
- Quick system investigation
- Ad-hoc performance analysis
- Complex tracing scenarios

## Crisis Response Workflow

### Phase 1: Initial Assessment (First 60 seconds)
```bash
# Load averages and uptime
uptime

# Recent kernel messages
dmesg -T | tail -20

# Virtual memory overview
vmstat 1 5

# Process listing
ps aux --sort=-%cpu | head
```

### Phase 2: Resource Analysis
```bash
# CPU analysis
mpstat -P ALL 1 5
pidstat 1 5

# Memory analysis
free -m
vmstat -s

# I/O analysis
iostat -xz 1 5
```

### Phase 3: Network Investigation
```bash
# Network interfaces
ip -s link

# Socket connections
ss -tunap

# Network statistics
netstat -s
```

### Phase 4: Deep Dive with eBPF
```bash
# File activity
opensnoop

# Process execution
execsnoop

# TCP connections
tcplife

# Block I/O latency
biolatency
```

## Tool Installation Strategies

### 1. Custom Base Images
- Include crisis tools in golden images
- Automate tool deployment
- Version control tool lists

### 2. Configuration Management
```yaml
# Example Ansible playbook
- name: Install crisis tools
  package:
    name:
      - procps
      - util-linux
      - sysstat
      - iproute2
      - bpfcc-tools
      - bpftrace
    state: present
```

### 3. Container Images
```dockerfile
# Add to Dockerfile
RUN apt-get update && apt-get install -y \
    procps \
    util-linux \
    sysstat \
    iproute2 \
    bpfcc-tools \
    bpftrace
```

## Specialized Environment Considerations

### Cloud Environments
- May need cloud-specific tools
- Consider provider monitoring integration
- Account for virtualization overhead

### Containers/Kubernetes
- Install tools in debug containers
- Use kubectl debug for access
- Consider sidecar containers

### Database Servers
Additional tools to consider:
- Database-specific monitors
- Query analyzers
- Lock inspectors

### Web Servers
Additional tools to consider:
- Request tracers
- Connection analyzers
- Cache inspectors

## Best Practices

### 1. Regular Tool Updates
- Keep tools current
- Test new versions
- Monitor for security patches

### 2. Documentation
- Document tool usage
- Create runbooks
- Share knowledge

### 3. Practice Scenarios
- Regular fire drills
- Tool familiarity training
- Incident response practice

### 4. Monitoring Integration
- Feed tool output to monitoring
- Create automated alerts
- Build dashboards

## Common Crisis Scenarios

### High CPU Usage
```bash
# Identify top consumers
top -b -n 1 | head -20
mpstat -P ALL 1
pidstat 1

# Profile with eBPF
profile-bpfcc -f 30
```

### Memory Pressure
```bash
# Memory overview
free -m
vmstat 1
slabtop

# Per-process memory
pidstat -r 1
```

### I/O Bottlenecks
```bash
# Disk statistics
iostat -xz 1
iotop -b -n 1

# I/O tracing
biosnoop
biolatency
```

### Network Issues
```bash
# Connection state
ss -s
netstat -i

# Packet analysis
tcpdump -i any -c 100
```

## Minimum Viable Toolset

For extremely constrained environments:
1. **procps** (ps, top, vmstat)
2. **util-linux** (dmesg)
3. **sysstat** (iostat, mpstat)
4. **iproute2** (ss)

## Extended Toolset

For comprehensive analysis:
- **strace**: System call tracing
- **tcpdump**: Packet capture
- **ltrace**: Library call tracing
- **perf**: Linux profiler
- **ftrace**: Kernel function tracer

## Key Takeaways

1. **Pre-install tools** before crises occur
2. **Standardize toolsets** across infrastructure
3. **Practice using tools** regularly
4. **Document procedures** for team use
5. **Automate deployment** for consistency

The goal is rapid problem identification and resolution during critical incidents, which requires having the right tools immediately available.