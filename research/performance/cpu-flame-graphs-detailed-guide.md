# CPU Flame Graphs - Detailed Creation and Analysis Guide

## Overview

CPU Flame Graphs are a visualization technique for analyzing stack traces and performance profiles. They provide an intuitive way to identify performance bottlenecks by showing where CPU time is spent in your code.

## Key Concepts

### Visual Elements
- **Each box**: Represents a function in the stack trace
- **Box width**: Indicates total time spent in that function (including children)
- **Box height**: Shows stack depth (call hierarchy)
- **Colors**: Warm-toned, randomly selected for visual distinction
- **X-axis**: Sample population (alphabetically sorted, not time-based)
- **Y-axis**: Stack depth from bottom (entry point) to top (currently executing)

## Creation Process

### Step 1: Capture Stack Traces

#### Linux perf
```bash
# System-wide profiling for 30 seconds
sudo perf record -F 99 -a -g -- sleep 30

# Profile specific process
sudo perf record -F 99 -p PID -g -- sleep 30

# Profile specific command
sudo perf record -F 99 -g ./myapp
```

#### DTrace (Solaris/BSD/macOS)
```bash
# Profile for 30 seconds
dtrace -x ustackframes=100 -n 'profile-99 /pid == 12345/ { @[ustack()] = count(); }' -o out.stacks
```

#### SystemTap (Linux)
```bash
# Profile script
stap -v profile.stp -o out.stacks
```

#### eBPF (Linux)
```bash
# Using profile tool from bcc
sudo profile -df -p PID 30 > out.stacks
```

### Step 2: Process Stack Traces

#### Convert perf data
```bash
# Generate folded stacks
perf script | ./stackcollapse-perf.pl > out.folded

# For Java applications
perf script | ./stackcollapse-perf.pl | ./stackcollapse-jit.pl > out.folded
```

#### Process DTrace output
```bash
./stackcollapse-dtrace.pl out.stacks > out.folded
```

### Step 3: Generate Flame Graph

```bash
# Basic flame graph
./flamegraph.pl out.folded > flamegraph.svg

# With custom title
./flamegraph.pl --title "CPU Profile of MyApp" out.folded > flamegraph.svg

# With different color scheme
./flamegraph.pl --colors java out.folded > flamegraph.svg
```

## Interpretation Guidelines

### Reading Flame Graphs

1. **Look for the widest towers**
   - Wide boxes indicate functions consuming most CPU time
   - Focus optimization efforts here

2. **Examine flat plateaus**
   - Wide, flat tops show actual work being done
   - May indicate tight loops or computational code

3. **Check stack depth**
   - Very deep stacks might indicate inefficient recursion
   - Or complex call chains that could be simplified

4. **Identify patterns**
   - Similar shaped towers suggest repeated operations
   - May benefit from caching or optimization

### Common Patterns

#### CPU-Intensive Code
```
wide_function()
├── computation_heavy()
│   └── math_operations()
└── data_processing()
```
- Wide boxes at leaf functions
- Clear hot paths visible

#### Framework Overhead
```
application_code()
├── framework_layer1()
│   ├── framework_layer2()
│   │   └── framework_layer3()
│   │       └── actual_work()
```
- Many thin layers
- Little time in application code
- Framework dominating CPU usage

#### Inefficient Loops
```
main()
└── process_loop()
    └── repeated_operation()
        └── expensive_calculation()
```
- Single wide tower
- Repetitive pattern
- Optimization opportunity

## Platform-Specific Considerations

### Java Applications
```bash
# Enable frame pointers
java -XX:+PreserveFramePointer MyApp

# Use async-profiler for better Java support
./profiler.sh -d 30 -f flamegraph.svg PID
```

### Node.js Applications
```bash
# Use --perf-basic-prof flag
node --perf-basic-prof app.js

# Process with specific collapse script
perf script | ./stackcollapse-perf.pl | ./stackcollapse-nodejs.pl > out.folded
```

### C/C++ Applications
```bash
# Compile with frame pointers
gcc -fno-omit-frame-pointer -g myapp.c

# Profile normally
perf record -F 99 -g ./myapp
```

### Python Applications
```bash
# Use py-spy for Python
py-spy record -d 30 -f flamegraph.svg python myapp.py
```

## Optimization Strategies

### Based on Flame Graph Analysis

1. **Wide Functions**
   - Profile internally
   - Consider algorithmic improvements
   - Add caching if appropriate

2. **Deep Call Stacks**
   - Flatten call hierarchies
   - Remove unnecessary abstractions
   - Consider inlining

3. **Repeated Patterns**
   - Cache results
   - Batch operations
   - Use more efficient data structures

4. **Framework Overhead**
   - Evaluate framework necessity
   - Use lighter alternatives
   - Bypass for hot paths

## Advanced Techniques

### Differential Flame Graphs
```bash
# Compare before and after optimization
./difffolded.pl out1.folded out2.folded | ./flamegraph.pl > diff.svg
```

### Filtered Flame Graphs
```bash
# Focus on specific functions
grep "myfunction" out.folded | ./flamegraph.pl > filtered.svg

# Exclude patterns
grep -v "idle" out.folded | ./flamegraph.pl > active.svg
```

### Flame Graph Variations

#### Icicle Graphs (Inverted)
```bash
./flamegraph.pl --inverted out.folded > icicle.svg
```

#### Time-based Flame Charts
- Different from flame graphs
- X-axis represents time
- Shows execution timeline

## Best Practices

### 1. Sampling Frequency
- Default 99 Hz works for most cases
- Higher frequencies for short-lived processes
- Lower for reduced overhead

### 2. Profile Duration
- At least 30 seconds for representative data
- Longer for applications with varied workloads
- Multiple samples for consistency

### 3. Production Profiling
- Use lowest acceptable frequency
- Monitor overhead impact
- Profile during peak load

### 4. Symbol Resolution
- Ensure debug symbols available
- Use symbol servers if needed
- Preserve frame pointers

## Common Issues and Solutions

### Missing Symbols
```bash
# Check for symbols
file myapp
objdump -t myapp | head

# Install debug packages
apt-get install myapp-dbg
```

### Broken Stacks
- Enable frame pointers
- Use DWARF unwinding
- Try different profilers

### High Overhead
- Reduce sampling frequency
- Profile specific processes
- Use sampling profilers only

## Integration with CI/CD

### Automated Performance Testing
```bash
#!/bin/bash
# Profile during load test
./load_test.sh &
LOAD_PID=$!

perf record -F 99 -a -g -- sleep 60
kill $LOAD_PID

perf script | ./stackcollapse-perf.pl | ./flamegraph.pl > profile_$(date +%Y%m%d).svg
```

### Performance Regression Detection
- Generate flame graphs for each build
- Compare with baseline
- Alert on significant changes

## Key Takeaways

1. **Flame graphs make performance visible** - Complex data becomes intuitive
2. **Width matters most** - Focus on wide functions
3. **Different languages need different approaches** - Use appropriate tools
4. **Regular profiling prevents surprises** - Make it part of development
5. **Optimization is data-driven** - Let flame graphs guide efforts

CPU Flame Graphs transform performance analysis from guesswork to science, enabling targeted optimizations with measurable impact.