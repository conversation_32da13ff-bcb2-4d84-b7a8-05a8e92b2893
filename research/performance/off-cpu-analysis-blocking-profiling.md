# Off-CPU Analysis - Blocking and Wait Time Profiling

## Overview

Off-CPU Analysis is a performance methodology that examines time threads spend blocked and not running on CPU. It complements CPU profiling by revealing where threads wait, providing a complete picture of application performance.

## Key Concept

**The Complete Picture**: 
```
Thread Time = On-CPU Time + Off-CPU Time
```

While CPU profiling shows where cycles are consumed, Off-CPU analysis reveals where threads are waiting - often the dominant component of latency.

## Why Off-CPU Analysis Matters

### Traditional CPU Profiling Limitations
- Only shows active CPU consumption
- Misses I/O wait time
- Ignores lock contention
- Overlooks blocking operations

### Off-CPU Analysis Benefits
- Identifies blocking bottlenecks
- Reveals I/O latency issues
- Exposes lock contention
- Shows complete latency breakdown

## What Causes Off-CPU Time?

### 1. I/O Operations
- Disk reads/writes
- Network operations
- Device I/O
- Pipe/socket operations

### 2. Synchronization
- Mutex locks
- Semaphores
- Condition variables
- Reader/writer locks

### 3. Timers and Sleeps
- Explicit sleep() calls
- Timer waits
- Scheduled delays
- Polling intervals

### 4. Memory Operations
- Page faults
- Swapping
- Memory allocation waits
- Cache misses (indirect)

### 5. Scheduler Events
- Voluntary context switches
- Preemption
- CPU throttling
- Priority inversions

## Core Principle

> "Application stack traces don't change while off-CPU"

This makes off-CPU analysis possible - we can capture the stack trace when a thread blocks and measure how long it remains in that state.

## Analysis Techniques

### 1. Off-CPU Tracing
**Method**: Record every blocking event
```bash
# Using bcc's offcputime
offcputime -df 30 > offcpu.stacks

# Using bpftrace
bpftrace -e 'kprobe:finish_task_switch { @[kstack, ustack, comm] = sum(nsecs - arg0); }'
```

**Advantages**:
- Complete data
- Accurate timings
- No sampling bias

**Disadvantages**:
- High overhead
- Large data volumes
- May perturb system

### 2. Off-CPU Sampling
**Method**: Periodically check blocked threads
```bash
# Sample-based approach
for i in {1..100}; do
    grep -E "^State.*\(blocked\)" /proc/*/task/*/stat
    sleep 0.1
done
```

**Advantages**:
- Lower overhead
- Tunable frequency
- Production-safe

**Disadvantages**:
- May miss short blocks
- Statistical accuracy
- Sampling bias

### 3. Hybrid Approaches
Combine tracing and sampling:
- Trace specific functions
- Sample system-wide
- Adaptive sampling rates

## Off-CPU Flame Graphs

### Creating Off-CPU Flame Graphs
```bash
# Capture off-CPU data
offcputime -df -p PID 30 > out.stacks

# Generate flame graph
flamegraph.pl --color=io --countname=us < out.stacks > offcpu.svg
```

### Reading Off-CPU Flame Graphs
- **X-axis**: Alphabetical stack ordering (not time)
- **Y-axis**: Stack depth
- **Width**: Total off-CPU time
- **Colors**: Can indicate reason (I/O, locks, etc.)

### Key Patterns
1. **Wide Towers**: Major blocking points
2. **I/O Patterns**: Storage/network operations
3. **Lock Contention**: Synchronization bottlenecks
4. **Timer Patterns**: Scheduled waits

## Tools for Off-CPU Analysis

### 1. bcc Tools
```bash
# offcputime - Off-CPU time by stack
offcputime -p PID

# offwaketime - Shows waker stack
offwaketime -p PID

# cpudist - CPU scheduling latency
cpudist
```

### 2. bpftrace Scripts
```bash
# Basic off-CPU tracking
bpftrace -e 'tracepoint:sched:sched_switch {
    @offcpu[tid] = nsecs;
}
tracepoint:sched:sched_switch /@offcpu[tid]/ {
    @time = hist(nsecs - @offcpu[tid]);
    delete(@offcpu[tid]);
}'
```

### 3. perf Tools
```bash
# Record scheduler events
perf record -e sched:sched_switch -a -g sleep 10
perf script
```

### 4. SystemTap
```stap
probe scheduler.cpu_off {
    off_cpu_time[tid()] = gettimeofday_us()
}
probe scheduler.cpu_on {
    if (off_cpu_time[tid()]) {
        time = gettimeofday_us() - off_cpu_time[tid()]
        printf("%d %s %d\n", tid(), execname(), time)
    }
}
```

## Common Blocking Patterns

### 1. Disk I/O
```
app_function()
  -> read()
    -> vfs_read()
      -> ext4_file_read()
        -> wait_on_page_bit()
```

### 2. Network I/O
```
app_function()
  -> recv()
    -> tcp_recvmsg()
      -> sk_wait_data()
        -> schedule()
```

### 3. Lock Contention
```
app_function()
  -> pthread_mutex_lock()
    -> futex()
      -> futex_wait()
        -> schedule()
```

### 4. Timer/Sleep
```
app_function()
  -> nanosleep()
    -> hrtimer_nanosleep()
      -> schedule()
```

## Performance Considerations

### Overhead Sources
1. **Stack Walking**: Can be expensive
2. **Event Frequency**: High with many threads
3. **Data Volume**: Large with deep stacks
4. **String Processing**: Symbol resolution

### Overhead Mitigation
- Filter by process/thread
- Limit stack depth
- Sample instead of trace
- Use in-kernel aggregation
- Focus on specific functions

## Best Practices

### 1. Start with Sampling
```bash
# Low-overhead sampling
offcputime -f 99 -p PID 10
```

### 2. Filter Appropriately
- Focus on specific processes
- Exclude known idle threads
- Set minimum duration thresholds

### 3. Combine with CPU Profiling
```bash
# Complete picture
cpu_profile.sh & offcpu_profile.sh
combine_flamegraphs.sh cpu.svg offcpu.svg > combined.svg
```

### 4. Validate Findings
- Cross-reference with application logs
- Check system metrics
- Verify with targeted tests

## Common Pitfalls

### 1. Including Idle Time
- Filter out threads waiting for work
- Focus on active application threads

### 2. Scheduler Overhead
- May include involuntary switches
- Consider scheduler latency separately

### 3. Missing Context
- Need both on-CPU and off-CPU views
- Consider application metrics too

### 4. Over-Analysis
- Not all off-CPU time is bad
- Some waiting is necessary
- Focus on unexpected patterns

## Integration Example

### Complete Performance Analysis
```bash
#!/bin/bash
# Capture both CPU and off-CPU

# CPU profile
perf record -F 99 -a -g -p $PID -- sleep 30 &

# Off-CPU profile  
offcputime -df -p $PID 30 > offcpu.stacks &

wait

# Generate flame graphs
perf script | stackcollapse-perf.pl | flamegraph.pl > cpu.svg
flamegraph.pl --color=io < offcpu.stacks > offcpu.svg

# Create combined view
echo "CPU and Off-CPU analysis complete"
```

## Key Takeaways

1. **Off-CPU time often dominates** application latency
2. **Different causes require different solutions**:
   - I/O: Optimize access patterns, add caching
   - Locks: Reduce contention, improve concurrency
   - Timers: Evaluate necessity, adjust intervals
3. **Use appropriate tools** for overhead requirements
4. **Combine with CPU profiling** for complete picture
5. **Focus on actionable patterns** not all waiting

Quote: "With CPU profiling and off-CPU analysis, you have the full picture of where threads spend their time."