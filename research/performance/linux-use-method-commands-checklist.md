# Linux USE Method - Complete Commands Checklist

## Overview

The USE Method checklist for Linux systems, providing specific commands to check Utilization, Saturation, and Errors for each system resource.

## Physical Resources

### 1. CPU

#### Utilization
```bash
# System-wide CPU utilization
vmstat 1              # %us + %sy columns
sar -u                # Historical data
dstat -c              # Color-coded output

# Per-CPU utilization  
mpstat -P ALL 1       # All CPUs individually
htop                  # Visual per-CPU bars

# Per-process CPU usage
top                   # %CPU column
pidstat 1             # Detailed per-process
ps aux                # Snapshot view
```

#### Saturation
```bash
# System-wide saturation
vmstat 1              # 'r' column (run queue)
sar -q                # Queue length over time
uptime                # Load averages

# Per-process scheduling delays
cat /proc/PID/schedstat    # 2nd field: run queue latency
perf sched latency         # Scheduling latency analysis
runqlat                    # BPF tool for run queue latency
```

#### Errors
```bash
# CPU errors (rare)
perf list | grep error     # Find CPU error events
dmesg | grep -i cpu        # Hardware errors
mcelog                     # Machine check exceptions
```

### 2. Memory Capacity

#### Utilization
```bash
# System memory usage
free -m                    # Memory summary
vmstat 1                   # Free memory trends
sar -r                     # Historical memory data
/proc/meminfo              # Detailed breakdown

# Cache usage
slabtop -s c               # Kernel slab cache
cat /proc/meminfo          # Cached, Buffers

# Per-process memory
top/htop                   # RES (physical), VIRT (virtual)
ps aux                     # RSS, VSZ columns
pmap -x PID                # Detailed memory map
```

#### Saturation
```bash
# Swapping activity
vmstat 1                   # si, so columns (swap in/out)
sar -B                     # Paging statistics
sar -W                     # Swapping statistics

# Memory pressure indicators
vmstat 1                   # Look at 'b' (blocked) processes
dmesg | grep -i memory     # OOM killer messages
/proc/pressure/memory      # PSI (Pressure Stall Information)

# Page scanning
sar -B                     # pgscank, pgscand
```

#### Errors
```bash
# Memory failures
dmesg | grep -i memory     # Physical memory errors
edac-util                  # ECC memory errors
ras-mc-ctl --status        # RAS memory controller status

# Allocation failures (dynamic tracing)
bpftrace -e 'kretprobe:__kmalloc /retval == 0/ { @fail = count(); }'
```

### 3. Network Interfaces

#### Utilization
```bash
# Interface throughput
sar -n DEV 1               # RX/TX packets and bytes
ip -s link                 # Packet statistics
ifconfig                   # RX/TX bytes
nicstat                    # Network utilization %

# Per-connection
ss -i                      # Socket statistics
iftop                      # Real-time bandwidth by connection
nethogs                    # Bandwidth by process
```

#### Saturation
```bash
# Packet drops and overruns
ifconfig                   # "overruns", "dropped"
ip -s link                 # "dropped", "overrun"
netstat -s                 # Protocol statistics
sar -n EDEV                # Network errors and drops

# Queue saturation
tc -s qdisc                # Queueing discipline stats
/proc/net/softnet_stat    # Per-CPU packet drops
```

#### Errors
```bash
# Interface errors
ifconfig                   # "errors", "collisions"
ip -s link                 # "errors" counter
netstat -i                 # Interface error summary
sar -n EDEV                # Error rates over time

# Protocol errors
netstat -s                 # TCP retransmits, checksum errors
ss -s                      # Socket summary statistics
```

### 4. Storage Device I/O

#### Utilization
```bash
# Device utilization
iostat -xz 1               # %util column
sar -d                     # Historical disk usage
iotop                      # Per-process I/O

# Detailed device stats
cat /proc/diskstats        # Raw kernel counters
zpool iostat -v 1          # ZFS specific
```

#### Saturation
```bash
# Queue depth
iostat -xnz 1              # avgqu-sz (average queue size)
blktrace                   # Detailed I/O tracing
biolatency                 # BPF tool for I/O latency

# Wait time
iostat -xnz 1              # await (average wait time)
ioping                     # Disk latency testing
```

#### Errors
```bash
# Device errors
smartctl -a /dev/sdX       # SMART data
dmesg | grep -i error      # Kernel I/O errors
/sys/block/*/device/ioerr_cnt  # Error counters

# Filesystem errors
dmesg | grep -i "I/O error"
mount | grep errors        # Filesystems mounted with errors
```

## Software Resources

### 5. File Descriptors

#### Utilization
```bash
# System-wide
cat /proc/sys/fs/file-nr   # Used file descriptors
lsof | wc -l               # Open files count

# Per-process
ls /proc/PID/fd | wc -l    # FDs for specific process
lsof -p PID                # Detailed file list
```

#### Saturation/Errors
```bash
# Check limits
ulimit -n                  # User limit
cat /proc/sys/fs/file-max  # System limit

# Monitor failures (dynamic tracing)
opensnoop -x               # Failed open() calls
```

### 6. Network Sockets

#### Utilization
```bash
# Socket counts
ss -s                      # Summary by type
netstat -an | wc -l        # Total sockets
/proc/net/sockstat         # Socket statistics
```

#### Saturation
```bash
# Connection queues
ss -lnt                    # Listen queue sizes
netstat -s | grep -i listen # Listen drops
```

## Quick USE Method Script

```bash
#!/bin/bash
# Quick USE Method check

echo "=== CPU ==="
echo "Utilization:" && vmstat 1 5
echo "Saturation:" && uptime
echo

echo "=== Memory ==="
echo "Utilization:" && free -m
echo "Saturation:" && vmstat 1 5 | awk '{print $7" "$8}'
echo

echo "=== Network ==="
echo "Utilization:" && sar -n DEV 1 5
echo "Errors:" && netstat -i
echo

echo "=== Disk ==="
echo "Utilization:" && iostat -xz 1 5
```

## Best Practices

1. **Start with easy metrics** - Don't try to check everything
2. **Focus on saturation** - Often the quickest win
3. **Check errors last** - Usually less common
4. **Use monitoring** - Automate these checks
5. **Know your baseline** - What's normal for your system

## Tool Installation

```bash
# Ubuntu/Debian
apt-get install sysstat iostat iotop nicstat htop

# RHEL/CentOS
yum install sysstat iotop htop

# BPF tools
apt-get install bpfcc-tools
```

## Key Indicators to Watch

- **CPU**: Run queue > CPU count = saturation
- **Memory**: Any swapping = potential saturation  
- **Network**: Drops/overruns = saturation
- **Disk**: %util approaching 100% = saturation

This checklist provides a comprehensive starting point for Linux performance analysis using the USE Method.