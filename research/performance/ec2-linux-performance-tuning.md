# Performance Tuning Linux Instances on EC2

## Overview

Performance tuning for Linux instances on EC2 involves multiple layers: instance selection, EC2 features, Linux kernel tuning, and observability. This guide summarizes key approaches and specific optimizations for cloud environments.

## Key Performance Areas

### 1. Instance Selection
- Choose appropriate instance types for workload
- Consider CPU, memory, network, and storage requirements
- Understand instance family characteristics
- Balance cost vs performance needs

### 2. EC2 Features
- Leverage placement groups for network performance
- Use enhanced networking (SR-IOV)
- Consider dedicated hosts for consistent performance
- Utilize instance store for temporary high-performance storage

### 3. Linux Kernel Tuning
- Systematic approach to kernel parameters
- Workload-specific optimizations
- Regular validation and adjustment

### 4. Observability
- Continuous monitoring and analysis
- Data-driven optimization decisions
- Performance regression detection

## Important Philosophy

> "Tuning is a process, not a product"

### Key Principles:
- **Context-Specific**: What works for one workload may harm another
- **Medicine Cabinet Approach**: Use tunables cautiously
- **Measure Everything**: Validate impact of changes
- **Evolve Continuously**: Regularly reassess and adjust

## CPU Optimizations

### Process Scheduling
```bash
# Set CPU affinity for critical processes
schedtool -B PID

# Pin processes to specific CPUs
taskset -c 0-3 command

# Adjust nice values for priority
nice -n -10 critical_process
```

### CPU Governance
```bash
# Check current governor
cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor

# Set performance mode
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor
```

## Virtual Memory Tuning

### Swappiness Control
```bash
# Reduce swapping tendency (0-100, lower = less swap)
echo "vm.swappiness = 0" >> /etc/sysctl.conf

# Disable swap entirely for memory-intensive workloads
swapoff -a
```

### Huge Pages Configuration
```bash
# Enable transparent huge pages
echo always > /sys/kernel/mm/transparent_hugepage/enabled

# Pre-allocate huge pages
echo 1024 > /proc/sys/vm/nr_hugepages
```

### Memory Overcommit
```bash
# Adjust overcommit behavior
echo "vm.overcommit_memory = 1" >> /etc/sysctl.conf
echo "vm.overcommit_ratio = 80" >> /etc/sysctl.conf
```

## Network Optimizations

### Buffer Sizes
```bash
# Increase network buffers
cat >> /etc/sysctl.conf << EOF
net.core.rmem_max = 67108864
net.core.wmem_max = 67108864
net.ipv4.tcp_rmem = 4096 87380 67108864
net.ipv4.tcp_wmem = 4096 65536 67108864
EOF
```

### Connection Handling
```bash
# Increase connection backlog
echo "net.core.somaxconn = 1000" >> /etc/sysctl.conf

# Optimize TCP settings
echo "net.ipv4.tcp_slow_start_after_idle = 0" >> /etc/sysctl.conf
echo "net.ipv4.tcp_fin_timeout = 30" >> /etc/sysctl.conf
```

### Network Stack Tuning
```bash
# Enable TCP fastopen
echo "net.ipv4.tcp_fastopen = 3" >> /etc/sysctl.conf

# Increase netdev budget for packet processing
echo "net.core.netdev_budget = 600" >> /etc/sysctl.conf
```

## Storage I/O Optimizations

### I/O Scheduler
```bash
# Check current scheduler
cat /sys/block/xvda/queue/scheduler

# Set noop scheduler for SSDs
echo noop > /sys/block/xvda/queue/scheduler
```

### Queue Parameters
```bash
# Adjust queue depth
echo 256 > /sys/block/xvda/queue/nr_requests

# Modify read-ahead
echo 256 > /sys/block/xvda/queue/read_ahead_kb

# Disable rotational flag for SSDs
echo 0 > /sys/block/xvda/queue/rotational
```

### File System Tuning
```bash
# Mount options for performance
mount -o noatime,nodiratime,nobarrier /dev/xvda1 /data

# XFS specific optimizations
mount -o noatime,nodiratime,allocsize=64m /dev/xvda1 /data
```

## Hypervisor Optimizations

### Clock Source
```bash
# Check available clock sources
cat /sys/devices/system/clocksource/clocksource0/available_clocksource

# Set TSC clock source (can reduce CPU by up to 30%)
echo tsc > /sys/devices/system/clocksource/clocksource0/current_clocksource
```

### Interrupt Handling
```bash
# Spread interrupts across CPUs
echo 2 > /proc/irq/default_smp_affinity

# Set IRQ affinity for network interfaces
echo 4 > /proc/irq/24/smp_affinity
```

## Observability Best Practices

### Essential Monitoring
1. **System Metrics**
   ```bash
   # CPU, memory, I/O overview
   vmstat 1
   iostat -xz 1
   sar -n DEV 1
   ```

2. **Application Metrics**
   - Response times
   - Throughput
   - Error rates

3. **Custom Metrics**
   - Business-specific KPIs
   - Service-level indicators

### Performance Analysis Tools
```bash
# CPU profiling
perf record -a -g -F 99 sleep 30
perf report

# System-wide tracing
strace -c -p PID

# Network analysis
tcpdump -i eth0 -w capture.pcap
```

## Validation Methodology

### Before Tuning
1. Establish baseline metrics
2. Document current configuration
3. Identify specific bottlenecks
4. Set performance goals

### During Tuning
1. Change one parameter at a time
2. Measure impact immediately
3. Run sufficient duration tests
4. Monitor for side effects

### After Tuning
1. Validate improvements
2. Document changes
3. Monitor for regressions
4. Plan regular reviews

## Common Pitfalls

### 1. Copy-Paste Tuning
- Never blindly copy settings
- Understand each parameter
- Test in your environment

### 2. Over-Tuning
- Not everything needs tuning
- Focus on actual bottlenecks
- Consider maintenance burden

### 3. Ignoring Workload Changes
- Workloads evolve
- Regular reassessment needed
- Automate where possible

## EC2-Specific Considerations

### Instance Types
- **Compute Optimized**: CPU-intensive workloads
- **Memory Optimized**: Large in-memory databases
- **Storage Optimized**: High sequential I/O
- **Network Optimized**: Low latency requirements

### Enhanced Networking
```bash
# Enable SR-IOV
aws ec2 modify-instance-attribute --instance-id i-1234567890abcdef0 --sriov-net-support simple

# Verify enhanced networking
ethtool -i eth0 | grep driver
```

### Placement Groups
- **Cluster**: Low latency, high throughput
- **Spread**: High availability
- **Partition**: Large distributed workloads

## Modern Recommendations (2024)

### What's Changed
- Container awareness
- eBPF for observability
- Auto-scaling integration
- Cloud-native optimizations

### Current Best Practices
1. Use instance metadata service v2
2. Enable Nitro System features
3. Leverage AWS Graviton processors
4. Implement proper monitoring

## Key Takeaways

1. **Measure First**: Never tune without data
2. **Understand Context**: Your workload is unique
3. **Iterate Continuously**: Performance tuning never ends
4. **Document Everything**: Future you will thank you
5. **Validate Results**: Trust but verify

Remember: "Copying tunables is like taking someone else's medication" - always understand and test in your specific context.