# How Netflix Tunes EC2 - Production Performance at Scale

## Overview

Netflix's approach to EC2 performance tuning represents one of the largest-scale production optimization efforts. This guide captures their methodologies, specific tunables, and organizational approaches to performance at scale.

## Key Performance Areas

### 1. Linux Kernel Tunables
Developed for Ubuntu Xenial instances on EC2 (late 2017)

### 2. Hypervisor Optimization
Focus on reducing virtualization overhead

### 3. Organizational Approach
Performance as a company-wide initiative

### 4. Continuous Improvement
Regular review and updates

## Specific Kernel Tunables

### CPU Optimizations
```bash
# Process scheduling improvements
schedtool -B PID  # Set batch scheduling

# CPU frequency scaling
echo performance > /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# Disable CPU throttling where appropriate
echo 0 > /sys/devices/system/cpu/cpu*/cpufreq/cpuinfo_max_freq
```

### Virtual Memory Tuning
```bash
# Reduce swapping aggressively
echo "vm.swappiness = 0" >> /etc/sysctl.conf

# Page cache settings
echo "vm.dirty_ratio = 80" >> /etc/sysctl.conf
echo "vm.dirty_background_ratio = 5" >> /etc/sysctl.conf
echo "vm.dirty_expire_centisecs = 12000" >> /etc/sysctl.conf
```

### Huge Pages Configuration
```bash
# Set to madvise mode (application opt-in)
echo madvise > /sys/kernel/mm/transparent_hugepage/enabled
echo madvise > /sys/kernel/mm/transparent_hugepage/defrag

# Configure huge page availability
echo "vm.nr_hugepages = 1024" >> /etc/sysctl.conf
```

### NUMA Optimization
```bash
# Disable NUMA balancing for consistent performance
echo "kernel.numa_balancing = 0" >> /etc/sysctl.conf

# Set NUMA memory policy
numactl --interleave=all application_command
```

### File System Tuning
```bash
# Adjust writeback behavior
echo "vm.dirty_ratio = 80" >> /etc/sysctl.conf
echo "vm.dirty_background_ratio = 5" >> /etc/sysctl.conf

# Mount options for performance
mount -o noatime,nodiratime,nobarrier /dev/xvdf /data
```

### Storage I/O Configuration
```bash
# Set I/O scheduler (noop for SSDs)
echo noop > /sys/block/xvd*/queue/scheduler

# Queue depth optimization
echo 256 > /sys/block/xvd*/queue/nr_requests

# Read-ahead settings
echo 256 > /sys/block/xvd*/queue/read_ahead_kb

# Request affinity
echo 2 > /sys/block/xvd*/queue/rq_affinity
```

### Network Stack Optimization
```bash
# Core network settings
cat >> /etc/sysctl.conf << EOF
# Increase Linux autotuning TCP buffer limits
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 87380 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728

# Increase connection backlog
net.core.somaxconn = 1024
net.core.netdev_max_backlog = 5000

# TCP optimizations
net.ipv4.tcp_congestion_control = bbr
net.ipv4.tcp_slow_start_after_idle = 0
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_mtu_probing = 1
EOF
```

## Hypervisor Improvements

### AWS Nitro System
- Reduced virtualization overhead
- Hardware-based security
- Improved network and storage performance
- Better CPU performance isolation

### Benefits Observed
- Lower latency
- More consistent performance
- Higher throughput
- Better multi-tenancy

## Organizational Approach

### Performance Team Structure
1. **Dedicated Performance Team**
   - Central expertise
   - Tool development
   - Knowledge sharing

2. **Distributed Responsibility**
   - Every team owns their service performance
   - Performance engineers embedded in teams
   - Shared accountability

### Tools Development
- **Vector**: Real-time performance monitoring
- **Atlas**: Metrics and alerting platform
- **Flamescope**: Subsecond offset heat maps
- Custom eBPF tools

### Cultural Aspects
- Performance reviews in architecture discussions
- Regular performance game days
- Blameless postmortems
- Knowledge sharing sessions

## Measurement and Validation

### Key Metrics Tracked
1. **Latency Percentiles**
   - P50, P90, P99, P99.9
   - Per-service tracking
   - Regional variations

2. **Throughput**
   - Requests per second
   - Bandwidth utilization
   - Connection rates

3. **Resource Utilization**
   - CPU usage patterns
   - Memory efficiency
   - Network saturation

4. **Cost Efficiency**
   - Performance per dollar
   - Resource waste identification
   - Optimization ROI

### Validation Process
```bash
# Before changes
capture_baseline_metrics.sh

# Apply changes gradually
apply_tuning_canary.sh
monitor_canary_performance.sh

# Full rollout if successful
deploy_tuning_production.sh
validate_improvements.sh
```

## Lessons Learned

### 1. Context Matters
- Tunables are workload-specific
- Test in your environment
- Monitor for regressions

### 2. Incremental Approach
- Small, measurable changes
- Gradual rollout
- Quick rollback capability

### 3. Automation Essential
- Configuration management
- Automated testing
- Performance regression detection

### 4. Continuous Evolution
- Regular reviews
- Stay current with kernel changes
- Adapt to new instance types

## Production Best Practices

### Deployment Strategy
1. **Canary Testing**
   - 1% traffic initially
   - Monitor all metrics
   - Gradual increase

2. **A/B Testing**
   - Compare configurations
   - Statistical significance
   - Real production load

3. **Rollback Plan**
   - Quick revert capability
   - Automated triggers
   - Clear procedures

### Monitoring Requirements
```yaml
# Example monitoring configuration
performance_alerts:
  - metric: latency_p99
    threshold: 100ms
    action: page_oncall
  
  - metric: cpu_utilization
    threshold: 80%
    duration: 5m
    action: auto_scale
  
  - metric: error_rate
    threshold: 0.1%
    action: investigate
```

## Impact and Results

### Performance Improvements
- **Latency**: 20-30% reduction in P99
- **Throughput**: 15-25% increase
- **CPU Efficiency**: 10-15% improvement
- **Cost Savings**: Millions annually

### Scalability Benefits
- Better handling of traffic spikes
- Improved multi-region performance
- Reduced cascade failures
- Enhanced user experience

## Modern Considerations (2024)

### What's Changed
- Graviton instances optimization
- Container-specific tuning
- eBPF for safer tuning
- AI/ML workload optimization

### What Remains Valid
- Systematic approach
- Measurement-driven decisions
- Organizational commitment
- Continuous improvement

## Key Takeaways

1. **Performance is a Team Sport**
   - Requires organizational commitment
   - Everyone contributes
   - Shared knowledge essential

2. **Measure Everything**
   - Data drives decisions
   - Validate assumptions
   - Track long-term trends

3. **Automate for Scale**
   - Manual tuning doesn't scale
   - Consistency matters
   - Quick iteration cycles

4. **Share Knowledge**
   - Internal documentation
   - Conference presentations
   - Open source contributions

5. **Never Stop Improving**
   - Regular reviews
   - New opportunities
   - Evolving workloads

Netflix's approach demonstrates that significant performance gains are possible through systematic, data-driven optimization combined with strong organizational support and continuous improvement culture.