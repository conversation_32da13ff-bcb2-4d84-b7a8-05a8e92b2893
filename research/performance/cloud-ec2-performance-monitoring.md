# Performance Monitoring Counters (PMCs) in EC2

## Overview

Performance Monitoring Counters (PMCs) are now available on dedicated EC2 host types, marking a significant advancement in cloud performance analysis capabilities.

## Available PMCs in EC2

Seven architectural PMCs are accessible:

1. **Unhalted Core Cycles**
   - Actual CPU cycles when not halted
   - Key for understanding CPU utilization

2. **Instructions Retired**
   - Completed instructions
   - Essential for IPC calculations

3. **LLC References**
   - Last Level Cache references
   - Memory hierarchy performance

4. **Branch Instructions**
   - Total branch instructions
   - Control flow analysis

5. **Branch Misses**
   - Mispredicted branches
   - Pipeline efficiency indicator

## Key Performance Metrics

### IPC (Instructions Per Cycle)
- **Critical metric** for CPU efficiency
- **Low IPC (<1)**: Indicates memory/stall bottlenecks
- **High IPC (>1)**: Suggests instruction-bound performance
- Calculated as: Instructions Retired / Unhalted Core Cycles

## Cloud Performance Challenges

### Hypervisor Considerations
- PMCs expose sensitive performance data
- Security risks from side-channel attacks
- Careful management required for multi-tenant environments

### Virtualization Impacts
- Context switching between guests complicates measurements
- PMC state must be saved/restored
- Overhead from virtualization layer

### Implementation Approach
- <PERSON><PERSON> uses "whitelist" approach for PMC exposure
- Only safe counters are made available
- Balance between functionality and security

## Tools and Measurement Techniques

### Linux perf
```bash
# Basic PMC measurement
perf stat -a sleep 10

# Specific events
perf stat -e cycles,instructions -a sleep 10
```

### Custom Tools
- **pmcarch**: Detailed architectural PMC summaries
- Provides comprehensive performance metrics
- Designed for cloud environments

### Advanced Analysis
- Combine PMC data with flame graphs
- Correlate IPC with code paths
- Identify optimization opportunities

## Performance Analysis Workflow

1. **Measure Basic PMCs**
   - Cycles, instructions, cache references
   - Calculate IPC baseline

2. **Identify Bottlenecks**
   - Low IPC: Memory-bound
   - High branch misses: Control flow issues
   - Cache misses: Data locality problems

3. **Deep Dive Analysis**
   - Use flame graphs with PMC annotations
   - Profile specific workloads
   - Compare against baselines

4. **Optimization**
   - Target identified bottlenecks
   - Measure improvement with PMCs
   - Iterate based on results

## Key Insights

### "Memory is the New Disk"
- Modern systems are increasingly memory-bound
- CPU speeds outpace memory latency improvements
- Cache optimization crucial for performance

### Cloud-Specific Considerations
- Limited PMC availability in shared instances
- Dedicated hosts provide fuller access
- Consider PMC availability when selecting instance types

## Best Practices

### 1. Baseline Establishment
- Measure PMCs for known workloads
- Document expected IPC ranges
- Track changes over time

### 2. Holistic Analysis
- Don't rely on PMCs alone
- Combine with other metrics
- Consider application-level measurements

### 3. Security Awareness
- Understand PMC access limitations
- Respect multi-tenant boundaries
- Use appropriate instance types

### 4. Tool Selection
- Use perf for quick measurements
- Develop custom tools for specific needs
- Integrate PMC data into monitoring

## Practical Examples

### Memory-Bound Detection
```bash
# High LLC references with low IPC
perf stat -e cycles,instructions,LLC-load-misses ./workload
```

### Branch Prediction Analysis
```bash
# Branch miss rate calculation
perf stat -e branches,branch-misses ./workload
```

### Comprehensive Profile
```bash
# Full architectural counter set
perf stat -e cycles,instructions,LLC-loads,LLC-load-misses,branches,branch-misses -a sleep 60
```

## Future Directions

- Increasing PMC availability in cloud
- Better virtualization support
- Enhanced security models
- Integration with cloud-native tools

## Key Takeaways

1. PMCs provide deep CPU insights
2. IPC is fundamental performance metric
3. Cloud environments have unique challenges
4. Proper tools essential for analysis
5. Memory optimization increasingly critical