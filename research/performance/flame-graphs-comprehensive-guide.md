# Flame Graphs - Comprehensive Guide

## What are Flame Graphs?

Flame Graphs are a visualization technique for hierarchical data, especially stack traces. Created by <PERSON> to "identify quickly and accurately" the most frequent code paths in performance profiles.

### Key Characteristics

- **X-axis**: Shows stack profile population (alphabetically sorted, not time-based)
- **Y-axis**: Shows stack depth (number of frames on stack)
- **Width**: Indicates how often that code path was present in samples
- **Color**: Can be used for different dimensions (e.g., code type, module)

## Visual Elements

Each rectangle represents a stack frame:
- Width = frequency of occurrence in samples
- Height = position in call stack
- Bottom = entry point (e.g., main, thread start)
- Top = currently executing function

## Interactive Features

- **Mouse hover**: Shows frame details (function name, percentage)
- **Click to zoom**: Horizontal zoom on selected frame and its ancestors
- **Search functionality**: Highlight specific terms or patterns
- **Reset zoom**: Return to full view

## Types of Flame Graphs

### 1. CPU Flame Graphs
- Shows on-CPU time
- Identifies CPU-intensive code paths
- Most common type

### 2. Memory Flame Graphs
- Allocation profiling
- Shows memory usage by code path
- Helps identify memory leaks

### 3. Off-CPU Flame Graphs
- Shows blocked time (I/O, locks, sleeps)
- Complements CPU flame graphs
- Identifies wait states

### 4. Hot/Cold Flame Graphs
- Combines on-CPU and off-CPU
- Comprehensive view of all states
- Different colors for different states

### 5. Differential Flame Graphs
- Compares two profiles
- Shows performance regressions/improvements
- Red = more time, blue = less time

### 6. AI/GPU Flame Graphs
- For machine learning workloads
- GPU kernel execution
- Training/inference optimization

## How to Generate Flame Graphs

### Basic Steps
1. Collect stack traces with profiler
2. Process into folded format
3. Generate SVG with flamegraph.pl
4. View in web browser

### Linux Example
```bash
# Record CPU profile
perf record -F 99 -a -g -- sleep 30

# Generate folded stacks
perf script | stackcollapse-perf.pl > out.folded

# Create flame graph
flamegraph.pl out.folded > flamegraph.svg
```

### Tools and Platforms

**Open Source:**
- Brendan Gregg's FlameGraph (GitHub)
- Support for multiple profilers
- Language-agnostic

**Platform-Specific:**
- Linux: perf, BPF tools
- Windows: WPA, PerfView
- Mac: Instruments
- Java: async-profiler
- Node.js: 0x, clinic.js

## How to Read Flame Graphs

### Key Principles
1. **Look for wide towers**: Wide frames indicate hot code paths
2. **Plateaus matter**: Wide, flat tops show functions doing actual work
3. **Depth indicates complexity**: Deep stacks may indicate inefficient recursion
4. **Compare widths at same level**: Shows relative time between sibling calls

### Common Patterns

**1. Wide Base, Narrow Top**
- Efficient code structure
- Work concentrated in leaf functions

**2. Wide Plateau**
- Function consuming significant time
- Primary optimization target

**3. Many Narrow Spikes**
- Diverse workload
- No single bottleneck

**4. Deep Recursion**
- Tall, narrow towers
- May indicate algorithmic issues

## Interpretation Guidelines

### What to Look For
- **Unexpected wide frames**: Functions using more time than expected
- **Known expensive operations**: Database queries, network calls
- **Framework overhead**: Excessive time in framework code
- **Language runtime**: GC, JIT compilation

### Anti-Patterns
- Excessive string manipulation
- Inefficient loops
- Repeated allocations
- Lock contention (in off-CPU graphs)

## Variations

### Icicle Charts
- Inverted flame graphs
- Root at top, leaves at bottom
- Same information, different perspective

### Flame Charts
- Time-based X-axis
- Shows execution over time
- Good for understanding sequences

### Sunburst Layout
- Radial coordinate system
- Circular visualization
- Space-efficient for deep stacks

## Best Practices

1. **Collect representative samples**
   - Profile under realistic load
   - Sufficient duration (30+ seconds)

2. **Use appropriate sampling rate**
   - Balance overhead vs accuracy
   - Typically 99-999 Hz

3. **Filter noise**
   - Remove idle threads
   - Focus on relevant code paths

4. **Compare before/after**
   - Use differential flame graphs
   - Validate optimizations

5. **Combine with other tools**
   - Use with metrics
   - Correlate with logs

## Origin Story

Brendan Gregg invented flame graphs while "working on a MySQL performance issue" because traditional profilers produced "walls of text" that were difficult to interpret quickly. The visualization revolutionized performance analysis by making it easy to identify hot code paths at a glance.

## Key Advantages

- **Visual pattern recognition**: Quickly spot bottlenecks
- **Hierarchical context**: See full call chains
- **Interactive exploration**: Zoom and search
- **Language agnostic**: Works for any stack-based execution
- **Low learning curve**: Intuitive visualization

## Common Use Cases

1. **Performance optimization**: Find slow code
2. **Capacity planning**: Understand resource usage
3. **Regression detection**: Compare versions
4. **Architecture decisions**: Evaluate frameworks
5. **Production debugging**: Analyze live systems