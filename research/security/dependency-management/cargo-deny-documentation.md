# Cargo Deny Documentation

## Overview
Cargo-deny is a comprehensive Rust CLI tool for managing and linting dependency graphs. It goes beyond security scanning to provide complete dependency management policies.

## Purpose
- Cargo plugin for comprehensive dependency checking
- Helps manage large and complex dependency graphs
- Enforces organizational policies on dependencies
- Provides multiple types of dependency validation

## Main Check Types

### 1. Licenses
- Verify that all dependencies have acceptable license terms
- Define allowed/denied licenses
- Handle exceptions for specific crates
- Ensure license compliance for commercial projects

### 2. Bans
- Deny specific crates from being used
- Allow-list approved crates
- Detect and prevent multiple versions of the same crate
- Enforce dependency version policies

### 3. Advisories
- Check against security advisory databases
- Similar to cargo-audit functionality
- Integrates with RustSec database
- Can be configured to fail on different severity levels

### 4. Sources
- Ensure crates come from trusted sources
- Verify crates.io as the source
- Prevent dependencies from untrusted registries
- Control allowed Git sources

## Installation and Usage

### Installation
```bash
cargo install --locked cargo-deny
```

### Quick Start
```bash
# Initialize configuration
cargo deny init

# Run all checks
cargo deny check

# Run specific checks
cargo deny check licenses
cargo deny check bans
cargo deny check advisories
cargo deny check sources
```

## Configuration
Cargo-deny uses a `deny.toml` configuration file:

```toml
[licenses]
# List of allowed licenses
allow = ["MIT", "Apache-2.0"]
# List of denied licenses
deny = ["GPL-3.0"]

[bans]
# Deny multiple versions of the same crate
multiple-versions = "warn"
# Specific crates to deny
deny = [
    { name = "openssl" },
]

[advisories]
# Database sources
db-path = "~/.cargo/advisory-db"
db-urls = ["https://github.com/rustsec/advisory-db"]
# Vulnerability severity to deny
vulnerability = "deny"
unmaintained = "warn"

[sources]
# Allowed sources
allow-registry = ["https://github.com/rust-lang/crates.io-index"]
allow-git = []
```

## Key Differences from Cargo-Audit
- **Broader Scope**: Not just security, but licenses, sources, and dependency policies
- **Policy Enforcement**: Define organizational rules for dependencies
- **Multiple Checks**: Comprehensive dependency validation beyond vulnerabilities
- **Configuration**: Highly configurable with deny.toml

## Integration
- Pre-commit hooks support
- CI/CD pipeline integration
- GitHub Actions support
- Compatible with existing Rust toolchain

## Best Practices
1. Run `cargo deny init` to create initial configuration
2. Customize policies based on project requirements
3. Integrate into CI/CD pipelines
4. Regular updates of advisory database
5. Review and update policies periodically

## Resources
- Documentation: https://docs.rs/cargo-deny/
- GitHub: https://github.com/EmbarkStudios/cargo-deny
- Created by: Embark Studios
- License: Apache-2.0 / MIT dual license