# Dependabot Configuration for Cargo/Rust Projects

## Overview
Dependabot provides automated dependency updates for Rust projects, helping maintain security by automatically creating pull requests when vulnerabilities are discovered or new versions are available.

## Basic Configuration

### 1. Enable Dependabot
Create `.github/dependabot.yml` in your repository:

```yaml
version: 2
updates:
  # Enable version updates for Cargo
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "daily"
    # Optional: Time zone for schedule
    timezone: "America/New_York"
    # Optional: Time of day to run
    time: "04:00"
```

### 2. Multiple Cargo Projects
For workspaces or multiple Cargo.toml files:

```yaml
version: 2
updates:
  # Root workspace
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "daily"
  
  # Service subdirectory
  - package-ecosystem: "cargo"
    directory: "/services/api"
    schedule:
      interval: "weekly"
  
  # Another service
  - package-ecosystem: "cargo"
    directory: "/services/worker"
    schedule:
      interval: "weekly"
```

## Advanced Configuration

### 1. Security Updates Only
```yaml
version: 2
updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 0  # Only security updates
```

### 2. Grouped Updates
```yaml
version: 2
updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "weekly"
    groups:
      # Group all patch updates
      patch-updates:
        patterns:
          - "*"
        update-types:
          - "patch"
      
      # Group tokio ecosystem
      tokio-ecosystem:
        patterns:
          - "tokio*"
          - "hyper"
          - "tonic"
```

### 3. Version Constraints
```yaml
version: 2
updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "daily"
    ignore:
      # Ignore specific versions
      - dependency-name: "tokio"
        versions: ["1.0.0", "1.0.1"]
      
      # Ignore major versions
      - dependency-name: "serde"
        update-types: ["version-update:semver-major"]
    
    allow:
      # Only allow direct dependencies
      - dependency-type: "direct"
      
      # Only allow production dependencies
      - dependency-type: "production"
```

### 4. Custom Labels and Reviewers
```yaml
version: 2
updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "daily"
    # Add custom labels to PRs
    labels:
      - "dependencies"
      - "rust"
      - "automated"
    
    # Assign reviewers
    reviewers:
      - "security-team"
      - "rust-maintainers"
    
    # Assign to team
    assignees:
      - "username"
    
    # Set milestone
    milestone: 4
```

## Security-Focused Configuration

### 1. Priority Security Updates
```yaml
version: 2
updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "daily"
    # Prioritize security updates
    open-pull-requests-limit: 10
    
    # Security-specific labels
    labels:
      - "security"
      - "dependencies"
    
    # Separate security PRs
    reviewers:
      - "security-team"
```

### 2. Breaking Change Management
```yaml
version: 2
updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "weekly"
    
    # Handle breaking changes carefully
    versioning-strategy: "increase-if-necessary"
    
    # Separate major updates
    groups:
      minor-and-patch:
        patterns: ["*"]
        update-types:
          - "minor"
          - "patch"
    
    # Major updates individually
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
```

## Integration with CI/CD

### 1. Automated Testing
```yaml
# .github/workflows/dependabot-auto-merge.yml
name: Dependabot Auto-Merge
on: pull_request

permissions:
  contents: write
  pull-requests: write

jobs:
  dependabot:
    runs-on: ubuntu-latest
    if: ${{ github.actor == 'dependabot[bot]' }}
    steps:
      - name: Dependabot metadata
        id: metadata
        uses: dependabot/fetch-metadata@v1
        with:
          github-token: "${{ secrets.GITHUB_TOKEN }}"
      
      - name: Auto-merge patch updates
        if: ${{ steps.metadata.outputs.update-type == 'version-update:semver-patch' }}
        run: gh pr merge --auto --merge "$PR_URL"
        env:
          PR_URL: ${{ github.event.pull_request.html_url }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
```

### 2. Security Validation
```yaml
# .github/workflows/security-check.yml
name: Security Check
on:
  pull_request:
    paths:
      - 'Cargo.lock'
      - 'Cargo.toml'

jobs:
  security-audit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Run cargo audit
        uses: actions-rs/audit-check@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Run cargo deny
        uses: EmbarkStudios/cargo-deny-action@v1
```

## Best Practices

### 1. Review Strategy
```yaml
version: 2
updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    
    # Different strategies for different updates
    groups:
      # Auto-merge safe updates
      safe-updates:
        patterns:
          - "serde*"
          - "tokio*"
        update-types:
          - "patch"
      
      # Manual review for others
      review-required:
        patterns:
          - "sqlx"
          - "diesel"
```

### 2. Commit Message Configuration
```yaml
version: 2
updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "daily"
    commit-message:
      prefix: "chore"
      prefix-development: "chore"
      include: "scope"
```

### 3. PR Limits and Scheduling
```yaml
version: 2
updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "wednesday"
    
    # Limit concurrent PRs
    open-pull-requests-limit: 5
    
    # Rebase strategy
    rebase-strategy: "auto"
```

## Handling Common Scenarios

### 1. Workspace Dependencies
```yaml
version: 2
updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "daily"
    # Update workspace dependencies
    vendor: true
```

### 2. Private Registries
```yaml
version: 2
registries:
  my-registry:
    type: cargo-registry
    url: https://my-registry.example.com
    token: ${{ secrets.CARGO_REGISTRY_TOKEN }}

updates:
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "daily"
    registries:
      - my-registry
```

### 3. Monorepo Configuration
```yaml
version: 2
updates:
  # Shared dependencies
  - package-ecosystem: "cargo"
    directory: "/"
    schedule:
      interval: "daily"
    groups:
      shared-deps:
        patterns:
          - "serde*"
          - "tokio*"
  
  # Service-specific
  - package-ecosystem: "cargo"
    directory: "/services/api"
    schedule:
      interval: "weekly"
    labels:
      - "service:api"
  
  - package-ecosystem: "cargo"
    directory: "/services/worker"
    schedule:
      interval: "weekly"
    labels:
      - "service:worker"
```

## Troubleshooting

### Common Issues and Solutions

1. **Dependabot ignoring updates**
   - Check `ignore` configuration
   - Verify directory paths
   - Ensure proper permissions

2. **Failed PR creation**
   - Check GitHub Actions permissions
   - Verify registry access
   - Review error logs in PR

3. **Merge conflicts**
   - Use `rebase-strategy: "auto"`
   - Consider grouping related updates
   - Manual intervention may be needed

## Security Considerations

1. **Token Security**
   - Use repository secrets
   - Limit token permissions
   - Rotate tokens regularly

2. **Review Process**
   - Always review security updates
   - Test thoroughly before merging
   - Monitor for breaking changes

3. **Automation Limits**
   - Don't auto-merge major versions
   - Require reviews for critical deps
   - Set up comprehensive CI tests