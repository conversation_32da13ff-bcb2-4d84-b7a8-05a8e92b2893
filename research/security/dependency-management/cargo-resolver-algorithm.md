# Cargo Dependency Resolver Algorithm

## Overview
Cargo's dependency resolver is responsible for determining the exact versions of all dependencies in a project. Understanding how it works is crucial for managing security updates and resolving version conflicts.

## Key Concepts

### Resolution Goals
1. Find a "generally applicable resolution" that satisfies all requirements
2. Prefer highest compatible versions
3. Minimize duplicate dependencies
4. Ensure build reproducibility via Cargo.lock

### Resolution Process
1. **Start with root package** requirements
2. **Recursively resolve** each dependency
3. **Unify versions** where possible
4. **Handle conflicts** through backtracking
5. **Generate Cargo.lock** with exact versions

## Algorithm Details

### Version Selection Strategy
```
1. For each dependency:
   - Find all available versions
   - Filter by version requirements
   - Exclude yanked versions (unless in lock)
   - Select highest compatible version
   
2. If conflicts arise:
   - Backtrack to previous decision
   - Try next best version
   - Repeat until solution found or exhausted
```

### SemVer Compatibility Rules
- **Major version 1+**: Patches and minors are compatible
- **Major version 0**: Only patches are compatible
- **Pre-release versions**: Treated as incompatible

Example compatibility:
```toml
# These can unify to 1.3.5:
crate-a = "1.2"   # >= 1.2.0, < 2.0.0
crate-a = "1.3"   # >= 1.3.0, < 2.0.0
crate-a = "^1.1"  # >= 1.1.0, < 2.0.0

# These cannot unify:
crate-b = "1.0"   # >= 1.0.0, < 2.0.0
crate-b = "2.0"   # >= 2.0.0, < 3.0.0
```

## Resolver Versions

### Resolver Version 1 (Legacy)
```toml
# Default for older projects
# Features unified across all uses
```

### Resolver Version 2 (Recommended)
```toml
# In Cargo.toml
[package]
resolver = "2"

# Or for workspace
[workspace]
resolver = "2"
```

Key differences:
- **Feature unification**: More granular per-target
- **Dev-dependencies**: Don't affect normal builds
- **Host dependencies**: Separate from target dependencies

## Handling Conflicts

### Common Conflict Scenarios

#### 1. Version Conflicts
```
Error: failed to select a version for `shared-dep`
  ... required by `crate-a v1.0.0`
  ... which satisfies dependency `shared-dep = "^1.0"`
  ... required by `crate-b v2.0.0`
  ... which satisfies dependency `shared-dep = "^2.0"`
```

Resolution strategies:
- Update one dependency to use compatible version
- Use `[patch]` to override
- Fork and modify dependency requirements

#### 2. Links Conflicts
```toml
# Only one crate can link to a native library
[package]
links = "sqlite3"
```

Resolution:
- Ensure only one version of linking crate
- Use feature flags to disable duplicate linking

#### 3. Cyclic Dependencies
- Normal dependencies cannot cycle
- Dev-dependencies can create cycles
- Build scripts handle cycles specially

## Debugging Resolution Issues

### Useful Commands

```bash
# Show full dependency tree
cargo tree

# Show duplicate dependencies
cargo tree -d

# Show inverse dependencies (who depends on X)
cargo tree -i package-name

# Show with features
cargo tree -f "{p} {f}"

# Show only specific dependency path
cargo tree -p package-name

# Explain why a version was chosen
cargo tree --explain package-name
```

### Resolution Strategies

#### 1. For Security Updates
```bash
# Force update to latest compatible
cargo update -p vulnerable-crate

# If blocked by other dependencies
cargo tree -i vulnerable-crate  # Find blockers
# Then update blocking dependencies first
```

#### 2. For Breaking Changes
```toml
# Temporarily allow multiple versions
[dependencies]
old-consumer = "1.0"  # Uses old-api v1
new-consumer = "2.0"  # Uses old-api v2
# Cargo allows both old-api versions
```

#### 3. For Minimum Versions
```bash
# Test with minimal versions
cargo +nightly update -Z minimal-versions
cargo test
```

## Advanced Features

### 1. Public/Private Dependencies
```toml
# Future feature for better encapsulation
[dependencies]
internal = { version = "1.0", public = false }
```

### 2. Rust Version Awareness
```toml
[package]
rust-version = "1.70"

# Resolver considers Rust version compatibility
```

### 3. Feature Resolver
Features are unified based on:
- Target platform
- Dependency type (normal/dev/build)
- Resolver version

```toml
# With resolver = "2"
[dependencies]
# Features for normal dependencies
serde = { version = "1.0", features = ["derive"] }

[dev-dependencies]
# Different features for tests don't affect main build
serde = { version = "1.0", features = ["unstable"] }
```

## Best Practices

### 1. Lock File Management
```bash
# Commit Cargo.lock for applications
git add Cargo.lock

# For libraries, generally don't commit
# But consider for security-critical libraries
```

### 2. Version Specification
```toml
# Be as flexible as reasonable
library = "1.0"     # Good for libraries
library = "~1.0.5"  # OK for applications
library = "=1.0.5"  # Only when necessary
```

### 3. Conflict Resolution
1. **Understand the conflict** using `cargo tree`
2. **Find minimal change** to resolve
3. **Document why** if using overrides
4. **Test thoroughly** after resolution

### 4. Security Considerations
- Regular `cargo update` for patch updates
- Monitor security advisories
- Use `cargo audit` regularly
- Pin versions only when necessary

## Troubleshooting Recipes

### Recipe: Update Transitive Dependency
```bash
# Find what's holding back the update
cargo tree -i old-vulnerable-crate

# Update the direct dependency
cargo update -p direct-dependency

# Verify the vulnerable crate updated
cargo tree -p old-vulnerable-crate
```

### Recipe: Handle Multiple Versions
```bash
# See why multiple versions exist
cargo tree -d

# Find common ancestor
cargo tree -p crate-v1 -i
cargo tree -p crate-v2 -i

# Update common ancestors to unify
```

### Recipe: Debug Feature Issues
```bash
# See all features in use
cargo tree -e features

# Check specific package features
cargo tree -p package -f "{f}"

# Test with different features
cargo check --no-default-features
cargo check --all-features
```