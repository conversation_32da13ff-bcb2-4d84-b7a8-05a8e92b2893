# Cargo Dependency Specification Guide

## Overview
Understanding how to specify dependencies in Cargo is crucial for managing security updates, breaking changes, and maintaining stable builds. This guide covers all dependency specification methods and best practices.

## Dependency Types

### 1. Crates.io Dependencies
The most common dependency source, using the official Rust package registry.

```toml
[dependencies]
# Simple version specification
time = "0.1.12"

# With specific features
serde = { version = "1.0", features = ["derive"] }

# Optional dependency
rand = { version = "0.8", optional = true }
```

### 2. Git Dependencies
For unreleased versions or patches not yet on crates.io.

```toml
[dependencies]
# Default branch (usually main/master)
regex = { git = "https://github.com/rust-lang/regex.git" }

# Specific branch
regex = { git = "https://github.com/rust-lang/regex.git", branch = "next" }

# Specific tag
regex = { git = "https://github.com/rust-lang/regex.git", tag = "1.7.0" }

# Specific commit
regex = { git = "https://github.com/rust-lang/regex.git", rev = "7f3e5a6" }
```

### 3. Path Dependencies
For local development and workspace management.

```toml
[dependencies]
# Relative path
my_utils = { path = "../my_utils" }

# Absolute path (not recommended)
my_lib = { path = "/home/<USER>/projects/my_lib" }

# With version (for eventual publishing)
local_crate = { path = "./local_crate", version = "0.1.0" }
```

## Version Requirements

### Default (Caret) Requirements
Allows SemVer-compatible updates:
```toml
# Equivalent specifications:
"1.2.3"  # Same as "^1.2.3"
"^1.2.3" # >= 1.2.3, < 2.0.0
"^1.2"   # >= 1.2.0, < 2.0.0
"^1"     # >= 1.0.0, < 2.0.0
"^0.2.3" # >= 0.2.3, < 0.3.0 (0.x.y treated specially)
"^0.0.3" # >= 0.0.3, < 0.0.4 (0.0.x treated specially)
```

### Tilde Requirements
More restrictive updates:
```toml
"~1.2.3" # >= 1.2.3, < 1.3.0
"~1.2"   # >= 1.2.0, < 1.3.0
"~1"     # >= 1.0.0, < 2.0.0
```

### Wildcard Requirements
Pattern matching:
```toml
"*"      # Any version
"1.*"    # >= 1.0.0, < 2.0.0
"1.2.*"  # >= 1.2.0, < 1.3.0
```

### Comparison Requirements
Explicit version ranges:
```toml
">= 1.2.0"                  # At least 1.2.0
"> 1.2.3, < 1.5"           # Greater than 1.2.3, less than 1.5
">=1.2, <1.5"              # At least 1.2.0, less than 1.5
"=1.2.3"                   # Exactly 1.2.3
">1.2.0, <=1.5.0, !=1.4.0" # Complex requirements
```

## Security-Focused Strategies

### 1. Pinning for Security
When exact versions are required for security:
```toml
[dependencies]
# Pin to exact secure version
vulnerable-crate = "=2.1.4"

# Or use lock file enforcement
# cargo build --locked
```

### 2. Minimum Secure Versions
Ensure at least a patched version:
```toml
[dependencies]
# Vulnerability fixed in 1.2.3
some-crate = ">=1.2.3"
```

### 3. Avoiding Vulnerable Ranges
Exclude known vulnerable versions:
```toml
[dependencies]
# Skip vulnerable 1.4.0
my-dep = ">=1.2.0, <1.4.0, >1.4.0"
# Or more concisely
my-dep = ">=1.2.0, !=1.4.0"
```

## Managing Breaking Changes

### 1. Conservative Updates
Use tilde for critical dependencies:
```toml
[dependencies]
# Only patch updates
critical-lib = "~1.2.3"
```

### 2. Feature Flags for Migration
Support multiple versions during transition:
```toml
[dependencies]
old-api = { version = "1.0", optional = true }
new-api = { version = "2.0", optional = true }

[features]
default = ["new-api"]
legacy = ["old-api"]
```

### 3. Workspace Version Management
Centralize version management in workspaces:
```toml
# workspace Cargo.toml
[workspace.dependencies]
serde = "1.0.150"
tokio = { version = "1.35", features = ["full"] }

# member Cargo.toml
[dependencies]
serde.workspace = true
tokio.workspace = true
```

## Platform-Specific Dependencies

```toml
[target.'cfg(windows)'.dependencies]
winapi = "0.3"

[target.'cfg(unix)'.dependencies]
libc = "0.2"

[target.'cfg(target_arch = "wasm32")'.dependencies]
wasm-bindgen = "0.2"
```

## Development Dependencies

```toml
[dev-dependencies]
# Only for tests, examples, benchmarks
criterion = "0.5"
mockall = "0.11"

[build-dependencies]
# Only for build scripts
cc = "1.0"
```

## Best Practices

### 1. Version Selection Strategy
- **Libraries**: Use flexible version requirements to avoid conflicts
- **Applications**: Can be more restrictive, rely on Cargo.lock
- **Security fixes**: Use minimum version requirements

### 2. Dependency Hygiene
```toml
[dependencies]
# Good: Specify features explicitly
serde = { version = "1.0", features = ["derive"], default-features = false }

# Avoid: Overly broad versions
# some-crate = "*"
```

### 3. Documentation
```toml
[dependencies]
# Document why specific versions or sources
# SECURITY: Using fork with CVE-2023-12345 fix
idna = { git = "https://github.com/servo/rust-url", branch = "security-fix" }

# TEMP: Using git until 2.0 is released with fix
other-crate = { git = "https://github.com/owner/repo", rev = "abc123" }
```

### 4. Regular Updates
```bash
# Check for outdated dependencies
cargo outdated

# Update conservatively
cargo update

# Update specific dependency
cargo update -p vulnerable-crate
```

## Troubleshooting

### Version Conflicts
```bash
# Understand version resolution
cargo tree -d  # Show duplicate dependencies
cargo tree -p crate-name  # Show specific crate's dependencies
```

### Feature Resolution
```bash
# See which features are enabled
cargo tree -f "{p} {f}"
```

### Lock File Management
```bash
# Ensure reproducible builds
cargo build --locked

# Update lock file after Cargo.toml changes
cargo update
```