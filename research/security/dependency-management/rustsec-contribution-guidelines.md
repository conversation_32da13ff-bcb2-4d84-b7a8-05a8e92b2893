# RustSec Advisory Database Contribution Guidelines

## Overview
This guide outlines the process for reporting security vulnerabilities to the RustSec advisory database, enabling the Rust community to stay informed about security issues.

## Reporting Process

### 1. Prepare the Advisory
- Fork the [RustSec advisory-db repository](https://github.com/rustsec/advisory-db)
- Navigate to the appropriate crate subdirectory under `crates/`
- Create a new file named `RUSTSEC-0000-0000.md` (ID will be assigned upon merge)

### 2. Advisory File Structure
Use the TOML template with Markdown description:

```toml
[advisory]
id = "RUSTSEC-0000-0000"
package = "crate-name"
date = "2024-01-01"
url = "https://example.com/vulnerability-details"
categories = ["code-execution", "memory-corruption"]
keywords = ["rce", "buffer-overflow"]

[versions]
patched = [">= 1.2.3"]
unaffected = ["< 1.0.0"]

[affected]
os = ["linux"]
functions = { "crate-name" = ["vulnerable_function"] }
```

### 3. Write Detailed Description
After the TOML frontmatter, provide a comprehensive Markdown description:
- Clear explanation of the vulnerability
- Impact assessment
- Proof of concept (if applicable)
- Remediation steps
- Credit to discoverers

### 4. Submit Pull Request
- Create PR with descriptive title
- Link to relevant issues/discussions
- Provide any additional context

## Qualifying Vulnerabilities

### Accepted Categories

#### Critical Security Issues
- **Remote Code Execution (RCE)**: Arbitrary code execution
- **Memory Corruption**: Buffer overflows, use-after-free
- **Privilege Escalation**: Unauthorized access elevation

#### Data Security
- **File Disclosure**: Unauthorized file access
- **Information Leakage**: Sensitive data exposure
- **SQL Injection**: Database manipulation vulnerabilities

#### Web Security
- **Cross-Site Scripting (XSS)**: Script injection
- **Cross-Site Request Forgery (CSRF)**: Unauthorized actions
- **Authentication Bypass**: Security mechanism failures

#### Cryptographic Failures
- **Weak Algorithms**: Insecure cryptographic implementations
- **Key Management**: Private key exposure or weak generation
- **Protocol Vulnerabilities**: TLS/SSL implementation issues

#### Rust-Specific Issues
- **Soundness Bugs**: Safe code causing undefined behavior
- **Memory Safety Violations**: Violations of Rust's safety guarantees
- **Type Safety Issues**: Type system bypass vulnerabilities

## Best Practices

### 1. Responsible Disclosure
- Notify maintainers before public disclosure
- Allow reasonable time for patches (typically 90 days)
- Coordinate disclosure timing

### 2. Advisory Quality
- Provide clear, actionable information
- Include version ranges accurately
- Link to patches or fixes
- Credit all contributors

### 3. Required Information
```toml
[advisory]
# Mandatory fields
id = "RUSTSEC-0000-0000"
package = "exact-crate-name"
date = "YYYY-MM-DD"
url = "https://link-to-details"
categories = ["category"]

[versions]
patched = [">= X.Y.Z"]  # When was it fixed?
```

### 4. Optional but Recommended
```toml
# CVE assignment
[advisory.metadata]
cvss = "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H"

# Affected components
[affected]
arch = ["x86_64"]
os = ["windows", "linux"]
functions = { "crate-name" = ["function1", "function2"] }
```

## Special Cases

### 1. Embargoed Vulnerabilities
Currently not handled by the public database. Contact <EMAIL> for coordination.

### 2. Official Rust Projects
For rust-lang organization projects, follow the [Rust Security Policy](https://www.rust-lang.org/security.html).

### 3. Disputed Vulnerabilities
When severity or impact is debated:
- Open an issue for discussion
- Provide supporting evidence
- Seek consensus before advisory

### 4. Mass Vulnerabilities
For issues affecting many crates:
- Coordinate with RustSec maintainers
- Consider grouped disclosure
- Provide tooling/scripts for detection

## Post-Submission

### 1. Immediate Actions
- **Yank affected versions** on crates.io
- **Release patched version**
- **Update documentation**

### 2. CVE Assignment
```bash
# Request CVE through GitHub Security Advisories
# Or use MITRE's CVE form
# Link CVE to RustSec advisory once assigned
```

### 3. Communication
- Announce via project channels
- Update README with security notice
- Consider rust-security-announcements list

## Tools and Resources

### Validation Tools
```bash
# Validate advisory format
cargo install rustsec-admin
rustsec-admin lint advisory.toml
```

### Templates and Examples
- [Example Advisory Template](https://github.com/RustSec/advisory-db/blob/main/.github/example_advisory.toml)
- [Published Advisories](https://github.com/RustSec/advisory-db/tree/main/crates)

### Getting Help
- Open an issue for questions
- Join #rustsec on IRC
- Email <EMAIL>

## Key Principles

1. **"When in doubt, open a PR"** - Better to report than not
2. **Anyone can file** - Not restricted to maintainers
3. **Transparency** - Public database for community benefit
4. **Actionable** - Provide clear remediation steps

## Common Mistakes to Avoid

1. **Incomplete version ranges** - Test thoroughly
2. **Missing patch information** - Always specify fixed versions
3. **Vague descriptions** - Be specific about impact
4. **No proof of concept** - Provide reproducible examples
5. **Delayed reporting** - Report promptly after patch