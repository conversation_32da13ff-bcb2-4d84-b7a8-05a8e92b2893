# Cargo Supply Chain Security

## Overview
`cargo-supply-chain` is a security tool for analyzing the trust relationships in your Rust dependency graph. It helps identify who you're trusting when you use third-party crates.

## Purpose
- **Trust Analysis**: Identify all publishers and contributors in your dependency tree
- **Risk Assessment**: Evaluate supply chain risks before they become vulnerabilities  
- **Support Discovery**: Find developers and projects worth supporting
- **Security Auditing**: Part of comprehensive dependency security strategy

## Installation
```bash
cargo install cargo-supply-chain
```

## Core Features

### 1. List Publishers
Show all crates.io publishers in your dependency graph:
```bash
cargo supply-chain publishers
```

Output example:
```
anyhow:
    dtolnay

tokio:
    tokio-rs
    carllerche
    
serde:
    dtolnay
```

### 2. List Crates
Display crates with their publishers:
```bash
cargo supply-chain crates
```

Output example:
```
anyhow 1.0.70 - dtolnay
tokio 1.28.0 - tokio-rs, carllerche
serde 1.0.160 - dtolnay
```

### 3. JSON Export
Get detailed information in JSON format:
```bash
cargo supply-chain json > supply-chain-audit.json
```

JSON structure:
```json
{
  "publishers": {
    "dtolnay": {
      "crates": ["anyhow", "serde", "syn"],
      "github": "https://github.com/dtolnay",
      "email": "..."
    }
  },
  "crates": {
    "anyhow": {
      "version": "1.0.70",
      "publishers": ["dtolnay"],
      "repository": "https://github.com/dtolnay/anyhow"
    }
  }
}
```

### 4. Update Data
Download latest crates.io publisher data:
```bash
cargo supply-chain update
```

## Advanced Usage

### Target-Specific Analysis
```bash
# Analyze specific target
cargo supply-chain publishers --target wasm32-unknown-unknown

# Analyze with specific features
cargo supply-chain publishers --features "tls,json"

# Exclude dev dependencies
cargo supply-chain publishers --no-dev
```

### Workspace Analysis
```bash
# Analyze entire workspace
cargo supply-chain publishers --workspace

# Analyze specific package
cargo supply-chain publishers -p my-service
```

## Security Workflows

### 1. Initial Audit
```bash
#!/bin/bash
# supply-chain-audit.sh

echo "=== Supply Chain Security Audit ==="

# Update to latest data
cargo supply-chain update

# Generate reports
cargo supply-chain publishers > publishers.txt
cargo supply-chain crates > crates.txt
cargo supply-chain json > supply-chain.json

# Count unique publishers
echo "Total unique publishers: $(cargo supply-chain publishers | sort -u | wc -l)"

# Identify single-maintainer crates (higher risk)
echo "Single maintainer crates:"
cargo supply-chain json | jq '.crates | to_entries[] | select(.value.publishers | length == 1) | .key'
```

### 2. Continuous Monitoring
```yaml
# .github/workflows/supply-chain-check.yml
name: Supply Chain Security Check

on:
  schedule:
    - cron: '0 0 * * 1'  # Weekly
  pull_request:
    paths:
      - 'Cargo.lock'

jobs:
  supply-chain-audit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Install cargo-supply-chain
        run: cargo install cargo-supply-chain
      
      - name: Update supply chain data
        run: cargo supply-chain update
      
      - name: Generate supply chain report
        run: |
          cargo supply-chain json > supply-chain.json
          cargo supply-chain publishers > publishers.txt
      
      - name: Check for new publishers
        run: |
          # Compare with previous audit
          if [ -f .supply-chain/publishers.txt ]; then
            diff .supply-chain/publishers.txt publishers.txt || true
          fi
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: supply-chain-audit
          path: |
            supply-chain.json
            publishers.txt
```

### 3. Risk Assessment Script
```rust
// analyze-supply-chain.rs
use std::collections::{HashMap, HashSet};
use serde::{Deserialize, Serialize};

#[derive(Deserialize)]
struct SupplyChainData {
    publishers: HashMap<String, Publisher>,
    crates: HashMap<String, Crate>,
}

#[derive(Deserialize)]
struct Publisher {
    crates: Vec<String>,
}

#[derive(Deserialize)]
struct Crate {
    publishers: Vec<String>,
}

fn analyze_risk(data: SupplyChainData) {
    // High-risk: Single maintainer critical crates
    let single_maintainer: Vec<_> = data.crates
        .iter()
        .filter(|(_, c)| c.publishers.len() == 1)
        .collect();
    
    println!("Single maintainer crates: {}", single_maintainer.len());
    
    // Publisher concentration
    let mut publisher_impact: HashMap<&str, usize> = HashMap::new();
    for (_, crate_data) in &data.crates {
        for publisher in &crate_data.publishers {
            *publisher_impact.entry(publisher).or_insert(0) += 1;
        }
    }
    
    // Find high-impact publishers
    let high_impact: Vec<_> = publisher_impact
        .iter()
        .filter(|(_, count)| **count > 5)
        .collect();
    
    println!("High-impact publishers (>5 crates):");
    for (publisher, count) in high_impact {
        println!("  {}: {} crates", publisher, count);
    }
}
```

## Best Practices

### 1. Regular Audits
```bash
# Create audit schedule
cat > .supply-chain/audit-schedule.md << EOF
# Supply Chain Audit Schedule

- Weekly: Automated scan for new publishers
- Monthly: Manual review of high-risk dependencies
- Quarterly: Full supply chain assessment
- On-demand: Before major releases
EOF
```

### 2. Publisher Verification
```bash
# Verify publisher identities
verify_publisher() {
    local publisher=$1
    
    # Check GitHub profile
    echo "Checking GitHub for $publisher..."
    curl -s "https://api.github.com/users/$publisher" | jq '.name, .company, .created_at'
    
    # Check crates.io profile
    echo "Checking crates.io..."
    curl -s "https://crates.io/api/v1/users/$publisher" | jq '.user'
}

# Usage
verify_publisher "dtolnay"
```

### 3. Risk Mitigation Strategies

#### Low Risk Publishers
- Well-known in Rust community
- Multiple maintained crates
- Active GitHub presence
- Responsive to issues

#### Medium Risk Publishers
- Newer to ecosystem
- Few published crates
- Limited activity

#### High Risk Publishers
- Anonymous or unclear identity
- Single crate published
- No GitHub/contact info
- Inactive maintenance

### 4. Integration with Other Tools
```toml
# deny.toml - Combine with cargo-deny
[bans]
# Ban crates from unknown publishers
deny = [
    { name = "suspicious-crate" },
]

# Warn about single-maintainer crates
warn = [
    { name = "*", whos = ["unknown-publisher"] },
]
```

## Security Policies

### 1. Publisher Allowlist
```toml
# .supply-chain/trusted-publishers.toml
[trusted]
publishers = [
    "rust-lang",
    "tokio-rs",
    "dtolnay",
    "serde-rs",
    # Add your trusted publishers
]

[requires-review]
# Publishers that need extra scrutiny
publishers = [
    "new-contributor",
]
```

### 2. Automated Checks
```rust
// Check against allowlist
fn check_publishers(allowed: &[&str], actual: Vec<String>) -> Result<(), Vec<String>> {
    let allowed_set: HashSet<_> = allowed.iter().collect();
    let unknown: Vec<_> = actual
        .into_iter()
        .filter(|p| !allowed_set.contains(&p.as_str()))
        .collect();
    
    if unknown.is_empty() {
        Ok(())
    } else {
        Err(unknown)
    }
}
```

### 3. Emergency Response
```bash
# If a publisher is compromised
handle_compromised_publisher() {
    local publisher=$1
    
    # List affected crates
    echo "Crates from $publisher:"
    cargo supply-chain json | jq -r ".publishers.\"$publisher\".crates[]"
    
    # Generate patch entries
    echo "Add to Cargo.toml [patch] section:"
    cargo supply-chain json | jq -r ".publishers.\"$publisher\".crates[]" | while read crate; do
        echo "$crate = { git = \"https://github.com/backup/$crate\" }"
    done
}
```

## Complementary Tools

Use `cargo-supply-chain` alongside:
- **cargo-audit**: Vulnerability scanning
- **cargo-deny**: Policy enforcement  
- **cargo-crev**: Code review database
- **cargo-outdated**: Version currency

## Summary
Supply chain security requires continuous vigilance. Regular audits with `cargo-supply-chain` help maintain awareness of trust relationships and potential risks in your dependency graph.