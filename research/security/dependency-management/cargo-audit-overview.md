# Cargo Audit Overview

## Purpose
Cargo-audit is a tool for auditing Cargo.lock files against the RustSec Advisory Database to check for known security vulnerabilities in dependencies.

## Key Information
- Part of the RustSec project
- Available on crates.io
- Documentation available at docs.rs/cargo-audit
- Integrated with GitHub Actions for CI/CD

## Installation
```bash
cargo install cargo-audit
```

## Basic Usage
```bash
# Run audit on current project
cargo audit

# Run audit with specific Cargo.lock file
cargo audit -f /path/to/Cargo.lock

# Generate JSON output
cargo audit --json
```

## Integration with CI/CD
Can be integrated into GitHub Actions workflows for continuous security monitoring.

## Resources
- Crates.io: https://crates.io/crates/cargo-audit
- Documentation: https://docs.rs/cargo-audit
- GitHub Repository: https://github.com/RustSec/cargo-audit