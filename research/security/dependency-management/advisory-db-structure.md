# RustSec Advisory Database Structure

## Overview
The RustSec Advisory Database is the authoritative repository of security advisories for Rust crates published on crates.io. It provides structured vulnerability data that can be consumed by various security tools.

## Advisory Format
Advisories use TOML "front matter" with a Markdown description:

```toml
[advisory]
# Unique identifier
id = "RUSTSEC-YYYY-NNNN"

# Package information
package = "crate-name"
date = "YYYY-MM-DD"

# Vulnerability metadata
title = "Brief description of vulnerability"
description = """
Detailed description in Markdown format
"""

# Affected versions
patched_versions = [">= 1.2.3"]
unaffected_versions = ["< 1.0.0"]

# Additional metadata
categories = ["denial-of-service", "memory-corruption"]
keywords = ["segfault", "use-after-free"]

# External references
[advisory.references]
url = "https://github.com/owner/repo/issues/123"

# CVE information (if applicable)
[advisory.cve]
id = "CVE-YYYY-NNNN"
```

## Directory Structure
```
advisory-db/
├── crates/           # Advisories organized by crate name
│   ├── crate-name/
│   │   ├── RUSTSEC-YYYY-NNNN.md
│   │   └── ...
├── EXAMPLE_ADVISORY.md   # Template for new advisories
├── CONTRIBUTING.md       # Contribution guidelines
└── README.md
```

## Key Features

### 1. Standardized Format
- Consistent TOML structure for machine parsing
- Human-readable Markdown descriptions
- Structured version constraints

### 2. Integration Support
The database is consumable by:
- **cargo-audit**: CLI security scanner
- **cargo-deny**: Dependency policy enforcement
- **Trivy**: Container security scanner
- **Dependabot**: Automated dependency updates
- **OSV.dev**: Open Source Vulnerability database

### 3. Export Formats
- Native TOML/Markdown format
- OSV (Open Source Vulnerability) JSON format
- Machine-readable API access

## Programmatic Access

### Direct Git Access
```bash
git clone https://github.com/RustSec/advisory-db
```

### OSV API
```bash
# Query by package
curl https://api.osv.dev/v1/query -X POST \
  -H "Content-Type: application/json" \
  -d '{"package": {"name": "crate-name", "ecosystem": "crates.io"}}'
```

### Tool Integration
```rust
// Using rustsec crate
use rustsec::Database;

let db = Database::fetch()?;
let advisories = db.query("package-name")?;
```

## Contributing Advisories

### Process
1. Fork the repository
2. Create advisory file using template
3. Submit pull request
4. Review by maintainers

### Required Information
- Unique RUSTSEC ID
- Affected package name
- Disclosure date
- Patched versions
- Clear vulnerability description
- Proof of concept (if applicable)

### Best Practices
- Coordinate disclosure with maintainers
- Include CVE ID if available
- Provide clear remediation steps
- Link to patches or fixes

## Licensing
- Most content: Public domain (CC0-1.0)
- Imported advisories: May use CC-BY-4.0
- Tool integrations: Check individual licenses

## Resources
- GitHub Repository: https://github.com/RustSec/advisory-db
- Contribution Guide: CONTRIBUTING.md
- Example Advisory: EXAMPLE_ADVISORY.md
- OSV Database: https://osv.dev