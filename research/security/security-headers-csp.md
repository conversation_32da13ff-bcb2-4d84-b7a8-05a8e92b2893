# Security Headers & Content Security Policy (CSP)

## Overview

HTTP Security Headers are a fundamental defense mechanism that instructs browsers how to behave when handling your site's content. They provide an additional layer of security by enabling browser-level protections against common attacks.

## Essential Security Headers

### 1. Strict-Transport-Security (HSTS)

Forces browsers to use HTTPS connections and prevents downgrade attacks.

```nginx
# Nginx configuration
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
```

```apache
# Apache configuration
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
```

**Parameters:**
- `max-age`: Time in seconds browsers should remember to use HTTPS
- `includeSubDomains`: Apply to all subdomains
- `preload`: Allow inclusion in browser HSTS preload lists

### 2. Content-Security-Policy (CSP)

Controls which resources the browser is allowed to load for a page.

```http
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://trusted-cdn.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' https://api.example.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self';
```

### 3. X-Frame-Options

Prevents clickjacking attacks by controlling whether your site can be embedded in frames.

```http
X-Frame-Options: DENY
# or
X-Frame-Options: SAMEORIGIN
```

### 4. X-Content-Type-Options

Prevents MIME type sniffing.

```http
X-Content-Type-Options: nosniff
```

### 5. Referrer-Policy

Controls how much referrer information is sent with requests.

```http
Referrer-Policy: strict-origin-when-cross-origin
```

### 6. Permissions-Policy

Controls which browser features can be used.

```http
Permissions-Policy: camera=(), microphone=(), geolocation=(self "https://trusted-site.com"), payment=()
```

## Content Security Policy (CSP) Deep Dive

### CSP Directives

#### Fetch Directives
Control locations from which resources can be loaded:

```http
Content-Security-Policy: 
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.example.com;
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
    img-src 'self' data: https:;
    font-src 'self' https://fonts.gstatic.com;
    connect-src 'self' https://api.example.com wss://socket.example.com;
    media-src 'self';
    object-src 'none';
    frame-src 'self' https://embed.example.com;
    worker-src 'self';
    manifest-src 'self';
```

#### Document Directives
Control properties of the document:

```http
Content-Security-Policy:
    base-uri 'self';
    form-action 'self' https://form-handler.example.com;
    frame-ancestors 'none';
    navigate-to 'self' https://trusted.example.com;
```

#### Other Directives

```http
Content-Security-Policy:
    upgrade-insecure-requests;
    block-all-mixed-content;
    require-sri-for script style;
    require-trusted-types-for 'script';
```

### CSP Implementation Strategies

#### 1. Report-Only Mode
Test CSP without breaking functionality:

```http
Content-Security-Policy-Report-Only: default-src 'self'; report-uri /csp-report
```

#### 2. Nonce-Based CSP
Use random nonces for inline scripts:

```python
# Python/Flask example
import secrets

@app.before_request
def generate_nonce():
    g.nonce = secrets.token_urlsafe(16)

@app.after_request
def set_csp(response):
    csp = f"script-src 'self' 'nonce-{g.nonce}';"
    response.headers['Content-Security-Policy'] = csp
    return response

# In template
# <script nonce="{{ g.nonce }}">
#     // Inline script
# </script>
```

#### 3. Hash-Based CSP
Allow specific inline scripts by hash:

```javascript
// Calculate hash of inline script
const crypto = require('crypto');
const scriptContent = 'console.log("Hello");';
const hash = crypto.createHash('sha256').update(scriptContent).digest('base64');

// CSP header
`script-src 'self' 'sha256-${hash}';`
```

### CSP Violation Reporting

```python
# Python/Flask CSP report endpoint
@app.route('/csp-report', methods=['POST'])
def csp_report():
    report = request.get_json(force=True)
    
    # Log the violation
    logger.warning('CSP Violation', extra={
        'document_uri': report.get('csp-report', {}).get('document-uri'),
        'violated_directive': report.get('csp-report', {}).get('violated-directive'),
        'blocked_uri': report.get('csp-report', {}).get('blocked-uri'),
        'source_file': report.get('csp-report', {}).get('source-file'),
        'line_number': report.get('csp-report', {}).get('line-number')
    })
    
    return '', 204
```

## Implementation Examples

### Express.js (Node.js)
```javascript
const helmet = require('helmet');
const express = require('express');
const app = express();

// Basic security headers
app.use(helmet());

// Custom CSP
app.use(helmet.contentSecurityPolicy({
    directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.example.com"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'", "https://api.example.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: [],
    },
    reportOnly: false
}));

// HSTS
app.use(helmet.hsts({
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
}));

// Additional headers
app.use((req, res, next) => {
    res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    next();
});
```

### Django (Python)
```python
# settings.py

# Security headers middleware
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    # ... other middleware
]

# HSTS
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Other security settings
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# CSP using django-csp
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = ("'self'", "'unsafe-inline'", "https://cdn.example.com")
CSP_STYLE_SRC = ("'self'", "'unsafe-inline'", "https://fonts.googleapis.com")
CSP_IMG_SRC = ("'self'", "data:", "https:")
CSP_FONT_SRC = ("'self'", "https://fonts.gstatic.com")
CSP_CONNECT_SRC = ("'self'", "https://api.example.com")
CSP_FRAME_ANCESTORS = ("'none'",)
CSP_BASE_URI = ("'self'",)
CSP_FORM_ACTION = ("'self'",)
```

### Nginx Configuration
```nginx
# Security headers
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
add_header X-Frame-Options "DENY" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;

# Content Security Policy
set $csp "default-src 'self'; ";
set $csp "${csp}script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.example.com; ";
set $csp "${csp}style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; ";
set $csp "${csp}img-src 'self' data: https:; ";
set $csp "${csp}font-src 'self' https://fonts.gstatic.com; ";
set $csp "${csp}connect-src 'self' https://api.example.com wss://socket.example.com; ";
set $csp "${csp}frame-ancestors 'none'; ";
set $csp "${csp}base-uri 'self'; ";
set $csp "${csp}form-action 'self'; ";
set $csp "${csp}upgrade-insecure-requests;";

add_header Content-Security-Policy $csp always;
```

## Common Pitfalls and Solutions

### 1. Breaking Inline Scripts/Styles
**Problem**: CSP blocks inline scripts and styles by default.

**Solutions**:
- Move inline code to external files
- Use nonces or hashes
- Use `'unsafe-inline'` (not recommended)

### 2. Third-Party Resources
**Problem**: Third-party resources blocked by CSP.

**Solution**: Explicitly whitelist trusted domains:
```http
script-src 'self' https://www.google-analytics.com https://www.googletagmanager.com;
```

### 3. Data URIs
**Problem**: Images using data: URIs blocked.

**Solution**: Add data: to img-src:
```http
img-src 'self' data: https:;
```

### 4. WebSocket Connections
**Problem**: WebSocket connections blocked.

**Solution**: Add wss: to connect-src:
```http
connect-src 'self' wss://socket.example.com https://api.example.com;
```

## Testing and Validation

### 1. Browser Developer Tools
- Check Network tab for blocked resources
- Console for CSP violations
- Security tab for header inspection

### 2. Online Tools
- securityheaders.com
- csp-evaluator.withgoogle.com
- report-uri.com

### 3. Automated Testing
```python
# Python test example
import requests

def test_security_headers():
    response = requests.get('https://example.com')
    headers = response.headers
    
    # Check HSTS
    assert 'Strict-Transport-Security' in headers
    assert 'max-age=31536000' in headers['Strict-Transport-Security']
    
    # Check CSP
    assert 'Content-Security-Policy' in headers
    csp = headers['Content-Security-Policy']
    assert "default-src 'self'" in csp
    assert "script-src" in csp
    
    # Check other headers
    assert headers.get('X-Frame-Options') == 'DENY'
    assert headers.get('X-Content-Type-Options') == 'nosniff'
    assert 'Referrer-Policy' in headers
```

## Progressive Enhancement Strategy

### Phase 1: Basic Protection
```http
Content-Security-Policy: default-src 'self'; frame-ancestors 'none';
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
```

### Phase 2: Report-Only Testing
```http
Content-Security-Policy-Report-Only: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; report-uri /csp-report;
```

### Phase 3: Enforcement with Unsafe
```http
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';
```

### Phase 4: Strict CSP
```http
Content-Security-Policy: default-src 'none'; script-src 'self' 'nonce-{random}'; style-src 'self' 'nonce-{random}'; img-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self';
```

## Monitoring and Maintenance

### CSP Report Analysis
```python
# Analyze CSP reports
def analyze_csp_reports(reports):
    violations = {}
    
    for report in reports:
        directive = report.get('violated-directive', 'unknown')
        blocked_uri = report.get('blocked-uri', 'unknown')
        
        key = f"{directive}:{blocked_uri}"
        violations[key] = violations.get(key, 0) + 1
    
    # Sort by frequency
    sorted_violations = sorted(
        violations.items(), 
        key=lambda x: x[1], 
        reverse=True
    )
    
    return sorted_violations
```

### Regular Reviews
1. **Monthly**: Review CSP reports for new violations
2. **Quarterly**: Audit third-party resources
3. **Annually**: Review and update security header policies

## Key Takeaways

1. **Start Simple**: Begin with basic headers and progressively enhance
2. **Test Thoroughly**: Use report-only mode before enforcement
3. **Monitor Continuously**: Set up reporting and review regularly
4. **Document Changes**: Keep track of CSP modifications
5. **Consider Performance**: Balance security with functionality
6. **Stay Updated**: Security header best practices evolve
7. **Automate Testing**: Include header validation in CI/CD
8. **Plan for Third Parties**: Account for external resources early