# A05:2021 - Security Misconfiguration

## Overview

Security Misconfiguration moved up from #6 to #5 in OWASP Top 10:2021. With 90% of applications tested showing some form of misconfiguration, this remains one of the most prevalent security issues. The category now includes XML External Entities (XXE) vulnerabilities.

### Key Statistics
- **Testing Coverage**: 90% of applications tested
- **Average Incidence Rate**: 4.51%
- **Total Occurrences**: Over 208,000
- **CWE Mappings**: Includes CWE-16 (Configuration) and CWE-611 (XXE)

## Common Misconfigurations

### 1. Default Configurations
- Default passwords unchanged
- Sample applications left installed
- Default error pages exposed
- Administrative interfaces exposed

### 2. Unnecessary Features
- Unused services running
- Unnecessary ports open
- Unneeded privileges granted
- Debug features in production

### 3. Missing Security Headers
- No Content Security Policy
- Missing X-Frame-Options
- No X-Content-Type-Options
- Missing Strict-Transport-Security

### 4. Verbose Error Messages
- Stack traces exposed
- Database errors shown
- System paths revealed
- Version information disclosed

### 5. Insecure Cloud Settings
- Public S3 buckets
- Open database ports
- Weak network segmentation
- Missing encryption

### 6. Outdated Software
- Unpatched operating systems
- Old framework versions
- Vulnerable dependencies
- Legacy protocols enabled

## Technology-Specific Misconfigurations

### Web Servers

#### Apache
```apache
# INSECURE - Directory listing enabled
Options Indexes FollowSymLinks

# SECURE - Disable directory listing
Options -Indexes +FollowSymLinks

# INSECURE - Server version exposed
ServerTokens Full
ServerSignature On

# SECURE - Hide server information
ServerTokens Prod
ServerSignature Off
```

#### Nginx
```nginx
# INSECURE - Server version exposed
server_tokens on;

# SECURE - Hide server version
server_tokens off;

# Add security headers
add_header X-Frame-Options "DENY" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
```

### Application Frameworks

#### Spring Boot
```yaml
# INSECURE - Actuator endpoints exposed
management:
  endpoints:
    web:
      exposure:
        include: "*"

# SECURE - Limit actuator exposure
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: never
```

#### Django
```python
# INSECURE settings.py
DEBUG = True
ALLOWED_HOSTS = ['*']
SECRET_KEY = 'hard-coded-secret-key'

# SECURE settings.py
DEBUG = False
ALLOWED_HOSTS = ['yourdomain.com']
SECRET_KEY = os.environ.get('SECRET_KEY')

# Security middleware
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
```

### Databases

#### MySQL
```sql
-- INSECURE - Root with no password
CREATE USER 'root'@'%' IDENTIFIED BY '';

-- SECURE - Strong passwords and limited access
CREATE USER 'appuser'@'10.0.0.%' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON myapp.* TO 'appuser'@'10.0.0.%';
```

#### MongoDB
```javascript
// INSECURE - No authentication
mongod --bind_ip 0.0.0.0

// SECURE - Enable authentication
mongod --auth --bind_ip 127.0.0.1
```

### Cloud Services

#### AWS S3
```json
// INSECURE - Public bucket policy
{
  "Version": "2012-10-17",
  "Statement": [{
    "Effect": "Allow",
    "Principal": "*",
    "Action": "s3:GetObject",
    "Resource": "arn:aws:s3:::mybucket/*"
  }]
}

// SECURE - Restricted access
{
  "Version": "2012-10-17",
  "Statement": [{
    "Effect": "Allow",
    "Principal": {
      "AWS": "arn:aws:iam::123456789012:user/MyUser"
    },
    "Action": "s3:GetObject",
    "Resource": "arn:aws:s3:::mybucket/*"
  }]
}
```

## Security Headers Implementation

### Essential Security Headers
```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline';
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Strict-Transport-Security: max-age=31536000; includeSubDomains
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
Permissions-Policy: geolocation=(), microphone=(), camera=()
```

### Implementation Examples

#### Express.js (Node.js)
```javascript
const helmet = require('helmet');

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

## Hardening Process

### 1. Operating System Hardening
```bash
# Disable unnecessary services
systemctl disable telnet
systemctl disable ftp

# Configure firewall
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow http
ufw allow https
ufw enable

# Remove unnecessary packages
apt-get remove --purge telnet
apt-get autoremove
```

### 2. Application Hardening
```yaml
# Configuration management
environments:
  development:
    debug: true
    verbose_errors: true
  production:
    debug: false
    verbose_errors: false
    secure_cookies: true
    force_https: true
```

### 3. Network Hardening
```
- Implement network segmentation
- Use private subnets for databases
- Configure security groups/firewalls
- Enable DDoS protection
- Use VPN for administrative access
```

## Configuration Management

### Infrastructure as Code
```terraform
# Terraform example - Secure EC2 instance
resource "aws_instance" "web" {
  ami           = "ami-0c55b159cbfafe1f0"
  instance_type = "t2.micro"
  
  vpc_security_group_ids = [aws_security_group.web.id]
  
  user_data = <<-EOF
    #!/bin/bash
    # Hardening script
    apt-get update
    apt-get upgrade -y
    ufw enable
    # Additional hardening steps
  EOF
}

resource "aws_security_group" "web" {
  name = "web-security-group"
  
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
```

### Configuration Validation
```bash
#!/bin/bash
# Security configuration audit script

echo "Checking security configurations..."

# Check for default passwords
if grep -q "password.*=.*admin" /etc/app/config; then
  echo "WARNING: Default password detected!"
fi

# Check SSL/TLS configuration
if ! grep -q "SSLProtocol.*-SSLv3" /etc/apache2/sites-enabled/*; then
  echo "WARNING: SSLv3 might be enabled!"
fi

# Check for unnecessary services
for service in telnet ftp; do
  if systemctl is-active --quiet $service; then
    echo "WARNING: $service is running!"
  fi
done
```

## Attack Scenarios

### Scenario 1: Sample Application Exploitation
```
1. Application server includes sample applications
2. Sample app has known SQL injection vulnerability
3. Attacker finds sample app via directory scanning
4. Exploits vulnerability to access database
```

### Scenario 2: Directory Listing Information Disclosure
```
1. Web server has directory listing enabled
2. Backup files visible: backup.sql, config.bak
3. Attacker downloads sensitive files
4. Gains database credentials and schema
```

### Scenario 3: Cloud Storage Misconfiguration
```
1. S3 bucket configured with public read access
2. Contains customer data and backups
3. Discovered via automated scanning
4. Data breach and compliance violations
```

## Prevention Checklist

### Initial Setup
- [ ] Remove all default accounts/passwords
- [ ] Disable unnecessary services and features
- [ ] Configure proper access controls
- [ ] Enable security features by default
- [ ] Implement network segmentation

### Ongoing Maintenance
- [ ] Regular security updates and patches
- [ ] Configuration reviews and audits
- [ ] Automated security scanning
- [ ] Monitor for configuration drift
- [ ] Document all configuration changes

### Security Headers
- [ ] Content-Security-Policy
- [ ] X-Frame-Options
- [ ] X-Content-Type-Options
- [ ] Strict-Transport-Security
- [ ] Referrer-Policy
- [ ] Permissions-Policy

### Error Handling
- [ ] Custom error pages
- [ ] No stack traces in production
- [ ] Generic error messages
- [ ] Log detailed errors server-side
- [ ] Monitor error rates

## Automation Tools

### Configuration Scanning
- **OWASP ZAP**: Web application scanner
- **Nessus**: Vulnerability scanner
- **OpenVAS**: Open source vulnerability scanner
- **Qualys**: Cloud-based scanning

### Configuration Management
- **Ansible**: Automation and configuration
- **Puppet**: Configuration management
- **Chef**: Infrastructure automation
- **Terraform**: Infrastructure as code

### Compliance Checking
- **InSpec**: Compliance as code
- **AWS Config**: AWS configuration monitoring
- **Azure Policy**: Azure compliance management
- **Google Cloud Security Command Center**: GCP security monitoring

## Key Takeaways

1. **Automate Everything**: Manual configuration leads to errors
2. **Principle of Least Functionality**: Remove what you don't need
3. **Defense in Depth**: Layer security controls
4. **Regular Audits**: Configuration drift is inevitable
5. **Environment Parity**: Dev/staging/production should match
6. **Secure by Default**: Start secure, stay secure
7. **Monitor Continuously**: Detect misconfigurations quickly