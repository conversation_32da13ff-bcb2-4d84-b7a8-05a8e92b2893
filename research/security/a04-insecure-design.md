# A04:2021 - Insecure Design

## Overview

Insecure Design is a new category in OWASP Top 10:2021, focusing on risks related to design and architectural flaws. This represents a critical shift in security thinking from "fixing bugs" to "building secure by design."

### Key Distinction
- **Insecure Design**: Missing or ineffective control design (can't be fixed by perfect implementation)
- **Insecure Implementation**: A defect in implementation of an otherwise secure design

## Core Concepts

### Design Flaws vs Implementation Bugs

| Design Flaw | Implementation Bug |
|-------------|-------------------|
| No security control exists | Security control implemented incorrectly |
| Cannot be patched | Can be patched |
| Requires redesign | Requires code fix |
| Architectural issue | Coding issue |

### Examples
- **Design Flaw**: No rate limiting designed for login attempts
- **Implementation Bug**: Rate limiting exists but can be bypassed due to coding error

## Common Design Weaknesses

### 1. Missing Security Controls
- No authentication for sensitive operations
- Lack of authorization checks
- Missing rate limiting
- No audit logging

### 2. Trust Boundary Violations
- Trusting client-side validation
- Insufficient server-side checks
- Improper data flow between trust zones

### 3. Business Logic Flaws
- Race conditions in critical operations
- Insufficient workflow validation
- Missing state management
- Inadequate transaction controls

### 4. Insufficient Threat Modeling
- Not identifying attack vectors
- Missing abuse cases
- Incomplete risk assessment
- Ignoring threat actors

## Secure Design Principles

### 1. Defense in Depth
```
Layer multiple security controls:
- Network segmentation
- Application firewalls
- Input validation
- Output encoding
- Access controls
- Monitoring
```

### 2. Principle of Least Privilege
```
- Minimal access by default
- Role-based access control
- Time-limited privileges
- Regular access reviews
```

### 3. Fail Securely
```
- Default deny
- Secure error handling
- Graceful degradation
- Safe defaults
```

### 4. Separation of Duties
```
- Split critical functions
- Require multiple approvals
- Segregate environments
- Separate roles and responsibilities
```

### 5. Zero Trust Architecture
```
- Never trust, always verify
- Assume breach
- Verify explicitly
- Least privilege access
```

## Threat Modeling

### STRIDE Framework
- **S**poofing: Can an attacker pretend to be someone else?
- **T**ampering: Can data be maliciously modified?
- **R**epudiation: Can actions be denied?
- **I**nformation Disclosure: Can information be exposed?
- **D**enial of Service: Can the service be made unavailable?
- **E**levation of Privilege: Can privileges be escalated?

### Threat Modeling Process
1. **Identify Assets**: What needs protection?
2. **Create Architecture Overview**: How does the system work?
3. **Decompose Application**: Break down into components
4. **Identify Threats**: What can go wrong?
5. **Document Threats**: Record and prioritize
6. **Rate Threats**: Assess likelihood and impact
7. **Identify Countermeasures**: How to mitigate?

## Secure Development Lifecycle

### 1. Requirements Phase
```
- Define security requirements
- Identify compliance needs
- Document abuse cases
- Set security objectives
```

### 2. Design Phase
```
- Threat modeling
- Security architecture review
- Define security controls
- Document design decisions
```

### 3. Implementation Phase
```
- Secure coding practices
- Code reviews
- Static analysis
- Unit testing
```

### 4. Testing Phase
```
- Security testing
- Penetration testing
- Vulnerability scanning
- Abuse case testing
```

### 5. Deployment Phase
```
- Security configuration
- Hardening
- Monitoring setup
- Incident response plan
```

## Example Attack Scenarios

### Scenario 1: Credential Recovery Flaw
```
Design Flaw:
- Using "security questions" for password recovery
- Questions have guessable answers
- No additional verification

Attack:
- Research target on social media
- Answer security questions
- Take over account

Secure Design:
- Multi-factor authentication
- Out-of-band verification
- Time-limited recovery tokens
```

### Scenario 2: Business Logic Bypass
```
Design Flaw:
- E-commerce checkout process
- No inventory reservation during checkout
- Race condition possible

Attack:
- Multiple users buy same limited item
- Oversell inventory
- Customer dissatisfaction

Secure Design:
- Atomic inventory operations
- Reservation system
- Transaction queuing
```

### Scenario 3: Insufficient Rate Limiting
```
Design Flaw:
- Movie theater booking system
- No limit on reservations
- No bot protection

Attack:
- Bot reserves all seats
- Demands ransom for release
- Legitimate users blocked

Secure Design:
- Rate limiting per user
- CAPTCHA for bulk operations
- Reservation limits
- Bot detection
```

## Secure Design Patterns

### 1. Authentication & Session Management
```
- Centralized authentication
- Strong session management
- Secure password storage
- Multi-factor authentication
- Account lockout mechanisms
```

### 2. Authorization
```
- Role-based access control (RBAC)
- Attribute-based access control (ABAC)
- Policy decision points
- Consistent enforcement
- Regular permission audits
```

### 3. Data Protection
```
- Encryption at rest and in transit
- Data classification
- Retention policies
- Secure deletion
- Privacy by design
```

### 4. API Security
```
- API gateway pattern
- Rate limiting
- Authentication/authorization
- Input validation
- Output filtering
```

### 5. Microservices Security
```
- Service mesh
- Zero trust networking
- Service-to-service authentication
- Distributed tracing
- Circuit breakers
```

## Implementation Guidelines

### 1. Security Requirements
```yaml
User Story: As a user, I want to reset my password
Security Requirements:
- Require email verification
- Generate cryptographically secure token
- Expire token after 1 hour
- Invalidate token after use
- Log password reset attempts
- Notify user of password change
```

### 2. Abuse Cases
```yaml
Abuse Case: Attacker attempts account takeover
Steps:
1. Request password reset for victim
2. Intercept or guess reset token
3. Change password
Mitigations:
- Out-of-band token delivery
- Strong token generation
- Short expiration time
- Account lockout after failures
```

### 3. Security Testing
```yaml
Test Case: Verify rate limiting
Steps:
1. Attempt 10 login failures
2. Verify account locked
3. Wait lockout period
4. Verify can login again
Expected: Temporary lockout after threshold
```

## Tools and Resources

### Design Tools
- Microsoft Threat Modeling Tool
- OWASP Threat Dragon
- IriusRisk
- ThreatModeler

### Frameworks
- OWASP SAMM (Software Assurance Maturity Model)
- BSIMM (Building Security In Maturity Model)
- Microsoft SDL
- NIST Cybersecurity Framework

### References
- OWASP Security Design Principles
- NIST SP 800-160
- ISO 27034
- SANS Secure Design Principles

## Key Takeaways

1. **Shift Left**: Security must be considered from the beginning
2. **Design != Implementation**: Perfect code can't fix bad design
3. **Threat Model**: Understand what you're protecting against
4. **Defense in Depth**: Layer security controls
5. **Continuous Process**: Design security evolves with threats
6. **Business Context**: Security must align with business needs
7. **Measurable**: Define and test security requirements