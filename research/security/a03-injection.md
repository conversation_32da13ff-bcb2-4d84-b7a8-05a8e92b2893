# A03:2021 - Injection

## Overview

Injection slides down to the third position in OWASP Top 10:2021. Despite this drop, injection attacks remain critically dangerous with 94% of applications tested showing some form of injection vulnerability. Cross-site scripting is now considered part of this category.

### Key Statistics
- **Testing Coverage**: 94% of applications tested
- **Maximum Incidence**: 19%
- **Average Incidence**: 3.37%
- **Total Occurrences**: 274,228
- **Total CVEs**: 32,078

## Types of Injection Attacks

### 1. SQL Injection
Most common and dangerous form where malicious SQL statements are inserted into application queries.

### 2. NoSQL Injection
Attacks against non-relational databases like MongoDB, CouchDB, Cassandra.

### 3. OS Command Injection
Execution of arbitrary commands on the host operating system.

### 4. ORM Injection
Exploiting Object-Relational Mapping layers to manipulate database queries.

### 5. LDAP Injection
Exploiting applications that construct LDAP statements from user input.

### 6. Expression Language (EL) Injection
Attacking template engines and expression evaluators.

### 7. OGNL Injection
Object-Graph Navigation Language exploitation, common in Java frameworks.

### 8. Cross-Site Scripting (XSS)
Now included in injection category, involves injecting malicious scripts into web pages.

## Vulnerability Conditions

An application is vulnerable to injection when:

1. **Unvalidated Input**: User-supplied data is not validated, filtered, or sanitized
2. **Dynamic Queries**: Dynamic queries or non-parameterized calls without context-aware escaping
3. **ORM Misuse**: Hostile data used within object-relational mapping (ORM) search parameters
4. **Direct Concatenation**: Hostile data directly concatenated into queries or commands

## Prevention Strategies

### 1. Use Safe APIs
```
Priority #1: Use parameterized interfaces
- Prepared statements
- Stored procedures
- Parameterized queries
- ORM with proper configuration
```

### 2. Positive Server-Side Validation
```
- Whitelist validation for all input
- Validate data type, length, format, range
- Reject suspicious input rather than sanitize
- Implement validation at multiple layers
```

### 3. Context-Specific Escaping
```
- Use interpreter-specific escape syntax
- Apply escaping based on output context
- HTML encoding for HTML context
- URL encoding for URL context
- JavaScript encoding for JS context
```

### 4. Use LIMIT and Other Controls
```
- Implement SQL query limits
- Use least privilege database accounts
- Separate accounts for different operations
- Implement query timeouts
```

### 5. Security Testing Integration
```
- Static Application Security Testing (SAST)
- Dynamic Application Security Testing (DAST)
- Interactive Application Security Testing (IAST)
- Code reviews focusing on injection points
```

## Language-Specific Prevention

### Java - SQL Injection Prevention
```java
// VULNERABLE
String query = "SELECT * FROM users WHERE email = '" + email + "'";
Statement statement = connection.createStatement();
ResultSet results = statement.executeQuery(query);

// SECURE - Using PreparedStatement
String query = "SELECT * FROM users WHERE email = ?";
PreparedStatement pstmt = connection.prepareStatement(query);
pstmt.setString(1, email);
ResultSet results = pstmt.executeQuery();
```

### Python - SQL Injection Prevention
```python
# VULNERABLE
query = f"SELECT * FROM users WHERE email = '{email}'"
cursor.execute(query)

# SECURE - Using parameterized queries
query = "SELECT * FROM users WHERE email = %s"
cursor.execute(query, (email,))
```

### Node.js - SQL Injection Prevention
```javascript
// VULNERABLE
const query = `SELECT * FROM users WHERE email = '${email}'`;
db.query(query);

// SECURE - Using parameterized queries
const query = 'SELECT * FROM users WHERE email = ?';
db.query(query, [email]);
```

### PHP - SQL Injection Prevention
```php
// VULNERABLE
$query = "SELECT * FROM users WHERE email = '$email'";
$result = mysqli_query($conn, $query);

// SECURE - Using prepared statements
$stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
$stmt->bind_param("s", $email);
$stmt->execute();
```

## OS Command Injection Prevention

### Vulnerable Pattern
```python
# VULNERABLE
os.system(f"ping {user_input}")
```

### Secure Implementation
```python
# SECURE - Using subprocess with list arguments
import subprocess
subprocess.run(["ping", "-c", "4", user_input], check=True)

# BETTER - Validate input first
import re
if re.match(r"^[\w.-]+$", user_input):
    subprocess.run(["ping", "-c", "4", user_input], check=True)
```

## NoSQL Injection Prevention

### MongoDB Example
```javascript
// VULNERABLE
db.users.find({username: req.body.username, password: req.body.password});

// SECURE - Sanitize input
const sanitize = require('mongo-sanitize');
db.users.find({
    username: sanitize(req.body.username),
    password: sanitize(req.body.password)
});
```

## LDAP Injection Prevention

### Vulnerable Pattern
```java
String filter = "(cn=" + userInput + ")";
```

### Secure Implementation
```java
// Use LDAP encoding
String filter = "(cn=" + LdapEncoder.filterEncode(userInput) + ")";
```

## XSS Prevention (Now Part of Injection)

### Output Encoding Based on Context
```javascript
// HTML Context
element.textContent = userInput; // Safe
element.innerHTML = escapeHtml(userInput); // With encoding

// JavaScript Context
const data = JSON.stringify(userInput);

// URL Context
const url = encodeURIComponent(userInput);

// CSS Context
element.style.color = escapeCss(userInput);
```

## Testing for Injection Vulnerabilities

### 1. Manual Testing
- Input special characters: `' " ; -- /* */`
- Test boundary conditions
- Try encoded payloads
- Check error messages for information leakage

### 2. Automated Testing
- Use OWASP ZAP or Burp Suite
- Implement SAST in CI/CD pipeline
- Regular dependency scanning
- Penetration testing

### 3. Code Review Focus Areas
- Database queries
- System commands
- File operations
- LDAP queries
- XML processors
- Expression evaluators

## Real-World Attack Scenarios

### Scenario 1: SQL Injection
```
// Application code
String query = "SELECT * FROM accounts WHERE custID='" + request.getParameter("id") + "'";

// Attack vector
http://example.com/app/accountView?id=' or '1'='1

// Resulting query
SELECT * FROM accounts WHERE custID='' or '1'='1'
```

### Scenario 2: OS Command Injection
```
// Application code
String cmd = "pdf-generator --url " + userUrl;
Runtime.getRuntime().exec(cmd);

// Attack vector
userUrl = "http://example.com; rm -rf /"

// Resulting command
pdf-generator --url http://example.com; rm -rf /
```

## Defense in Depth

### 1. Input Validation Layer
- Whitelist validation
- Length restrictions
- Format validation
- Business logic validation

### 2. Query Construction Layer
- Parameterized queries
- Stored procedures
- ORM with safe defaults

### 3. Execution Layer
- Least privilege accounts
- Read-only connections where possible
- Query timeouts
- Resource limits

### 4. Monitoring Layer
- Log injection attempts
- Alert on suspicious patterns
- Monitor query execution times
- Track error rates

## Key Takeaways

1. **Never Trust User Input**: All input must be validated
2. **Use Parameterized Queries**: Primary defense against SQL injection
3. **Context-Aware Encoding**: Different contexts require different encoding
4. **Defense in Depth**: Multiple layers of protection
5. **Regular Testing**: Continuous security testing is essential
6. **Keep Learning**: New injection techniques emerge regularly