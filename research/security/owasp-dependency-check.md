# OWASP Dependency-Check - Software Composition Analysis

## Overview

OWASP Dependency-Check is a Software Composition Analysis (SCA) tool that identifies project dependencies and checks if there are any known, publicly disclosed vulnerabilities. It addresses the OWASP Top 10 2021 risk "A06:2021 – Vulnerable and Outdated Components."

### Key Features
- **Automated Vulnerability Detection**: Scans dependencies against known CVEs
- **Multiple Data Sources**: NVD, NPM Audit, OSS Index, RetireJS
- **Comprehensive Reporting**: HTML, XML, JSON, CSV, JUnit formats
- **CI/CD Integration**: <PERSON>, <PERSON><PERSON>, <PERSON>rad<PERSON>, and more
- **Evidence Collection**: Gathers multiple data points for accurate identification

## How It Works

### 1. Dependency Analysis
```
1. Scans project files to identify dependencies
2. Extracts version information
3. Collects "evidence" (file names, vendor, product, version)
4. Generates dependency fingerprints
```

### 2. CPE Identification
```
- Maps dependencies to Common Platform Enumeration (CPE)
- Uses evidence to determine exact component
- Handles various naming conventions
- Reduces false positives through intelligent matching
```

### 3. Vulnerability Matching
```
- Queries local vulnerability database
- Matches CPEs against known CVEs
- Calculates CVSS scores
- Identifies severity levels
```

### 4. Report Generation
```
- Aggregates findings
- Generates detailed reports
- Provides remediation guidance
- Links to vulnerability details
```

## Installation and Setup

### Command Line Tool
```bash
# Download latest release
wget https://github.com/jeremylong/DependencyCheck/releases/download/v12.1.0/dependency-check-12.1.0-release.zip

# Extract
unzip dependency-check-12.1.0-release.zip

# Run initial update (may take 10+ minutes)
./dependency-check/bin/dependency-check.sh --updateonly

# Scan a project
./dependency-check/bin/dependency-check.sh \
    --project "My Project" \
    --scan /path/to/project \
    --format HTML \
    --out ./reports
```

### Maven Integration
```xml
<plugin>
    <groupId>org.owasp</groupId>
    <artifactId>dependency-check-maven</artifactId>
    <version>12.1.0</version>
    <configuration>
        <failBuildOnCVSS>7</failBuildOnCVSS>
        <formats>
            <format>HTML</format>
            <format>JSON</format>
        </formats>
    </configuration>
    <executions>
        <execution>
            <goals>
                <goal>check</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

### Gradle Integration
```gradle
plugins {
    id 'org.owasp.dependencycheck' version '12.1.0'
}

dependencyCheck {
    failBuildOnCVSS = 7
    format = 'ALL'
    suppressionFile = 'dependency-check-suppressions.xml'
}
```

### Docker Usage
```bash
# Pull the image
docker pull owasp/dependency-check

# Run scan
docker run --rm \
    -v $(pwd):/src \
    -v $(pwd)/reports:/report \
    owasp/dependency-check \
    --scan /src \
    --format "ALL" \
    --project "My Project" \
    --out /report
```

## Configuration Options

### Core Settings
```properties
# Update configuration
odc.autoupdate=true
odc.data.directory=/var/dependency-check/data

# Proxy settings
proxy.server=proxy.example.com
proxy.port=8080

# Performance tuning
analyzer.npm.enabled=true
analyzer.retirejs.enabled=true
analyzer.ossindex.enabled=true
```

### Suppression Rules
```xml
<?xml version="1.0" encoding="UTF-8"?>
<suppressions>
    <!-- Suppress a specific CVE -->
    <suppress>
        <notes>False positive - not affected</notes>
        <cve>CVE-2021-12345</cve>
    </suppress>
    
    <!-- Suppress by file pattern -->
    <suppress>
        <notes>Test dependencies only</notes>
        <filePath regex="true">.*test.*\.jar</filePath>
        <cvssBelow>7</cvssBelow>
    </suppress>
</suppressions>
```

## CI/CD Integration

### Jenkins Pipeline
```groovy
pipeline {
    agent any
    stages {
        stage('Dependency Check') {
            steps {
                dependencyCheck additionalArguments: '''
                    --format HTML 
                    --format JSON
                    --failOnCVSS 7
                    ''', 
                    odcInstallation: 'dependency-check'
                
                dependencyCheckPublisher pattern: '**/dependency-check-report.json'
            }
        }
    }
}
```

### GitHub Actions
```yaml
name: Dependency Check

on: [push, pull_request]

jobs:
  depcheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Run Dependency Check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'my-project'
          path: '.'
          format: 'HTML'
          args: >
            --failOnCVSS 7
            --enableRetired
            
      - name: Upload results
        uses: actions/upload-artifact@v3
        with:
          name: dependency-check-report
          path: reports/
```

## Best Practices

### 1. Regular Updates
```bash
# Update vulnerability database daily
0 0 * * * /opt/dependency-check/bin/dependency-check.sh --updateonly

# Scan projects after updates
0 1 * * * /opt/dependency-check/bin/dependency-check.sh --scan /projects
```

### 2. False Positive Management
```yaml
Strategy:
  - Investigate all findings
  - Document false positives
  - Create suppression rules
  - Review suppressions regularly
  - Remove outdated suppressions
```

### 3. Severity Thresholds
```yaml
Environment Thresholds:
  Production: CVSS >= 7.0
  Staging: CVSS >= 8.0
  Development: CVSS >= 9.0
  
Actions:
  Critical (9.0-10.0): Block deployment
  High (7.0-8.9): Require approval
  Medium (4.0-6.9): Create ticket
  Low (0.1-3.9): Log only
```

### 4. Integration Points
```yaml
Integrate with:
  - Build pipelines
  - Pull request checks
  - Security dashboards
  - Ticketing systems
  - Notification channels
```

## Advanced Features

### Experimental Analyzers
```bash
# Enable experimental analyzers
--enableExperimental

# Specific analyzers
--disableNuspec
--disableAssembly
--enableRetired
```

### Custom Data Sources
```bash
# Use additional vulnerability sources
--noupdate \
--data /custom/vulnerability/data \
--connectionString "jdbc:h2:file:/custom/db"
```

### Report Formats
```bash
# Generate multiple formats
--format HTML \
--format JSON \
--format CSV \
--format JUNIT \
--format SARIF
```

## Troubleshooting

### Common Issues

#### Slow Initial Download
```
Issue: First run takes 10+ minutes
Solution: 
- Be patient, it's downloading years of vulnerability data
- Use a local mirror for faster updates
- Share data directory across projects
```

#### High False Positive Rate
```
Issue: Many incorrect vulnerability matches
Solution:
- Update to latest version
- Use suppression files
- Report issues to project
- Provide additional evidence hints
```

#### Memory Issues
```
Issue: OutOfMemoryError
Solution:
export JAVA_OPTS="-Xmx4G"
```

## Integration with Other Tools

### SonarQube
```xml
<plugin>
    <groupId>org.sonarsource.scanner.maven</groupId>
    <artifactId>sonar-maven-plugin</artifactId>
    <configuration>
        <sonar.dependencyCheck.jsonReportPath>
            target/dependency-check-report.json
        </sonar.dependencyCheck.jsonReportPath>
    </configuration>
</plugin>
```

### OWASP DefectDojo
```python
# Import Dependency Check results
import requests

api_url = "https://defectdojo.example.com/api/v2"
headers = {"Authorization": f"Token {api_token}"}

with open("dependency-check-report.json", "r") as f:
    report = f.read()

response = requests.post(
    f"{api_url}/import-scan/",
    headers=headers,
    data={
        "scan_type": "Dependency Check Scan",
        "engagement": engagement_id,
        "file": report
    }
)
```

## Metrics and Reporting

### Key Metrics
```yaml
Track:
  - Total dependencies scanned
  - Vulnerable dependencies found
  - Critical/High/Medium/Low counts
  - Time to remediation
  - False positive rate
  - Suppression count
```

### Dashboard Example
```
┌─────────────────────────────────────┐
│ Dependency Security Dashboard       │
├─────────────────────────────────────┤
│ Total Dependencies: 247             │
│ Vulnerable: 12 (4.9%)               │
│                                     │
│ Critical: 2                         │
│ High: 5                            │
│ Medium: 3                          │
│ Low: 2                             │
│                                     │
│ Last Scan: 2024-01-15 08:00        │
│ Database Updated: 2024-01-15 00:00  │
└─────────────────────────────────────┘
```

## Key Takeaways

1. **Automate Scanning**: Integrate into CI/CD pipelines
2. **Update Regularly**: Keep vulnerability database current
3. **Manage False Positives**: Use suppressions wisely
4. **Set Thresholds**: Define failure criteria by environment
5. **Monitor Trends**: Track vulnerability metrics over time
6. **Act Quickly**: Remediate critical vulnerabilities immediately
7. **Document Decisions**: Record why suppressions were added