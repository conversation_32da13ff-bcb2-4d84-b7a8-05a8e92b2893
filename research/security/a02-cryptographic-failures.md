# A02:2021 - Cryptographic Failures

## Overview

Cryptographic Failures (previously known as "Sensitive Data Exposure") shifts up one position to #2 in the OWASP Top 10:2021. This category focuses on failures related to cryptography (or lack thereof), which often lead to exposure of sensitive data.

## Core Principle

The first priority is determining the protection needs of data in transit and at rest. Data requiring protection includes:
- Passwords
- Credit card numbers
- Health records
- Personal information
- Business secrets

## Common Vulnerabilities

### 1. Data Transmission Issues
- **Clear Text Transmission**: HTTP, SMTP, FTP protocols
- **Weak TLS**: Outdated protocols, weak ciphers
- **Missing Encryption**: Internal API communications
- **Certificate Issues**: Invalid, expired, or self-signed certificates

### 2. Data Storage Problems
- **Unencrypted Storage**: Sensitive data stored in plain text
- **Weak Encryption**: Using broken algorithms (DES, RC4)
- **Key Management**: Hardcoded keys, weak key generation
- **Backup Exposure**: Unencrypted database backups

### 3. Cryptographic Weaknesses
- **Deprecated Algorithms**: MD5, SHA1 for hashing
- **Weak Random Numbers**: Predictable random generation
- **Improper Implementation**: Home-grown cryptography
- **Mode Selection**: Using ECB mode for encryption

### 4. Password Specific Issues
- **Plain Text Storage**: Passwords stored without hashing
- **Simple Hashing**: Using MD5 or SHA1 without salt
- **Weak Salt**: Predictable or reused salts
- **Insufficient Iterations**: Low work factor in key derivation

## Prevention Strategies

### 1. Data Classification and Minimization
```
- Classify data processed, stored, or transmitted
- Identify sensitive data according to privacy laws
- Apply controls as per classification
- Don't store sensitive data unnecessarily
- Discard it ASAP or use PCI DSS compliant tokenization
```

### 2. Encryption at Rest
```
- Encrypt all sensitive data at rest
- Use strong, standard algorithms (AES-256)
- Implement proper key management
- Encrypt database backups
- Use encrypted file systems where appropriate
```

### 3. Encryption in Transit
```
- Encrypt all data in transit with secure protocols
- Use TLS 1.2 or higher
- Implement perfect forward secrecy (PFS)
- Encrypt backend connections
- Verify certificate chains and hostnames
```

### 4. Strong Key Management
```
- Generate keys cryptographically randomly
- Store keys securely (HSM, key vault)
- Rotate keys regularly
- Separate keys from data
- Implement key escrow where required
```

### 5. Algorithm Selection
```
Recommended Algorithms:
- Encryption: AES (128 bits or higher)
- Hashing: SHA-256 or higher
- Key Exchange: ECDHE, DHE
- Digital Signatures: RSA (2048 bits+), ECDSA

Deprecated (DO NOT USE):
- DES, 3DES (except for legacy)
- MD5, SHA1
- ECB mode
- PKCS#1 v1.5 padding
```

### 6. Password Storage
```
Use adaptive salted hashing functions:
- Argon2 (preferred)
- scrypt
- bcrypt
- PBKDF2

Configuration minimums:
- Argon2: memory=64MB, iterations=3, parallelism=1
- scrypt: N=16384, r=8, p=1
- bcrypt: cost=10
- PBKDF2: iterations=100,000
```

### 7. Browser Security
```
- Implement HTTP Strict Transport Security (HSTS)
- Use secure cookie flags
- Disable caching for sensitive data
- Implement Certificate Pinning for mobile apps
```

## Implementation Examples

### Secure Password Hashing (Python)
```python
import argon2

# Hash password
hasher = argon2.PasswordHasher()
hash = hasher.hash(password)

# Verify password
try:
    hasher.verify(hash, password)
    # Password is correct
except argon2.exceptions.VerifyMismatchError:
    # Password is incorrect
```

### TLS Configuration (Nginx)
```nginx
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_tickets off;
ssl_stapling on;
ssl_stapling_verify on;
```

## Attack Scenarios

### Scenario 1: Database Exposure
```
- Application stores credit cards encrypted in database
- Database gets compromised via SQL injection
- Weak encryption allows attackers to decrypt data
- Credit card numbers exposed
```

### Scenario 2: Man-in-the-Middle
```
- Application uses HTTP for login page
- Attacker intercepts traffic on public WiFi
- Credentials transmitted in clear text
- Account compromised
```

### Scenario 3: Weak Password Storage
```
- Passwords hashed with unsalted MD5
- Database breach exposes hashes
- Rainbow tables crack passwords quickly
- Mass account compromise
```

## Testing and Validation

### 1. Identify Sensitive Data
- Review data flows
- Check regulatory requirements
- Document data classification

### 2. Verify Encryption
- Test for HTTPS everywhere
- Verify TLS configuration
- Check certificate validity
- Test for mixed content

### 3. Review Algorithms
- Audit cryptographic usage
- Check for deprecated algorithms
- Verify proper implementation
- Test key strength

### 4. Test Key Management
- Review key storage
- Check rotation procedures
- Verify separation of keys and data
- Test recovery procedures

## Compliance Considerations

### Regulatory Requirements
- **GDPR**: Encryption as appropriate technical measure
- **PCI DSS**: Specific encryption requirements for card data
- **HIPAA**: Encryption for PHI transmission and storage
- **SOC 2**: Encryption controls for Type II compliance

### Industry Standards
- NIST SP 800-175B for cryptographic standards
- FIPS 140-2 for cryptographic modules
- Common Criteria for product certification

## Key Takeaways

1. **Data Protection First**: Identify and classify sensitive data
2. **Strong Cryptography**: Use current, strong algorithms
3. **Proper Implementation**: Avoid custom cryptography
4. **Key Management**: Secure key storage and rotation
5. **Defense in Depth**: Layer security controls
6. **Regular Updates**: Keep libraries and protocols current
7. **Compliance**: Meet regulatory requirements