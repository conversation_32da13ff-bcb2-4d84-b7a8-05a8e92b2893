# Secure Coding Practices - Comprehensive Guide

## Overview

Secure coding practices are fundamental techniques and principles that developers must follow to create applications resistant to security vulnerabilities. This guide provides technology-agnostic practices applicable across all programming languages and frameworks.

## Core Security Principles

### 1. Defense in Depth
Implement multiple layers of security controls throughout the application.

### 2. <PERSON>rinciple of Least Privilege
Grant minimum permissions necessary for functionality.

### 3. Fail Securely
Ensure failures do not compromise security or leak information.

### 4. Don't Trust User Input
All external input must be validated and sanitized.

### 5. Separation of Duties
Critical operations should require multiple steps or approvals.

### 6. Avoid Security by Obscurity
Security should not depend on secrecy of implementation.

### 7. Keep Security Simple
Complex security mechanisms are more likely to have vulnerabilities.

### 8. Fix Security Issues Correctly
Address root causes, not just symptoms.

## Secure Coding Checklist

### 1. Input Validation

#### Requirements
- [ ] Define acceptable input for all data entry points
- [ ] Validate all input on the server side
- [ ] Reject invalid input rather than sanitizing
- [ ] Use positive validation (allowlist) over negative (blocklist)

#### Implementation
```python
# Good: Positive validation
def validate_username(username):
    # Only allow alphanumeric and underscore, 3-20 characters
    pattern = r'^[a-zA-Z0-9_]{3,20}$'
    if not re.match(pattern, username):
        raise ValidationError("Invalid username format")
    return username

# Bad: Negative validation
def validate_username_bad(username):
    # Trying to block bad characters - incomplete
    if '<' in username or '>' in username:
        raise ValidationError("Invalid characters")
    return username
```

### 2. Output Encoding

#### Context-Specific Encoding
```javascript
// HTML Context
function encodeHTML(str) {
    return str.replace(/[&<>"']/g, function(match) {
        const encode = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#39;'
        };
        return encode[match];
    });
}

// JavaScript Context
function encodeJS(str) {
    return JSON.stringify(str);
}

// URL Context
function encodeURL(str) {
    return encodeURIComponent(str);
}

// CSS Context
function encodeCSS(str) {
    return str.replace(/[<>"'&]/g, function(match) {
        return '\\' + match.charCodeAt(0).toString(16) + ' ';
    });
}
```

### 3. Authentication and Password Management

#### Password Storage
```python
# Good: Using bcrypt with appropriate cost factor
import bcrypt

def hash_password(password):
    # Generate salt and hash with cost factor 12
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt(12))

def verify_password(password, hashed):
    return bcrypt.checkpw(password.encode('utf-8'), hashed)
```

#### Session Management
```java
// Good: Secure session configuration
session.setHttpOnly(true);
session.setSecure(true); // HTTPS only
session.setMaxInactiveInterval(1800); // 30 minutes
session.setAttribute("csrf_token", generateCSRFToken());

// Regenerate session ID after login
request.changeSessionId();
```

### 4. Access Control

#### Implementation Pattern
```python
# Good: Centralized access control
class AccessControl:
    def __init__(self, user):
        self.user = user
    
    def can_access_resource(self, resource):
        # Check ownership
        if resource.owner_id == self.user.id:
            return True
        
        # Check role-based permissions
        if self.user.has_role('admin'):
            return True
        
        # Check specific permissions
        if resource.is_public:
            return True
        
        return False

# Usage
@require_permission('resource.read')
def view_resource(request, resource_id):
    resource = Resource.get(resource_id)
    if not AccessControl(request.user).can_access_resource(resource):
        raise PermissionDenied()
    return render_resource(resource)
```

### 5. Cryptographic Practices

#### Secure Random Number Generation
```java
// Good: Cryptographically secure random
import java.security.SecureRandom;

SecureRandom random = new SecureRandom();
byte[] bytes = new byte[32];
random.nextBytes(bytes);

// Bad: Predictable random
Random random = new Random();
int value = random.nextInt();
```

#### Encryption Best Practices
```python
# Good: Using established library with secure defaults
from cryptography.fernet import Fernet

# Generate key
key = Fernet.generate_key()

# Encrypt
f = Fernet(key)
encrypted = f.encrypt(data.encode())

# Decrypt
decrypted = f.decrypt(encrypted)
```

### 6. Error Handling and Logging

#### Secure Error Handling
```python
# Good: Generic error messages to users
try:
    authenticate_user(username, password)
except AuthenticationError:
    # Log detailed error internally
    logger.error(f"Authentication failed for user {username}: {e}")
    # Return generic error to user
    return JsonResponse({"error": "Invalid credentials"}, status=401)

# Bad: Exposing internal details
except DatabaseError as e:
    return JsonResponse({"error": str(e)}, status=500)
```

#### Security Logging
```python
# Good: Comprehensive security logging
import logging
from datetime import datetime

security_logger = logging.getLogger('security')

def log_security_event(event_type, user, details, success=True):
    security_logger.info({
        'timestamp': datetime.utcnow().isoformat(),
        'event_type': event_type,
        'user_id': user.id if user else None,
        'ip_address': get_client_ip(),
        'success': success,
        'details': details
    })

# Usage
log_security_event('login_attempt', user, {'method': '2fa'}, success=True)
log_security_event('access_denied', user, {'resource': 'admin_panel'}, success=False)
```

### 7. Data Protection

#### Sensitive Data Handling
```python
# Good: Minimize sensitive data exposure
class User:
    def to_dict(self, include_sensitive=False):
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email
        }
        
        if include_sensitive:
            # Only include sensitive data when explicitly requested
            # and user has permission
            data['ssn'] = self.ssn
            data['credit_card'] = self.credit_card
        
        return data

# Clear sensitive data from memory
def clear_sensitive_data(data):
    if isinstance(data, str):
        # Overwrite string in memory (Python specific)
        ctypes.memset(id(data), 0, len(data))
```

### 8. File Management

#### Secure File Upload
```python
# Good: Comprehensive file upload validation
import magic
import os

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf'}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

def secure_file_upload(file):
    # Check file size
    if file.size > MAX_FILE_SIZE:
        raise ValidationError("File too large")
    
    # Check extension
    ext = file.name.split('.')[-1].lower()
    if ext not in ALLOWED_EXTENSIONS:
        raise ValidationError("Invalid file type")
    
    # Check MIME type
    file_mime = magic.from_buffer(file.read(1024), mime=True)
    file.seek(0)  # Reset file pointer
    
    allowed_mimes = {
        'image/png', 'image/jpeg', 'image/gif', 'application/pdf'
    }
    if file_mime not in allowed_mimes:
        raise ValidationError("Invalid file content")
    
    # Generate secure filename
    filename = generate_secure_filename(ext)
    
    # Store outside web root
    upload_path = os.path.join(SECURE_UPLOAD_DIR, filename)
    
    # Set restrictive permissions
    with open(upload_path, 'wb') as f:
        for chunk in file.chunks():
            f.write(chunk)
    os.chmod(upload_path, 0o640)
    
    return filename
```

### 9. Memory Management

#### Prevent Buffer Overflows (C/C++)
```c
// Good: Bounds checking
char buffer[100];
strncpy(buffer, user_input, sizeof(buffer) - 1);
buffer[sizeof(buffer) - 1] = '\0';

// Better: Use safe functions
#define __STDC_WANT_LIB_EXT1__ 1
strcpy_s(buffer, sizeof(buffer), user_input);

// Bad: No bounds checking
strcpy(buffer, user_input);
```

### 10. Database Security

#### Parameterized Queries
```java
// Good: Prepared statement
String sql = "SELECT * FROM users WHERE email = ? AND status = ?";
PreparedStatement pstmt = connection.prepareStatement(sql);
pstmt.setString(1, email);
pstmt.setString(2, "active");
ResultSet rs = pstmt.executeQuery();

// Bad: String concatenation
String sql = "SELECT * FROM users WHERE email = '" + email + "'";
Statement stmt = connection.createStatement();
ResultSet rs = stmt.executeQuery(sql);
```

## Security Testing Integration

### Static Analysis
```yaml
# Example: Integration with CI/CD
security-scan:
  stage: test
  script:
    - bandit -r src/ -f json -o bandit-report.json
    - semgrep --config=auto --json -o semgrep-report.json src/
    - safety check --json -o safety-report.json
  artifacts:
    reports:
      sast:
        - bandit-report.json
        - semgrep-report.json
        - safety-report.json
```

### Dynamic Testing
```python
# Example: Security test cases
import pytest

class TestSecurity:
    def test_sql_injection(self, client):
        # Attempt SQL injection
        response = client.post('/login', data={
            'username': "admin' OR '1'='1",
            'password': 'password'
        })
        assert response.status_code == 401
        assert 'Invalid credentials' in response.json()['error']
    
    def test_xss_prevention(self, client):
        # Attempt XSS
        payload = '<script>alert("XSS")</script>'
        response = client.post('/comment', data={'text': payload})
        
        # Verify encoding in response
        assert '<script>' not in response.content
        assert '&lt;script&gt;' in response.content
```

## Security Code Review Checklist

### Authentication
- [ ] Passwords stored using strong hashing (bcrypt, Argon2)
- [ ] Session IDs regenerated after login
- [ ] Account lockout mechanisms implemented
- [ ] Multi-factor authentication available
- [ ] Password complexity requirements enforced

### Authorization
- [ ] Access control checks on all restricted resources
- [ ] Principle of least privilege enforced
- [ ] No authorization logic in client-side code
- [ ] Consistent authorization across all layers

### Input Validation
- [ ] All inputs validated server-side
- [ ] Positive validation (allowlist) used
- [ ] File uploads restricted and validated
- [ ] No deserialization of untrusted data

### Output Encoding
- [ ] Context-appropriate encoding used
- [ ] No dynamic HTML/JS/CSS generation
- [ ] User content properly sandboxed

### Cryptography
- [ ] Strong algorithms used (AES-256, RSA-2048+)
- [ ] Secure random number generation
- [ ] Proper key management
- [ ] TLS used for data in transit

### Error Handling
- [ ] Generic error messages to users
- [ ] Detailed errors logged internally
- [ ] No stack traces exposed
- [ ] Failed operations logged

### Data Protection
- [ ] Sensitive data encrypted at rest
- [ ] PII handling complies with regulations
- [ ] Data retention policies implemented
- [ ] Secure data deletion

## Key Takeaways

1. **Security is Everyone's Responsibility**: Every developer must understand secure coding
2. **Start Secure**: Building security in is easier than adding it later
3. **Validate Everything**: Never trust any input from any source
4. **Fail Securely**: Ensure failures don't create vulnerabilities
5. **Keep Learning**: Security threats evolve constantly
6. **Use Established Libraries**: Don't implement your own crypto
7. **Test Security**: Include security in your testing strategy
8. **Review Regularly**: Security code reviews catch what tools miss