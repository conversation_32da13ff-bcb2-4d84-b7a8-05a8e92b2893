# OWASP API Security Top 10 - 2023

## Overview

APIs are the backbone of modern applications, exposing application logic and sensitive data. The OWASP API Security Top 10 provides awareness about the most critical security risks to APIs.

## API1:2023 - Broken Object Level Authorization

### Description
APIs tend to expose endpoints that handle object identifiers, creating a wide attack surface of Object Level Access Control issues. Object level authorization checks should be considered in every function that accesses a data source using an ID from the user.

### Example Attack
```http
# Legitimate request
GET /api/users/123/profile
Authorization: Bearer <user_123_token>

# Attack - Access another user's data
GET /api/users/456/profile
Authorization: Bearer <user_123_token>
```

### Prevention
```python
# Good: Verify user owns the resource
@app.route('/api/users/<user_id>/profile')
@require_auth
def get_user_profile(user_id):
    # Check if authenticated user can access this profile
    if current_user.id != user_id and not current_user.is_admin:
        return jsonify({'error': 'Forbidden'}), 403
    
    user = User.get(user_id)
    return jsonify(user.to_dict())

# Better: Use indirect object references
@app.route('/api/my/profile')
@require_auth
def get_my_profile():
    # No user ID in URL - use authenticated user
    return jsonify(current_user.to_dict())
```

### Testing
```bash
# Test for BOLA vulnerabilities
1. Identify API endpoints with object IDs
2. Replace IDs with other values
3. Monitor for successful unauthorized access
```

## API2:2023 - Broken Authentication

### Description
Authentication mechanisms are often implemented incorrectly, allowing attackers to compromise authentication tokens or exploit implementation flaws to assume other users' identities temporarily or permanently.

### Common Issues
- Weak password requirements
- Credential stuffing vulnerabilities
- No account lockout mechanisms
- Weak token generation
- Token storage in URLs
- Missing token expiration
- JWT validation flaws

### Prevention
```python
# Good: Secure authentication implementation
import jwt
import bcrypt
from datetime import datetime, timedelta

class AuthService:
    def __init__(self):
        self.secret_key = os.environ.get('JWT_SECRET_KEY')
        self.refresh_secret = os.environ.get('JWT_REFRESH_SECRET')
    
    def authenticate(self, username, password):
        user = User.get_by_username(username)
        
        # Check account lockout
        if user.is_locked():
            raise AuthError("Account locked")
        
        # Verify password
        if not bcrypt.checkpw(password.encode(), user.password_hash):
            user.record_failed_login()
            raise AuthError("Invalid credentials")
        
        # Reset failed attempts
        user.reset_failed_attempts()
        
        # Generate tokens
        access_token = self.generate_access_token(user)
        refresh_token = self.generate_refresh_token(user)
        
        return {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'token_type': 'Bearer',
            'expires_in': 3600
        }
    
    def generate_access_token(self, user):
        payload = {
            'user_id': user.id,
            'exp': datetime.utcnow() + timedelta(hours=1),
            'iat': datetime.utcnow(),
            'type': 'access'
        }
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
```

### Security Headers
```python
# Good: Secure token handling
@app.after_request
def security_headers(response):
    # Prevent token leakage
    response.headers['Cache-Control'] = 'no-store'
    response.headers['Pragma'] = 'no-cache'
    
    # Secure cookies
    response.set_cookie(
        'refresh_token',
        value=refresh_token,
        secure=True,
        httponly=True,
        samesite='Strict',
        max_age=86400  # 24 hours
    )
    return response
```

## API3:2023 - Broken Object Property Level Authorization

### Description
This combines Excessive Data Exposure and Mass Assignment, focusing on the lack of or improper authorization validation at the object property level. This leads to information exposure or manipulation by unauthorized parties.

### Example Vulnerabilities
```json
// Excessive Data Exposure
// Response includes sensitive fields
{
    "id": 123,
    "username": "john",
    "email": "<EMAIL>",
    "password_hash": "$2b$10$...",  // Should not be exposed
    "ssn": "***********",           // Should not be exposed
    "role": "user"
}

// Mass Assignment
// User can modify restricted fields
PUT /api/users/123
{
    "email": "<EMAIL>",
    "role": "admin"  // Should not be modifiable by user
}
```

### Prevention
```python
# Good: Field-level access control
class UserSerializer:
    def __init__(self, user, requester):
        self.user = user
        self.requester = requester
    
    def to_dict(self, fields=None):
        # Base fields available to all
        data = {
            'id': self.user.id,
            'username': self.user.username,
            'created_at': self.user.created_at
        }
        
        # Additional fields based on permissions
        if self.requester.id == self.user.id or self.requester.is_admin:
            data['email'] = self.user.email
            data['phone'] = self.user.phone
        
        # Admin-only fields
        if self.requester.is_admin:
            data['role'] = self.user.role
            data['last_login'] = self.user.last_login
        
        # Filter by requested fields
        if fields:
            data = {k: v for k, v in data.items() if k in fields}
        
        return data

# Good: Mass assignment protection
class UserUpdateSchema:
    # Define allowed fields for updates
    user_allowed_fields = ['email', 'phone', 'name']
    admin_allowed_fields = ['role', 'status', 'verified']
    
    @classmethod
    def validate_update(cls, data, user, is_admin=False):
        allowed = cls.user_allowed_fields.copy()
        if is_admin:
            allowed.extend(cls.admin_allowed_fields)
        
        # Remove any fields not allowed
        cleaned_data = {k: v for k, v in data.items() if k in allowed}
        
        return cleaned_data
```

## API4:2023 - Unrestricted Resource Consumption

### Description
Satisfying API requests requires resources such as network bandwidth, CPU, memory, and storage. Successful attacks can lead to Denial of Service or an increase of operational costs.

### Attack Vectors
- No pagination limits
- Unlimited file uploads
- Complex query processing
- Third-party service abuse
- Resource-intensive operations

### Prevention
```python
# Good: Comprehensive rate limiting
from flask_limiter import Limiter

limiter = Limiter(
    app,
    key_func=lambda: get_user_id() or get_remote_address(),
    default_limits=["100 per minute", "1000 per hour"]
)

# Different limits for different operations
@app.route('/api/search')
@limiter.limit("10 per minute")
def search():
    # Limit query complexity
    query = request.args.get('q', '')
    if len(query) > 100:
        return jsonify({'error': 'Query too long'}), 400
    
    # Limit results
    limit = min(int(request.args.get('limit', 20)), 100)
    
    results = perform_search(query, limit=limit)
    return jsonify(results)

# File upload limits
@app.route('/api/upload', methods=['POST'])
@limiter.limit("5 per hour")
def upload():
    file = request.files.get('file')
    
    # Check file size
    if file.content_length > 10 * 1024 * 1024:  # 10MB
        return jsonify({'error': 'File too large'}), 413
    
    # Limit processing time
    with timeout(30):  # 30 second timeout
        process_file(file)
```

### Resource Monitoring
```python
# Good: Monitor resource usage
import resource
import psutil

def check_resources():
    # Memory usage
    memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
    if memory > 500:  # 500MB limit
        raise ResourceError("Memory limit exceeded")
    
    # CPU usage
    cpu_percent = psutil.Process().cpu_percent(interval=1)
    if cpu_percent > 80:
        raise ResourceError("CPU limit exceeded")
    
    # Active connections
    connections = len(psutil.Process().connections())
    if connections > 1000:
        raise ResourceError("Connection limit exceeded")
```

## API5:2023 - Broken Function Level Authorization

### Description
Complex access control policies with different hierarchies, groups, and roles, and an unclear separation between administrative and regular functions, tend to lead to authorization flaws.

### Example Attack
```http
# Regular user endpoint
GET /api/users/profile

# Admin endpoint - should be protected
GET /api/admin/users
DELETE /api/admin/users/123

# Attack - Regular user accessing admin functions
GET /api/admin/users
Authorization: Bearer <regular_user_token>
```

### Prevention
```python
# Good: Role-based access control
from functools import wraps

def require_role(roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return jsonify({'error': 'Authentication required'}), 401
            
            if not any(current_user.has_role(role) for role in roles):
                return jsonify({'error': 'Insufficient privileges'}), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Usage
@app.route('/api/admin/users')
@require_role(['admin', 'user_manager'])
def list_all_users():
    users = User.query.all()
    return jsonify([u.to_dict() for u in users])

# Good: Separate admin routes
admin_bp = Blueprint('admin', __name__, url_prefix='/api/admin')

@admin_bp.before_request
def check_admin_access():
    if not current_user.is_admin:
        return jsonify({'error': 'Admin access required'}), 403
```

## API6:2023 - Unrestricted Access to Sensitive Business Flows

### Description
APIs vulnerable to this risk expose a business flow without compensating for how the functionality could harm the business if used excessively in an automated manner.

### Examples
- Mass ticket purchasing
- High-volume betting
- Automated content creation
- Resource reservation abuse

### Prevention
```python
# Good: Business flow protection
class BusinessFlowProtection:
    def __init__(self):
        self.user_actions = {}  # Track user actions
    
    def check_purchase_limit(self, user_id, product_id):
        key = f"{user_id}:{product_id}"
        
        # Check velocity
        recent_purchases = self.get_recent_purchases(user_id, hours=1)
        if len(recent_purchases) > 5:
            raise BusinessLogicError("Purchase limit exceeded")
        
        # Check product-specific limits
        product = Product.get(product_id)
        if product.is_limited_edition:
            user_purchases = self.get_user_product_purchases(user_id, product_id)
            if len(user_purchases) >= product.max_per_customer:
                raise BusinessLogicError("Product limit reached")
        
        # CAPTCHA for suspicious activity
        if self.is_suspicious_activity(user_id):
            return {'requires_captcha': True}
        
        return {'allowed': True}
    
    def is_suspicious_activity(self, user_id):
        # Check patterns
        actions = self.user_actions.get(user_id, [])
        
        # Rapid successive requests
        if len(actions) > 10 and all(
            actions[i+1] - actions[i] < 1 
            for i in range(len(actions)-1)
        ):
            return True
        
        return False
```

## API7:2023 - Server Side Request Forgery (SSRF)

### Description
SSRF flaws occur when an API fetches a remote resource without validating the user-supplied URI. This enables an attacker to coerce the application to send crafted requests to unexpected destinations.

### Example Attack
```http
# Vulnerable endpoint
POST /api/fetch-image
{
    "url": "http://internal-server/admin/delete-all-data"
}

# Attack vectors
- http://localhost/admin
- http://***************/latest/meta-data/
- file:///etc/passwd
```

### Prevention
```python
# Good: SSRF protection
import ipaddress
from urllib.parse import urlparse

class URLValidator:
    # Whitelist allowed domains
    ALLOWED_DOMAINS = ['example.com', 'cdn.example.com']
    
    # Blacklist private IP ranges
    PRIVATE_NETWORKS = [
        ipaddress.ip_network('10.0.0.0/8'),
        ipaddress.ip_network('**********/12'),
        ipaddress.ip_network('***********/16'),
        ipaddress.ip_network('*********/8'),
        ipaddress.ip_network('***********/16'),
    ]
    
    @classmethod
    def validate_url(cls, url):
        parsed = urlparse(url)
        
        # Check protocol
        if parsed.scheme not in ['http', 'https']:
            raise ValueError("Invalid protocol")
        
        # Check domain whitelist
        if parsed.hostname not in cls.ALLOWED_DOMAINS:
            raise ValueError("Domain not allowed")
        
        # Resolve IP and check if private
        try:
            ip = socket.gethostbyname(parsed.hostname)
            ip_obj = ipaddress.ip_address(ip)
            
            for network in cls.PRIVATE_NETWORKS:
                if ip_obj in network:
                    raise ValueError("Private IP not allowed")
        except:
            raise ValueError("Invalid hostname")
        
        return True

# Usage
@app.route('/api/fetch-image', methods=['POST'])
def fetch_image():
    url = request.json.get('url')
    
    try:
        URLValidator.validate_url(url)
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    
    # Fetch with timeout and size limit
    response = requests.get(url, timeout=5, stream=True)
    
    # Check content type
    if not response.headers.get('content-type', '').startswith('image/'):
        return jsonify({'error': 'Not an image'}), 400
    
    # Process image...
```

## API8:2023 - Security Misconfiguration

### Description
APIs and the systems supporting them typically contain complex configurations, meant to make the APIs more customizable. Software engineers and DevOps personnel may miss these configurations, or not follow security best practices.

### Common Misconfigurations
- Missing security headers
- Permissive CORS
- Verbose error messages
- Default credentials
- Unnecessary HTTP methods
- Missing TLS

### Prevention
```python
# Good: Security configuration
from flask_talisman import Talisman

# Force HTTPS and set security headers
Talisman(app, force_https=True)

# CORS configuration
from flask_cors import CORS

CORS(app, origins=['https://trusted-domain.com'], 
     methods=['GET', 'POST'],
     allow_headers=['Authorization', 'Content-Type'],
     expose_headers=['X-Total-Count'],
     supports_credentials=True,
     max_age=3600)

# Disable unnecessary methods
@app.before_request
def limit_methods():
    if request.method not in ['GET', 'POST', 'PUT', 'DELETE']:
        return jsonify({'error': 'Method not allowed'}), 405

# Environment-specific config
class Config:
    def __init__(self, env):
        self.env = env
        
        # Common settings
        self.JWT_ALGORITHM = 'HS256'
        self.SESSION_COOKIE_SECURE = True
        self.SESSION_COOKIE_HTTPONLY = True
        self.SESSION_COOKIE_SAMESITE = 'Lax'
        
        # Environment specific
        if env == 'production':
            self.DEBUG = False
            self.TESTING = False
            self.LOG_LEVEL = 'WARNING'
        else:
            self.DEBUG = True
            self.TESTING = True
            self.LOG_LEVEL = 'DEBUG'
```

## API9:2023 - Improper Inventory Management

### Description
APIs tend to expose more endpoints than traditional web applications, making proper and updated documentation highly important. A proper inventory of hosts and deployed API versions also plays an important role in mitigating issues such as deprecated API versions and exposed debug endpoints.

### Issues
- Undocumented endpoints
- Old API versions running
- Different configurations between environments
- Exposed development/staging APIs
- Missing retirement process

### Prevention
```python
# Good: API versioning and documentation
from flask import Flask, Blueprint
from flask_restx import Api, Resource

app = Flask(__name__)

# Version management
API_VERSIONS = {
    'v1': {
        'status': 'deprecated',
        'sunset_date': '2024-12-31',
        'blueprint': Blueprint('api_v1', __name__)
    },
    'v2': {
        'status': 'current',
        'blueprint': Blueprint('api_v2', __name__)
    },
    'v3': {
        'status': 'beta',
        'blueprint': Blueprint('api_v3', __name__)
    }
}

# Auto-documentation with Flask-RESTX
api_v2 = Api(
    API_VERSIONS['v2']['blueprint'],
    version='2.0',
    title='My API',
    description='Production API v2',
    doc='/docs'
)

# Inventory tracking
@app.route('/api/inventory')
@require_admin
def api_inventory():
    inventory = {
        'versions': {},
        'endpoints': []
    }
    
    for version, config in API_VERSIONS.items():
        inventory['versions'][version] = {
            'status': config['status'],
            'sunset_date': config.get('sunset_date'),
            'endpoints': get_endpoints_for_version(version)
        }
    
    return jsonify(inventory)

# Deprecation headers
@app.after_request
def add_deprecation_headers(response):
    if request.path.startswith('/api/v1'):
        response.headers['Sunset'] = 'Sat, 31 Dec 2024 23:59:59 GMT'
        response.headers['Deprecation'] = 'true'
        response.headers['Link'] = '</api/v2>; rel="successor-version"'
    
    return response
```

## API10:2023 - Unsafe Consumption of APIs

### Description
Developers tend to trust data received from third-party APIs more than user input, and so tend to adopt weaker security standards. To target third-party APIs, attackers go after their integrated services instead of trying to compromise the target API directly.

### Vulnerabilities
- Trusting third-party data without validation
- No timeout/retry limits
- Weak authentication to third-party
- Not validating redirects
- Processing untrusted content

### Prevention
```python
# Good: Safe third-party API consumption
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

class SafeAPIClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.api_key = api_key
        self.session = self._create_session()
    
    def _create_session(self):
        session = requests.Session()
        
        # Retry configuration
        retry = Retry(
            total=3,
            backoff_factor=0.3,
            status_forcelist=[500, 502, 503, 504]
        )
        adapter = HTTPAdapter(max_retries=retry)
        session.mount('http://', adapter)
        session.mount('https://', adapter)
        
        # Default headers
        session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'User-Agent': 'MyApp/1.0'
        })
        
        return session
    
    def get_user_data(self, user_id):
        try:
            # Make request with timeout
            response = self.session.get(
                f'{self.base_url}/users/{user_id}',
                timeout=5
            )
            response.raise_for_status()
            
            # Validate response
            data = response.json()
            
            # Don't trust third-party data
            validated_data = self._validate_user_data(data)
            
            return validated_data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Third-party API error: {e}")
            # Graceful degradation
            return self._get_cached_user_data(user_id)
    
    def _validate_user_data(self, data):
        # Validate structure
        required_fields = ['id', 'name', 'email']
        if not all(field in data for field in required_fields):
            raise ValueError("Invalid data structure")
        
        # Sanitize data
        return {
            'id': int(data['id']),
            'name': self._sanitize_string(data['name'], max_length=100),
            'email': self._validate_email(data['email'])
        }
    
    def _sanitize_string(self, value, max_length):
        # Remove dangerous characters
        cleaned = re.sub(r'[<>&"\'`]', '', str(value))
        return cleaned[:max_length]
```

## Implementation Checklist

### Design Phase
- [ ] Define API security requirements
- [ ] Plan authentication/authorization strategy
- [ ] Design rate limiting approach
- [ ] Document security assumptions

### Development Phase
- [ ] Implement proper authentication
- [ ] Add authorization checks
- [ ] Validate all inputs
- [ ] Implement rate limiting
- [ ] Add security headers
- [ ] Handle errors securely

### Testing Phase
- [ ] Test authentication bypass
- [ ] Check authorization flaws
- [ ] Verify rate limits
- [ ] Test input validation
- [ ] Check for data exposure
- [ ] Scan for misconfigurations

### Deployment Phase
- [ ] Review configurations
- [ ] Set up monitoring
- [ ] Configure alerts
- [ ] Document API inventory
- [ ] Plan deprecation strategy

## Key Takeaways

1. **Authorization is Critical**: Both object and function level
2. **Never Trust Input**: Whether from users or third parties
3. **Rate Limit Everything**: Prevent resource exhaustion
4. **Document APIs**: Maintain accurate inventory
5. **Monitor Continuously**: Detect attacks early
6. **Version Properly**: Plan for deprecation
7. **Validate Third-Party Data**: Don't trust external sources
8. **Secure by Default**: Start with restrictive settings