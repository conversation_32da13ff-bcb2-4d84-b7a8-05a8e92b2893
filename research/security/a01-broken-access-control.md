# A01:2021 - Broken Access Control

## Overview

Broken Access Control moved up from the fifth position to become the #1 most critical web application security risk in OWASP Top 10 2021. This rise reflects both its prevalence and severe impact on application security.

### Key Statistics
- **Prevalence**: 94% of applications tested had some form of broken access control
- **Average Incidence Rate**: 3.81%
- **Total Occurrences**: Over 318,487 instances identified
- **CWE Mappings**: 34 Common Weakness Enumerations

## Description

Access control enforces policy such that users cannot act outside of their intended permissions. Failures typically lead to unauthorized information disclosure, modification, or destruction of data, or performing business functions outside user limits.

## Common Vulnerabilities

### 1. Principle of Least Privilege Violations
- Users granted excessive permissions
- Default permissions too permissive
- Privilege creep over time

### 2. Access Control Check Bypasses
- Direct URL access (forced browsing)
- Parameter manipulation
- Missing function level access control

### 3. Unauthorized Resource Access
- Horizontal privilege escalation (accessing other users' data)
- Vertical privilege escalation (accessing admin functions)
- Insecure direct object references

### 4. Technical Misconfigurations
- CORS misconfiguration allowing untrusted origins
- JWT token manipulation
- Cookie/session manipulation
- Metadata tampering (hidden fields, JWTs)

### 5. API-Specific Issues
- Missing access controls on POST, PUT, DELETE methods
- Inconsistent controls between UI and API
- GraphQL query depth attacks

## Prevention Strategies

### 1. Deny by Default
```
- Implement "fail-secure" mechanisms
- Explicitly grant permissions rather than deny
- Default to no access for new resources
```

### 2. Centralized Access Control
```
- Use a single, consistent access control mechanism
- Avoid duplicating access control logic
- Implement reusable access control libraries
```

### 3. Enforce Record Ownership
```
- Verify user owns the record being accessed
- Implement proper object-level authorization
- Use indirect object references where possible
```

### 4. Business Logic Controls
```
- Implement domain-specific limits
- Rate limiting on sensitive operations
- Transaction limits and thresholds
```

### 5. Technical Controls
```
- Disable web server directory listing
- Remove metadata from production
- Secure file upload functionality
- Implement proper CORS policies
```

### 6. Monitoring and Logging
```
- Log all access control failures
- Alert on repeated failures
- Monitor for unusual access patterns
- Implement audit trails
```

### 7. Session Management
```
- Invalidate tokens server-side on logout
- Implement proper session timeout
- Use secure session identifiers
- Rotate tokens regularly
```

## Example Attack Scenarios

### Scenario 1: Parameter Manipulation
```
// Vulnerable URL
https://example.com/app/accountInfo?acct=12345

// Attack: Change parameter to access another account
https://example.com/app/accountInfo?acct=12346
```

### Scenario 2: Forced Browsing
```
// Public page
https://example.com/app/public/login

// Attack: Directly access admin page
https://example.com/app/admin/userManagement
```

### Scenario 3: API Method Exploitation
```
// UI only shows GET requests
GET /api/users/profile

// Attack: Use other methods not properly secured
DELETE /api/users/profile
PUT /api/users/profile
```

## Implementation Guidelines

### 1. Development Phase
- Design access control early in development
- Document authorization requirements
- Implement principle of least privilege
- Use secure frameworks and libraries

### 2. Testing Phase
- Develop comprehensive access control test cases
- Include both positive and negative tests
- Test all HTTP methods
- Verify authorization at multiple layers

### 3. Deployment Phase
- Review production configurations
- Disable unnecessary features
- Implement proper logging
- Set up monitoring and alerting

### 4. Maintenance Phase
- Regular access reviews
- Monitor for privilege creep
- Update access control as requirements change
- Patch security vulnerabilities promptly

## Tools and Resources

### Testing Tools
- OWASP ZAP for automated scanning
- Burp Suite for manual testing
- Custom scripts for access control verification

### Standards and Frameworks
- OWASP Application Security Verification Standard (ASVS)
- NIST access control guidelines
- OAuth 2.0 for token-based authentication
- RBAC/ABAC implementation patterns

## Key Takeaways

1. **Most Common Risk**: Broken access control is the #1 security risk
2. **Design Consideration**: Must be designed from the start, not added later
3. **Consistency is Key**: Use centralized, consistent mechanisms
4. **Testing Required**: Cannot rely on obscurity; must test thoroughly
5. **Ongoing Process**: Requires continuous monitoring and updates