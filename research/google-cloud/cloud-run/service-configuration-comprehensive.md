# Cloud Run Service Configuration - Comprehensive Guide

## Overview
Cloud Run provides extensive configuration options for optimizing service performance, security, and cost-efficiency. This guide covers all aspects of service configuration from capacity management to advanced networking.

## Capacity Management

### Memory Configuration
```bash
# Set memory limits
gcloud run deploy --memory 512Mi   # Minimum
gcloud run deploy --memory 32Gi    # Maximum

# Available memory options
# 512Mi, 1Gi, 2Gi, 4Gi, 8Gi, 16Gi, 32Gi
```

Memory considerations:
- Higher memory allows more CPU allocation
- Affects cold start times
- Impacts billing
- Must accommodate peak usage

### CPU Allocation
```bash
# Allocate CPU
gcloud run deploy --cpu 1      # 1 vCPU
gcloud run deploy --cpu 2      # 2 vCPUs
gcloud run deploy --cpu 4      # 4 vCPUs
gcloud run deploy --cpu 8      # 8 vCPUs

# CPU boost for better cold starts
gcloud run deploy --cpu-boost
```

CPU allocation rules:
- Minimum 0.5 vCPU with 512Mi memory
- Maximum 8 vCPUs with 32Gi memory
- CPU boost available for faster startup

### Concurrency Settings
```bash
# Set maximum concurrent requests per instance
gcloud run deploy --concurrency 1000  # Default
gcloud run deploy --concurrency 1     # Single-threaded
gcloud run deploy --concurrency 250   # Moderate load
```

Concurrency factors:
- Application thread safety
- Memory per request
- CPU requirements
- Database connection limits

### Billing Configuration
```bash
# Per-request billing (default)
gcloud run deploy --cpu-throttling

# Always-on CPU allocation
gcloud run deploy --no-cpu-throttling
```

## Environment Configuration

### Environment Variables
```bash
# Set environment variables
gcloud run deploy --set-env-vars KEY1=value1,KEY2=value2

# From YAML file
gcloud run deploy --env-vars-file env.yaml

# Clear environment variables
gcloud run deploy --clear-env-vars
```

Environment variable YAML format:
```yaml
KEY1: value1
KEY2: value2
DATABASE_URL: ****************************
```

### Execution Environments
```bash
# First generation (default)
gcloud run deploy --execution-environment gen1

# Second generation (recommended)
gcloud run deploy --execution-environment gen2
```

Generation 2 benefits:
- Faster startup times
- Better network performance
- Full Linux compatibility
- Improved file system operations

### GPU Support (Preview)
```bash
# Deploy with GPU
gcloud run deploy --gpu nvidia-l4 --gpu-count 1
```

GPU use cases:
- AI/ML inference
- Image processing
- Video transcoding
- Scientific computing

### Volume Mounts

#### Cloud Storage (FUSE)
```yaml
spec:
  template:
    spec:
      volumes:
      - name: gcs-volume
        csi:
          driver: gcsfuse.csi.storage.gke.io
          volumeAttributes:
            bucketName: my-bucket
            mountOptions: "implicit-dirs"
```

#### Network File System (NFS)
```yaml
volumes:
- name: nfs-volume
  nfs:
    server: ********
    path: /share
    readOnly: false
```

#### In-Memory Volume
```yaml
volumes:
- name: cache-volume
  emptyDir:
    medium: Memory
    sizeLimit: 128Mi
```

## Scaling Configuration

### Automatic Scaling
```bash
# Set maximum instances
gcloud run deploy --max-instances 100

# Set minimum instances (prevent cold starts)
gcloud run deploy --min-instances 1

# Scale to zero
gcloud run deploy --min-instances 0
```

### Scaling Considerations
1. **Cold Start Mitigation**
   - Use minimum instances for consistent latency
   - Optimize container startup time
   - Enable CPU boost

2. **Cost Optimization**
   - Scale to zero during off-hours
   - Use appropriate maximum instances
   - Monitor actual usage patterns

3. **Performance Tuning**
   - Balance concurrency and instance count
   - Consider request processing time
   - Monitor CPU and memory utilization

### Manual Scaling
```bash
# Fixed instance count
gcloud run deploy --min-instances 5 --max-instances 5
```

Use cases:
- Predictable workloads
- Background processing
- Development/testing

## Security and Identity

### Service Account Configuration
```bash
# Use custom service account
gcloud run deploy --service-account <EMAIL>

# Use default service account
gcloud run deploy --service-account <EMAIL>
```

Best practices:
- Create dedicated service accounts
- Follow least privilege principle
- Avoid using default compute service account
- Regular permission audits

### Secrets Management
```bash
# Mount secret as file
gcloud run deploy --set-secrets="/secrets/api-key=api-key:latest"

# Use secret as environment variable
gcloud run deploy --set-secrets="API_KEY=api-key:latest"

# Multiple secrets
gcloud run deploy --set-secrets="DB_PASS=db-password:1,API_KEY=api-key:2"
```

Secret configuration YAML:
```yaml
spec:
  template:
    spec:
      containers:
      - image: IMAGE
        env:
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-password
              key: latest
```

### Health Checks
```yaml
spec:
  template:
    spec:
      containers:
      - image: IMAGE
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /startup
            port: 8080
          initialDelaySeconds: 0
          periodSeconds: 5
          timeoutSeconds: 10
          failureThreshold: 12
```

## Performance Optimization

### Request Timeout
```bash
# Set request timeout (max 60 minutes)
gcloud run deploy --timeout 300  # 5 minutes
gcloud run deploy --timeout 3600 # 60 minutes
```

Timeout considerations:
- Default: 60 seconds
- Long-running requests need higher timeouts
- Consider async processing for very long operations

### Traffic Management

#### Traffic Splitting
```bash
# Split traffic between revisions
gcloud run services update-traffic my-service \
  --to-revisions LATEST=50,my-service-v1=50

# Gradual rollout
gcloud run services update-traffic my-service \
  --to-revisions LATEST=10
```

#### Blue-Green Deployment
```bash
# Deploy new revision without traffic
gcloud run deploy --no-traffic

# Switch all traffic
gcloud run services update-traffic my-service \
  --to-latest
```

#### Canary Deployment
```bash
# Progressive rollout
gcloud run services update-traffic my-service \
  --to-revisions LATEST=5  # 5% canary
  
# Increase canary traffic
gcloud run services update-traffic my-service \
  --to-revisions LATEST=25 # 25% canary
```

### Recommender Integration
Cloud Run Recommender provides:
- Resource optimization suggestions
- Cost reduction opportunities
- Performance improvement recommendations
- Security enhancements

Access via:
- Cloud Console
- gcloud CLI
- Recommender API

## Networking Configuration

### VPC Connector
```bash
# Attach VPC connector
gcloud run deploy --vpc-connector projects/PROJECT/locations/REGION/connectors/CONNECTOR

# Configure egress
gcloud run deploy --vpc-egress all-traffic  # Route all through VPC
gcloud run deploy --vpc-egress private-ranges-only  # Default
```

### Static Outbound IP
1. Create VPC connector
2. Configure Cloud NAT
3. Reserve static IP
4. Route service traffic through VPC

### Private Service Access
```bash
# Internal-only access
gcloud run deploy --ingress internal

# Internal and Load Balancer
gcloud run deploy --ingress internal-and-cloud-load-balancing

# Public access (default)
gcloud run deploy --ingress all
```

## Metadata and Management

### Labels
```bash
# Add labels
gcloud run deploy --labels env=prod,team=backend,cost-center=engineering

# Update labels
gcloud run services update --update-labels version=2.0

# Remove labels
gcloud run services update --remove-labels temp-flag
```

Label use cases:
- Cost allocation
- Resource organization
- Automation targeting
- Access control

### Tags
```bash
# Add tags (Preview)
gcloud run deploy --tag stable

# Multiple tags
gcloud run deploy --tag stable --tag v2
```

Tag benefits:
- Stable URLs for revisions
- Fine-grained IAM policies
- Testing specific revisions

### Service Description
```bash
# Add description
gcloud run deploy --description "Production API service for user management"
```

## Best Practices Summary

### 1. Resource Optimization
- Right-size memory and CPU
- Use Recommender suggestions
- Monitor actual usage
- Adjust based on metrics

### 2. Security Hardening
- Minimize service account permissions
- Use Secret Manager for sensitive data
- Enable Binary Authorization
- Implement proper health checks

### 3. Performance Tuning
- Configure minimum instances for critical services
- Optimize cold start times
- Use appropriate concurrency settings
- Enable CPU boost when needed

### 4. Cost Management
- Scale to zero when possible
- Use per-request billing for variable loads
- Set maximum instance limits
- Regular configuration reviews

### 5. Operational Excellence
- Implement gradual rollouts
- Use structured logging
- Set up comprehensive monitoring
- Document configuration decisions

### 6. Network Security
- Use VPC connectors for private resources
- Configure appropriate ingress settings
- Implement Cloud Armor for DDoS protection
- Regular security audits

## Configuration Validation

### Pre-deployment Checks
```bash
# Validate configuration locally
gcloud run deploy --dry-run

# Check service YAML
kubectl apply --dry-run=client -f service.yaml
```

### Post-deployment Validation
```bash
# Verify configuration
gcloud run services describe my-service

# Check revision details
gcloud run revisions describe my-service-00001-abc

# Test endpoints
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
  https://my-service-abc123-uc.a.run.app
```

## Monitoring and Observability

### Key Metrics to Monitor
1. **Request latency** - P50, P95, P99
2. **Instance count** - Scaling behavior
3. **CPU utilization** - Resource efficiency
4. **Memory usage** - Capacity planning
5. **Error rate** - Service health
6. **Cold start frequency** - User experience

### Logging Configuration
```bash
# Set log level
gcloud run deploy --set-env-vars LOG_LEVEL=info

# Structured logging
gcloud run deploy --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID
```

### Tracing Setup
- Automatic for HTTP requests
- Manual instrumentation for detailed traces
- Integration with Cloud Trace
- Distributed tracing support