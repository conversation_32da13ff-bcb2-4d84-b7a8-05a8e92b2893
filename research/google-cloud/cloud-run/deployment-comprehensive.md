# Cloud Run Deployment - Comprehensive Guide

## Overview
Cloud Run provides multiple deployment methods and configuration options for containerized applications. This guide covers all deployment approaches, configuration options, and production best practices.

## Deployment Methods

### 1. Console Deployment
- Visual interface with step-by-step guidance
- Immediate validation and feedback
- Best for initial deployments and testing

### 2. gcloud CLI Deployment
```bash
# Basic deployment command
gcloud run deploy SERVICE --image IMAGE_URL

# With additional options
gcloud run deploy SERVICE \
  --image IMAGE_URL \
  --region REGION \
  --platform managed \
  --allow-unauthenticated
```

### 3. YAML Configuration Deployment
- Declarative service configuration
- Version controlled infrastructure
- Repeatable deployments
```yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: SERVICE_NAME
spec:
  template:
    spec:
      containers:
      - image: IMAGE_URL
```

### 4. Terraform Deployment
- Infrastructure as Code approach
- Integrated with other GCP resources
- State management and drift detection

### 5. Client Libraries
- Programmatic deployment
- Available for multiple languages
- Integration with application code

### 6. REST API
- Direct API calls
- Maximum flexibility
- Custom automation tools

## Container Registry Options

### Recommended: Artifact Registry
- Native GCP integration
- Multi-format support
- Fine-grained access control
- Vulnerability scanning

### Docker Hub Support
- Public and private repositories
- Requires authentication configuration
- May have rate limiting considerations

### Other Registries
- Supported via Artifact Registry remote repositories
- Includes GitHub Container Registry, Amazon ECR, etc.
- Provides caching and security scanning

## Service Configuration

### Naming Requirements
- Maximum 49 characters
- Lowercase letters, numbers, and hyphens
- Must start with a letter
- Unique per region and project

### Resource Configuration
```bash
# Memory allocation
gcloud run deploy --memory 512Mi

# CPU allocation
gcloud run deploy --cpu 1

# Concurrency
gcloud run deploy --concurrency 100

# Timeout
gcloud run deploy --timeout 300
```

### Environment Variables
```bash
# Set environment variables
gcloud run deploy --set-env-vars KEY1=VALUE1,KEY2=VALUE2

# From file
gcloud run deploy --env-vars-file .env.yaml
```

### Secrets Management
```bash
# Mount secret as volume
gcloud run deploy --set-secrets /secret/path=SECRET_NAME:latest

# As environment variable
gcloud run deploy --set-secrets KEY=SECRET_NAME:VERSION
```

## Scaling Configuration

### Autoscaling
```bash
# Minimum instances (prevents cold starts)
gcloud run deploy --min-instances 1

# Maximum instances
gcloud run deploy --max-instances 100

# Scale to zero
gcloud run deploy --min-instances 0
```

### Concurrency Settings
- Default: 1000 concurrent requests per instance
- Configurable based on application needs
- Consider memory and CPU per request

## Networking and Security

### Ingress Settings
```bash
# Allow all traffic (default)
gcloud run deploy --ingress all

# Allow internal traffic only
gcloud run deploy --ingress internal

# Allow internal and Cloud Load Balancing
gcloud run deploy --ingress internal-and-cloud-load-balancing
```

### Authentication
```bash
# Allow unauthenticated access
gcloud run deploy --allow-unauthenticated

# Require authentication
gcloud run deploy --no-allow-unauthenticated
```

## Multi-Container Deployments

### Container Configuration
- Define multiple containers in service YAML
- Configure startup order dependencies
- Share volumes between containers

### Health Checks
```yaml
livenessProbe:
  httpGet:
    path: /health
  initialDelaySeconds: 0
  periodSeconds: 10
```

### Startup Dependencies
```yaml
containers:
- name: app
  image: IMAGE
  dependsOn:
  - database-proxy
```

## Regional Considerations

### Tier 1 Regions (Lower Cost)
- asia-east1 (Taiwan)
- europe-north1 (Finland)
- europe-west1 (Belgium)
- us-central1 (Iowa)
- us-east1 (South Carolina)
- us-west1 (Oregon)

### Tier 2 Regions
- asia-northeast1 (Tokyo)
- asia-southeast1 (Singapore)
- europe-west4 (Netherlands)
- us-east4 (Northern Virginia)
- And many more...

### Region Selection Criteria
1. **Latency**: Choose region closest to users
2. **Cost**: Consider pricing tiers
3. **Compliance**: Data residency requirements
4. **Service dependencies**: Co-locate with other GCP services

## Production Best Practices

### 1. Container Image Management
- Use specific tags, not :latest
- Implement vulnerability scanning
- Cache base images in Artifact Registry
- Use multi-stage builds for smaller images

### 2. Configuration Management
- Use service YAML for complex configurations
- Store configuration in version control
- Separate environment-specific settings
- Use secrets for sensitive data

### 3. Deployment Strategy
- Implement gradual rollouts
- Use traffic splitting for canary deployments
- Configure rollback procedures
- Monitor deployment health

### 4. Performance Optimization
- Configure appropriate CPU and memory
- Set minimum instances for latency-sensitive apps
- Optimize container startup time
- Use connection pooling for databases

### 5. Monitoring and Observability
- Enable Cloud Logging
- Configure custom metrics
- Set up alerts for key metrics
- Use distributed tracing

### 6. Security Hardening
- Run containers as non-root user
- Use least-privilege service accounts
- Enable Binary Authorization
- Scan images for vulnerabilities

## Deployment Validation

### Pre-deployment Checks
1. Container image exists and is accessible
2. Service account has necessary permissions
3. Quotas are sufficient
4. Dependencies are available

### Post-deployment Validation
```bash
# Check service status
gcloud run services describe SERVICE --region REGION

# Test endpoint
curl https://SERVICE-HASH-REGION.a.run.app

# View logs
gcloud logging read "resource.type=cloud_run_revision"
```

## Troubleshooting Common Issues

### Container Import Failures
- Verify registry authentication
- Check image format compatibility
- Ensure network connectivity

### Deployment Timeouts
- Reduce container startup time
- Increase deployment timeout
- Check for blocking operations

### Service Unavailable
- Verify health check configuration
- Check container logs
- Validate resource allocation

## Advanced Deployment Patterns

### Blue-Green Deployment
1. Deploy new revision
2. Validate new revision
3. Switch traffic instantly
4. Keep old revision for rollback

### Canary Deployment
```bash
# Split traffic between revisions
gcloud run services update-traffic SERVICE \
  --to-revisions NEW_REVISION=10,OLD_REVISION=90
```

### Multi-Region Deployment
- Deploy to multiple regions
- Use Cloud Load Balancing
- Configure failover policies
- Monitor regional health