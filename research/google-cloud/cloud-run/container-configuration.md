# Cloud Run Container Configuration Guide

## Overview
Cloud Run has specific requirements and configuration options for containers. This guide covers the container runtime contract, configuration options, and best practices for containerizing applications.

## Container Runtime Contract

### Core Requirements
1. **Stateless**: Containers must not rely on persistent local state
2. **PORT listening**: Must listen on the `PORT` environment variable
3. **HTTP handling**: Must respond to HTTP requests
4. **Graceful shutdown**: Should handle SIGTERM signals properly

### Port Configuration

#### Default Behavior
```python
# Cloud Run injects PORT environment variable
import os
port = int(os.environ.get("PORT", 8080))
```

#### Custom Port Configuration
```bash
# Using gcloud
gcloud run deploy SERVICE --port 3000

# Using YAML
spec:
  template:
    spec:
      containers:
      - image: IMAGE_URL
        ports:
        - containerPort: 3000
```

#### Port Configuration Methods
1. **Console**: Service settings → Container port
2. **gcloud CLI**: `--port` flag
3. **YAML**: `containerPort` specification
4. **Terraform**: `ports` block in container configuration

### Important Port Considerations
- Only one port per container
- Port must match application listening port
- Default is 8080 if not specified
- HTTP/2 and WebSocket support available

## Entrypoint and Arguments Configuration

### Override Default ENTRYPOINT and CMD
```bash
# Using gcloud
gcloud run deploy SERVICE \
  --image IMAGE_URL \
  --command /app/server \
  --args serve,--port,8080

# Using YAML
spec:
  template:
    spec:
      containers:
      - image: IMAGE_URL
        command: ["/app/server"]
        args: ["serve", "--port", "8080"]
```

### Limitations
- Maximum 1000 arguments per container
- Arguments passed as array/list
- Can override both ENTRYPOINT and CMD or just CMD

### Configuration Examples

#### Python Application
```yaml
command: ["python"]
args: ["app.py", "--workers", "4"]
```

#### Node.js Application
```yaml
command: ["node"]
args: ["server.js", "--cluster"]
```

#### Custom Binary
```yaml
command: ["/usr/local/bin/myapp"]
args: ["--config", "/etc/myapp/config.yaml"]
```

## Container Startup Dependencies (Sidecar Pattern)

### Configuring Startup Order
```yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: my-service
spec:
  template:
    spec:
      containers:
      - name: app
        image: APP_IMAGE
        dependsOn:
        - proxy
      - name: proxy
        image: PROXY_IMAGE
```

### Dependency Requirements
1. **Health checks required**: Dependent containers need startup probes
2. **Concurrent start**: Containers without dependencies start together
3. **Sequential waiting**: Dependent containers wait for dependencies

### Health Check Configuration
```yaml
containers:
- name: database-proxy
  image: PROXY_IMAGE
  startupProbe:
    tcpSocket:
      port: 5432
    initialDelaySeconds: 0
    periodSeconds: 1
    timeoutSeconds: 1
    successThreshold: 1
    failureThreshold: 10
```

### Common Sidecar Patterns
1. **Database Proxy**
```yaml
- name: cloud-sql-proxy
  image: gcr.io/cloud-sql-connectors/cloud-sql-proxy:latest
  args:
  - "--port=5432"
  - "PROJECT:REGION:INSTANCE"
```

2. **Logging Agent**
```yaml
- name: logging-agent
  image: LOGGING_AGENT_IMAGE
  volumeMounts:
  - name: logs
    mountPath: /var/log
```

3. **Service Mesh Proxy**
```yaml
- name: envoy-proxy
  image: ENVOY_IMAGE
  ports:
  - containerPort: 8080
```

## Filesystem and Storage

### Filesystem Characteristics
- **Ephemeral**: In-memory filesystem, cleared on instance shutdown
- **Writable**: Can write temporary files during request processing
- **Size limits**: Subject to memory allocation
- **Not shared**: Each instance has separate filesystem

### Best Practices for File Handling
```python
# Use temporary directories
import tempfile

def process_file():
    with tempfile.TemporaryDirectory() as temp_dir:
        # Process files in temp_dir
        pass
```

### Volume Mounts (Multi-container)
```yaml
spec:
  template:
    spec:
      volumes:
      - name: shared-data
        emptyDir: {}
      containers:
      - name: app
        volumeMounts:
        - name: shared-data
          mountPath: /data
      - name: worker
        volumeMounts:
        - name: shared-data
          mountPath: /data
```

## Process and Signal Management

### Graceful Shutdown
```python
import signal
import sys

def signal_handler(sig, frame):
    print('Gracefully shutting down...')
    # Clean up resources
    sys.exit(0)

signal.signal(signal.SIGTERM, signal_handler)
```

### Container Lifecycle
1. **Startup**: Container starts and initializes
2. **Ready**: Health check passes, receives traffic
3. **Serving**: Handles incoming requests
4. **Shutdown**: Receives SIGTERM, stops accepting new requests
5. **Termination**: Force killed after grace period

### Shutdown Grace Period
- Default: 10 seconds
- Configurable up to 60 seconds
- Container should complete cleanup within this period

## Resource Configuration

### Memory Limits
```bash
# Set memory limit
gcloud run deploy --memory 512Mi  # 512 MiB
gcloud run deploy --memory 2Gi    # 2 GiB

# Available options: 512Mi, 1Gi, 2Gi, 4Gi, 8Gi, 16Gi, 32Gi
```

### CPU Allocation
```bash
# Set CPU limit
gcloud run deploy --cpu 1    # 1 vCPU
gcloud run deploy --cpu 2    # 2 vCPUs
gcloud run deploy --cpu 4    # 4 vCPUs

# CPU throttling: Can be enabled/disabled
gcloud run deploy --cpu-throttling
gcloud run deploy --no-cpu-throttling
```

### Resource Relationships
- More memory allows more CPU allocation
- CPU throttling affects billing (only charged during request processing)
- Higher resources = higher cold start times

## Container Image Best Practices

### 1. Minimize Image Size
```dockerfile
# Multi-stage build
FROM node:16 AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:16-alpine
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
CMD ["node", "server.js"]
```

### 2. Use Specific Base Images
```dockerfile
# Good: Specific version
FROM python:3.11-slim

# Avoid: Latest tag
FROM python:latest
```

### 3. Run as Non-Root User
```dockerfile
# Create non-root user
RUN useradd -r -u 1001 appuser
USER appuser
```

### 4. Optimize for Cold Starts
- Minimize dependencies
- Lazy load where possible
- Use compiled languages for faster startup
- Pre-compile assets

### 5. Handle Environment Variables
```python
import os

# Required by Cloud Run
PORT = int(os.environ.get('PORT', 8080))

# Custom configuration
DB_URL = os.environ.get('DATABASE_URL', 'sqlite:///local.db')
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
```

## Configuration Management

### Environment-Specific Configuration
```yaml
# Production configuration
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: my-service-prod
spec:
  template:
    spec:
      containers:
      - image: IMAGE_URL
        env:
        - name: ENVIRONMENT
          value: production
        - name: LOG_LEVEL
          value: error
        resources:
          limits:
            memory: 2Gi
            cpu: "2"
```

### Configuration Sources Priority
1. Runtime environment variables
2. Container image defaults
3. Build-time arguments
4. Configuration files in image

## Troubleshooting Container Issues

### Common Problems and Solutions

#### 1. Container Not Starting
- Check logs for startup errors
- Verify ENTRYPOINT and CMD
- Ensure dependencies are available
- Check for port binding issues

#### 2. Health Check Failures
- Implement proper health endpoints
- Configure appropriate timeouts
- Check startup probe settings
- Verify network connectivity

#### 3. Memory Issues
- Monitor memory usage
- Increase memory limits
- Optimize application memory usage
- Check for memory leaks

#### 4. Performance Problems
- Enable CPU boost for better cold starts
- Optimize container image size
- Review startup sequence
- Profile application performance

### Debugging Commands
```bash
# View container logs
gcloud logging read "resource.type=cloud_run_revision" --limit 50

# Describe service configuration
gcloud run services describe SERVICE --region REGION

# List revisions
gcloud run revisions list --service SERVICE

# Check container image
docker run -e PORT=8080 -p 8080:8080 IMAGE_URL
```