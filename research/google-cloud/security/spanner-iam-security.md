# Spanner IAM Security Best Practices

## Overview

Cloud Spanner uses Identity and Access Management (IAM) to control access at multiple levels:
- **Project level**: Controls who can create and manage instances
- **Instance level**: Controls who can access specific instances
- **Database level**: Controls who can read/write specific databases

## IAM Role Categories

### 1. Predefined Roles

#### Administrative Roles

**Cloud Spanner Admin** (`roles/spanner.admin`)
- Complete access to all Spanner resources
- Can create, modify, and delete instances
- Full database management capabilities
- Manage IAM policies

**Cloud Spanner Database Admin** (`roles/spanner.databaseAdmin`)
- Create and manage databases
- Perform schema changes
- Read and write data
- Cannot manage instances or IAM

#### Data Access Roles

**Cloud Spanner Database User** (`roles/spanner.databaseUser`)
- Read and write database access
- Execute queries and DML statements
- Cannot modify schema
- Cannot manage backups

**Cloud Spanner Database Reader** (`roles/spanner.databaseReader`)
- Read-only database access
- Execute SELECT queries
- View schema information
- Cannot modify data

#### Specialized Roles

**Cloud Spanner Viewer** (`roles/spanner.viewer`)
- List and view metadata
- Cannot access data
- Useful for monitoring

**Cloud Spanner Backup Admin** (`roles/spanner.backupAdmin`)
- Create and manage backups
- Restore databases
- Cannot access data directly

**Cloud Spanner Backup Writer** (`roles/spanner.backupWriter`)
- Create backups only
- Cannot restore or delete

**Cloud Spanner Restore Admin** (`roles/spanner.restoreAdmin`)
- Restore databases from backups
- Cannot create or delete backups

### 2. Basic Roles (Not Recommended)

- **Viewer**: Can list and get metadata
- **Editor**: Can create and modify resources
- **Owner**: Can modify access controls

## Key Permissions

### Database Operations
```
spanner.databases.read
spanner.databases.write
spanner.databases.select
spanner.databases.updateDdl
spanner.databases.beginOrRollbackReadWriteTransaction
spanner.databases.beginReadOnlyTransaction
```

### Instance Management
```
spanner.instances.create
spanner.instances.update
spanner.instances.delete
spanner.instances.get
spanner.instances.list
spanner.instances.getIamPolicy
spanner.instances.setIamPolicy
```

### Backup Operations
```
spanner.backups.create
spanner.backups.get
spanner.backups.list
spanner.backups.update
spanner.backups.delete
spanner.backups.restoreDatabase
```

### Session Management
```
spanner.sessions.create
spanner.sessions.delete
spanner.sessions.get
spanner.sessions.list
```

## Implementation Patterns

### 1. Service Account Configuration

```bash
# Create service account for application
gcloud iam service-accounts create app-spanner-sa \
    --display-name="Application Spanner Service Account"

# Grant database user role at database level
gcloud spanner databases add-iam-policy-binding my-database \
    --instance=my-instance \
    --member="serviceAccount:app-spanner-sa@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/spanner.databaseUser"
```

### 2. Least Privilege Access

```bash
# Read-only service account for analytics
gcloud iam service-accounts create analytics-sa \
    --display-name="Analytics Read-Only"

gcloud spanner databases add-iam-policy-binding analytics-db \
    --instance=production \
    --member="serviceAccount:analytics-sa@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/spanner.databaseReader"

# Admin account for schema migrations
gcloud iam service-accounts create migration-sa \
    --display-name="Schema Migration Account"

gcloud spanner instances add-iam-policy-binding production \
    --member="serviceAccount:migration-sa@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/spanner.databaseAdmin"
```

### 3. Custom Roles

```yaml
# custom-spanner-role.yaml
title: "Custom Spanner Data Analyst"
description: "Read data and execute analytical queries"
stage: "GA"
includedPermissions:
- spanner.databases.select
- spanner.databases.beginReadOnlyTransaction
- spanner.sessions.create
- spanner.sessions.delete
- spanner.sessions.get
- spanner.instances.get
- spanner.databases.get
- spanner.databases.list
```

```bash
# Create custom role
gcloud iam roles create customSpannerDataAnalyst \
    --project=PROJECT_ID \
    --file=custom-spanner-role.yaml

# Assign custom role
gcloud spanner databases add-iam-policy-binding my-database \
    --instance=my-instance \
    --member="user:<EMAIL>" \
    --role="projects/PROJECT_ID/roles/customSpannerDataAnalyst"
```

### 4. Conditional Access

```bash
# Grant access only during business hours
gcloud spanner databases add-iam-policy-binding sensitive-db \
    --instance=production \
    --member="user:<EMAIL>" \
    --role="roles/spanner.databaseUser" \
    --condition='expression=request.time >= timestamp("2024-01-01T09:00:00Z") && request.time <= timestamp("2024-12-31T18:00:00Z") && request.time.getDayOfWeek() >= 1 && request.time.getDayOfWeek() <= 5,title=BusinessHours,description=Access only during business hours'
```

## Security Best Practices

### 1. Access Control Strategy

```bash
# Hierarchical access control
Project Level:
├── Spanner Admin (DevOps team)
├── Instance Level:
│   ├── Database Admin (DBA team)
│   └── Backup Admin (Operations)
└── Database Level:
    ├── Database User (Applications)
    └── Database Reader (Analytics)
```

### 2. Service Account Management

```bash
# Create dedicated service accounts per environment
gcloud iam service-accounts create prod-app-sa
gcloud iam service-accounts create staging-app-sa
gcloud iam service-accounts create dev-app-sa

# Use workload identity for GKE
kubectl annotate serviceaccount app-sa \
    iam.gke.io/gcp-service-account=prod-app-sa@PROJECT_ID.iam.gserviceaccount.com
```

### 3. Audit Logging

```yaml
# Enable audit logging for Spanner
auditConfigs:
- service: spanner.googleapis.com
  auditLogConfigs:
  - logType: ADMIN_READ
  - logType: DATA_READ
  - logType: DATA_WRITE
```

### 4. Regular Access Reviews

```bash
# List all IAM bindings for instance
gcloud spanner instances get-iam-policy my-instance

# List database-level permissions
gcloud spanner databases get-iam-policy my-database \
    --instance=my-instance

# Export for review
gcloud asset export \
    --asset-types=spanner.googleapis.com/Instance,spanner.googleapis.com/Database \
    --content-type=iam-policy \
    --output-path=gs://audit-bucket/spanner-iam-audit.json
```

## Production Security Patterns

### 1. Multi-Environment Access

```bash
# Production - Restricted access
gcloud spanner databases add-iam-policy-binding prod-db \
    --instance=prod-instance \
    --member="group:<EMAIL>" \
    --role="roles/spanner.databaseUser"

# Staging - Broader access
gcloud spanner databases add-iam-policy-binding staging-db \
    --instance=staging-instance \
    --member="group:<EMAIL>" \
    --role="roles/spanner.databaseUser"
```

### 2. Break-Glass Access

```bash
# Emergency access with conditions
gcloud spanner databases add-iam-policy-binding critical-db \
    --instance=production \
    --member="group:<EMAIL>" \
    --role="roles/spanner.databaseAdmin" \
    --condition='expression=request.auth.claims["break_glass"] == true,title=BreakGlass,description=Emergency access only'
```

### 3. Automated Access Management

```python
# Python script for automated IAM management
from google.cloud import spanner_admin_instance_v1
from google.iam.v1 import iam_policy_pb2

def grant_temporary_access(user_email, database_id, hours=8):
    """Grant temporary database access."""
    client = spanner_admin_instance_v1.InstanceAdminClient()
    
    # Get current policy
    database_name = f"projects/{PROJECT}/instances/{INSTANCE}/databases/{database_id}"
    policy = client.get_iam_policy(resource=database_name)
    
    # Add temporary binding with condition
    binding = policy.bindings.add()
    binding.role = "roles/spanner.databaseUser"
    binding.members.append(f"user:{user_email}")
    
    # Set time-based condition
    expiry_time = datetime.now() + timedelta(hours=hours)
    binding.condition.expression = f'request.time < timestamp("{expiry_time.isoformat()}Z")'
    binding.condition.title = "TemporaryAccess"
    
    # Update policy
    client.set_iam_policy(resource=database_name, policy=policy)
```

### 4. Cross-Project Access

```bash
# Grant access to service account from another project
gcloud spanner databases add-iam-policy-binding shared-db \
    --instance=central-instance \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/spanner.databaseReader"
```

## Monitoring and Compliance

### 1. Access Monitoring

```sql
-- Query audit logs for access patterns
SELECT
  timestamp,
  protoPayload.authenticationInfo.principalEmail as user,
  protoPayload.methodName as operation,
  protoPayload.resourceName as resource
FROM
  `PROJECT_ID.audit_logs.cloudaudit_googleapis_com_data_access`
WHERE
  protoPayload.serviceName = "spanner.googleapis.com"
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
ORDER BY
  timestamp DESC
```

### 2. Compliance Automation

```python
# Check for overly permissive access
def audit_spanner_permissions():
    """Audit Spanner IAM permissions for compliance."""
    findings = []
    
    # Check for basic roles
    for binding in policy.bindings:
        if binding.role in ['roles/owner', 'roles/editor']:
            findings.append({
                'severity': 'HIGH',
                'finding': f'Basic role {binding.role} assigned',
                'members': list(binding.members)
            })
    
    # Check for allUsers or allAuthenticatedUsers
    for binding in policy.bindings:
        if 'allUsers' in binding.members or 'allAuthenticatedUsers' in binding.members:
            findings.append({
                'severity': 'CRITICAL',
                'finding': 'Public access granted',
                'role': binding.role
            })
    
    return findings
```

### 3. Regular Permission Audits

```bash
# Generate comprehensive IAM report
#!/bin/bash

echo "Spanner IAM Audit Report - $(date)"
echo "================================"

# List all instances
for instance in $(gcloud spanner instances list --format="value(name)"); do
    echo -e "\nInstance: $instance"
    echo "Instance IAM Policy:"
    gcloud spanner instances get-iam-policy $instance
    
    # List databases in instance
    for database in $(gcloud spanner databases list --instance=$instance --format="value(name)"); do
        echo -e "\n  Database: $database"
        echo "  Database IAM Policy:"
        gcloud spanner databases get-iam-policy $database --instance=$instance
    done
done
```

## Troubleshooting Common Issues

### Permission Denied Errors

```bash
# Debug permission issues
gcloud spanner databases test-iam-permissions my-database \
    --instance=my-instance \
    --permissions=spanner.databases.select,spanner.databases.updateData

# Check effective permissions for service account
gcloud projects get-iam-policy PROJECT_ID \
    --flatten="bindings[].members" \
    --filter="bindings.members:serviceAccount:SA_EMAIL"
```

### Access Propagation Delays

```python
# Retry logic for IAM propagation
import time
from google.api_core import retry

@retry.Retry(deadline=60)
def verify_access(client, database):
    """Verify access with retry for IAM propagation."""
    try:
        with client.database(database).snapshot() as snapshot:
            snapshot.execute_sql("SELECT 1")
        return True
    except Exception as e:
        if "PERMISSION_DENIED" in str(e):
            raise  # Retry
        return False
```