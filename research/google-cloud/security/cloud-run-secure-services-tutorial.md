# Securing Cloud Run Services Tutorial

## Overview

This tutorial demonstrates how to create a secure two-service application on Cloud Run with proper authentication and authorization. The architecture consists of:

- **Public "frontend" editor service**: Accepts user requests
- **Private "backend" renderer service**: Only accessible through authenticated requests from the frontend

## Key Security Components

### 1. Service Architecture

```
User → Frontend Service (Public) → Backend Service (Private)
         ↓                           ↓
    Service Account A          Service Account B
```

### 2. Authentication Mechanism

- **IAM-based service-to-service authentication**: Restricts who can invoke services
- **Identity tokens**: Generated for authenticated requests
- **Google Cloud metadata server**: Used to obtain identity tokens

### 3. Service Account Configuration

Each service runs with its own service account following the principle of least privilege:

- **Frontend service account**: Has permission to invoke the backend service
- **Backend service account**: Has minimal permissions, cannot invoke other services

## Implementation Steps

### Step 1: Set Up Service Accounts

```bash
# Create frontend service account
gcloud iam service-accounts create frontend-sa \
    --display-name "Frontend Service Account"

# Create backend service account  
gcloud iam service-accounts create backend-sa \
    --display-name "Backend Service Account"

# Grant frontend permission to invoke backend
gcloud run services add-iam-policy-binding backend-service \
    --member="serviceAccount:frontend-sa@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/run.invoker"
```

### Step 2: Build Container Images

Both services should be containerized with proper security configurations:

```dockerfile
# Use minimal base images
FROM gcr.io/distroless/nodejs18-debian11

# Run as non-root user
USER nonroot

# Copy only necessary files
COPY --chown=nonroot:nonroot app.js .
```

### Step 3: Deploy Services with Security

```bash
# Deploy backend service (private by default)
gcloud run deploy backend-service \
    --image gcr.io/PROJECT_ID/backend \
    --service-account backend-sa@PROJECT_ID.iam.gserviceaccount.com \
    --no-allow-unauthenticated

# Deploy frontend service (public)
gcloud run deploy frontend-service \
    --image gcr.io/PROJECT_ID/frontend \
    --service-account frontend-sa@PROJECT_ID.iam.gserviceaccount.com \
    --set-env-vars BACKEND_URL=https://backend-service-xxxxx.run.app \
    --allow-unauthenticated
```

### Step 4: Configure Authenticated Communication

In the frontend service, make authenticated requests to the backend:

```javascript
// Obtain identity token from metadata server
const metadataServerURL = 'http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity?audience=';
const audience = process.env.BACKEND_URL;

const response = await fetch(metadataServerURL + audience, {
  headers: {
    'Metadata-Flavor': 'Google'
  }
});

const identityToken = await response.text();

// Make authenticated request to backend
const backendResponse = await fetch(process.env.BACKEND_URL + '/render', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${identityToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ markdown: userInput })
});
```

### Step 5: Establish Environment Variables

Configure services with necessary environment variables:

```bash
# Frontend service needs backend URL
gcloud run services update frontend-service \
    --set-env-vars BACKEND_URL=https://backend-service-xxxxx.run.app

# Backend service operates independently
# No external service URLs needed
```

## Security Best Practices

### 1. Principle of Least Privilege
- Create dedicated service accounts for each service
- Grant only necessary permissions
- Avoid using default service accounts

### 2. Service Isolation
- Keep backend services private (no public access)
- Use IAM policies to control service-to-service access
- Implement defense in depth

### 3. Authentication Best Practices
- Always verify identity tokens
- Use short-lived tokens
- Implement proper error handling for authentication failures

### 4. Network Security
- Use HTTPS for all communications
- Configure VPC Service Controls if needed
- Implement ingress/egress restrictions

### 5. Monitoring and Auditing
- Enable Cloud Audit Logs
- Monitor authentication failures
- Set up alerts for suspicious activities

## Common Patterns

### Pattern 1: Multi-tier Architecture
```
Public API Gateway → Private Business Logic → Private Database Service
```

### Pattern 2: Microservices with Selective Access
```
Service A → Service B (allowed)
Service A ✗ Service C (blocked)
Service B → Service C (allowed)
```

### Pattern 3: External Integration
```
Cloud Run Service → Cloud Function → External API
                 ↓
          Service Account with
          Workload Identity
```

## Limitations and Considerations

### Cannot Combine Authentication Methods
- Choose either IAM-based OR ID token authentication
- Cannot use both simultaneously on the same service

### End-User Authentication
- This pattern doesn't cover end-user authentication
- For end-user auth, integrate with:
  - Firebase Authentication
  - Identity Platform
  - Custom JWT validation

### Token Refresh
- Identity tokens expire after 1 hour
- Implement token refresh logic for long-running operations
- Cache tokens appropriately

## Troubleshooting

### Common Issues

1. **403 Forbidden Errors**
   - Check IAM bindings
   - Verify service account permissions
   - Ensure correct audience in token request

2. **Invalid Token Errors**
   - Verify token hasn't expired
   - Check audience matches service URL
   - Ensure proper token format

3. **Service Account Issues**
   - Verify service account exists
   - Check service is using correct service account
   - Ensure metadata server is accessible