# Cloud Run Authentication Overview

## Default Security Model

Cloud Run implements a **secure-by-default** approach:

- **Private deployment by default**: All services require authentication credentials
- **IAM-based security**: Leverages Google Cloud Identity and Access Management
- **Restricted default access**: Only Project Owners, Editors, Cloud Run Admins, and Cloud Run Invokers can access services

## Authentication Use Cases

### 1. Public (Unauthenticated) Access

**When to use:**
- Public APIs or websites
- Services that need to be accessible without credentials
- Content delivery endpoints

**Implementation:**
```bash
# Allow public access
gcloud run services add-iam-policy-binding SERVICE_NAME \
    --member="allUsers" \
    --role="roles/run.invoker"
```

**Security considerations:**
- Implement rate limiting
- Use Cloud Armor for DDoS protection
- Monitor for abuse
- Validate all inputs

### 2. Developer Access

**When to use:**
- Local development and testing
- CI/CD pipelines
- Administrative access

**Implementation methods:**
- **gcloud CLI**: Automatic credential handling
- **Local testing**: Use Application Default Credentials
- **Service accounts**: For automated systems

**Example:**
```bash
# Authenticate as developer
gcloud auth login

# Test service invocation
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
    https://SERVICE_URL
```

### 3. Service-to-Service Authentication

**When to use:**
- Microservices architecture
- Backend service communication
- Internal API calls

**Implementation approach:**
1. Create dedicated service accounts
2. Grant minimal necessary permissions
3. Use metadata server for token generation

**Example pattern:**
```python
import requests
from google.auth import compute_engine
from google.auth.transport.requests import Request

# Get credentials from metadata server
credentials = compute_engine.IDTokenCredentials(
    request=Request(),
    target_audience=SERVICE_URL
)

# Make authenticated request
credentials.refresh()
response = requests.get(
    SERVICE_URL,
    headers={'Authorization': f'Bearer {credentials.token}'}
)
```

### 4. End-User Authentication

**When to use:**
- Web applications with user login
- Mobile applications
- Multi-tenant services

**Implementation options:**
- **Firebase Authentication**: For mobile/web apps
- **Identity Platform**: Enterprise identity management
- **Custom JWT validation**: For existing auth systems
- **Identity-Aware Proxy (IAP)**: For internal applications

**Example flow:**
```javascript
// Client-side authentication
const userToken = await firebase.auth().currentUser.getIdToken();

// Pass token to Cloud Run service
const response = await fetch(SERVICE_URL, {
    headers: {
        'Authorization': `Bearer ${userToken}`
    }
});
```

## Authentication Methods Comparison

| Method | Use Case | Security Level | Complexity |
|--------|----------|----------------|------------|
| Public Access | Open APIs | Low | Simple |
| IAM + Service Accounts | Service-to-service | High | Medium |
| Firebase Auth | Consumer apps | High | Medium |
| Identity Platform | Enterprise apps | Very High | Complex |
| IAP | Internal tools | Very High | Medium |

## Security Best Practices

### 1. Default Deny
- Keep services private by default
- Only allow public access when necessary
- Regularly audit access policies

### 2. Least Privilege
- Create dedicated service accounts
- Grant minimal required permissions
- Avoid using default service accounts

### 3. Token Management
- Use short-lived tokens
- Implement token refresh logic
- Never hardcode credentials

### 4. Defense in Depth
- Combine authentication with authorization
- Implement request validation
- Use network security controls

### 5. Monitoring
- Enable audit logging
- Monitor authentication failures
- Set up alerts for anomalies

## Implementation Checklist

- [ ] Determine authentication requirements
- [ ] Choose appropriate authentication method
- [ ] Create necessary service accounts
- [ ] Configure IAM policies
- [ ] Implement token handling
- [ ] Test authentication flows
- [ ] Set up monitoring and alerts
- [ ] Document authentication process

## Common Pitfalls to Avoid

1. **Over-permissioning**: Granting broader access than needed
2. **Token expiration**: Not handling token refresh
3. **Mixing methods**: Trying to combine incompatible auth methods
4. **Hardcoding**: Embedding credentials in code
5. **Missing validation**: Not validating tokens properly

## Integration with Other GCP Services

### VPC Service Controls
- Add additional network-based security
- Create service perimeters
- Control data egress

### Cloud Armor
- Protect against DDoS attacks
- Implement WAF rules
- Geographic restrictions

### Secret Manager
- Store API keys securely
- Manage certificates
- Rotate credentials automatically

### Cloud KMS
- Encrypt sensitive data
- Manage encryption keys
- Support compliance requirements