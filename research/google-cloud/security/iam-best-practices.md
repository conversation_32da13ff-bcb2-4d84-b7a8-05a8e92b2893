# IAM and Service Account Security Best Practices

## Core Security Principles

### 1. Least Privilege

**Grant the most limited roles that meet specific needs**
- Use predefined or custom roles instead of basic roles (Owner, Editor, Viewer)
- Grant roles at the smallest scope necessary
- Treat each application component as a separate trust boundary
- Regularly review and remove unused permissions

```bash
# BAD: Granting Editor role at project level
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="user:<EMAIL>" \
    --role="roles/editor"

# GOOD: Grant specific role at resource level
gcloud spanner databases add-iam-policy-binding my-database \
    --instance=my-instance \
    --member="user:<EMAIL>" \
    --role="roles/spanner.databaseUser"
```

### 2. Service Account Strategy

**Create dedicated service accounts for each purpose**
```bash
# Create service accounts with descriptive names
gcloud iam service-accounts create frontend-api-sa \
    --display-name="Frontend API Service Account" \
    --description="Handles API requests from frontend"

gcloud iam service-accounts create backend-processor-sa \
    --display-name="Backend Processor Service Account" \
    --description="Processes background jobs"

gcloud iam service-accounts create data-analytics-sa \
    --display-name="Data Analytics Service Account" \
    --description="Read-only access for analytics"
```

### 3. Avoid Service Account Keys

**Security risks of service account keys:**
- Privilege escalation potential
- Information disclosure
- Non-repudiation issues
- Malicious credential configurations

**Alternatives to service account keys:**
```bash
# Use Workload Identity for GKE
kubectl annotate serviceaccount app-sa \
    iam.gke.io/gcp-service-account=app-sa@PROJECT_ID.iam.gserviceaccount.com

# Use metadata server for Compute Engine
curl -H "Metadata-Flavor: Google" \
    "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token"

# Use impersonation for local development
gcloud auth application-default login --impersonate-service-account=SA_EMAIL
```

## Implementation Guidelines

### 1. Service Account Creation and Management

```python
from google.cloud import iam_admin_v1
from google.iam.v1 import iam_policy_pb2

def create_secure_service_account(project_id, sa_name, description):
    """Create a service account with secure defaults."""
    client = iam_admin_v1.IAMClient()
    
    # Create service account
    service_account = client.create_service_account(
        request={
            "name": f"projects/{project_id}",
            "account_id": sa_name,
            "service_account": {
                "display_name": sa_name,
                "description": description
            }
        }
    )
    
    # Don't create keys by default
    print(f"Created service account: {service_account.email}")
    print("No keys created - use workload identity or metadata server")
    
    return service_account
```

### 2. Role Assignment with Conditions

```bash
# Time-bound access
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="user:<EMAIL>" \
    --role="roles/bigquery.dataViewer" \
    --condition="expression=request.time < timestamp('2024-12-31T00:00:00Z'),title=Temporary Access,description=Access expires end of year"

# IP-based restrictions
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:external-api@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.objectViewer" \
    --condition="expression=request.auth.access_levels.in(['TRUSTED_IP_RANGES']),title=Trusted IPs Only"
```

### 3. Custom Roles for Specific Needs

```yaml
# custom-role-definition.yaml
title: "Minimal Spanner Reader"
description: "Read-only access to specific Spanner operations"
stage: "GA"
includedPermissions:
- spanner.databases.select
- spanner.databases.get
- spanner.instances.get
- spanner.sessions.create
- spanner.sessions.delete
```

```bash
# Create custom role
gcloud iam roles create minimalSpannerReader \
    --project=PROJECT_ID \
    --file=custom-role-definition.yaml
```

## Security Patterns

### 1. Token Broker Pattern

```python
class TokenBroker:
    """Implement token broker for temporary privilege elevation."""
    
    def __init__(self, supervisor_sa):
        self.supervisor_sa = supervisor_sa
        self.credentials = self._get_supervisor_credentials()
    
    def get_token_for_service(self, target_sa, scopes):
        """Get short-lived token for target service account."""
        # Supervisor impersonates target service account
        from google.auth import impersonated_credentials
        
        target_credentials = impersonated_credentials.Credentials(
            source_credentials=self.credentials,
            target_principal=target_sa,
            target_scopes=scopes,
            lifetime=3600  # 1 hour
        )
        
        target_credentials.refresh()
        return target_credentials.token
```

### 2. Least Privilege Automation

```python
def analyze_unused_permissions(project_id):
    """Identify unused permissions using IAM Recommender."""
    from google.cloud import recommender_v1
    
    client = recommender_v1.RecommenderClient()
    parent = f"projects/{project_id}/locations/global/recommenders/google.iam.policy.Recommender"
    
    recommendations = client.list_recommendations(parent=parent)
    
    high_risk_unused = []
    for recommendation in recommendations:
        if recommendation.priority == "P1":  # High priority
            high_risk_unused.append({
                "resource": recommendation.content.resource_name,
                "member": recommendation.content.member,
                "unused_role": recommendation.content.role,
                "risk_score": recommendation.priority
            })
    
    return high_risk_unused
```

### 3. Service Account Key Rotation

```bash
#!/bin/bash
# Automated key rotation script

SA_EMAIL="app-sa@PROJECT_ID.iam.gserviceaccount.com"

# List existing keys
OLD_KEYS=$(gcloud iam service-accounts keys list \
    --iam-account=$SA_EMAIL \
    --filter="validAfterTime<-P90D" \
    --format="value(name)")

# Create new key
gcloud iam service-accounts keys create new-key.json \
    --iam-account=$SA_EMAIL

# Update application with new key
# (Application-specific deployment steps here)

# Delete old keys after verification
for KEY in $OLD_KEYS; do
    gcloud iam service-accounts keys delete $KEY \
        --iam-account=$SA_EMAIL --quiet
done
```

## Monitoring and Compliance

### 1. Audit Policy Changes

```sql
-- Query for IAM policy changes
SELECT
  timestamp,
  protoPayload.authenticationInfo.principalEmail as who_changed,
  protoPayload.methodName as what_changed,
  protoPayload.request.policy.bindings as new_bindings,
  resource.labels.project_id
FROM
  `PROJECT_ID.DATASET.cloudaudit_googleapis_com_activity`
WHERE
  protoPayload.methodName LIKE "%.setIamPolicy"
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
ORDER BY
  timestamp DESC
```

### 2. Service Account Usage Tracking

```python
def track_service_account_usage():
    """Monitor service account authentication patterns."""
    query = """
    SELECT
      protoPayload.authenticationInfo.serviceAccountKeyName as key_used,
      COUNT(*) as usage_count,
      ARRAY_AGG(DISTINCT protoPayload.methodName LIMIT 10) as methods,
      MAX(timestamp) as last_used
    FROM
      `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
    WHERE
      protoPayload.authenticationInfo.serviceAccountKeyName IS NOT NULL
      AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
    GROUP BY
      key_used
    ORDER BY
      usage_count DESC
    """
    
    results = bigquery_client.query(query)
    
    # Alert on unused keys
    for row in results:
        if row.usage_count == 0:
            alert_unused_key(row.key_used)
```

### 3. Privileged Access Management

```python
class PrivilegedAccessManager:
    """Manage temporary elevated access."""
    
    def grant_temporary_access(self, user, role, resource, duration_hours, justification):
        """Grant time-bound privileged access."""
        import datetime
        
        expiry = datetime.datetime.utcnow() + datetime.timedelta(hours=duration_hours)
        
        condition = {
            "expression": f'request.time < timestamp("{expiry.isoformat()}Z")',
            "title": "Temporary Elevated Access",
            "description": f"Justification: {justification}"
        }
        
        # Add policy binding with condition
        policy = self.get_iam_policy(resource)
        binding = {
            "role": role,
            "members": [f"user:{user}"],
            "condition": condition
        }
        policy["bindings"].append(binding)
        
        self.set_iam_policy(resource, policy)
        
        # Log the grant
        self.log_access_grant(user, role, resource, duration_hours, justification)
        
        # Schedule automatic revocation
        self.schedule_revocation(user, role, resource, expiry)
```

## Organization-Level Policies

### 1. Enforce Security Constraints

```yaml
# org-policy-constraints.yaml
name: organizations/ORG_ID/policies/iam.disableServiceAccountKeyCreation
spec:
  rules:
  - enforce: true
    condition:
      expression: |
        !resource.matchTag("projects/PROJECT_ID/tags/allow-sa-keys")
      title: "Block SA key creation except for tagged resources"
```

### 2. Require MFA for Privileged Operations

```yaml
name: organizations/ORG_ID/policies/iam.requireMfaForAdminOperations
spec:
  rules:
  - enforce: true
    values:
      deniedValues:
      - roles/owner
      - roles/editor
      - roles/iam.securityAdmin
    condition:
      expression: |
        !request.auth.claims.mfa_authenticated
      title: "Require MFA for admin roles"
```

## Incident Response

### 1. Compromised Service Account Response

```python
def respond_to_compromised_sa(service_account_email):
    """Immediate response to compromised service account."""
    
    # 1. Disable all keys
    keys = list_service_account_keys(service_account_email)
    for key in keys:
        disable_service_account_key(key)
    
    # 2. Remove all IAM bindings
    remove_all_iam_bindings(service_account_email)
    
    # 3. Create audit report
    generate_sa_activity_report(service_account_email)
    
    # 4. Notify security team
    notify_security_team({
        "incident": "compromised_service_account",
        "account": service_account_email,
        "actions_taken": ["keys_disabled", "bindings_removed", "audit_generated"]
    })
```

### 2. Emergency Access Procedures

```bash
#!/bin/bash
# Break-glass emergency access

EMERGENCY_USER=$1
JUSTIFICATION=$2

# Grant temporary owner access with audit
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="user:${EMERGENCY_USER}" \
    --role="roles/owner" \
    --condition="expression=request.time < timestamp('$(date -u -d '+4 hours' --iso-8601=seconds)'),title=Emergency Access,description=${JUSTIFICATION}"

# Log emergency access
echo "$(date): Emergency access granted to ${EMERGENCY_USER} - ${JUSTIFICATION}" >> /var/log/emergency_access.log

# Send notifications
send_emergency_notification "${EMERGENCY_USER}" "${JUSTIFICATION}"
```

## Continuous Improvement

### 1. Regular Security Reviews

```python
def quarterly_iam_review():
    """Perform quarterly IAM security review."""
    
    findings = {
        "overprivileged_accounts": find_overprivileged_accounts(),
        "unused_service_accounts": find_unused_service_accounts(),
        "stale_permissions": find_stale_permissions(),
        "basic_roles_usage": find_basic_role_usage(),
        "external_members": find_external_members()
    }
    
    # Generate recommendations
    recommendations = generate_security_recommendations(findings)
    
    # Create tickets for remediation
    for rec in recommendations:
        create_remediation_ticket(rec)
    
    return findings, recommendations
```

### 2. Metrics and KPIs

```sql
-- IAM security metrics dashboard
WITH metrics AS (
  SELECT
    -- Least privilege score
    COUNTIF(role IN ('roles/owner', 'roles/editor')) / COUNT(*) as basic_role_ratio,
    
    -- Service account hygiene
    COUNTIF(member LIKE 'serviceAccount:%' AND NOT REGEXP_CONTAINS(member, '@.*\.gserviceaccount\.com$')) / 
    COUNTIF(member LIKE 'serviceAccount:%') as non_gcp_sa_ratio,
    
    -- External access
    COUNTIF(NOT REGEXP_CONTAINS(member, '@(example\.com|.*\.gserviceaccount\.com)$')) / 
    COUNT(*) as external_member_ratio
    
  FROM
    iam_bindings_snapshot
)
SELECT
  ROUND((1 - basic_role_ratio) * 100, 2) as least_privilege_score,
  ROUND((1 - non_gcp_sa_ratio) * 100, 2) as sa_hygiene_score,
  ROUND((1 - external_member_ratio) * 100, 2) as access_control_score
FROM
  metrics
```