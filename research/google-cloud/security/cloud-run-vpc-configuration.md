# Cloud Run VPC Configuration and Network Security

## Overview

VPC connectivity enables Cloud Run services to access resources in your Virtual Private Cloud (VPC) network securely. There are two main approaches:

1. **Direct VPC Egress** (Recommended): Native VPC connectivity without additional infrastructure
2. **VPC Connectors**: Traditional approach using Serverless VPC Access connectors

## Direct VPC Egress (Recommended)

### Benefits
- No additional infrastructure or costs
- Better performance
- Simpler configuration
- Native VPC integration

### Configuration
```bash
# Deploy with Direct VPC egress
gcloud run deploy SERVICE_NAME \
    --image IMAGE_URL \
    --vpc-egress=all-traffic \
    --network=VPC_NETWORK \
    --subnet=SUBNET_NAME \
    --region=REGION
```

## VPC Connectors (Legacy Approach)

### When to Use
- Existing infrastructure using connectors
- Specific compliance requirements
- Gradual migration scenarios

### Setup Requirements

#### 1. Enable APIs
```bash
gcloud services enable vpcaccess.googleapis.com
gcloud services enable compute.googleapis.com
```

#### 2. Create VPC Connector
```bash
# Create connector with dedicated subnet
gcloud compute networks vpc-access connectors create CONNECTOR_NAME \
    --region=REGION \
    --subnet=SUBNET_NAME \
    --subnet-project=PROJECT_ID \
    --min-instances=2 \
    --max-instances=10 \
    --machine-type=e2-micro
```

#### 3. Configure Cloud Run Service
```bash
# Deploy service with VPC connector
gcloud run deploy SERVICE_NAME \
    --image=IMAGE_URL \
    --vpc-connector=CONNECTOR_NAME \
    --vpc-egress=all-traffic
```

## Network Architecture

### Subnet Requirements
- **Size**: Minimum `/28` subnet (16 IP addresses)
- **Isolation**: Dedicated subnet with no other resources
- **IP Range**: Must not overlap with existing ranges

### Example Network Design
```
VPC Network: my-vpc
├── Subnet: default (10.0.0.0/20) - General resources
├── Subnet: cloud-run (*********/28) - VPC connector
└── Subnet: private-services (********/20) - Internal services
```

## Egress Configuration Options

### 1. Private IP Only (Default)
```yaml
# Only routes RFC 1918 and RFC 6598 addresses through VPC
vpc-egress: private-ranges-only
```

**Routes through VPC:**
- 10.0.0.0/8
- **********/12
- ***********/16
- **********/10

### 2. All Traffic
```yaml
# Routes all outbound traffic through VPC
vpc-egress: all-traffic
```

**Use cases:**
- Enforcing egress firewall rules
- Static IP requirements
- Complete traffic control

## Security Best Practices

### 1. Network Isolation
```bash
# Create isolated subnet for Cloud Run
gcloud compute networks subnets create cloud-run-subnet \
    --network=my-vpc \
    --range=*********/28 \
    --region=us-central1 \
    --purpose=VPC_CONNECTOR
```

### 2. Firewall Rules
```bash
# Allow Cloud Run to access specific resources
gcloud compute firewall-rules create allow-cloud-run-to-db \
    --network=my-vpc \
    --allow=tcp:3306 \
    --source-ranges=*********/28 \
    --destination-ranges=********/24 \
    --direction=INGRESS

# Deny all other traffic
gcloud compute firewall-rules create deny-cloud-run-default \
    --network=my-vpc \
    --action=DENY \
    --rules=all \
    --source-ranges=*********/28 \
    --priority=1000
```

### 3. Network Tags
```bash
# Tag-based access control
gcloud compute instances create backend-server \
    --network=my-vpc \
    --tags=allow-from-cloud-run

# Firewall rule using tags
gcloud compute firewall-rules create allow-tagged-access \
    --network=my-vpc \
    --allow=tcp:8080 \
    --source-ranges=*********/28 \
    --target-tags=allow-from-cloud-run
```

## Access Control Patterns

### 1. Service-Specific Access
```bash
# Create connector with limited access
gcloud compute networks vpc-access connectors create limited-connector \
    --region=us-central1 \
    --subnet=connector-subnet \
    --min-instances=2 \
    --max-instances=3

# Update connector access
gcloud compute networks vpc-access connectors update limited-connector \
    --region=us-central1 \
    --add-authorized-principals=serviceAccount:SERVICE_ACCOUNT_EMAIL
```

### 2. Organization Policies
```yaml
# Restrict VPC connector usage
name: organizations/ORG_ID/policies/run.allowedVPCConnectors
spec:
  rules:
    - allowAll: false
      values:
        allowed_values:
          - projects/PROJECT_ID/locations/REGION/connectors/CONNECTOR_NAME
```

### 3. Private Google Access
```bash
# Enable Private Google Access on subnet
gcloud compute networks subnets update SUBNET_NAME \
    --region=REGION \
    --enable-private-ip-google-access
```

## Common Use Cases

### 1. Accessing Cloud SQL
```python
import sqlalchemy

def connect_to_cloudsql():
    # Connection through VPC using private IP
    engine = sqlalchemy.create_engine(
        sqlalchemy.engine.url.URL(
            drivername="postgresql+pg8000",
            username=DB_USER,
            password=DB_PASS,
            host="********",  # Private IP
            port=5432,
            database=DB_NAME
        )
    )
    return engine
```

### 2. Connecting to Memorystore
```python
import redis

# Connect to Redis via private IP
redis_client = redis.Redis(
    host='*********',  # Private IP
    port=6379,
    decode_responses=True
)
```

### 3. On-Premises Access
```bash
# Configure Cloud VPN for on-premises connectivity
gcloud compute vpn-gateways create my-vpn-gateway \
    --network=my-vpc \
    --region=us-central1

# Cloud Run can now access on-premises resources
# through the VPC connector
```

## Monitoring and Troubleshooting

### 1. Connector Metrics
```bash
# View connector metrics
gcloud compute networks vpc-access connectors describe CONNECTOR_NAME \
    --region=REGION

# Monitor throughput
gcloud monitoring metrics list \
    --filter="metric.type:vpcaccess.googleapis.com/connector/sent_bytes_count"
```

### 2. Connection Testing
```python
# Test VPC connectivity from Cloud Run
import requests
import socket

def test_vpc_connectivity():
    try:
        # Test DNS resolution
        ip = socket.gethostbyname('internal-service.local')
        print(f"Resolved to: {ip}")
        
        # Test connection
        response = requests.get('http://internal-service.local:8080')
        return response.status_code == 200
    except Exception as e:
        print(f"Connection failed: {e}")
        return False
```

### 3. Common Issues

**Issue: Connection timeouts**
```bash
# Check firewall rules
gcloud compute firewall-rules list \
    --filter="network:my-vpc" \
    --format="table(name,sourceRanges,allowed[].ports[])"

# Verify routes
gcloud compute routes list \
    --filter="network:my-vpc"
```

**Issue: DNS resolution failures**
```bash
# Enable Cloud DNS private zones
gcloud dns managed-zones create private-zone \
    --dns-name="internal.local." \
    --networks=my-vpc \
    --visibility=private
```

## Performance Optimization

### 1. Connector Sizing
```bash
# High-throughput configuration
gcloud compute networks vpc-access connectors create high-perf-connector \
    --region=us-central1 \
    --subnet=connector-subnet \
    --min-instances=10 \
    --max-instances=100 \
    --machine-type=f1-micro
```

### 2. Regional Deployment
- Deploy connectors in the same region as Cloud Run services
- Co-locate with frequently accessed resources
- Use regional load balancing

### 3. Connection Pooling
```python
# Implement connection pooling for database access
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=0,
    pool_pre_ping=True,
    pool_recycle=3600
)
```

## Cost Optimization

### 1. Connector Costs
- **Base charge**: ~$0.36/hour per connector
- **Data processing**: $0.01/GB
- **Machine type impact**: Higher machine types increase costs

### 2. Cost-Saving Strategies
- Use Direct VPC egress when possible (no additional costs)
- Right-size connector instances
- Monitor and optimize data transfer
- Delete unused connectors

### 3. Billing Alerts
```bash
# Set up budget alert for VPC Access
gcloud billing budgets create \
    --billing-account=BILLING_ACCOUNT_ID \
    --display-name="VPC Access Budget" \
    --budget-amount=100 \
    --threshold-rule=percent=0.8
```