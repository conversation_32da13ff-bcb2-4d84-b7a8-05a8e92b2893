# Cloud Run Service-to-Service Authentication

## Overview

Service-to-service authentication in Cloud Run enables secure communication between services using Google-signed OpenID Connect ID tokens. This ensures that only authorized services can invoke your Cloud Run services.

## Communication Patterns

### 1. Asynchronous Communication

**Supported Services:**
- **Cloud Tasks**: For reliable task execution
- **Pub/Sub**: For event-driven architectures
- **Cloud Scheduler**: For scheduled invocations
- **Eventarc**: For event routing

These services handle authentication automatically when configured with appropriate service accounts.

### 2. Synchronous Communication

For direct service-to-service calls, you must:
1. Configure IAM permissions
2. Acquire ID tokens
3. Include tokens in request headers

## Implementation Steps

### Step 1: Set Up Service Account

```bash
# Create service account for calling service
gcloud iam service-accounts create calling-service-sa \
    --display-name="Calling Service Account"

# Deploy calling service with this service account
gcloud run deploy calling-service \
    --service-account=calling-service-sa@PROJECT_ID.iam.gserviceaccount.com
```

### Step 2: Grant Permissions

```bash
# Grant Cloud Run Invoker role to calling service
gcloud run services add-iam-policy-binding receiving-service \
    --member="serviceAccount:calling-service-sa@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/run.invoker"
```

### Step 3: Acquire ID Token

#### Method 1: Using Authentication Libraries

**Python Example:**
```python
import google.oauth2.id_token
import google.auth.transport.requests
import requests

def make_authorized_request(service_url):
    # Create auth request
    auth_req = google.auth.transport.requests.Request()
    
    # Fetch ID token using the service URL as audience
    id_token = google.oauth2.id_token.fetch_id_token(auth_req, service_url)
    
    # Make request with token
    headers = {'Authorization': f'Bearer {id_token}'}
    response = requests.get(service_url, headers=headers)
    
    return response
```

**Node.js Example:**
```javascript
const {GoogleAuth} = require('google-auth-library');
const fetch = require('node-fetch');

async function makeAuthorizedRequest(serviceUrl) {
    const auth = new GoogleAuth();
    const client = await auth.getIdTokenClient(serviceUrl);
    
    const response = await client.request({
        url: serviceUrl,
        method: 'GET'
    });
    
    return response.data;
}
```

**Go Example:**
```go
import (
    "context"
    "fmt"
    "google.golang.org/api/idtoken"
    "net/http"
)

func makeAuthorizedRequest(serviceURL string) (*http.Response, error) {
    ctx := context.Background()
    
    // Create ID token client
    client, err := idtoken.NewClient(ctx, serviceURL)
    if err != nil {
        return nil, fmt.Errorf("idtoken.NewClient: %v", err)
    }
    
    // Make request
    resp, err := client.Get(serviceURL)
    if err != nil {
        return nil, fmt.Errorf("client.Get: %v", err)
    }
    
    return resp, nil
}
```

#### Method 2: Using Metadata Server

```bash
# Get token from metadata server
curl "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity?audience=SERVICE_URL" \
  -H "Metadata-Flavor: Google"
```

**Implementation in code:**
```python
import requests

def get_id_token_from_metadata_server(audience):
    url = f"http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity?audience={audience}"
    headers = {"Metadata-Flavor": "Google"}
    
    response = requests.get(url, headers=headers)
    return response.text
```

#### Method 3: Workload Identity Federation

For services outside Google Cloud:
```python
from google.auth import external_account
from google.auth.transport.requests import Request

# Configure workload identity
credentials = external_account.Credentials.from_file(
    "workload_identity_config.json",
    scopes=["https://www.googleapis.com/auth/cloud-platform"]
)

# Get ID token
credentials.refresh(Request())
id_token = credentials.token
```

#### Method 4: Service Account Key (Not Recommended)

```python
from google.oauth2 import service_account
from google.auth.transport.requests import Request

# Load service account key
credentials = service_account.IDTokenCredentials.from_service_account_file(
    'path/to/key.json',
    target_audience=SERVICE_URL
)

# Get token
request = Request()
credentials.refresh(request)
id_token = credentials.token
```

### Step 4: Include Token in Requests

```python
# Add token to Authorization header
headers = {
    'Authorization': f'Bearer {id_token}',
    'Content-Type': 'application/json'
}

response = requests.post(
    service_url,
    headers=headers,
    json={"data": "example"}
)
```

## Best Practices

### 1. Security
- **Use minimum permissions**: Only grant necessary roles
- **Avoid service account keys**: Use metadata server or workload identity
- **Validate tokens**: Always verify token authenticity and expiration
- **Rotate credentials**: If using keys, rotate them regularly

### 2. Token Management
- **Cache tokens**: Tokens are valid for 1 hour
- **Implement refresh logic**: Handle token expiration gracefully
- **Use appropriate audiences**: Token audience must match service URL

### 3. Error Handling
```python
def robust_service_call(service_url, max_retries=3):
    for attempt in range(max_retries):
        try:
            token = get_fresh_token(service_url)
            response = make_request_with_token(service_url, token)
            
            if response.status_code == 401:
                # Token might be expired, refresh
                continue
            elif response.status_code == 403:
                # Permission denied, don't retry
                raise PermissionError("Service account lacks permission")
            
            return response
            
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            time.sleep(2 ** attempt)  # Exponential backoff
```

### 4. Performance Optimization

**Token Caching:**
```python
from functools import lru_cache
from datetime import datetime, timedelta

@lru_cache(maxsize=128)
def get_cached_token(audience):
    token = fetch_id_token(audience)
    return token, datetime.now() + timedelta(minutes=55)

def get_valid_token(audience):
    token, expiry = get_cached_token(audience)
    if datetime.now() > expiry:
        get_cached_token.cache_clear()
        token, expiry = get_cached_token(audience)
    return token
```

**Regional Optimization:**
- Deploy services in the same region to avoid networking charges
- Use regional endpoints for better latency

## Common Scenarios

### 1. API Gateway Pattern
```
External Client → API Gateway Service → Backend Services
                        ↓
                 Service Account with
                 invoker permissions
```

### 2. Microservices Chain
```
Service A → Service B → Service C
    ↓           ↓           ↓
  SA-A        SA-B        SA-C
```

### 3. Fan-out Pattern
```
Orchestrator Service → Service 1
                    → Service 2
                    → Service 3
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Token expired or invalid
   - Wrong audience specified
   - Service account doesn't exist

2. **403 Forbidden**
   - Service account lacks Cloud Run Invoker role
   - IAM policy not propagated yet (wait 60 seconds)
   - Service is configured to allow only specific service accounts

3. **Token Acquisition Fails**
   - Metadata server not accessible (wrong environment)
   - Service account key invalid or expired
   - Workload identity misconfigured

### Debug Commands
```bash
# Check service IAM policy
gcloud run services get-iam-policy SERVICE_NAME

# Test token acquisition
curl -H "Metadata-Flavor: Google" \
  "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity?audience=https://SERVICE_URL"

# Verify service account
gcloud iam service-accounts describe SERVICE_ACCOUNT_EMAIL
```

## Cost Optimization

- **No networking charges** for service-to-service traffic in the same region
- Token acquisition has minimal overhead
- Cache tokens to reduce metadata server calls
- Use connection pooling for frequent requests