# Spanner Encryption and CMEK Implementation

## Overview

Cloud Spanner provides two encryption options for data at rest:

1. **Google Default Encryption**: Automatic encryption managed by Google (default)
2. **Customer-Managed Encryption Keys (CMEK)**: Customer controls encryption keys via Cloud KMS

## Default Encryption

### How It Works
- All data automatically encrypted at rest
- No configuration required
- Google manages all encryption keys
- Transparent to applications

### Encryption Layers
```
Application Data
    ↓
Subfile Chunks (encrypted with DEK)
    ↓
Data Encryption Keys (encrypted with KEK)
    ↓
Key Encryption Keys (encrypted with root key)
    ↓
Google Root Key
```

## Customer-Managed Encryption Keys (CMEK)

### Benefits
- **Control**: Rotate, disable, or destroy encryption keys
- **Compliance**: Meet regulatory requirements
- **Auditability**: Track key usage through Cloud Audit Logs
- **Access Control**: Additional layer of data protection

### Architecture
```
Application Data
    ↓
Subfile Chunks (encrypted with DEK)
    ↓
Data Encryption Keys (encrypted with KEK)
    ↓
Key Encryption Keys (encrypted with CMEK)
    ↓
Customer's Cloud KMS Key
```

## CMEK Implementation

### Prerequisites

1. **Enable APIs**
```bash
gcloud services enable spanner.googleapis.com
gcloud services enable cloudkms.googleapis.com
```

2. **Create Service Account Identity**
```bash
# Get or create Spanner service account
gcloud beta services identity create \
    --service=spanner.googleapis.com \
    --project=PROJECT_ID
```

### Step 1: Create Cloud KMS Key

```bash
# Create key ring (must match Spanner instance region)
gcloud kms keyrings create spanner-keyring \
    --location=us-central1

# Create encryption key
gcloud kms keys create spanner-database-key \
    --location=us-central1 \
    --keyring=spanner-keyring \
    --purpose=encryption \
    --protection-level=software \
    --rotation-period=90d \
    --next-rotation-time=2024-04-01T00:00:00.000Z
```

### Step 2: Grant Permissions

```bash
# Grant Spanner service account access to key
gcloud kms keys add-iam-policy-binding spanner-database-key \
    --location=us-central1 \
    --keyring=spanner-keyring \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/cloudkms.cryptoKeyEncrypterDecrypter"
```

### Step 3: Create CMEK-Protected Database

```bash
# Create database with CMEK
gcloud spanner databases create secure-database \
    --instance=my-instance \
    --database-dialect=GOOGLE_STANDARD_SQL \
    --kms-key=projects/PROJECT_ID/locations/us-central1/keyRings/spanner-keyring/cryptoKeys/spanner-database-key
```

### Multi-Regional CMEK Configuration

```bash
# For multi-regional instances, specify multiple keys
gcloud spanner databases create multi-region-db \
    --instance=multi-region-instance \
    --kms-keys=projects/PROJECT_ID/locations/us-central1/keyRings/keyring1/cryptoKeys/key1,\
projects/PROJECT_ID/locations/us-east1/keyRings/keyring2/cryptoKeys/key2
```

## Key Management Operations

### Key Rotation

```bash
# Automatic rotation (configured during key creation)
gcloud kms keys update spanner-database-key \
    --location=us-central1 \
    --keyring=spanner-keyring \
    --rotation-period=30d

# Manual rotation
gcloud kms keys versions create \
    --location=us-central1 \
    --keyring=spanner-keyring \
    --key=spanner-database-key
```

### Key Status Monitoring

```python
from google.cloud import kms

def check_key_status(project_id, location, keyring, key_name):
    """Check the status of a KMS key."""
    client = kms.KeyManagementServiceClient()
    
    key_path = client.crypto_key_path(
        project_id, location, keyring, key_name
    )
    
    key = client.get_crypto_key(request={"name": key_path})
    
    # Check primary version
    primary_version = key.primary
    version = client.get_crypto_key_version(
        request={"name": primary_version.name}
    )
    
    return {
        "key_name": key.name,
        "primary_version": primary_version.name,
        "state": version.state.name,
        "create_time": key.create_time,
        "rotation_period": key.rotation_period
    }
```

### Access Control

```bash
# List current IAM bindings
gcloud kms keys get-iam-policy spanner-database-key \
    --location=us-central1 \
    --keyring=spanner-keyring

# Remove access (will make database inaccessible)
gcloud kms keys remove-iam-policy-binding spanner-database-key \
    --location=us-central1 \
    --keyring=spanner-keyring \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/cloudkms.cryptoKeyEncrypterDecrypter"
```

## Backup and Restore with CMEK

### Create CMEK-Protected Backup

```bash
# Backup inherits database encryption by default
gcloud spanner backups create secure-backup \
    --instance=my-instance \
    --database=secure-database \
    --retention-period=365d

# Or specify different CMEK for backup
gcloud spanner backups create secure-backup-alt \
    --instance=my-instance \
    --database=secure-database \
    --retention-period=365d \
    --kms-key=projects/PROJECT_ID/locations/us-central1/keyRings/backup-keyring/cryptoKeys/backup-key
```

### Restore from CMEK Backup

```bash
# Restore with same encryption
gcloud spanner databases restore restored-db \
    --instance=my-instance \
    --backup=secure-backup

# Restore with different CMEK
gcloud spanner databases restore restored-db-new \
    --instance=my-instance \
    --backup=secure-backup \
    --kms-key=projects/PROJECT_ID/locations/us-central1/keyRings/new-keyring/cryptoKeys/new-key
```

## Security Best Practices

### 1. Key Lifecycle Management

```yaml
# key-lifecycle-policy.yaml
keyRotationPeriod: 90 days
keyDestructionDelay: 30 days
allowedCryptoKeyPurposes:
  - ENCRYPT_DECRYPT
minimumCryptoKeyVersion: 1
requireAttestationForAccess: true
```

### 2. Monitoring and Alerting

```python
# Monitor key access patterns
def create_key_access_alert():
    """Create alert for unusual key access."""
    from google.cloud import monitoring_v3
    
    client = monitoring_v3.AlertPolicyServiceClient()
    project_name = f"projects/{PROJECT_ID}"
    
    alert_policy = monitoring_v3.AlertPolicy(
        display_name="Spanner CMEK Access Alert",
        conditions=[
            monitoring_v3.AlertPolicy.Condition(
                display_name="High key access rate",
                condition_threshold=monitoring_v3.AlertPolicy.Condition.MetricThreshold(
                    filter='resource.type="cloudkms.googleapis.com/CryptoKey" '
                           'AND metric.type="cloudkms.googleapis.com/cryptokey/request_count"',
                    comparison=monitoring_v3.ComparisonType.COMPARISON_GT,
                    threshold_value=1000,
                    duration={"seconds": 300}
                )
            )
        ],
        notification_channels=["projects/PROJECT_ID/notificationChannels/CHANNEL_ID"]
    )
    
    client.create_alert_policy(
        name=project_name,
        alert_policy=alert_policy
    )
```

### 3. Audit Logging

```bash
# Enable Cloud KMS audit logs
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/cloudkms.viewer" \
    --condition=None
```

### 4. Organization Policies

```yaml
# Enforce CMEK usage for Spanner
name: organizations/ORG_ID/policies/spanner.disableDefaultEncryption
spec:
  rules:
    - enforce: true
```

## Operational Considerations

### Key Availability
- Spanner validates key accessibility every 5 minutes
- Key changes can take up to 3 hours to propagate
- Database becomes inaccessible if key is disabled/destroyed

### Performance Impact
- CMEK has equivalent performance to default encryption
- No additional latency for read/write operations
- Minimal overhead for key validation checks

### Cost Implications
```
Spanner Costs: No additional charges for CMEK
Cloud KMS Costs:
- Key storage: ~$0.06/month per key version
- Operations: ~$0.03 per 10,000 operations
- Typical monthly cost: <$1 per database
```

## Troubleshooting

### Common Issues

1. **Database Creation Fails**
```bash
# Check service account permissions
gcloud kms keys get-iam-policy spanner-database-key \
    --location=us-central1 \
    --keyring=spanner-keyring \
    --filter="bindings.members:service-*@gcp-sa-spanner.iam.gserviceaccount.com"
```

2. **Key Access Errors**
```bash
# Verify key is enabled
gcloud kms keys describe spanner-database-key \
    --location=us-central1 \
    --keyring=spanner-keyring \
    --format="value(state)"
```

3. **Performance Degradation**
```sql
-- Monitor encryption operations
SELECT
  timestamp,
  protoPayload.methodName,
  protoPayload.status.code as status,
  protoPayload.latency
FROM
  `PROJECT_ID.cloudkms_logs.cloudaudit_googleapis_com_data_access`
WHERE
  protoPayload.resourceName LIKE "%spanner-database-key%"
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
```

## Migration Strategies

### Migrate to CMEK
```bash
# 1. Create backup of existing database
gcloud spanner backups create migration-backup \
    --instance=my-instance \
    --database=existing-database

# 2. Restore with CMEK
gcloud spanner databases restore cmek-database \
    --instance=my-instance \
    --backup=migration-backup \
    --kms-key=projects/PROJECT_ID/locations/us-central1/keyRings/spanner-keyring/cryptoKeys/spanner-database-key

# 3. Update application connection strings
# 4. Delete original database after verification
```

### Emergency Access Procedures

```python
# Break-glass key re-enablement
def emergency_key_enable(key_path):
    """Emergency procedure to re-enable a disabled key."""
    client = kms.KeyManagementServiceClient()
    
    # Get current primary version
    key = client.get_crypto_key(request={"name": key_path})
    primary_version = key.primary
    
    # Enable the key version
    version = client.crypto_key_version_path(
        PROJECT_ID, LOCATION, KEYRING, KEY_NAME, 
        primary_version.name.split('/')[-1]
    )
    
    client.update_crypto_key_version(
        request={
            "crypto_key_version": {
                "name": version,
                "state": kms.CryptoKeyVersion.State.ENABLED
            },
            "update_mask": {"paths": ["state"]}
        }
    )
```