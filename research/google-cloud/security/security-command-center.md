# Security Command Center Implementation Guide

## Overview

Security Command Center (SCC) is Google Cloud's centralized security risk management platform that helps security teams prevent, detect, and respond to threats across their cloud environments. It provides comprehensive visibility into security vulnerabilities, misconfigurations, and active threats.

## Service Tiers

### Standard Tier (Free)
- Basic vulnerability scanning
- Security Health Analytics
- Asset inventory
- Limited retention (30 days)

### Premium Tier
- All Standard features
- Container threat detection
- Virtual machine threat detection
- Event Threat Detection
- Extended retention (13 months)

### Enterprise Tier
- All Premium features
- Security posture management
- Compliance monitoring
- Advanced threat hunting
- Custom detection rules

## Setup and Activation

### 1. Organization-Level Activation

```bash
# Enable APIs
gcloud services enable securitycenter.googleapis.com

# Grant necessary roles
gcloud organizations add-iam-policy-binding ORGANIZATION_ID \
    --member="user:<EMAIL>" \
    --role="roles/securitycenter.admin"

# Activate Security Command Center
gcloud scc organizations update ORGANIZATION_ID \
    --enable-asset-discovery
```

### 2. Service Account Configuration

```python
def setup_scc_service_accounts():
    """Configure service accounts for Security Command Center."""
    
    # Create service account for SCC automation
    scc_sa = create_service_account(
        "scc-automation",
        "Security Command Center Automation"
    )
    
    # Grant necessary roles
    roles = [
        "roles/securitycenter.findingsEditor",
        "roles/securitycenter.sourcesViewer",
        "roles/bigquery.dataEditor",  # For exporting findings
        "roles/pubsub.publisher"       # For notifications
    ]
    
    for role in roles:
        grant_role(scc_sa, role)
    
    return scc_sa
```

## Finding Sources Configuration

### 1. Built-in Services

```python
from google.cloud import securitycenter

def configure_built_in_sources():
    """Configure built-in Security Command Center services."""
    
    client = securitycenter.SecurityCenterClient()
    
    # Enable Security Health Analytics
    enable_security_health_analytics()
    
    # Configure Web Security Scanner
    configure_web_security_scanner({
        "scan_config": {
            "display_name": "Production Web Scan",
            "starting_urls": ["https://app.example.com"],
            "schedule": {
                "schedule_time": "00:00",
                "interval_duration_days": 1
            }
        }
    })
    
    # Configure Container Analysis
    enable_container_analysis({
        "vulnerability_scanning": True,
        "continuous_analysis": True
    })
```

### 2. Event Threat Detection

```yaml
# custom-detection-rule.yaml
name: "Suspicious Cloud SQL Activity"
description: "Detect unusual database access patterns"
detection_query: |
  SELECT
    timestamp,
    protoPayload.authenticationInfo.principalEmail as user,
    protoPayload.methodName as operation,
    protoPayload.resourceName as database
  FROM
    `PROJECT_ID.cloudaudit_googleapis_com_data_access`
  WHERE
    protoPayload.serviceName = "cloudsql.googleapis.com"
    AND (
      protoPayload.methodName LIKE "%export%"
      OR protoPayload.methodName LIKE "%backup%"
    )
    AND EXTRACT(HOUR FROM timestamp) NOT BETWEEN 6 AND 22
```

### 3. Third-Party Integration

```python
def integrate_third_party_security():
    """Integrate third-party security tools with SCC."""
    
    # Example: Integrate Forseti Security
    forseti_integration = {
        "source_id": "forseti-security",
        "display_name": "Forseti Security Scanner",
        "description": "Policy violations from Forseti"
    }
    
    # Create custom source
    create_custom_source(forseti_integration)
    
    # Setup Pub/Sub topic for findings
    create_pubsub_topic("forseti-findings")
    
    # Configure finding import
    configure_finding_import({
        "source": forseti_integration["source_id"],
        "pubsub_topic": "forseti-findings",
        "finding_parser": "forseti_to_scc_parser"
    })
```

## Security Posture Management

### 1. Define Security Posture

```yaml
# security-posture.yaml
name: "production-security-posture"
description: "Production environment security requirements"
policies:
  - id: "no-public-ips"
    description: "Compute instances should not have public IPs"
    compliance_standards:
      - "CIS-1.1"
      - "PCI-DSS-1.1"
    resource_selector:
      resource_types:
        - "compute.googleapis.com/Instance"
    policy_rules:
      - condition:
          expression: |
            resource.networkInterfaces.all(interface,
              !has(interface.accessConfigs) || 
              interface.accessConfigs.size() == 0
            )
        description: "Check for public IP assignment"
        
  - id: "encryption-at-rest"
    description: "All storage must be encrypted"
    compliance_standards:
      - "HIPAA-164.312(a)(2)(iv)"
    resource_selector:
      resource_types:
        - "storage.googleapis.com/Bucket"
        - "compute.googleapis.com/Disk"
    policy_rules:
      - condition:
          expression: |
            resource.encryption != null && 
            resource.encryption.defaultKmsKeyName != null
```

### 2. Deploy and Monitor Posture

```python
def deploy_security_posture(posture_config):
    """Deploy and monitor security posture."""
    
    # Create posture
    posture = create_security_posture(posture_config)
    
    # Deploy to specific folders/projects
    deployments = [
        {
            "target": "folders/production-folder",
            "posture_id": posture.id,
            "posture_revision": "latest"
        },
        {
            "target": "projects/critical-project",
            "posture_id": posture.id,
            "posture_revision": "latest"
        }
    ]
    
    for deployment in deployments:
        deploy_posture(deployment)
    
    # Setup continuous monitoring
    create_posture_monitoring({
        "posture_id": posture.id,
        "check_interval": "1h",
        "alert_on_drift": True
    })
```

## Threat Detection and Response

### 1. Real-time Threat Detection

```python
def setup_threat_detection():
    """Configure real-time threat detection."""
    
    # Container threat detection
    enable_container_threat_detection({
        "clusters": ["prod-gke-cluster"],
        "detection_rules": [
            "malicious_binary",
            "crypto_mining",
            "suspicious_library",
            "reverse_shell"
        ]
    })
    
    # VM threat detection
    enable_vm_threat_detection({
        "projects": ["production-project"],
        "os_types": ["linux", "windows"],
        "detection_capabilities": [
            "malware_detection",
            "crypto_mining_detection",
            "suspicious_process_detection"
        ]
    })
```

### 2. Automated Response

```python
class ThreatResponseAutomation:
    """Automated threat response system."""
    
    def __init__(self):
        self.scc_client = securitycenter.SecurityCenterClient()
        self.compute_client = compute_v1.InstancesClient()
    
    def handle_threat_finding(self, finding):
        """Automatically respond to threat findings."""
        
        severity = finding.severity
        finding_type = finding.category
        
        if severity == "CRITICAL":
            if finding_type == "MALWARE_DETECTED":
                self.isolate_infected_instance(finding)
            elif finding_type == "CRYPTO_MINING":
                self.terminate_mining_process(finding)
            elif finding_type == "DATA_EXFILTRATION":
                self.block_egress_traffic(finding)
        
        # Log response
        self.log_response_action(finding)
    
    def isolate_infected_instance(self, finding):
        """Isolate compromised instance."""
        instance_id = finding.resource_name
        
        # Remove external IP
        self.remove_external_ip(instance_id)
        
        # Apply quarantine firewall rules
        self.apply_quarantine_rules(instance_id)
        
        # Create snapshot for forensics
        self.create_forensic_snapshot(instance_id)
        
        # Notify security team
        self.notify_security_team(finding, "Instance quarantined")
```

### 3. Finding Management

```python
def manage_findings():
    """Manage Security Command Center findings."""
    
    # Query findings
    findings = query_findings({
        "filter": 'severity="HIGH" OR severity="CRITICAL"',
        "order_by": "event_time desc",
        "limit": 100
    })
    
    for finding in findings:
        # Enrich finding with additional context
        enriched = enrich_finding(finding)
        
        # Determine response action
        if requires_immediate_action(enriched):
            trigger_incident_response(enriched)
        
        # Update finding state
        if is_false_positive(enriched):
            mark_as_inactive(finding, "FALSE_POSITIVE")
        elif is_resolved(enriched):
            mark_as_inactive(finding, "RESOLVED")
```

## Monitoring and Alerting

### 1. BigQuery Export

```sql
-- Create dataset for SCC findings
CREATE SCHEMA IF NOT EXISTS scc_findings
OPTIONS(
  description="Security Command Center findings export",
  location="US"
);

-- Create view for high-priority findings
CREATE OR REPLACE VIEW scc_findings.high_priority AS
SELECT
  finding.name,
  finding.category,
  finding.severity,
  finding.state,
  finding.event_time,
  finding.create_time,
  finding.source_properties,
  resource.name as resource_name,
  resource.type as resource_type,
  resource.project_display_name
FROM
  `PROJECT_ID.scc_findings.findings`
WHERE
  finding.severity IN ('HIGH', 'CRITICAL')
  AND finding.state = 'ACTIVE'
  AND DATE(finding.event_time) >= CURRENT_DATE() - 7
```

### 2. Pub/Sub Notifications

```python
def setup_finding_notifications():
    """Configure real-time notifications for findings."""
    
    # Create notification config
    notification_config = {
        "name": "critical-findings-notification",
        "description": "Notify on critical security findings",
        "pubsub_topic": f"projects/{PROJECT_ID}/topics/security-alerts",
        "streaming_config": {
            "filter": 'severity="CRITICAL" AND state="ACTIVE"'
        }
    }
    
    create_notification_config(notification_config)
    
    # Setup Cloud Function to process notifications
    deploy_notification_processor({
        "function_name": "process-security-alerts",
        "trigger_topic": "security-alerts",
        "actions": [
            "send_to_slack",
            "create_incident",
            "trigger_runbook"
        ]
    })
```

### 3. Custom Dashboards

```python
def create_security_dashboard():
    """Create custom security dashboard."""
    
    dashboard_config = {
        "displayName": "Security Command Center Overview",
        "mosaicLayout": {
            "tiles": [
                {
                    "width": 6,
                    "height": 4,
                    "widget": {
                        "scorecard": {
                            "timeSeriesQuery": {
                                "filter": 'resource.type="securitycenter.googleapis.com/Finding"',
                                "aggregation": {
                                    "alignmentPeriod": "3600s",
                                    "perSeriesAligner": "ALIGN_COUNT"
                                }
                            }
                        }
                    }
                },
                {
                    "width": 6,
                    "height": 4,
                    "widget": {
                        "pieChart": {
                            "dataSets": [{
                                "filter": 'metric.type="securitycenter.googleapis.com/finding/severity"'
                            }]
                        }
                    }
                }
            ]
        }
    }
    
    create_dashboard(dashboard_config)
```

## Compliance Monitoring

### 1. Compliance Standards Mapping

```python
def map_compliance_standards():
    """Map findings to compliance standards."""
    
    compliance_mapping = {
        "CIS": {
            "1.1": ["no-public-ips", "firewall-rules-logging"],
            "2.1": ["iam-no-basic-roles", "service-account-keys"],
            "3.1": ["encryption-at-rest", "encryption-in-transit"]
        },
        "PCI-DSS": {
            "1.1": ["network-segmentation", "firewall-rules"],
            "2.3": ["encryption-at-rest"],
            "7.1": ["least-privilege-access"]
        },
        "HIPAA": {
            "164.308(a)(4)": ["access-controls", "audit-logging"],
            "164.312(a)(1)": ["encryption-at-rest"],
            "164.312(e)(1)": ["encryption-in-transit"]
        }
    }
    
    return compliance_mapping
```

### 2. Compliance Reporting

```sql
-- Generate compliance report
WITH compliance_findings AS (
  SELECT
    f.category,
    f.compliance_standards,
    f.state,
    COUNT(*) as finding_count,
    SUM(CASE WHEN f.state = 'ACTIVE' THEN 1 ELSE 0 END) as active_count
  FROM
    `PROJECT_ID.scc_findings.findings` f
  WHERE
    f.event_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
  GROUP BY
    f.category, f.compliance_standards, f.state
)
SELECT
  compliance_standards,
  COUNT(DISTINCT category) as total_controls,
  SUM(active_count) as active_violations,
  ROUND(100 * (1 - SUM(active_count) / COUNT(DISTINCT category)), 2) as compliance_score
FROM
  compliance_findings
GROUP BY
  compliance_standards
ORDER BY
  compliance_score DESC
```

## Advanced Threat Hunting

### 1. Custom Detection Rules

```python
def create_advanced_detection_rules():
    """Create custom detection rules for advanced threats."""
    
    rules = [
        {
            "name": "lateral-movement-detection",
            "query": """
            WITH auth_events AS (
              SELECT
                timestamp,
                protoPayload.authenticationInfo.principalEmail as user,
                protoPayload.requestMetadata.callerIp as source_ip,
                resource.labels.project_id as project
              FROM
                `cloudaudit_googleapis_com_activity`
              WHERE
                timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
            )
            SELECT
              user,
              COUNT(DISTINCT project) as projects_accessed,
              ARRAY_AGG(DISTINCT source_ip) as source_ips
            FROM
              auth_events
            GROUP BY
              user
            HAVING
              projects_accessed > 5
            """
        },
        {
            "name": "data-exfiltration-detection",
            "query": """
            SELECT
              protoPayload.authenticationInfo.principalEmail as user,
              SUM(CAST(protoPayload.request.downloadBytesCount AS INT64)) as bytes_downloaded,
              COUNT(*) as download_count
            FROM
              `cloudaudit_googleapis_com_data_access`
            WHERE
              protoPayload.methodName LIKE "%download%"
              AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 6 HOUR)
            GROUP BY
              user
            HAVING
              bytes_downloaded > 10737418240  -- 10GB
            """
        }
    ]
    
    for rule in rules:
        create_detection_rule(rule)
```

### 2. Threat Intelligence Integration

```python
def integrate_threat_intelligence():
    """Integrate external threat intelligence feeds."""
    
    # Configure threat intel sources
    threat_feeds = [
        {
            "name": "malicious-ips",
            "source": "https://feeds.example.com/malicious-ips",
            "type": "IP_ADDRESS",
            "update_frequency": "1h"
        },
        {
            "name": "malware-hashes",
            "source": "https://feeds.example.com/malware-hashes",
            "type": "FILE_HASH",
            "update_frequency": "6h"
        }
    ]
    
    for feed in threat_feeds:
        # Create indicator source
        create_indicator_source(feed)
        
        # Setup automatic correlation
        create_correlation_rule({
            "indicator_source": feed["name"],
            "finding_categories": get_relevant_categories(feed["type"]),
            "action": "ENRICH_FINDING"
        })
```

## Best Practices

### 1. Organizational Structure

```python
def setup_scc_organization():
    """Setup Security Command Center organizational structure."""
    
    # Create folder hierarchy for security management
    folders = {
        "security-baseline": {
            "display_name": "Security Baseline",
            "projects": ["security-tools", "logging-project"]
        },
        "production": {
            "display_name": "Production Environment",
            "projects": ["prod-app", "prod-data"],
            "security_posture": "strict"
        },
        "development": {
            "display_name": "Development Environment",
            "projects": ["dev-app", "dev-testing"],
            "security_posture": "baseline"
        }
    }
    
    # Apply security policies per folder
    for folder_name, config in folders.items():
        apply_folder_security_policy(folder_name, config)
```

### 2. Regular Security Reviews

```python
def conduct_security_review():
    """Conduct regular security review using SCC data."""
    
    review_metrics = {
        "total_active_findings": count_active_findings(),
        "critical_findings": count_findings_by_severity("CRITICAL"),
        "mean_time_to_resolve": calculate_mttr(),
        "compliance_scores": calculate_compliance_scores(),
        "top_vulnerability_types": get_top_vulnerabilities(10),
        "most_affected_resources": get_most_affected_resources(10)
    }
    
    # Generate executive report
    generate_executive_report(review_metrics)
    
    # Identify improvement areas
    improvements = identify_improvements(review_metrics)
    
    # Create action items
    for improvement in improvements:
        create_improvement_ticket(improvement)
    
    return review_metrics
```