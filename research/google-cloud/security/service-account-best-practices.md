# Service Account Security Best Practices

## Overview

Service accounts are special Google accounts that represent non-human users. They're designed for scenarios where workloads need to access resources without end-user involvement. Due to their dual nature as both principals (identities) and resources, service accounts require careful security management.

## Core Principles

### 1. Single-Purpose Service Accounts

**Create dedicated service accounts for each application or use case**

```bash
# BAD: One service account for everything
gcloud iam service-accounts create general-purpose-sa

# GOOD: Specific service accounts for each component
gcloud iam service-accounts create frontend-api-sa \
    --display-name="Frontend API Service" \
    --description="Handles user-facing API requests"

gcloud iam service-accounts create batch-processor-sa \
    --display-name="Batch Processing Service" \
    --description="Processes background jobs and data pipelines"

gcloud iam service-accounts create monitoring-sa \
    --display-name="Monitoring Service" \
    --description="Collects and reports metrics"
```

### 2. Naming Conventions

**Follow consistent, descriptive naming patterns**

```python
def create_service_account_name(environment, service, component):
    """Generate standardized service account names."""
    # Pattern: {env}-{service}-{component}-sa
    # Example: prod-api-frontend-sa
    
    name = f"{environment}-{service}-{component}-sa"
    
    # Ensure compliance with naming rules
    if len(name) > 30:  # Max 30 chars for account ID
        # Shorten intelligently
        name = f"{environment[:4]}-{service[:8]}-{component[:8]}-sa"
    
    return name.lower().replace("_", "-")

# Examples:
# prod-api-frontend-sa
# dev-etl-processor-sa
# staging-ml-trainer-sa
```

### 3. Avoid Sensitive Information

**Never include confidential data in service account names**

```bash
# BAD: Exposing sensitive information
gcloud iam service-accounts create mysql-prod-admin-password123-sa
gcloud iam service-accounts create api-key-xyz789-sa

# GOOD: Generic, non-revealing names
gcloud iam service-accounts create database-reader-sa
gcloud iam service-accounts create external-api-client-sa
```

## Security Management

### 1. Disable Default Service Accounts

```bash
# Disable automatic role grants for default service accounts
gcloud resource-manager org-policies set-policy \
    --organization=ORG_ID \
    disable-default-sa-grants-policy.yaml
```

```yaml
# disable-default-sa-grants-policy.yaml
constraint: iam.automaticIamGrantsForDefaultServiceAccounts
booleanPolicy:
  enforced: true
```

### 2. Service Account Lifecycle

```python
class ServiceAccountManager:
    """Manage service account lifecycle securely."""
    
    def __init__(self, project_id):
        self.project_id = project_id
        self.iam_client = iam_admin_v1.IAMClient()
    
    def create_service_account(self, name, description, roles=[]):
        """Create service account with minimal permissions."""
        # Create account
        sa = self.iam_client.create_service_account(
            request={
                "name": f"projects/{self.project_id}",
                "account_id": name,
                "service_account": {
                    "display_name": name,
                    "description": description
                }
            }
        )
        
        # Grant only necessary roles
        for role in roles:
            self.grant_role(sa.email, role)
        
        # Log creation
        self.audit_log("CREATE", sa.email, roles)
        
        return sa
    
    def disable_unused_service_account(self, email):
        """Disable service account before deletion."""
        # First disable
        self.iam_client.disable_service_account(name=email)
        
        # Schedule deletion after grace period
        self.schedule_deletion(email, days=30)
        
        # Log action
        self.audit_log("DISABLE", email)
    
    def audit_service_accounts(self):
        """Regular audit of service accounts."""
        accounts = self.list_all_service_accounts()
        
        findings = []
        for account in accounts:
            # Check last authentication
            last_auth = self.get_last_authentication(account.email)
            
            if not last_auth or last_auth < datetime.now() - timedelta(days=90):
                findings.append({
                    "account": account.email,
                    "issue": "unused_90_days",
                    "last_auth": last_auth
                })
            
            # Check for keys
            keys = self.list_keys(account.email)
            if keys:
                findings.append({
                    "account": account.email,
                    "issue": "has_keys",
                    "key_count": len(keys)
                })
        
        return findings
```

### 3. Privilege Management

```bash
# Use IAM conditions for temporary access
gcloud iam service-accounts add-iam-policy-binding \
    SERVICE_ACCOUNT_EMAIL \
    --member="user:<EMAIL>" \
    --role="roles/iam.serviceAccountUser" \
    --condition="expression=request.time < timestamp('2024-06-30T00:00:00Z'),title=Temporary Dev Access"
```

## Authentication Best Practices

### 1. Avoid Service Account Keys

```python
def get_credentials_without_keys():
    """Get credentials without creating keys."""
    
    # Option 1: Use ADC in Google Cloud environments
    from google.auth import default
    credentials, project = default()
    
    # Option 2: Use impersonation for development
    from google.auth import impersonated_credentials
    
    target_scopes = ['https://www.googleapis.com/auth/cloud-platform']
    source_credentials, project = default()
    
    target_credentials = impersonated_credentials.Credentials(
        source_credentials=source_credentials,
        target_principal='<EMAIL>',
        target_scopes=target_scopes,
        lifetime=3600  # 1 hour
    )
    
    return target_credentials
```

### 2. Workload Identity Federation

```bash
# Configure workload identity for external workloads
gcloud iam workload-identity-pools create github-pool \
    --location="global" \
    --display-name="GitHub Actions Pool"

gcloud iam workload-identity-pools providers create-oidc github \
    --location="global" \
    --workload-identity-pool="github-pool" \
    --display-name="GitHub Provider" \
    --attribute-mapping="google.subject=assertion.sub,attribute.repository=assertion.repository" \
    --issuer-uri="https://token.actions.githubusercontent.com"
```

### 3. Metadata Server Access Control

```bash
# Restrict metadata server access on VMs
#!/bin/bash

# Create iptables rules to limit metadata access
# Only allow specific UIDs to access metadata server

# Allow root and specific service user
iptables -A OUTPUT -m owner --uid-owner 0 -d *************** -j ACCEPT
iptables -A OUTPUT -m owner --uid-owner 1000 -d *************** -j ACCEPT

# Block all other access
iptables -A OUTPUT -d *************** -j REJECT
```

## Key Management

### 1. Key Rotation Automation

```python
class KeyRotationManager:
    """Automated service account key rotation."""
    
    def rotate_keys(self, service_account_email, max_age_days=90):
        """Rotate keys older than max_age_days."""
        
        # List existing keys
        keys = self.iam_client.list_service_account_keys(
            name=service_account_email
        )
        
        old_keys = []
        for key in keys.keys:
            age = datetime.now() - key.valid_after_time
            if age.days > max_age_days:
                old_keys.append(key)
        
        if not old_keys:
            return "No rotation needed"
        
        # Create new key
        new_key = self.iam_client.create_service_account_key(
            name=service_account_email
        )
        
        # Deploy new key to application
        self.deploy_new_key(new_key)
        
        # Wait for propagation
        time.sleep(300)  # 5 minutes
        
        # Delete old keys
        for key in old_keys:
            self.iam_client.delete_service_account_key(name=key.name)
        
        return f"Rotated {len(old_keys)} keys"
```

### 2. Emergency Key Management

```bash
#!/bin/bash
# Emergency key revocation script

SERVICE_ACCOUNT=$1

# List all keys
KEYS=$(gcloud iam service-accounts keys list \
    --iam-account=$SERVICE_ACCOUNT \
    --format="value(name)" \
    --filter="keyType=USER_MANAGED")

# Delete all user-managed keys
for KEY in $KEYS; do
    echo "Deleting key: $KEY"
    gcloud iam service-accounts keys delete $KEY \
        --iam-account=$SERVICE_ACCOUNT \
        --quiet
done

# Log the action
echo "$(date): Emergency key revocation for $SERVICE_ACCOUNT" >> /var/log/key-revocation.log
```

## Monitoring and Auditing

### 1. Service Account Activity Monitoring

```sql
-- Monitor service account authentication patterns
WITH sa_activity AS (
  SELECT
    protoPayload.authenticationInfo.principalEmail as service_account,
    EXTRACT(DATE FROM timestamp) as activity_date,
    COUNT(*) as request_count,
    ARRAY_AGG(DISTINCT protoPayload.methodName LIMIT 10) as methods_used,
    ARRAY_AGG(DISTINCT protoPayload.request.resource LIMIT 10) as resources_accessed
  FROM
    `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
  WHERE
    protoPayload.authenticationInfo.principalEmail LIKE '%gserviceaccount.com'
    AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
  GROUP BY
    service_account, activity_date
)
SELECT
  service_account,
  MAX(activity_date) as last_active,
  SUM(request_count) as total_requests,
  ARRAY_CONCAT_AGG(methods_used) as all_methods,
  ARRAY_CONCAT_AGG(resources_accessed) as all_resources
FROM
  sa_activity
GROUP BY
  service_account
ORDER BY
  total_requests DESC
```

### 2. Detect Anomalous Behavior

```python
def detect_service_account_anomalies():
    """Detect unusual service account behavior."""
    
    query = """
    WITH baseline AS (
      -- Calculate normal behavior over 30 days
      SELECT
        protoPayload.authenticationInfo.principalEmail as sa,
        EXTRACT(HOUR FROM timestamp) as hour,
        COUNT(*) / 30 as avg_hourly_requests
      FROM
        `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
      WHERE
        protoPayload.authenticationInfo.principalEmail LIKE '%gserviceaccount.com'
        AND timestamp BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
                          AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
      GROUP BY sa, hour
    ),
    current AS (
      -- Current hour activity
      SELECT
        protoPayload.authenticationInfo.principalEmail as sa,
        COUNT(*) as current_requests
      FROM
        `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
      WHERE
        protoPayload.authenticationInfo.principalEmail LIKE '%gserviceaccount.com'
        AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
      GROUP BY sa
    )
    SELECT
      c.sa,
      c.current_requests,
      b.avg_hourly_requests,
      (c.current_requests - b.avg_hourly_requests) / b.avg_hourly_requests * 100 as percent_change
    FROM current c
    JOIN baseline b ON c.sa = b.sa
    WHERE c.current_requests > b.avg_hourly_requests * 5  -- 5x normal
    """
    
    anomalies = bigquery_client.query(query)
    
    for anomaly in anomalies:
        alert_security_team(anomaly)
```

### 3. Compliance Reporting

```python
def generate_service_account_compliance_report():
    """Generate compliance report for service accounts."""
    
    report = {
        "generated_at": datetime.now().isoformat(),
        "findings": []
    }
    
    # Check for overprivileged service accounts
    overprivileged = find_overprivileged_service_accounts()
    report["findings"].extend(overprivileged)
    
    # Check for service accounts with keys
    with_keys = find_service_accounts_with_keys()
    report["findings"].extend(with_keys)
    
    # Check for unused service accounts
    unused = find_unused_service_accounts(days=90)
    report["findings"].extend(unused)
    
    # Check for service accounts with admin roles
    with_admin = find_service_accounts_with_admin_roles()
    report["findings"].extend(with_admin)
    
    # Generate recommendations
    report["recommendations"] = generate_remediation_recommendations(report["findings"])
    
    return report
```

## Production Patterns

### 1. Workload Identity for GKE

```yaml
# kubernetes-service-account.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: app-ksa
  namespace: production
  annotations:
    iam.gke.io/gcp-service-account: app-sa@PROJECT_ID.iam.gserviceaccount.com
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app
  namespace: production
spec:
  template:
    spec:
      serviceAccountName: app-ksa
      containers:
      - name: app
        image: gcr.io/PROJECT_ID/app:latest
        env:
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: /var/run/secrets/cloud.google.com/service-account/key.json
```

### 2. Multi-Environment Service Accounts

```bash
#!/bin/bash
# Setup service accounts for multiple environments

ENVIRONMENTS=("dev" "staging" "prod")
SERVICES=("api" "worker" "scheduler")

for ENV in "${ENVIRONMENTS[@]}"; do
    for SERVICE in "${SERVICES[@]}"; do
        SA_NAME="${ENV}-${SERVICE}-sa"
        SA_EMAIL="${SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
        
        # Create service account
        gcloud iam service-accounts create ${SA_NAME} \
            --display-name="${ENV} ${SERVICE} Service Account"
        
        # Grant appropriate roles based on environment
        if [ "$ENV" == "prod" ]; then
            # Production: minimal permissions
            gcloud projects add-iam-policy-binding ${PROJECT_ID} \
                --member="serviceAccount:${SA_EMAIL}" \
                --role="roles/logging.logWriter"
        else
            # Non-prod: slightly more permissive
            gcloud projects add-iam-policy-binding ${PROJECT_ID} \
                --member="serviceAccount:${SA_EMAIL}" \
                --role="roles/monitoring.metricWriter"
        fi
    done
done
```

### 3. Cross-Project Access

```python
def setup_cross_project_access(source_project, target_project, service_account):
    """Configure service account for cross-project access."""
    
    # Grant minimal necessary permissions in target project
    target_policy = get_project_policy(target_project)
    
    binding = {
        "role": "roles/bigquery.dataViewer",  # Example: read-only BigQuery access
        "members": [f"serviceAccount:{service_account}"],
        "condition": {
            "expression": f'resource.name.startsWith("projects/{target_project}/datasets/public_")',
            "title": "Access to public datasets only"
        }
    }
    
    target_policy["bindings"].append(binding)
    set_project_policy(target_project, target_policy)
    
    # Audit log the cross-project grant
    log_cross_project_access(source_project, target_project, service_account)
```

## Security Hardening

### 1. Prevent Lateral Movement

```yaml
# Organization policy to prevent cross-project impersonation
name: organizations/ORG_ID/policies/iam.disableCrossProjectServiceAccountUsage
spec:
  rules:
  - enforce: true
    condition:
      expression: |
        !resource.matchTag("projects/PROJECT_ID/tags/allow-cross-project")
```

### 2. Audit Trail Enhancement

```python
def enhance_audit_trail(original_function):
    """Decorator to enhance audit trail for service account operations."""
    
    def wrapper(*args, **kwargs):
        # Capture context
        context = {
            "timestamp": datetime.utcnow().isoformat(),
            "user": get_current_user(),
            "source_ip": get_source_ip(),
            "operation": original_function.__name__,
            "parameters": kwargs
        }
        
        # Execute operation
        try:
            result = original_function(*args, **kwargs)
            context["status"] = "success"
            context["result"] = str(result)
        except Exception as e:
            context["status"] = "failure"
            context["error"] = str(e)
            raise
        finally:
            # Log to custom audit system
            custom_audit_log.write(context)
        
        return result
    
    return wrapper

# Apply to sensitive operations
@enhance_audit_trail
def create_service_account_key(service_account_email):
    """Create service account key with enhanced logging."""
    # Implementation
    pass
```