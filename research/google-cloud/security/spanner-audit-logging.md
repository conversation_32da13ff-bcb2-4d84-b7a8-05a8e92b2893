# Spanner Audit Logging and Security Monitoring

## Overview

Cloud Spanner audit logging provides comprehensive tracking of administrative and data access activities. These logs are essential for:
- Security monitoring and threat detection
- Compliance and regulatory requirements
- Performance analysis
- Access pattern understanding

## Log Types

### 1. Admin Activity Logs

**Automatically Enabled**: Always on, no configuration needed

**Captured Operations**:
- Database creation/deletion
- Instance configuration changes
- Backup operations
- IAM policy modifications
- Schema changes (DDL)

**Example Methods**:
```
CreateDatabase
UpdateInstance
DeleteBackup
SetIamPolicy
UpdateDatabaseDdl
```

### 2. Data Access Logs

**Must Be Enabled**: Requires explicit configuration

**Captured Operations**:
- SQL queries (SELECT)
- Data modifications (INSERT, UPDATE, DELETE)
- Transaction operations
- Session management

**Example Methods**:
```
ExecuteSql
Read
Commit
BeginTransaction
CreateSession
```

## Configuration

### Enable Data Access Logs

```bash
# Enable data access logs for specific methods
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:SERVICE_ACCOUNT" \
    --role="roles/logging.privateLogViewer" \
    --condition=None

# Configure audit log policy
cat > audit-config.yaml << EOF
auditConfigs:
- service: spanner.googleapis.com
  auditLogConfigs:
  - logType: DATA_READ
    exemptedMembers:
    - serviceAccount:exempted-sa@PROJECT_ID.iam.gserviceaccount.com
  - logType: DATA_WRITE
  - logType: ADMIN_READ
EOF

gcloud projects set-iam-policy PROJECT_ID audit-config.yaml
```

### Terraform Configuration

```hcl
resource "google_project_iam_audit_config" "spanner_audit" {
  project = var.project_id
  service = "spanner.googleapis.com"

  audit_log_config {
    log_type = "DATA_READ"
    exempted_members = [
      "serviceAccount:${var.exempted_service_account}",
    ]
  }

  audit_log_config {
    log_type = "DATA_WRITE"
  }

  audit_log_config {
    log_type = "ADMIN_READ"
  }
}
```

## Log Structure

### Key Fields

```json
{
  "protoPayload": {
    "@type": "type.googleapis.com/google.cloud.audit.AuditLog",
    "serviceName": "spanner.googleapis.com",
    "methodName": "google.spanner.v1.Spanner.ExecuteSql",
    "authenticationInfo": {
      "principalEmail": "<EMAIL>",
      "serviceAccountKeyName": "//iam.googleapis.com/projects/PROJECT/serviceAccounts/<EMAIL>/keys/KEY_ID"
    },
    "authorizationInfo": [{
      "resource": "projects/PROJECT/instances/INSTANCE/databases/DATABASE",
      "permission": "spanner.databases.select",
      "granted": true
    }],
    "resourceName": "projects/PROJECT/instances/INSTANCE/databases/DATABASE/sessions/SESSION",
    "request": {
      "sql": "SELECT * FROM users WHERE user_id = @userId",
      "params": {
        "userId": "12345"
      }
    },
    "response": {
      "metadata": {
        "rowType": {
          "fields": [{"name": "user_id"}, {"name": "email"}]
        }
      }
    },
    "processingDuration": "0.125s"
  },
  "resource": {
    "type": "spanner.googleapis.com/Instance",
    "labels": {
      "project_id": "PROJECT",
      "instance_id": "INSTANCE",
      "database_id": "DATABASE"
    }
  },
  "timestamp": "2024-01-15T10:30:45.123Z",
  "severity": "INFO"
}
```

## Monitoring Queries

### 1. Track High-Volume Users

```sql
SELECT
  protoPayload.authenticationInfo.principalEmail as user_email,
  COUNT(*) as query_count,
  SUM(CAST(REGEXP_EXTRACT(protoPayload.processingDuration, r'(\d+\.?\d*)') AS FLOAT64)) as total_duration
FROM
  `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
WHERE
  protoPayload.serviceName = "spanner.googleapis.com"
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
GROUP BY
  user_email
ORDER BY
  query_count DESC
LIMIT 20
```

### 2. Identify Slow Queries

```sql
SELECT
  timestamp,
  protoPayload.authenticationInfo.principalEmail as user,
  protoPayload.request.sql as query,
  CAST(REGEXP_EXTRACT(protoPayload.processingDuration, r'(\d+\.?\d*)') AS FLOAT64) as duration_seconds,
  protoPayload.resourceName as session
FROM
  `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
WHERE
  protoPayload.serviceName = "spanner.googleapis.com"
  AND protoPayload.methodName = "google.spanner.v1.Spanner.ExecuteSql"
  AND CAST(REGEXP_EXTRACT(protoPayload.processingDuration, r'(\d+\.?\d*)') AS FLOAT64) > 1.0
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
ORDER BY
  duration_seconds DESC
```

### 3. Monitor Schema Changes

```sql
SELECT
  timestamp,
  protoPayload.authenticationInfo.principalEmail as user,
  protoPayload.methodName as operation,
  protoPayload.request.statements as ddl_statements,
  protoPayload.resourceName as database
FROM
  `PROJECT_ID.DATASET.cloudaudit_googleapis_com_activity`
WHERE
  protoPayload.serviceName = "spanner.googleapis.com"
  AND protoPayload.methodName IN ("google.spanner.admin.database.v1.DatabaseAdmin.UpdateDatabaseDdl")
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
ORDER BY
  timestamp DESC
```

### 4. Access Pattern Analysis

```sql
SELECT
  EXTRACT(HOUR FROM timestamp) as hour,
  protoPayload.methodName as method,
  COUNT(*) as request_count,
  AVG(CAST(REGEXP_EXTRACT(protoPayload.processingDuration, r'(\d+\.?\d*)') AS FLOAT64)) as avg_duration
FROM
  `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
WHERE
  protoPayload.serviceName = "spanner.googleapis.com"
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
GROUP BY
  hour, method
ORDER BY
  hour, request_count DESC
```

## Security Monitoring

### 1. Failed Access Attempts

```python
def monitor_failed_access():
    """Monitor failed Spanner access attempts."""
    from google.cloud import bigquery
    
    client = bigquery.Client()
    
    query = """
    SELECT
      timestamp,
      protoPayload.authenticationInfo.principalEmail as user,
      protoPayload.authorizationInfo[0].permission as permission,
      protoPayload.authorizationInfo[0].resource as resource,
      protoPayload.status.code as error_code,
      protoPayload.status.message as error_message
    FROM
      `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
    WHERE
      protoPayload.serviceName = "spanner.googleapis.com"
      AND protoPayload.authorizationInfo[0].granted = false
      AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
    ORDER BY
      timestamp DESC
    """
    
    results = client.query(query)
    
    for row in results:
        if row.error_code == 7:  # PERMISSION_DENIED
            alert_security_team(row)
```

### 2. Unusual Access Patterns

```python
def detect_anomalous_access():
    """Detect unusual database access patterns."""
    query = """
    WITH hourly_baseline AS (
      SELECT
        EXTRACT(HOUR FROM timestamp) as hour,
        COUNT(*) as typical_count
      FROM
        `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
      WHERE
        protoPayload.serviceName = "spanner.googleapis.com"
        AND timestamp BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
                          AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
      GROUP BY hour
    ),
    current_activity AS (
      SELECT
        EXTRACT(HOUR FROM timestamp) as hour,
        COUNT(*) as current_count
      FROM
        `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
      WHERE
        protoPayload.serviceName = "spanner.googleapis.com"
        AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
      GROUP BY hour
    )
    SELECT
      c.hour,
      c.current_count,
      b.typical_count,
      (c.current_count - b.typical_count) / b.typical_count * 100 as percent_change
    FROM current_activity c
    JOIN hourly_baseline b ON c.hour = b.hour
    WHERE ABS((c.current_count - b.typical_count) / b.typical_count) > 2.0
    """
```

### 3. Data Exfiltration Detection

```sql
-- Detect large data exports
SELECT
  timestamp,
  protoPayload.authenticationInfo.principalEmail as user,
  protoPayload.request.sql as query,
  protoPayload.response.metadata.rowCount as rows_returned,
  protoPayload.resourceName as resource
FROM
  `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
WHERE
  protoPayload.serviceName = "spanner.googleapis.com"
  AND protoPayload.methodName = "google.spanner.v1.Spanner.ExecuteSql"
  AND CAST(protoPayload.response.metadata.rowCount AS INT64) > 10000
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
ORDER BY
  rows_returned DESC
```

## Alerting Configuration

### 1. Cloud Monitoring Alert

```python
from google.cloud import monitoring_v3

def create_spanner_security_alert():
    """Create alert for suspicious Spanner activity."""
    client = monitoring_v3.AlertPolicyServiceClient()
    project_name = f"projects/{PROJECT_ID}"
    
    # Alert for failed authentication
    alert_policy = monitoring_v3.AlertPolicy(
        display_name="Spanner Failed Authentication Alert",
        conditions=[
            monitoring_v3.AlertPolicy.Condition(
                display_name="Multiple failed auth attempts",
                condition_threshold=monitoring_v3.AlertPolicy.Condition.MetricThreshold(
                    filter="""
                    resource.type="spanner_instance"
                    AND log_name="projects/PROJECT_ID/logs/cloudaudit.googleapis.com%2Fdata_access"
                    AND protoPayload.authorizationInfo.granted=false
                    """,
                    comparison=monitoring_v3.ComparisonType.COMPARISON_GT,
                    threshold_value=5,
                    duration={"seconds": 300},
                    aggregations=[{
                        "alignment_period": {"seconds": 60},
                        "per_series_aligner": "ALIGN_COUNT_TRUE"
                    }]
                )
            )
        ],
        notification_channels=[NOTIFICATION_CHANNEL_ID],
        alert_strategy={
            "notification_rate_limit": {
                "period": {"seconds": 3600}
            }
        }
    )
    
    client.create_alert_policy(name=project_name, alert_policy=alert_policy)
```

### 2. Log-Based Metrics

```bash
# Create metric for tracking query volume
gcloud logging metrics create spanner_query_count \
    --description="Count of Spanner queries" \
    --log-filter='
    protoPayload.serviceName="spanner.googleapis.com"
    AND protoPayload.methodName="google.spanner.v1.Spanner.ExecuteSql"'

# Create metric for slow queries
gcloud logging metrics create spanner_slow_queries \
    --description="Count of slow Spanner queries" \
    --log-filter='
    protoPayload.serviceName="spanner.googleapis.com"
    AND protoPayload.methodName="google.spanner.v1.Spanner.ExecuteSql"
    AND CAST(REGEXP_EXTRACT(protoPayload.processingDuration, r"(\d+\.?\d*)") AS FLOAT64) > 1.0'
```

## Compliance and Retention

### 1. Log Retention Configuration

```bash
# Set custom retention for audit logs
gcloud logging buckets update _Default \
    --location=global \
    --retention-days=365
```

### 2. Export to BigQuery

```bash
# Create log sink for long-term storage
gcloud logging sinks create spanner-audit-sink \
    bigquery.googleapis.com/projects/PROJECT_ID/datasets/audit_logs \
    --log-filter='
    protoPayload.serviceName="spanner.googleapis.com"' \
    --project=PROJECT_ID
```

### 3. Compliance Reports

```python
def generate_access_report(start_date, end_date):
    """Generate compliance report for data access."""
    query = f"""
    SELECT
      DATE(timestamp) as access_date,
      protoPayload.authenticationInfo.principalEmail as user,
      protoPayload.resourceName as resource,
      COUNT(*) as access_count,
      ARRAY_AGG(DISTINCT protoPayload.methodName) as operations
    FROM
      `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
    WHERE
      protoPayload.serviceName = "spanner.googleapis.com"
      AND timestamp >= TIMESTAMP('{start_date}')
      AND timestamp < TIMESTAMP('{end_date}')
    GROUP BY
      access_date, user, resource
    ORDER BY
      access_date, user
    """
    
    results = bigquery_client.query(query)
    return results.to_dataframe()
```

## Best Practices

### 1. Log Analysis Automation

```python
# Automated daily security report
def daily_security_scan():
    """Run daily security analysis on Spanner logs."""
    checks = [
        check_failed_authentications(),
        check_unusual_access_times(),
        check_large_data_exports(),
        check_schema_modifications(),
        check_permission_changes()
    ]
    
    findings = []
    for check in checks:
        if check['severity'] in ['HIGH', 'CRITICAL']:
            findings.append(check)
    
    if findings:
        send_security_alert(findings)
```

### 2. Performance Monitoring

```sql
-- Track query performance trends
SELECT
  DATE(timestamp) as date,
  APPROX_QUANTILES(
    CAST(REGEXP_EXTRACT(protoPayload.processingDuration, r'(\d+\.?\d*)') AS FLOAT64),
    100
  )[OFFSET(50)] as p50_duration,
  APPROX_QUANTILES(
    CAST(REGEXP_EXTRACT(protoPayload.processingDuration, r'(\d+\.?\d*)') AS FLOAT64),
    100
  )[OFFSET(95)] as p95_duration,
  APPROX_QUANTILES(
    CAST(REGEXP_EXTRACT(protoPayload.processingDuration, r'(\d+\.?\d*)') AS FLOAT64),
    100
  )[OFFSET(99)] as p99_duration
FROM
  `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
WHERE
  protoPayload.serviceName = "spanner.googleapis.com"
  AND protoPayload.methodName = "google.spanner.v1.Spanner.ExecuteSql"
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
GROUP BY
  date
ORDER BY
  date DESC
```

### 3. Integration with SIEM

```python
# Export logs to SIEM system
def export_to_siem(log_entries):
    """Export Spanner audit logs to SIEM."""
    for entry in log_entries:
        siem_event = {
            "timestamp": entry.timestamp,
            "event_type": "database_access",
            "source": "cloud_spanner",
            "user": entry.proto_payload.authentication_info.principal_email,
            "action": entry.proto_payload.method_name,
            "resource": entry.proto_payload.resource_name,
            "result": "success" if entry.proto_payload.authorization_info[0].granted else "failure",
            "duration": entry.proto_payload.processing_duration
        }
        
        siem_client.send_event(siem_event)
```