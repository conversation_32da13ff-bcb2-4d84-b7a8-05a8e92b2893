# VPC Service Controls Security Implementation

## Overview

VPC Service Controls is a Google Cloud security technology that protects sensitive data by creating security perimeters around Google Cloud resources. It helps mitigate data exfiltration risks from both external threats and insider actions.

## Core Concepts

### Service Perimeters

Service perimeters create security boundaries that:
- Allow free communication within the boundary
- Block unauthorized communication across boundaries
- Protect against data exfiltration
- Enforce context-aware access policies

```bash
# Create a service perimeter
gcloud access-context-manager perimeters create PERIMETER_NAME \
    --title="Production Data Perimeter" \
    --resources=projects/PROJECT_NUMBER \
    --restricted-services=storage.googleapis.com,bigquery.googleapis.com,spanner.googleapis.com \
    --access-levels=TRUSTED_NETWORK_ACCESS
```

### Access Levels

Define conditions that requests must meet to access resources:
- IP addresses or CIDR ranges
- Device attributes
- User identity attributes
- Request attributes

```yaml
# access-level-definition.yaml
name: accessPolicies/POLICY_ID/accessLevels/TRUSTED_CORP_NETWORK
title: "Trusted Corporate Network"
basic:
  combiningFunction: AND
  conditions:
  - ipSubnetworks:
    - "***********/24"  # Corporate network
    - "************/24" # VPN range
  - devicePolicy:
      requireCorpOwned: true
      requireScreenlock: true
```

## Implementation Patterns

### 1. Basic Perimeter Setup

```python
from google.cloud import access_context_manager_v1

def create_production_perimeter():
    """Create a VPC Service Controls perimeter for production."""
    
    client = access_context_manager_v1.AccessContextManagerClient()
    
    # Define the perimeter
    perimeter = {
        "name": f"accessPolicies/{POLICY_ID}/servicePerimeters/production_perimeter",
        "title": "Production Data Perimeter",
        "perimeter_type": "PERIMETER_TYPE_REGULAR",
        "status": {
            "resources": [
                f"projects/{PROJECT_NUMBER}",
            ],
            "restricted_services": [
                "storage.googleapis.com",
                "bigquery.googleapis.com",
                "spanner.googleapis.com",
                "compute.googleapis.com",
                "container.googleapis.com"
            ],
            "access_levels": [
                f"accessPolicies/{POLICY_ID}/accessLevels/trusted_network"
            ]
        }
    }
    
    # Create the perimeter
    operation = client.create_service_perimeter(
        parent=f"accessPolicies/{POLICY_ID}",
        service_perimeter=perimeter
    )
    
    return operation
```

### 2. Ingress and Egress Rules

```yaml
# ingress-rules.yaml
ingressPolicies:
- ingressFrom:
    identityType: ANY_IDENTITY
    sources:
    - accessLevel: accessPolicies/POLICY_ID/accessLevels/trusted_network
  ingressTo:
    operations:
    - serviceName: storage.googleapis.com
      methodSelectors:
      - method: "google.storage.v1.Storage.GetObject"
      - method: "google.storage.v1.Storage.ListObjects"
    resources:
    - "projects/PROJECT_NUMBER"

# egress-rules.yaml  
egressPolicies:
- egressFrom:
    identityType: ANY_IDENTITY
  egressTo:
    resources:
    - "projects/EXTERNAL_PROJECT_NUMBER"
    operations:
    - serviceName: bigquery.googleapis.com
      methodSelectors:
      - method: "google.cloud.bigquery.v2.TableService.GetTable"
```

### 3. Bridge Perimeters

```bash
# Create bridge perimeter for data sharing
gcloud access-context-manager perimeters create data_bridge \
    --perimeter-type=bridge \
    --title="Data Sharing Bridge" \
    --resources=projects/PROJECT_A,projects/PROJECT_B \
    --restricted-services=""
```

## Security Architecture

### 1. Multi-Project Organization

```python
def setup_multi_project_perimeters():
    """Set up VPC Service Controls for multi-project organization."""
    
    perimeters = {
        "development": {
            "projects": ["dev-project-1", "dev-project-2"],
            "services": ["storage.googleapis.com", "compute.googleapis.com"],
            "access_levels": ["dev_network", "dev_users"]
        },
        "staging": {
            "projects": ["staging-project"],
            "services": ["storage.googleapis.com", "bigquery.googleapis.com"],
            "access_levels": ["staging_network", "staging_users"]
        },
        "production": {
            "projects": ["prod-data", "prod-compute", "prod-analytics"],
            "services": [
                "storage.googleapis.com",
                "bigquery.googleapis.com", 
                "spanner.googleapis.com",
                "compute.googleapis.com"
            ],
            "access_levels": ["prod_network", "prod_users", "break_glass"]
        }
    }
    
    for env, config in perimeters.items():
        create_perimeter(env, config)
```

### 2. Access Level Configuration

```python
def create_comprehensive_access_levels():
    """Create access levels for different scenarios."""
    
    access_levels = [
        {
            "name": "corporate_network",
            "title": "Corporate Network Access",
            "basic": {
                "conditions": [{
                    "ip_subnetworks": ["10.0.0.0/8", "**********/12"]
                }]
            }
        },
        {
            "name": "trusted_devices",
            "title": "Trusted Corporate Devices",
            "basic": {
                "combining_function": "AND",
                "conditions": [
                    {
                        "device_policy": {
                            "require_corp_owned": True,
                            "require_screenlock": True,
                            "os_constraints": [{
                                "os_type": "DESKTOP_WINDOWS",
                                "minimum_version": "10.0.0"
                            }]
                        }
                    },
                    {
                        "members": ["group:<EMAIL>"]
                    }
                ]
            }
        },
        {
            "name": "emergency_access",
            "title": "Emergency Break Glass Access",
            "basic": {
                "combining_function": "AND",
                "conditions": [
                    {
                        "members": ["group:<EMAIL>"]
                    },
                    {
                        "ip_subnetworks": ["***********/24"]  # Security ops center
                    }
                ]
            }
        }
    ]
    
    for level in access_levels:
        create_access_level(level)
```

### 3. Dry Run Mode

```bash
# Create perimeter in dry run mode for testing
gcloud access-context-manager perimeters dry-run create PERIMETER_NAME \
    --perimeter=existing_perimeter \
    --add-resources=projects/NEW_PROJECT_NUMBER \
    --add-restricted-services=pubsub.googleapis.com
```

## Best Practices

### 1. Gradual Rollout

```python
def gradual_perimeter_rollout():
    """Implement VPC Service Controls gradually."""
    
    phases = [
        {
            "phase": 1,
            "description": "Dry run mode - monitoring only",
            "actions": [
                "Create perimeter in dry run mode",
                "Monitor Cloud Audit Logs for violations",
                "Identify and document legitimate access patterns"
            ]
        },
        {
            "phase": 2,
            "description": "Limited enforcement",
            "actions": [
                "Enforce perimeter for non-critical services",
                "Create ingress/egress rules for known patterns",
                "Test emergency access procedures"
            ]
        },
        {
            "phase": 3,
            "description": "Full enforcement",
            "actions": [
                "Enable enforcement for all services",
                "Remove dry run configuration",
                "Implement monitoring and alerting"
            ]
        }
    ]
    
    return phases
```

### 2. Monitoring and Alerting

```sql
-- Monitor VPC Service Controls violations
SELECT
  timestamp,
  protoPayload.authenticationInfo.principalEmail as user,
  protoPayload.resourceName as resource,
  protoPayload.status.message as violation_reason,
  protoPayload.requestMetadata.callerIp as source_ip,
  protoPayload.methodName as attempted_operation
FROM
  `PROJECT_ID.DATASET.cloudaudit_googleapis_com_policy`
WHERE
  protoPayload.status.code = 7  -- PERMISSION_DENIED
  AND protoPayload.status.message LIKE '%VPC Service Controls%'
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
ORDER BY
  timestamp DESC
```

### 3. Emergency Access Procedures

```python
def emergency_access_procedure(user_email, reason, duration_hours=4):
    """Grant emergency access through VPC Service Controls."""
    
    # Create temporary access level
    emergency_level = {
        "name": f"emergency_access_{int(time.time())}",
        "title": f"Emergency Access - {reason}",
        "basic": {
            "combining_function": "OR",
            "conditions": [
                {
                    "members": [f"user:{user_email}"]
                },
                {
                    # Existing emergency access group
                    "members": ["group:<EMAIL>"]
                }
            ]
        }
    }
    
    # Add access level
    create_access_level(emergency_level)
    
    # Update perimeter
    add_access_level_to_perimeter("production_perimeter", emergency_level["name"])
    
    # Schedule removal
    schedule_access_removal(emergency_level["name"], duration_hours)
    
    # Log and notify
    log_emergency_access(user_email, reason, duration_hours)
    notify_security_team(user_email, reason)
```

## Integration Patterns

### 1. Hybrid Connectivity

```yaml
# Configure for on-premises access
accessLevels:
- name: on_premises_access
  title: "On-Premises Network Access"
  basic:
    conditions:
    - ipSubnetworks:
      - "***********/16"    # On-premises range via VPN
      - "10.0.0.0/8"        # Cloud VPC ranges
    - regions:
      - "US"
      - "EU"
```

### 2. Private Google Access

```bash
# Enable Private Google Access for VPC
gcloud compute networks subnets update SUBNET_NAME \
    --region=REGION \
    --enable-private-ip-google-access

# Configure DNS for private access
gcloud dns managed-zones create google-apis \
    --dns-name="googleapis.com." \
    --description="Private Google APIs" \
    --networks=VPC_NETWORK \
    --visibility=private
```

### 3. Cloud Run Integration

```python
def configure_cloud_run_perimeter():
    """Configure VPC Service Controls for Cloud Run."""
    
    # Update perimeter for Cloud Run
    perimeter_update = {
        "updateMask": "status.restrictedServices,status.vpcAccessibleServices",
        "servicePerimeter": {
            "status": {
                "restrictedServices": [
                    "run.googleapis.com",
                    "containerregistry.googleapis.com"
                ],
                "vpcAccessibleServices": {
                    "enableRestriction": True,
                    "allowedServices": [
                        "storage.googleapis.com",
                        "secretmanager.googleapis.com"
                    ]
                }
            }
        }
    }
    
    update_perimeter("production_perimeter", perimeter_update)
```

## Troubleshooting

### 1. Common Issues

```python
def diagnose_vpc_sc_issues(project_id, user_email):
    """Diagnose common VPC Service Controls issues."""
    
    diagnostics = []
    
    # Check if project is in a perimeter
    perimeters = list_perimeters_for_project(project_id)
    if not perimeters:
        diagnostics.append({
            "issue": "Project not in any perimeter",
            "severity": "INFO",
            "resolution": "Add project to appropriate perimeter"
        })
    
    # Check access levels
    for perimeter in perimeters:
        access_levels = get_perimeter_access_levels(perimeter)
        user_has_access = check_user_access_levels(user_email, access_levels)
        
        if not user_has_access:
            diagnostics.append({
                "issue": f"User not in access levels for {perimeter}",
                "severity": "HIGH",
                "resolution": "Add user to appropriate access level or create new one"
            })
    
    # Check for dry run violations
    violations = get_dry_run_violations(project_id)
    if violations:
        diagnostics.append({
            "issue": f"Found {len(violations)} dry run violations",
            "severity": "MEDIUM",
            "violations": violations[:5]  # First 5
        })
    
    return diagnostics
```

### 2. Access Patterns Analysis

```sql
-- Analyze access patterns for creating rules
WITH access_patterns AS (
  SELECT
    protoPayload.authenticationInfo.principalEmail as principal,
    protoPayload.requestMetadata.callerIp as source_ip,
    protoPayload.serviceName as service,
    protoPayload.methodName as method,
    protoPayload.resourceName as resource,
    COUNT(*) as access_count
  FROM
    `PROJECT_ID.DATASET.cloudaudit_googleapis_com_data_access`
  WHERE
    timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
    AND protoPayload.serviceName IN ('storage.googleapis.com', 'bigquery.googleapis.com')
  GROUP BY
    principal, source_ip, service, method, resource
)
SELECT
  principal,
  source_ip,
  service,
  method,
  COUNT(DISTINCT resource) as unique_resources,
  SUM(access_count) as total_accesses
FROM
  access_patterns
GROUP BY
  principal, source_ip, service, method
HAVING
  total_accesses > 10
ORDER BY
  total_accesses DESC
```

### 3. Migration Planning

```python
def plan_vpc_sc_migration(organization_id):
    """Plan VPC Service Controls migration for organization."""
    
    migration_plan = {
        "assessment": {
            "projects": list_all_projects(organization_id),
            "services_in_use": identify_services_in_use(),
            "cross_project_dependencies": find_cross_project_dependencies(),
            "external_dependencies": find_external_api_dependencies()
        },
        "perimeter_design": {
            "proposed_perimeters": design_perimeters(),
            "access_levels": design_access_levels(),
            "ingress_egress_rules": design_data_exchange_rules()
        },
        "rollout_plan": {
            "phase1": "Create perimeters in dry run mode",
            "phase2": "Implement ingress/egress rules",
            "phase3": "Test with non-critical projects",
            "phase4": "Full production rollout"
        },
        "risk_mitigation": {
            "emergency_access": design_break_glass_procedures(),
            "rollback_plan": create_rollback_procedures(),
            "monitoring": setup_monitoring_alerts()
        }
    }
    
    return migration_plan
```

## Compliance and Auditing

### 1. Compliance Reporting

```python
def generate_vpc_sc_compliance_report():
    """Generate VPC Service Controls compliance report."""
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "perimeters": []
    }
    
    for perimeter in list_all_perimeters():
        perimeter_report = {
            "name": perimeter.name,
            "resources_protected": len(perimeter.status.resources),
            "services_restricted": perimeter.status.restricted_services,
            "access_levels": perimeter.status.access_levels,
            "violations_last_30d": count_violations(perimeter.name, days=30),
            "configuration_changes": get_config_changes(perimeter.name),
            "compliance_status": evaluate_compliance(perimeter)
        }
        
        report["perimeters"].append(perimeter_report)
    
    return report
```

### 2. Audit Trail Enhancement

```sql
-- Create comprehensive audit view
CREATE OR REPLACE VIEW vpc_sc_audit AS
SELECT
  timestamp,
  LOG_TYPE,
  protoPayload.authenticationInfo.principalEmail as actor,
  protoPayload.methodName as action,
  protoPayload.resourceName as target,
  protoPayload.status.code as status_code,
  protoPayload.status.message as status_message,
  protoPayload.request as request_details,
  protoPayload.response as response_details
FROM (
  SELECT 'POLICY_VIOLATION' as LOG_TYPE, * 
  FROM `PROJECT_ID.DATASET.cloudaudit_googleapis_com_policy`
  UNION ALL
  SELECT 'CONFIG_CHANGE' as LOG_TYPE, *
  FROM `PROJECT_ID.DATASET.cloudaudit_googleapis_com_activity`
  WHERE protoPayload.serviceName = 'accesscontextmanager.googleapis.com'
)
WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 DAY)
```