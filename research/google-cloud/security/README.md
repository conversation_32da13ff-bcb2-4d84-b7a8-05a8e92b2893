# Google Cloud Security Documentation

This directory contains comprehensive security documentation for implementing production-grade security on Google Cloud Platform, with a focus on Cloud Run, Spanner, and related services.

## Documentation Overview

### 1. Cloud Run Security
- **[Cloud Run Security Overview](./cloud-run-security-overview.md)**: Architecture, security features, and threat model
- **[Secure Services Tutorial](./cloud-run-secure-services-tutorial.md)**: Step-by-step guide for implementing secure service-to-service communication
- **[Authentication Overview](./cloud-run-authentication-overview.md)**: Authentication methods and implementation patterns
- **[Service-to-Service Authentication](./cloud-run-service-to-service-auth.md)**: Detailed patterns for secure inter-service communication
- **[VPC Configuration](./cloud-run-vpc-configuration.md)**: Network security and VPC integration

### 2. Spanner Security
- **[IAM Security](./spanner-iam-security.md)**: Identity and access management for Spanner
- **[Encryption and CMEK](./spanner-encryption-cmek.md)**: Data encryption and customer-managed encryption keys
- **[Audit Logging](./spanner-audit-logging.md)**: Comprehensive audit logging and monitoring

### 3. IAM and Service Accounts
- **[IAM Best Practices](./iam-best-practices.md)**: Core security principles and implementation guidelines
- **[Service Account Best Practices](./service-account-best-practices.md)**: Secure service account management

### 4. Advanced Security
- **[VPC Service Controls](./vpc-service-controls.md)**: Data exfiltration prevention and security perimeters
- **[Security Command Center](./security-command-center.md)**: Centralized security management and threat detection

## Quick Start Security Checklist

### Cloud Run Deployment
- [ ] Deploy services privately by default
- [ ] Use dedicated service accounts with minimal permissions
- [ ] Implement service-to-service authentication
- [ ] Configure VPC egress for internal resources
- [ ] Enable Binary Authorization
- [ ] Set up Cloud Armor for DDoS protection

### Spanner Security
- [ ] Use customer-managed encryption keys (CMEK)
- [ ] Enable audit logging for all databases
- [ ] Implement least-privilege IAM policies
- [ ] Regular key rotation
- [ ] Monitor access patterns

### General Security
- [ ] Enable Security Command Center
- [ ] Configure VPC Service Controls
- [ ] Implement workload identity
- [ ] Avoid service account keys
- [ ] Regular security audits
- [ ] Automated compliance monitoring

## Implementation Priority

### Phase 1: Foundation (Week 1-2)
1. Enable audit logging across all services
2. Implement proper IAM hierarchy
3. Create dedicated service accounts
4. Enable Security Command Center

### Phase 2: Network Security (Week 3-4)
1. Configure VPC for Cloud Run
2. Implement VPC Service Controls
3. Set up private Google access
4. Configure firewall rules

### Phase 3: Data Protection (Week 5-6)
1. Enable CMEK for Spanner
2. Implement Binary Authorization
3. Configure backup encryption
4. Set up data loss prevention

### Phase 4: Monitoring & Response (Week 7-8)
1. Configure Security Command Center policies
2. Set up automated threat detection
3. Implement incident response procedures
4. Create security dashboards

## Security Architecture Patterns

### Zero-Trust Architecture
```
Internet → Cloud Armor → Cloud Run (Private) → VPC → Spanner (CMEK)
              ↓                ↓                  ↓         ↓
         WAF Rules    Service Identity    VPC SC    Audit Logs
```

### Defense in Depth
1. **Edge Security**: Cloud Armor, DDoS protection
2. **Application Security**: IAM, service accounts, authentication
3. **Network Security**: VPC, firewall rules, private endpoints
4. **Data Security**: Encryption at rest/transit, CMEK, DLP
5. **Monitoring**: Audit logs, Security Command Center, alerts

## Common Security Patterns

### 1. Secure Service Communication
```python
# Frontend service calling backend
headers = {
    'Authorization': f'Bearer {get_identity_token(BACKEND_URL)}'
}
response = requests.get(BACKEND_URL, headers=headers)
```

### 2. Least Privilege Service Account
```bash
# Create minimal service account
gcloud iam service-accounts create app-sa \
    --display-name="Application Service Account"

# Grant only necessary permissions
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:app-sa@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/spanner.databaseUser"
```

### 3. VPC Service Controls
```yaml
# Protect sensitive data
servicePerimeter:
  resources:
    - projects/PROJECT_NUMBER
  restrictedServices:
    - storage.googleapis.com
    - bigquery.googleapis.com
    - spanner.googleapis.com
```

## Security Tools and Commands

### Audit and Monitoring
```bash
# Check IAM policies
gcloud projects get-iam-policy PROJECT_ID

# View audit logs
gcloud logging read "protoPayload.serviceName=spanner.googleapis.com" \
    --limit=50 --format=json

# List service accounts
gcloud iam service-accounts list
```

### Security Validation
```bash
# Test Cloud Run authentication
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
    https://SERVICE-URL

# Check VPC Service Controls
gcloud access-context-manager perimeters list

# Verify CMEK configuration
gcloud spanner databases describe DATABASE_ID \
    --instance=INSTANCE_ID
```

## Compliance Mappings

### CIS Google Cloud Foundation Benchmark
- **1.x Identity and Access Management**: See IAM best practices
- **2.x Logging and Monitoring**: See audit logging guides
- **3.x Networking**: See VPC configuration
- **4.x Virtual Machines**: N/A (using Cloud Run)
- **5.x Storage**: See CMEK implementation
- **6.x Cloud SQL**: See Spanner security
- **7.x BigQuery**: See VPC Service Controls

### PCI DSS Requirements
- **Requirement 1**: Network segmentation via VPC
- **Requirement 2**: Default security configurations
- **Requirement 3**: Encryption with CMEK
- **Requirement 7**: Least privilege access
- **Requirement 8**: Strong authentication
- **Requirement 10**: Audit logging

## Emergency Procedures

### Compromised Service Account
1. Disable all keys immediately
2. Revoke IAM permissions
3. Audit recent activity
4. Rotate affected resources
5. Implement additional monitoring

### Data Breach Response
1. Enable VPC Service Controls emergency perimeter
2. Revoke external access
3. Capture forensic data
4. Notify security team
5. Begin incident investigation

## Resources and References

### Official Documentation
- [Cloud Run Security](https://cloud.google.com/run/docs/securing)
- [Spanner Security](https://cloud.google.com/spanner/docs/security)
- [IAM Best Practices](https://cloud.google.com/iam/docs/using-iam-securely)
- [Security Command Center](https://cloud.google.com/security-command-center/docs)

### Security Tools
- [Security Command Center](https://console.cloud.google.com/security/command-center)
- [IAM Recommender](https://console.cloud.google.com/iam-admin/recommender)
- [Cloud Asset Inventory](https://console.cloud.google.com/assets)
- [VPC Service Controls](https://console.cloud.google.com/security/service-perimeter)

### Monitoring Dashboards
- Create custom dashboards for:
  - Failed authentication attempts
  - Service account activity
  - Data access patterns
  - Security findings trends

## Next Steps

1. Review all documentation in order
2. Assess current security posture
3. Create implementation plan
4. Execute security improvements
5. Set up continuous monitoring
6. Schedule regular security reviews

For questions or additional security requirements, consult with the Google Cloud security team or professional services.