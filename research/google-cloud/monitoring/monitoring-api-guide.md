# Google Cloud Monitoring API v3 - Comprehensive Guide

## Overview
The Cloud Monitoring API v3 provides programmatic access to approximately 6,500 metrics from Google Cloud and Amazon Web Services. It enables creation of custom metrics, management of alerting policies, and comprehensive monitoring of cloud resources.

## Core Concepts

### Monitored Resources
Monitored resources are entities from which metrics are collected:
- **VM instances**: Compute Engine virtual machines
- **Cloud Storage buckets**: Storage resources
- **Cloud SQL databases**: Managed database instances
- **Load balancers**: Traffic distribution resources
- **Kubernetes clusters**: Container orchestration resources

### Metric Types
1. **System Metrics**: Automatically collected by Google Cloud
2. **Custom Metrics**: User-defined metrics for application-specific monitoring
3. **Log-based Metrics**: Derived from log entries
4. **External Metrics**: From AWS or other external sources

### Time Series Data
- Each metric is stored as time series data
- Contains data points with timestamps
- Supports various aggregation methods
- Enables historical analysis and trending

## API Resources

### 1. Metric Descriptors
Define the structure and metadata of metrics:
```python
from google.cloud import monitoring_v3

client = monitoring_v3.MetricServiceClient()
project_name = f"projects/{project_id}"

# Create custom metric descriptor
descriptor = monitoring_v3.MetricDescriptor(
    type="custom.googleapis.com/my_app/request_count",
    metric_kind=monitoring_v3.MetricDescriptor.MetricKind.CUMULATIVE,
    value_type=monitoring_v3.MetricDescriptor.ValueType.INT64,
    description="Number of requests to my application",
    display_name="Request Count",
    labels=[
        monitoring_v3.LabelDescriptor(
            key="endpoint",
            value_type=monitoring_v3.LabelDescriptor.ValueType.STRING,
            description="API endpoint"
        ),
        monitoring_v3.LabelDescriptor(
            key="status_code",
            value_type=monitoring_v3.LabelDescriptor.ValueType.INT64,
            description="HTTP status code"
        )
    ]
)

# Create the metric descriptor
created = client.create_metric_descriptor(
    name=project_name,
    metric_descriptor=descriptor
)
```

### 2. Time Series
Write and query time series data:
```python
# Write time series data
from google.cloud import monitoring_v3
import time

client = monitoring_v3.MetricServiceClient()
project_name = f"projects/{project_id}"

# Create time series
series = monitoring_v3.TimeSeries()
series.metric.type = "custom.googleapis.com/my_app/request_count"
series.metric.labels["endpoint"] = "/api/users"
series.metric.labels["status_code"] = "200"

# Set resource
series.resource.type = "global"
series.resource.labels["project_id"] = project_id

# Add data point
now = time.time()
seconds = int(now)
nanos = int((now - seconds) * 10 ** 9)
interval = monitoring_v3.TimeInterval(
    {"end_time": {"seconds": seconds, "nanos": nanos}}
)
point = monitoring_v3.Point(
    {"interval": interval, "value": {"int64_value": 100}}
)
series.points = [point]

# Write time series
client.create_time_series(name=project_name, time_series=[series])
```

### 3. Alerting Policies
Configure automated alerting:
```python
from google.cloud import monitoring_v3

alert_client = monitoring_v3.AlertPolicyServiceClient()
project_name = f"projects/{project_id}"

# Create alerting policy
policy = monitoring_v3.AlertPolicy(
    display_name="High CPU Usage Alert",
    conditions=[
        monitoring_v3.AlertPolicy.Condition(
            display_name="CPU usage above 80%",
            condition_threshold=monitoring_v3.AlertPolicy.Condition.MetricThreshold(
                filter='metric.type="compute.googleapis.com/instance/cpu/utilization" '
                       'resource.type="gce_instance"',
                comparison=monitoring_v3.ComparisonType.COMPARISON_GT,
                threshold_value=0.8,
                duration={"seconds": 300},  # 5 minutes
                aggregations=[
                    monitoring_v3.Aggregation(
                        alignment_period={"seconds": 60},
                        per_series_aligner=monitoring_v3.Aggregation.Aligner.ALIGN_MEAN,
                    )
                ],
            ),
        )
    ],
    notification_channels=[channel_name],  # Add notification channels
    alert_strategy=monitoring_v3.AlertPolicy.AlertStrategy(
        notification_rate_limit=monitoring_v3.AlertPolicy.AlertStrategy.NotificationRateLimit(
            period={"seconds": 300}  # Limit to one notification per 5 minutes
        )
    ),
)

# Create the alert policy
created_policy = alert_client.create_alert_policy(
    name=project_name,
    alert_policy=policy
)
```

### 4. Notification Channels
Set up alert delivery methods:
```python
notification_client = monitoring_v3.NotificationChannelServiceClient()

# Create email notification channel
email_channel = monitoring_v3.NotificationChannel(
    type="email",
    display_name="DevOps Team Email",
    labels={"email_address": "<EMAIL>"},
    enabled=True,
)

created_channel = notification_client.create_notification_channel(
    name=project_name,
    notification_channel=email_channel
)

# Create Slack notification channel
slack_channel = monitoring_v3.NotificationChannel(
    type="slack",
    display_name="DevOps Slack Channel",
    labels={"channel_name": "#alerts"},
    enabled=True,
    user_labels={"severity": "critical"},
)
```

### 5. Service Monitoring
Track service-level objectives (SLOs):
```python
from google.cloud import monitoring_v3

service_client = monitoring_v3.ServiceMonitoringServiceClient()
slo_client = monitoring_v3.ServiceLevelObjectiveServiceClient()

# Create service
service = monitoring_v3.Service(
    display_name="My API Service",
    custom=monitoring_v3.Service.Custom(),
)

created_service = service_client.create_service(
    parent=project_name,
    service=service
)

# Create SLO
slo = monitoring_v3.ServiceLevelObjective(
    display_name="99.9% Availability SLO",
    goal=0.999,
    rolling_period={"seconds": 86400 * 30},  # 30 days
    service_level_indicator=monitoring_v3.ServiceLevelIndicator(
        request_based=monitoring_v3.RequestBasedSli(
            good_total_ratio=monitoring_v3.RequestBasedSli.GoodTotalRatio(
                good_service_filter='metric.type="serviceruntime.googleapis.com/api/request_count" '
                                   'resource.type="api" '
                                   'metric.label.response_code_class="2xx"',
                total_service_filter='metric.type="serviceruntime.googleapis.com/api/request_count" '
                                    'resource.type="api"',
            )
        )
    ),
)

created_slo = slo_client.create_service_level_objective(
    parent=created_service.name,
    service_level_objective=slo
)
```

## Query Methods

### Monitoring Query Language (MQL)
```python
# Query using MQL
from google.cloud.monitoring_dashboard import v1

query = """
fetch gce_instance
| metric 'compute.googleapis.com/instance/cpu/utilization'
| filter zone =~ 'us-central.*'
| group_by 5m, [value_utilization_mean: mean(value.utilization)]
| every 5m
| condition value_utilization_mean > 0.8
"""

# Execute query
results = client.query_time_series(
    name=project_name,
    query=query
)
```

### List Time Series
```python
# List time series with filtering
interval = monitoring_v3.TimeInterval(
    {
        "end_time": {"seconds": int(time.time())},
        "start_time": {"seconds": int(time.time() - 3600)},  # Last hour
    }
)

results = client.list_time_series(
    request={
        "name": project_name,
        "filter": 'metric.type="compute.googleapis.com/instance/cpu/utilization"',
        "interval": interval,
        "view": monitoring_v3.ListTimeSeriesRequest.TimeSeriesView.FULL,
    }
)

for result in results:
    print(f"Metric: {result.metric}")
    for point in result.points:
        print(f"  {point.interval.end_time}: {point.value.double_value}")
```

## Custom Metrics

### Creating Custom Metrics
```python
def write_custom_metric(project_id, metric_type, value, labels=None):
    """Write a custom metric data point."""
    client = monitoring_v3.MetricServiceClient()
    project_name = f"projects/{project_id}"
    
    series = monitoring_v3.TimeSeries()
    series.metric.type = f"custom.googleapis.com/{metric_type}"
    
    # Add labels
    if labels:
        for key, value in labels.items():
            series.metric.labels[key] = str(value)
    
    # Set resource
    series.resource.type = "global"
    series.resource.labels["project_id"] = project_id
    
    # Create point
    now = time.time()
    seconds = int(now)
    nanos = int((now - seconds) * 10 ** 9)
    
    interval = monitoring_v3.TimeInterval(
        {"end_time": {"seconds": seconds, "nanos": nanos}}
    )
    
    point = monitoring_v3.Point(
        {"interval": interval, "value": {"double_value": value}}
    )
    
    series.points = [point]
    
    # Write time series
    client.create_time_series(name=project_name, time_series=[series])
```

### Application Metrics Example
```python
class ApplicationMetrics:
    """Helper class for application metrics."""
    
    def __init__(self, project_id):
        self.client = monitoring_v3.MetricServiceClient()
        self.project_name = f"projects/{project_id}"
        self.project_id = project_id
    
    def record_request_latency(self, endpoint, method, latency_ms):
        """Record API request latency."""
        self._write_metric(
            metric_type="api/request_latency",
            value=latency_ms,
            labels={
                "endpoint": endpoint,
                "method": method
            }
        )
    
    def record_error_count(self, error_type, service):
        """Record error occurrence."""
        self._write_metric(
            metric_type="errors/count",
            value=1,
            labels={
                "error_type": error_type,
                "service": service
            }
        )
    
    def record_queue_depth(self, queue_name, depth):
        """Record message queue depth."""
        self._write_metric(
            metric_type="queue/depth",
            value=depth,
            labels={"queue_name": queue_name}
        )
    
    def _write_metric(self, metric_type, value, labels=None):
        """Internal method to write metrics."""
        series = monitoring_v3.TimeSeries()
        series.metric.type = f"custom.googleapis.com/{metric_type}"
        
        if labels:
            for key, val in labels.items():
                series.metric.labels[key] = str(val)
        
        series.resource.type = "global"
        series.resource.labels["project_id"] = self.project_id
        
        now = time.time()
        seconds = int(now)
        nanos = int((now - seconds) * 10 ** 9)
        
        interval = monitoring_v3.TimeInterval(
            {"end_time": {"seconds": seconds, "nanos": nanos}}
        )
        
        point = monitoring_v3.Point(
            {"interval": interval, "value": {"double_value": float(value)}}
        )
        
        series.points = [point]
        
        try:
            self.client.create_time_series(
                name=self.project_name,
                time_series=[series]
            )
        except Exception as e:
            print(f"Failed to write metric: {e}")
```

## Dashboard API

### Creating Dashboards Programmatically
```python
from google.cloud import monitoring_dashboard_v1

dashboard_client = monitoring_dashboard_v1.DashboardsServiceClient()

# Define dashboard
dashboard = monitoring_dashboard_v1.Dashboard(
    display_name="Application Performance Dashboard",
    grid_layout=monitoring_dashboard_v1.GridLayout(
        widgets=[
            monitoring_dashboard_v1.Widget(
                title="CPU Utilization",
                xy_chart=monitoring_dashboard_v1.XyChart(
                    data_sets=[
                        monitoring_dashboard_v1.XyChart.DataSet(
                            time_series_query=monitoring_dashboard_v1.XyChart.TimeSeriesQuery(
                                time_series_filter=monitoring_dashboard_v1.XyChart.TimeSeriesFilter(
                                    filter='metric.type="compute.googleapis.com/instance/cpu/utilization" '
                                           'resource.type="gce_instance"',
                                    aggregation=monitoring_dashboard_v1.Aggregation(
                                        alignment_period={"seconds": 60},
                                        per_series_aligner=monitoring_dashboard_v1.Aggregation.Aligner.ALIGN_MEAN,
                                        cross_series_reducer=monitoring_dashboard_v1.Aggregation.Reducer.REDUCE_MEAN,
                                        group_by_fields=["resource.instance_id"],
                                    ),
                                )
                            ),
                            plot_type=monitoring_dashboard_v1.XyChart.DataSet.PlotType.LINE,
                        )
                    ],
                    y_axis=monitoring_dashboard_v1.XyChart.Axis(
                        label="CPU Utilization (%)",
                        scale=monitoring_dashboard_v1.XyChart.Axis.Scale.LINEAR,
                    ),
                ),
            ),
            monitoring_dashboard_v1.Widget(
                title="Request Latency",
                xy_chart=monitoring_dashboard_v1.XyChart(
                    data_sets=[
                        monitoring_dashboard_v1.XyChart.DataSet(
                            time_series_query=monitoring_dashboard_v1.XyChart.TimeSeriesQuery(
                                time_series_filter=monitoring_dashboard_v1.XyChart.TimeSeriesFilter(
                                    filter='metric.type="custom.googleapis.com/api/request_latency"',
                                    aggregation=monitoring_dashboard_v1.Aggregation(
                                        alignment_period={"seconds": 60},
                                        per_series_aligner=monitoring_dashboard_v1.Aggregation.Aligner.ALIGN_DELTA,
                                        cross_series_reducer=monitoring_dashboard_v1.Aggregation.Reducer.REDUCE_PERCENTILE_95,
                                        group_by_fields=["metric.endpoint"],
                                    ),
                                )
                            ),
                            plot_type=monitoring_dashboard_v1.XyChart.DataSet.PlotType.LINE,
                        )
                    ],
                ),
            ),
        ]
    ),
)

# Create dashboard
created = dashboard_client.create_dashboard(
    parent=project_name,
    dashboard=dashboard
)
```

## Integration Patterns

### 1. Application Performance Monitoring
```python
import time
import functools
from google.cloud import monitoring_v3

class PerformanceMonitor:
    def __init__(self, project_id):
        self.metrics = ApplicationMetrics(project_id)
    
    def track_latency(self, endpoint):
        """Decorator to track function execution latency."""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start = time.time()
                try:
                    result = func(*args, **kwargs)
                    latency_ms = (time.time() - start) * 1000
                    self.metrics.record_request_latency(
                        endpoint=endpoint,
                        method=func.__name__,
                        latency_ms=latency_ms
                    )
                    return result
                except Exception as e:
                    self.metrics.record_error_count(
                        error_type=type(e).__name__,
                        service=endpoint
                    )
                    raise
            return wrapper
        return decorator

# Usage
monitor = PerformanceMonitor(project_id)

@monitor.track_latency("/api/users")
def get_users():
    # Simulate API call
    time.sleep(0.1)
    return {"users": []}
```

### 2. Infrastructure Monitoring
```python
class InfrastructureMonitor:
    def __init__(self, project_id):
        self.client = monitoring_v3.MetricServiceClient()
        self.project_name = f"projects/{project_id}"
    
    def get_instance_metrics(self, instance_id, metric_type, hours=1):
        """Get metrics for a specific instance."""
        interval = monitoring_v3.TimeInterval(
            {
                "end_time": {"seconds": int(time.time())},
                "start_time": {"seconds": int(time.time() - hours * 3600)},
            }
        )
        
        filter_str = (
            f'metric.type="{metric_type}" '
            f'resource.type="gce_instance" '
            f'resource.label.instance_id="{instance_id}"'
        )
        
        results = self.client.list_time_series(
            request={
                "name": self.project_name,
                "filter": filter_str,
                "interval": interval,
                "view": monitoring_v3.ListTimeSeriesRequest.TimeSeriesView.FULL,
            }
        )
        
        return list(results)
    
    def get_all_instances_cpu(self):
        """Get CPU usage for all instances."""
        return self.get_aggregate_metric(
            metric_type="compute.googleapis.com/instance/cpu/utilization",
            resource_type="gce_instance",
            aggregation="MEAN"
        )
```

### 3. Log-based Metrics
```python
from google.cloud import logging_v2

def create_log_based_metric(project_id, metric_name, log_filter, description):
    """Create a metric from log entries."""
    client = logging_v2.MetricsServiceV2Client()
    project_name = f"projects/{project_id}"
    
    metric = logging_v2.LogMetric(
        name=metric_name,
        description=description,
        filter=log_filter,
        metric_descriptor=monitoring_v3.MetricDescriptor(
            metric_kind=monitoring_v3.MetricDescriptor.MetricKind.DELTA,
            value_type=monitoring_v3.MetricDescriptor.ValueType.INT64,
        ),
        label_extractors={
            "status_code": "EXTRACT(jsonPayload.status_code)",
            "method": "EXTRACT(jsonPayload.method)",
        },
    )
    
    created = client.create_log_metric(
        parent=project_name,
        metric=metric
    )
    
    return created

# Example: Create metric for 5xx errors
create_log_based_metric(
    project_id=project_id,
    metric_name="api_5xx_errors",
    log_filter='jsonPayload.status_code >= 500 AND jsonPayload.status_code < 600',
    description="Count of 5xx HTTP errors"
)
```

## Best Practices

### 1. Metric Design
- Use descriptive metric names
- Include relevant labels for filtering
- Choose appropriate metric kinds (GAUGE, DELTA, CUMULATIVE)
- Avoid high-cardinality labels

### 2. Alert Configuration
- Set meaningful thresholds based on baselines
- Use appropriate aggregation windows
- Configure notification rate limits
- Test alerts before production deployment

### 3. Performance Optimization
- Batch metric writes when possible
- Use appropriate sampling rates
- Implement client-side aggregation for high-frequency metrics
- Cache metric descriptors

### 4. Cost Management
- Monitor metric ingestion rates
- Use metric filtering in queries
- Set appropriate data retention policies
- Archive old data to Cloud Storage

### 5. Security
- Use service accounts with minimal permissions
- Encrypt sensitive metric data
- Audit metric access
- Implement metric data governance

## Advanced Features

### Multi-Project Monitoring
```python
def get_metrics_across_projects(organization_id, metric_type):
    """Query metrics across all projects in organization."""
    # Requires appropriate IAM permissions
    client = monitoring_v3.MetricServiceClient()
    
    # Create metrics scope for organization
    scope_name = f"organizations/{organization_id}"
    
    # Query across all projects
    results = client.list_time_series(
        request={
            "name": scope_name,
            "filter": f'metric.type="{metric_type}"',
            "interval": interval,
            "view": monitoring_v3.ListTimeSeriesRequest.TimeSeriesView.FULL,
        }
    )
    
    return results
```

### Metric Export
```python
def export_metrics_to_bigquery(project_id, dataset_id, table_id):
    """Export metrics to BigQuery for analysis."""
    from google.cloud import bigquery
    
    monitoring_client = monitoring_v3.MetricServiceClient()
    bq_client = bigquery.Client()
    
    # Get metrics
    # ... (fetch metrics as shown earlier)
    
    # Format for BigQuery
    rows = []
    for ts in time_series:
        for point in ts.points:
            rows.append({
                "timestamp": point.interval.end_time,
                "metric_type": ts.metric.type,
                "value": point.value.double_value,
                "labels": dict(ts.metric.labels),
                "resource_type": ts.resource.type,
            })
    
    # Insert into BigQuery
    table_ref = bq_client.dataset(dataset_id).table(table_id)
    job = bq_client.insert_rows_json(table_ref, rows)
    
    if job.errors:
        print(f"Errors: {job.errors}")
```

## Conclusion
The Cloud Monitoring API v3 provides comprehensive capabilities for monitoring Google Cloud resources, creating custom metrics, and building sophisticated alerting systems. By leveraging these APIs, you can build robust monitoring solutions that scale with your infrastructure and provide deep insights into system performance and health.