# Google Cloud Structured Logging Guide

## Overview
Structured logging in Google Cloud uses JSON-formatted log entries that enable precise querying, better organization, and enhanced observability. This guide covers best practices, implementation patterns, and language-specific examples.

## Structured vs Unstructured Logs

### Unstructured Logs (textPayload)
```
2024-01-15 10:30:45 ERROR Failed to process order 12345 <NAME_EMAIL>
```

### Structured Logs (jsonPayload)
```json
{
  "timestamp": "2024-01-15T10:30:45Z",
  "severity": "ERROR",
  "message": "Failed to process order",
  "labels": {
    "order_id": "12345",
    "user_email": "<EMAIL>"
  },
  "error": {
    "code": "PAYMENT_DECLINED",
    "details": "Insufficient funds"
  }
}
```

## Special JSON Fields

### Core Fields
These fields have special meaning in Cloud Logging:

#### severity
Log entry severity level:
```json
{
  "severity": "ERROR",  // DEBUG, INFO, WARNING, ERROR, CR<PERSON><PERSON><PERSON>, ALERT, EMERGENCY
  "message": "Database connection failed"
}
```

#### message
Primary log message:
```json
{
  "message": "User authentication successful",
  "userId": "12345"
}
```

#### httpRequest
HTTP request details:
```json
{
  "httpRequest": {
    "requestMethod": "POST",
    "requestUrl": "/api/users",
    "status": 201,
    "responseSize": "1024",
    "userAgent": "Mozilla/5.0...",
    "remoteIp": "***********",
    "latency": "0.145s"
  }
}
```

### Trace Correlation Fields

#### logging.googleapis.com/trace
```json
{
  "logging.googleapis.com/trace": "projects/my-project/traces/12345",
  "message": "Processing payment"
}
```

#### logging.googleapis.com/spanId
```json
{
  "logging.googleapis.com/spanId": "000000000000004a",
  "message": "Database query executed"
}
```

#### logging.googleapis.com/trace_sampled
```json
{
  "logging.googleapis.com/trace_sampled": true,
  "message": "Request being traced"
}
```

### Additional Metadata Fields

#### logging.googleapis.com/sourceLocation
```json
{
  "logging.googleapis.com/sourceLocation": {
    "file": "app.py",
    "line": "142",
    "function": "process_order"
  }
}
```

#### logging.googleapis.com/operation
```json
{
  "logging.googleapis.com/operation": {
    "id": "operation-12345",
    "producer": "payment-service",
    "first": true,
    "last": false
  }
}
```

## Language-Specific Implementation

### Python
```python
import logging
import json
import google.cloud.logging
from google.cloud.logging_v2.handlers import CloudLoggingHandler, setup_logging

# Setup Cloud Logging
client = google.cloud.logging.Client()
handler = CloudLoggingHandler(client)
cloud_logger = logging.getLogger('cloudLogger')
cloud_logger.setLevel(logging.INFO)
cloud_logger.addHandler(handler)

# Custom JSON formatter
class StructuredFormatter(logging.Formatter):
    def format(self, record):
        log_obj = {
            "severity": record.levelname,
            "message": record.getMessage(),
            "timestamp": self.formatTime(record),
            "logging.googleapis.com/sourceLocation": {
                "file": record.filename,
                "line": record.lineno,
                "function": record.funcName
            }
        }
        
        # Add custom fields
        if hasattr(record, 'user_id'):
            log_obj['userId'] = record.user_id
        if hasattr(record, 'trace_id'):
            log_obj['logging.googleapis.com/trace'] = record.trace_id
            
        # Add exception info if present
        if record.exc_info:
            log_obj['exception'] = self.formatException(record.exc_info)
            
        return json.dumps(log_obj)

# Apply formatter
formatter = StructuredFormatter()
handler.setFormatter(formatter)

# Usage example
def process_order(order_id, user_id):
    extra = {
        'user_id': user_id,
        'order_id': order_id
    }
    
    try:
        cloud_logger.info("Processing order", extra=extra)
        # Process order logic
    except Exception as e:
        cloud_logger.error("Order processing failed", extra=extra, exc_info=True)
```

### Node.js
```javascript
const {Logging} = require('@google-cloud/logging');
const winston = require('winston');
const {LoggingWinston} = require('@google-cloud/logging-winston');

// Create Cloud Logging client
const logging = new Logging();
const cloudLogging = new LoggingWinston({
  projectId: 'your-project-id',
  keyFilename: 'path/to/keyfile.json'
});

// Configure Winston with structured logging
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'user-service' },
  transports: [
    cloudLogging,
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Custom structured logging function
function structuredLog(severity, message, metadata = {}) {
  const logEntry = {
    severity,
    message,
    timestamp: new Date().toISOString(),
    ...metadata
  };
  
  // Add trace context if available
  const trace = getTraceContext();
  if (trace) {
    logEntry['logging.googleapis.com/trace'] = trace.traceId;
    logEntry['logging.googleapis.com/spanId'] = trace.spanId;
  }
  
  logger.log(severity.toLowerCase(), logEntry);
}

// Usage examples
structuredLog('INFO', 'User logged in', {
  userId: '12345',
  ipAddress: '***********',
  userAgent: 'Mozilla/5.0...'
});

structuredLog('ERROR', 'Payment failed', {
  orderId: '67890',
  errorCode: 'INSUFFICIENT_FUNDS',
  amount: 99.99
});

// With HTTP request context
app.use((req, res, next) => {
  const requestLog = {
    httpRequest: {
      requestMethod: req.method,
      requestUrl: req.url,
      userAgent: req.get('user-agent'),
      remoteIp: req.ip
    }
  };
  
  req.structuredLog = (severity, message, metadata = {}) => {
    structuredLog(severity, message, { ...requestLog, ...metadata });
  };
  
  next();
});
```

### Java (Logback)
```java
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.LayoutBase;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.logging.LogEntry;
import com.google.cloud.logging.Logging;
import com.google.cloud.logging.LoggingOptions;
import com.google.cloud.logging.Severity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.HashMap;
import java.util.Map;

// Custom Logback layout for structured logging
public class StructuredJsonLayout extends LayoutBase<ILoggingEvent> {
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public String doLayout(ILoggingEvent event) {
        Map<String, Object> logEntry = new HashMap<>();
        
        // Standard fields
        logEntry.put("timestamp", event.getTimeStamp());
        logEntry.put("severity", event.getLevel().toString());
        logEntry.put("message", event.getFormattedMessage());
        logEntry.put("logger", event.getLoggerName());
        
        // Source location
        StackTraceElement[] callerData = event.getCallerData();
        if (callerData != null && callerData.length > 0) {
            Map<String, Object> sourceLocation = new HashMap<>();
            sourceLocation.put("file", callerData[0].getFileName());
            sourceLocation.put("line", callerData[0].getLineNumber());
            sourceLocation.put("function", callerData[0].getMethodName());
            logEntry.put("logging.googleapis.com/sourceLocation", sourceLocation);
        }
        
        // MDC context
        Map<String, String> mdc = event.getMDCPropertyMap();
        if (mdc != null && !mdc.isEmpty()) {
            logEntry.putAll(mdc);
            
            // Handle special fields
            if (mdc.containsKey("traceId")) {
                logEntry.put("logging.googleapis.com/trace", 
                    "projects/" + PROJECT_ID + "/traces/" + mdc.get("traceId"));
            }
            if (mdc.containsKey("spanId")) {
                logEntry.put("logging.googleapis.com/spanId", mdc.get("spanId"));
            }
        }
        
        // Exception handling
        if (event.getThrowableProxy() != null) {
            logEntry.put("exception", event.getThrowableProxy().getMessage());
            logEntry.put("stackTrace", ThrowableProxyUtil.asString(event.getThrowableProxy()));
        }
        
        try {
            return objectMapper.writeValueAsString(logEntry) + "\n";
        } catch (Exception e) {
            return "{\"error\":\"Failed to serialize log entry\",\"message\":\"" + 
                   event.getFormattedMessage() + "\"}\n";
        }
    }
}

// Usage in application
public class OrderService {
    private static final Logger logger = LoggerFactory.getLogger(OrderService.class);
    
    public void processOrder(String orderId, String userId) {
        // Add context to MDC
        MDC.put("orderId", orderId);
        MDC.put("userId", userId);
        MDC.put("service", "order-service");
        
        try {
            logger.info("Processing order");
            
            // Simulate processing
            validateOrder(orderId);
            chargePayment(orderId);
            
            logger.info("Order processed successfully");
        } catch (PaymentException e) {
            MDC.put("errorCode", e.getErrorCode());
            logger.error("Payment failed for order", e);
        } finally {
            // Clear MDC
            MDC.clear();
        }
    }
}
```

### Go
```go
package main

import (
    "context"
    "encoding/json"
    "fmt"
    "log"
    "os"
    "runtime"
    "time"
    
    "cloud.google.com/go/logging"
)

// StructuredLogger provides structured logging capabilities
type StructuredLogger struct {
    client *logging.Client
    logger *logging.Logger
}

// LogEntry represents a structured log entry
type LogEntry struct {
    Severity     string                 `json:"severity"`
    Message      string                 `json:"message"`
    Timestamp    string                 `json:"timestamp"`
    Labels       map[string]string      `json:"labels,omitempty"`
    Operation    *Operation             `json:"logging.googleapis.com/operation,omitempty"`
    SourceLocation *SourceLocation      `json:"logging.googleapis.com/sourceLocation,omitempty"`
    HTTPRequest  *HTTPRequest           `json:"httpRequest,omitempty"`
    Trace        string                 `json:"logging.googleapis.com/trace,omitempty"`
    SpanID       string                 `json:"logging.googleapis.com/spanId,omitempty"`
    Custom       map[string]interface{} `json:",inline"`
}

// SourceLocation represents source code location
type SourceLocation struct {
    File     string `json:"file"`
    Line     int    `json:"line"`
    Function string `json:"function"`
}

// Operation represents a long-running operation
type Operation struct {
    ID       string `json:"id"`
    Producer string `json:"producer"`
    First    bool   `json:"first"`
    Last     bool   `json:"last"`
}

// HTTPRequest represents HTTP request metadata
type HTTPRequest struct {
    Method       string `json:"requestMethod"`
    URL          string `json:"requestUrl"`
    Status       int    `json:"status"`
    ResponseSize int64  `json:"responseSize"`
    UserAgent    string `json:"userAgent"`
    RemoteIP     string `json:"remoteIp"`
    Latency      string `json:"latency"`
}

// NewStructuredLogger creates a new structured logger
func NewStructuredLogger(projectID string) (*StructuredLogger, error) {
    ctx := context.Background()
    client, err := logging.NewClient(ctx, projectID)
    if err != nil {
        return nil, err
    }
    
    logger := client.Logger("application")
    return &StructuredLogger{
        client: client,
        logger: logger,
    }, nil
}

// Log writes a structured log entry
func (sl *StructuredLogger) Log(severity logging.Severity, message string, fields map[string]interface{}) {
    entry := logging.Entry{
        Severity: severity,
        Payload: map[string]interface{}{
            "message":   message,
            "timestamp": time.Now().Format(time.RFC3339),
        },
    }
    
    // Add source location
    if pc, file, line, ok := runtime.Caller(1); ok {
        fn := runtime.FuncForPC(pc)
        entry.Payload.(map[string]interface{})["logging.googleapis.com/sourceLocation"] = map[string]interface{}{
            "file":     file,
            "line":     line,
            "function": fn.Name(),
        }
    }
    
    // Merge custom fields
    for k, v := range fields {
        entry.Payload.(map[string]interface{})[k] = v
    }
    
    sl.logger.Log(entry)
}

// LogHTTPRequest logs an HTTP request with structured data
func (sl *StructuredLogger) LogHTTPRequest(r *http.Request, status int, responseSize int64, duration time.Duration) {
    fields := map[string]interface{}{
        "httpRequest": map[string]interface{}{
            "requestMethod": r.Method,
            "requestUrl":    r.URL.String(),
            "status":        status,
            "responseSize":  responseSize,
            "userAgent":     r.UserAgent(),
            "remoteIp":      r.RemoteAddr,
            "latency":       fmt.Sprintf("%.3fs", duration.Seconds()),
        },
    }
    
    // Add trace context if available
    if traceID := r.Header.Get("X-Cloud-Trace-Context"); traceID != "" {
        fields["logging.googleapis.com/trace"] = fmt.Sprintf("projects/%s/traces/%s", sl.client.ProjectID, traceID)
    }
    
    sl.Log(logging.Info, "HTTP request processed", fields)
}

// Usage example
func main() {
    logger, err := NewStructuredLogger("your-project-id")
    if err != nil {
        log.Fatal(err)
    }
    defer logger.client.Close()
    
    // Simple log
    logger.Log(logging.Info, "Application started", map[string]interface{}{
        "version": "1.0.0",
        "environment": "production",
    })
    
    // Log with custom fields
    logger.Log(logging.Error, "Order processing failed", map[string]interface{}{
        "orderId": "12345",
        "userId":  "user-67890",
        "error": map[string]interface{}{
            "code":    "PAYMENT_DECLINED",
            "message": "Insufficient funds",
        },
    })
}
```

## Best Practices

### 1. Consistent Structure
Define a standard log schema for your organization:
```json
{
  "timestamp": "ISO8601 timestamp",
  "severity": "LEVEL",
  "message": "Human-readable message",
  "service": "service-name",
  "version": "1.0.0",
  "environment": "production",
  "labels": {
    "key": "value"
  },
  "context": {
    "requestId": "uuid",
    "userId": "id",
    "sessionId": "id"
  }
}
```

### 2. Correlation Across Services
Use trace IDs to correlate logs across microservices:
```python
import uuid
from flask import Flask, request, g

app = Flask(__name__)

@app.before_request
def before_request():
    # Get or create trace ID
    trace_id = request.headers.get('X-Trace-ID', str(uuid.uuid4()))
    g.trace_id = trace_id
    
    # Add to logger context
    logger = logging.LoggerAdapter(cloud_logger, {
        'trace_id': f"projects/{PROJECT_ID}/traces/{trace_id}"
    })
    g.logger = logger

@app.route('/api/process')
def process():
    g.logger.info("Processing request", extra={
        'endpoint': '/api/process',
        'method': request.method
    })
    
    # Pass trace ID to downstream services
    headers = {'X-Trace-ID': g.trace_id}
    response = requests.post('http://other-service/api/endpoint', headers=headers)
    
    return jsonify({'status': 'processed'})
```

### 3. Contextual Logging
Include relevant context without over-logging:
```python
class ContextualLogger:
    def __init__(self, base_logger, **context):
        self.logger = base_logger
        self.context = context
    
    def log(self, level, message, **kwargs):
        # Merge context with additional fields
        log_data = {**self.context, **kwargs}
        getattr(self.logger, level)(message, extra=log_data)
    
    def with_context(self, **additional_context):
        # Create new logger with extended context
        return ContextualLogger(
            self.logger, 
            **{**self.context, **additional_context}
        )

# Usage
base_logger = setup_cloud_logging()
request_logger = ContextualLogger(base_logger, service='api', version='1.0')

# Add more context as needed
user_logger = request_logger.with_context(user_id='12345')
user_logger.log('info', 'User action performed', action='login')
```

### 4. Error Logging
Structure error logs with actionable information:
```python
def log_error(logger, exception, context=None):
    error_log = {
        'severity': 'ERROR',
        'message': str(exception),
        'error': {
            'type': type(exception).__name__,
            'stackTrace': traceback.format_exc(),
            'context': context or {}
        }
    }
    
    # Add debugging information
    if hasattr(exception, 'error_code'):
        error_log['error']['code'] = exception.error_code
    
    logger.error(json.dumps(error_log))
```

### 5. Performance Logging
Track performance metrics in structured logs:
```python
import time
from contextlib import contextmanager

@contextmanager
def log_performance(logger, operation_name, **context):
    start_time = time.time()
    
    # Log operation start
    logger.info(f"{operation_name} started", extra={
        'operation': operation_name,
        'phase': 'start',
        **context
    })
    
    try:
        yield
        
        # Log successful completion
        duration = time.time() - start_time
        logger.info(f"{operation_name} completed", extra={
            'operation': operation_name,
            'phase': 'complete',
            'duration_seconds': duration,
            'status': 'success',
            **context
        })
        
    except Exception as e:
        # Log failure
        duration = time.time() - start_time
        logger.error(f"{operation_name} failed", extra={
            'operation': operation_name,
            'phase': 'error',
            'duration_seconds': duration,
            'status': 'failure',
            'error': str(e),
            **context
        }, exc_info=True)
        raise

# Usage
with log_performance(logger, 'database_query', query_type='user_lookup'):
    result = db.query("SELECT * FROM users WHERE id = ?", user_id)
```

### 6. Log Sampling
Implement sampling for high-volume logs:
```python
import random
import hashlib

class SamplingLogger:
    def __init__(self, logger, sample_rate=0.1):
        self.logger = logger
        self.sample_rate = sample_rate
    
    def should_log(self, key=None):
        if key:
            # Deterministic sampling based on key
            hash_value = int(hashlib.md5(key.encode()).hexdigest(), 16)
            return (hash_value % 100) < (self.sample_rate * 100)
        else:
            # Random sampling
            return random.random() < self.sample_rate
    
    def log(self, level, message, sampling_key=None, **kwargs):
        if self.should_log(sampling_key):
            kwargs['sampled'] = True
            kwargs['sample_rate'] = self.sample_rate
            getattr(self.logger, level)(message, extra=kwargs)

# Usage
sampled_logger = SamplingLogger(cloud_logger, sample_rate=0.01)  # 1% sampling

# Log high-volume events with sampling
for user_id in range(1000000):
    sampled_logger.log('info', 'Page view', 
                      sampling_key=f"pageview_{user_id}",
                      page='/home', 
                      user_id=user_id)
```

## Query Examples

### Basic Queries
```
# Find all errors
severity="ERROR"

# Find logs from specific service
jsonPayload.service="payment-service"

# Find logs with specific user
jsonPayload.userId="12345"

# Combine conditions
severity="ERROR" AND jsonPayload.service="payment-service"
```

### Advanced Queries
```
# Find slow requests
jsonPayload.httpRequest.latency > "1s"

# Find specific error codes
jsonPayload.error.code="PAYMENT_DECLINED"

# Find logs within trace
trace="projects/my-project/traces/12345"

# Regex matching
jsonPayload.message=~"Failed to process order.*"
```

### Aggregation Queries
```
# Count errors by service
severity="ERROR"
| group_by jsonPayload.service
| count()

# Average latency by endpoint
jsonPayload.httpRequest.latency is not null
| group_by jsonPayload.httpRequest.requestUrl
| avg(cast(jsonPayload.httpRequest.latency as float))
```

## Integration with Cloud Monitoring

### Create Log-based Metrics
```python
from google.cloud import logging_v2

def create_log_metric(project_id, metric_name, log_filter):
    client = logging_v2.MetricsServiceV2Client()
    project_path = f"projects/{project_id}"
    
    metric = {
        "name": metric_name,
        "filter": log_filter,
        "value_extractor": 'EXTRACT(jsonPayload.duration_seconds)',
        "metric_descriptor": {
            "metric_kind": "GAUGE",
            "value_type": "DOUBLE",
            "labels": [
                {
                    "key": "operation",
                    "value_type": "STRING",
                    "description": "Operation name"
                }
            ]
        },
        "label_extractors": {
            "operation": "EXTRACT(jsonPayload.operation)"
        }
    }
    
    response = client.create_log_metric(
        parent=project_path,
        metric=metric
    )
    
    return response

# Create metric for operation duration
create_log_metric(
    project_id="my-project",
    metric_name="operation_duration",
    log_filter='jsonPayload.phase="complete"'
)
```

## Conclusion
Structured logging is essential for modern cloud applications. By following these patterns and best practices, you can build robust logging systems that provide deep insights into application behavior, enable efficient troubleshooting, and support comprehensive monitoring and alerting.