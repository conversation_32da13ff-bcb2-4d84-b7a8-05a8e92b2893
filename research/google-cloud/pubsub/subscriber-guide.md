# Google Cloud Pub/Sub Subscriber Guide

## Overview
Subscribers receive messages from Pub/Sub subscriptions. This guide covers different subscription types, message processing patterns, acknowledgment strategies, and best practices for building reliable consumers.

## Subscription Types

### Pull Subscriptions
Pull subscriptions give subscribers control over message delivery rate and are ideal for high-throughput scenarios.

#### Characteristics
- **Volume**: Handles GBs per second
- **Control**: Subscriber controls delivery rate
- **Efficiency**: Batch message retrieval
- **Flexibility**: Dynamic acknowledgment deadlines
- **Concurrency**: Multiple subscribers can share subscription

#### Use Cases
- High-volume data processing
- Batch processing systems
- Applications requiring fine-grained flow control
- Microservices with variable processing capacity

### Push Subscriptions
Push subscriptions automatically deliver messages to configured HTTPS endpoints.

#### Characteristics
- **Delivery**: Automatic HTTP POST to endpoint
- **Flow Control**: Server-side rate limiting
- **Integration**: Works with webhooks
- **Simplicity**: No client library needed
- **Platform**: Ideal for App Engine, Cloud Run

#### Use Cases
- Serverless applications
- Webhook integrations
- Simple event handlers
- Environments with limited Google Cloud dependencies

### Export Subscriptions
Export subscriptions automatically write messages to Google Cloud storage systems.

#### Characteristics
- **Scale**: Millions of messages per second
- **Direct Export**: BigQuery or Cloud Storage
- **Automation**: No processing code needed
- **Load Balancing**: Automatic distribution
- **Simplicity**: Minimal operational overhead

#### Use Cases
- Data lake ingestion
- Long-term message archival
- Analytics pipelines
- Compliance and audit logging

## Pull Subscription Implementation

### Basic Pull Subscriber
```python
from google.cloud import pubsub_v1

def pull_messages(project_id, subscription_id):
    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(project_id, subscription_id)
    
    # Synchronous pull
    response = subscriber.pull(
        request={
            "subscription": subscription_path,
            "max_messages": 10,
        }
    )
    
    # Process messages
    ack_ids = []
    for message in response.received_messages:
        print(f"Received: {message.message.data}")
        ack_ids.append(message.ack_id)
    
    # Acknowledge messages
    if ack_ids:
        subscriber.acknowledge(
            request={
                "subscription": subscription_path,
                "ack_ids": ack_ids,
            }
        )
```

### Streaming Pull Subscriber
```python
def streaming_pull(project_id, subscription_id):
    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(project_id, subscription_id)
    
    def callback(message):
        try:
            # Process message
            print(f"Received: {message.data.decode('utf-8')}")
            
            # Process attributes
            if message.attributes:
                print(f"Attributes: {message.attributes}")
            
            # Acknowledge message
            message.ack()
            
        except Exception as e:
            print(f"Error processing message: {e}")
            # Nack will cause redelivery
            message.nack()
    
    # Configure flow control
    flow_control = pubsub_v1.types.FlowControl(
        max_messages=1000,
        max_bytes=100 * 1024 * 1024,  # 100 MB
    )
    
    streaming_pull_future = subscriber.subscribe(
        subscription_path,
        callback=callback,
        flow_control=flow_control,
    )
    
    print(f"Listening for messages on {subscription_path}")
    
    # Keep main thread alive
    with subscriber:
        try:
            streaming_pull_future.result()
        except KeyboardInterrupt:
            streaming_pull_future.cancel()
```

### Asynchronous Processing
```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class AsyncSubscriber:
    def __init__(self, project_id, subscription_id, max_workers=10):
        self.subscriber = pubsub_v1.SubscriberClient()
        self.subscription_path = self.subscriber.subscription_path(
            project_id, subscription_id
        )
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    async def process_message_async(self, message):
        """Process message asynchronously."""
        loop = asyncio.get_event_loop()
        
        # Run CPU-intensive work in thread pool
        result = await loop.run_in_executor(
            self.executor,
            self.process_message_sync,
            message
        )
        
        return result
    
    def process_message_sync(self, message):
        """Synchronous message processing."""
        # Simulate processing
        data = message.data.decode('utf-8')
        # Process data...
        return f"Processed: {data}"
    
    def start(self):
        """Start async subscriber."""
        def callback(message):
            # Schedule async processing
            asyncio.create_task(self.handle_message(message))
        
        self.streaming_pull_future = self.subscriber.subscribe(
            self.subscription_path,
            callback=callback
        )
    
    async def handle_message(self, message):
        """Handle message with async processing."""
        try:
            result = await self.process_message_async(message)
            print(result)
            message.ack()
        except Exception as e:
            print(f"Error: {e}")
            message.nack()
```

## Push Subscription Implementation

### Configure Push Endpoint
```python
def create_push_subscription(project_id, topic_id, subscription_id, endpoint):
    publisher = pubsub_v1.PublisherClient()
    subscriber = pubsub_v1.SubscriberClient()
    
    topic_path = publisher.topic_path(project_id, topic_id)
    subscription_path = subscriber.subscription_path(project_id, subscription_id)
    
    # Configure push endpoint
    push_config = pubsub_v1.types.PushConfig(
        push_endpoint=endpoint,
        oidc_token=pubsub_v1.types.PushConfig.OidcToken(
            service_account_email=f"{project_id}@appspot.gserviceaccount.com"
        )
    )
    
    # Create subscription
    subscription = subscriber.create_subscription(
        request={
            "name": subscription_path,
            "topic": topic_path,
            "push_config": push_config,
            "ack_deadline_seconds": 60,
        }
    )
    
    print(f"Push subscription created: {subscription.name}")
```

### Push Endpoint Handler (Flask)
```python
from flask import Flask, request, jsonify
import base64
import json

app = Flask(__name__)

@app.route('/pubsub/push', methods=['POST'])
def pubsub_push():
    """Handle Pub/Sub push messages."""
    envelope = request.get_json()
    
    if not envelope:
        return 'Bad Request: no message received', 400
    
    if not isinstance(envelope, dict) or 'message' not in envelope:
        return 'Bad Request: invalid message format', 400
    
    message = envelope['message']
    
    # Process message
    if isinstance(message, dict) and 'data' in message:
        try:
            # Decode message data
            data = base64.b64decode(message['data']).decode('utf-8')
            
            # Process attributes
            attributes = message.get('attributes', {})
            
            # Process the message
            process_push_message(data, attributes)
            
            # Return 2xx to acknowledge message
            return '', 204
            
        except Exception as e:
            print(f"Error processing message: {e}")
            # Return 5xx to trigger retry
            return 'Internal Server Error', 500
    
    return 'Bad Request: invalid message data', 400

def process_push_message(data, attributes):
    """Process the push message."""
    print(f"Received message: {data}")
    print(f"Attributes: {attributes}")
    # Add your processing logic here
```

### Cloud Run Push Handler
```python
import os
from flask import Flask, request

app = Flask(__name__)

@app.route('/', methods=['POST'])
def index():
    """Cloud Run push endpoint."""
    envelope = request.get_json()
    if not envelope:
        return 'Bad Request', 400
    
    message = envelope.get('message')
    if not message:
        return 'Bad Request', 400
    
    # Verify token if configured
    if os.environ.get('VERIFY_TOKEN'):
        token = request.args.get('token', '')
        if token != os.environ.get('VERIFY_TOKEN'):
            return 'Forbidden', 403
    
    # Process message
    process_message(message)
    
    return '', 204

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=int(os.environ.get('PORT', 8080)))
```

## Message Acknowledgment

### Acknowledgment Strategies

#### 1. Immediate Acknowledgment
```python
def immediate_ack(message):
    """Acknowledge immediately after receipt."""
    try:
        # Acknowledge first
        message.ack()
        
        # Then process (risky - may lose messages)
        process_message(message)
        
    except Exception as e:
        # Message already acked, can't retry
        log_error(e)
```

#### 2. Process-Then-Acknowledge (Recommended)
```python
def process_then_ack(message):
    """Process message before acknowledging."""
    try:
        # Process first
        result = process_message(message)
        
        # Acknowledge only after successful processing
        message.ack()
        
    except Exception as e:
        # Nack to retry
        message.nack()
        log_error(e)
```

#### 3. Deadline Extension
```python
def long_processing(message):
    """Extend deadline for long-running tasks."""
    # Initial processing
    start_time = time.time()
    
    # Extend deadline every 30 seconds
    while not processing_complete:
        if time.time() - start_time > 30:
            message.modify_ack_deadline(60)  # Extend by 60 seconds
            start_time = time.time()
        
        # Continue processing
        process_chunk()
    
    # Acknowledge when complete
    message.ack()
```

### Batch Acknowledgment
```python
def batch_acknowledge(subscription_path, messages):
    """Acknowledge messages in batches."""
    subscriber = pubsub_v1.SubscriberClient()
    
    # Group messages by processing result
    to_ack = []
    to_nack = []
    
    for message in messages:
        try:
            process_message(message)
            to_ack.append(message.ack_id)
        except Exception:
            to_nack.append(message.ack_id)
    
    # Batch acknowledge
    if to_ack:
        subscriber.acknowledge(
            request={
                "subscription": subscription_path,
                "ack_ids": to_ack,
            }
        )
    
    # Batch nack
    if to_nack:
        subscriber.modify_ack_deadline(
            request={
                "subscription": subscription_path,
                "ack_ids": to_nack,
                "ack_deadline_seconds": 0,  # Immediate redelivery
            }
        )
```

## Flow Control

### Subscriber Flow Control
```python
# Configure flow control settings
flow_control = pubsub_v1.types.FlowControl(
    # Maximum number of messages
    max_messages=1000,
    
    # Maximum size of messages
    max_bytes=100 * 1024 * 1024,  # 100 MB
    
    # Maximum duration to extend deadlines
    max_duration_per_lease_extension=3600,  # 1 hour
    
    # Minimum duration for lease extensions
    min_duration_per_lease_extension=60,    # 1 minute
)

# Apply to subscriber
streaming_pull_future = subscriber.subscribe(
    subscription_path,
    callback=callback,
    flow_control=flow_control,
)
```

### Dynamic Flow Control
```python
class DynamicFlowControlSubscriber:
    def __init__(self, project_id, subscription_id):
        self.subscriber = pubsub_v1.SubscriberClient()
        self.subscription_path = self.subscriber.subscription_path(
            project_id, subscription_id
        )
        self.processing_times = []
        self.flow_control = self._initial_flow_control()
    
    def _initial_flow_control(self):
        return pubsub_v1.types.FlowControl(
            max_messages=100,
            max_bytes=10 * 1024 * 1024,
        )
    
    def adjust_flow_control(self):
        """Adjust flow control based on processing performance."""
        if len(self.processing_times) < 100:
            return
        
        avg_time = sum(self.processing_times[-100:]) / 100
        
        if avg_time < 0.1:  # Fast processing
            self.flow_control.max_messages = min(1000, self.flow_control.max_messages * 2)
        elif avg_time > 1.0:  # Slow processing
            self.flow_control.max_messages = max(10, self.flow_control.max_messages // 2)
    
    def process_message(self, message):
        start = time.time()
        try:
            # Process message
            handle_message(message)
            message.ack()
        finally:
            self.processing_times.append(time.time() - start)
            if len(self.processing_times) % 100 == 0:
                self.adjust_flow_control()
```

## Error Handling

### Dead Letter Topics
```python
def configure_dead_letter_topic(
    project_id, 
    subscription_id, 
    dead_letter_topic_id,
    max_delivery_attempts=5
):
    """Configure dead letter topic for failed messages."""
    subscriber = pubsub_v1.SubscriberClient()
    
    subscription_path = subscriber.subscription_path(project_id, subscription_id)
    dead_letter_topic_path = f"projects/{project_id}/topics/{dead_letter_topic_id}"
    
    # Create dead letter policy
    dead_letter_policy = pubsub_v1.types.DeadLetterPolicy(
        dead_letter_topic=dead_letter_topic_path,
        max_delivery_attempts=max_delivery_attempts,
    )
    
    # Update subscription
    subscription = subscriber.update_subscription(
        request={
            "subscription": {
                "name": subscription_path,
                "dead_letter_policy": dead_letter_policy,
            },
            "update_mask": {"paths": ["dead_letter_policy"]},
        }
    )
    
    print(f"Dead letter policy configured: {subscription.dead_letter_policy}")
```

### Retry Configuration
```python
def configure_retry_policy(project_id, subscription_id):
    """Configure exponential backoff retry policy."""
    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(project_id, subscription_id)
    
    # Create retry policy
    retry_policy = pubsub_v1.types.RetryPolicy(
        minimum_backoff={"seconds": 10},
        maximum_backoff={"seconds": 600},  # 10 minutes
    )
    
    # Update subscription
    subscription = subscriber.update_subscription(
        request={
            "subscription": {
                "name": subscription_path,
                "retry_policy": retry_policy,
            },
            "update_mask": {"paths": ["retry_policy"]},
        }
    )
```

### Error Recovery Patterns
```python
class ResilientSubscriber:
    def __init__(self, project_id, subscription_id):
        self.project_id = project_id
        self.subscription_id = subscription_id
        self.subscriber = None
        self.future = None
    
    def start(self):
        """Start subscriber with automatic recovery."""
        while True:
            try:
                self._create_subscriber()
                self._subscribe()
                # Block until error or shutdown
                self.future.result()
            except Exception as e:
                print(f"Subscriber error: {e}")
                self._cleanup()
                time.sleep(5)  # Wait before retry
    
    def _create_subscriber(self):
        """Create new subscriber client."""
        self.subscriber = pubsub_v1.SubscriberClient()
    
    def _subscribe(self):
        """Start subscription."""
        subscription_path = self.subscriber.subscription_path(
            self.project_id, self.subscription_id
        )
        
        self.future = self.subscriber.subscribe(
            subscription_path,
            callback=self._process_message,
            flow_control=pubsub_v1.types.FlowControl(max_messages=100),
        )
    
    def _process_message(self, message):
        """Process message with error handling."""
        try:
            # Process message
            process_message(message)
            message.ack()
        except RecoverableError:
            # Retry later
            message.nack()
        except NonRecoverableError:
            # Don't retry, acknowledge to prevent redelivery
            message.ack()
            log_error(message)
    
    def _cleanup(self):
        """Clean up resources."""
        if self.future:
            self.future.cancel()
        if self.subscriber:
            self.subscriber.close()
```

## Seek and Replay

### Seek to Timestamp
```python
def seek_to_timestamp(project_id, subscription_id, timestamp):
    """Replay messages from specific timestamp."""
    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(project_id, subscription_id)
    
    # Seek to timestamp
    seek_request = pubsub_v1.types.SeekRequest(
        subscription=subscription_path,
        time=timestamp,
    )
    
    response = subscriber.seek(seek_request)
    print(f"Seek completed: {response}")
```

### Seek to Snapshot
```python
def create_snapshot(project_id, subscription_id, snapshot_id):
    """Create snapshot for future replay."""
    subscriber = pubsub_v1.SubscriberClient()
    
    subscription_path = subscriber.subscription_path(project_id, subscription_id)
    snapshot_path = subscriber.snapshot_path(project_id, snapshot_id)
    
    snapshot = subscriber.create_snapshot(
        request={
            "name": snapshot_path,
            "subscription": subscription_path,
        }
    )
    
    print(f"Snapshot created: {snapshot.name}")
    return snapshot

def seek_to_snapshot(project_id, subscription_id, snapshot_id):
    """Replay messages from snapshot."""
    subscriber = pubsub_v1.SubscriberClient()
    
    subscription_path = subscriber.subscription_path(project_id, subscription_id)
    snapshot_path = subscriber.snapshot_path(project_id, snapshot_id)
    
    seek_request = pubsub_v1.types.SeekRequest(
        subscription=subscription_path,
        snapshot=snapshot_path,
    )
    
    response = subscriber.seek(seek_request)
    print(f"Seek to snapshot completed: {response}")
```

## Best Practices

### 1. Message Processing
- **Idempotency**: Design processors to handle duplicates
- **Timeouts**: Set appropriate processing timeouts
- **Logging**: Log message IDs for troubleshooting
- **Metrics**: Track processing times and success rates

### 2. Acknowledgment Strategy
- Acknowledge only after successful processing
- Use deadline extensions for long operations
- Implement proper error classification
- Configure dead letter topics

### 3. Flow Control
- Start conservative and scale up
- Monitor memory usage
- Adjust based on processing capacity
- Consider message size variations

### 4. Error Handling
- Distinguish recoverable vs non-recoverable errors
- Implement exponential backoff
- Use dead letter topics
- Monitor error rates

### 5. Performance Optimization
- Use batching where appropriate
- Implement connection pooling
- Process messages concurrently
- Monitor and tune flow control

### 6. Monitoring
- Track message age
- Monitor processing latency
- Set up alerts for backlogs
- Review dead letter messages

## Example: Production Subscriber
```python
class ProductionSubscriber:
    """Production-ready subscriber with all best practices."""
    
    def __init__(self, project_id, subscription_id, processor):
        self.subscriber = pubsub_v1.SubscriberClient()
        self.subscription_path = self.subscriber.subscription_path(
            project_id, subscription_id
        )
        self.processor = processor
        self.metrics = SubscriberMetrics()
        
    def start(self):
        """Start subscriber with production configuration."""
        flow_control = pubsub_v1.types.FlowControl(
            max_messages=100,
            max_bytes=50 * 1024 * 1024,  # 50 MB
        )
        
        scheduler = pubsub_v1.subscriber.scheduler.ThreadScheduler(
            executor=ThreadPoolExecutor(max_workers=10)
        )
        
        self.streaming_pull_future = self.subscriber.subscribe(
            self.subscription_path,
            callback=self._process_message,
            flow_control=flow_control,
            scheduler=scheduler,
        )
        
        # Start metrics collection
        self._start_metrics_collection()
        
        print(f"Subscriber started for {self.subscription_path}")
        
        # Keep running
        with self.subscriber:
            try:
                self.streaming_pull_future.result()
            except KeyboardInterrupt:
                self.streaming_pull_future.cancel()
                self.streaming_pull_future.result()  # Block until cancelled
    
    def _process_message(self, message):
        """Process message with full error handling."""
        start_time = time.time()
        message_id = message.message_id
        
        try:
            # Log receipt
            self.metrics.message_received()
            
            # Process message
            result = self.processor.process(
                data=message.data,
                attributes=message.attributes,
                message_id=message_id
            )
            
            # Acknowledge success
            message.ack()
            self.metrics.message_processed(time.time() - start_time)
            
        except RecoverableError as e:
            # Nack for retry
            message.nack()
            self.metrics.message_failed(recoverable=True)
            logging.warning(f"Recoverable error for {message_id}: {e}")
            
        except Exception as e:
            # Non-recoverable error
            message.ack()  # Prevent infinite retries
            self.metrics.message_failed(recoverable=False)
            logging.error(f"Non-recoverable error for {message_id}: {e}")
            
            # Send to error tracking
            self._report_error(message, e)
    
    def _report_error(self, message, error):
        """Report non-recoverable errors."""
        # Implement error reporting (e.g., to Cloud Logging, Sentry, etc.)
        pass
    
    def _start_metrics_collection(self):
        """Start background metrics collection."""
        def collect_metrics():
            while True:
                time.sleep(60)
                stats = self.metrics.get_stats()
                # Send to monitoring system
                print(f"Subscriber stats: {stats}")
        
        thread = threading.Thread(target=collect_metrics, daemon=True)
        thread.start()
```

## Conclusion
Choosing the right subscription type and implementing proper message handling patterns is crucial for building reliable Pub/Sub consumers. Consider your specific requirements for throughput, latency, and operational complexity when designing your subscription strategy.