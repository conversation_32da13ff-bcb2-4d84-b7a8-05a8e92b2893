# Google Cloud Pub/Sub - Comprehensive Overview

## Introduction
Google Cloud Pub/Sub is an asynchronous and scalable messaging service that decouples services producing messages from services processing those messages. It provides reliable, real-time messaging for event-driven systems and streaming analytics.

## Core Concepts

### Fundamental Components
1. **Topics**: Named resources to which messages are sent by publishers
2. **Subscriptions**: Named resources representing the stream of messages from a topic
3. **Messages**: The data units that move through the system
4. **Publishers**: Applications that create and send messages to topics
5. **Subscribers**: Applications that receive messages from subscriptions

### Message Flow Architecture
```
Publishers → Topics → Subscriptions → Subscribers
```

- Publishers send messages to topics
- Topics forward messages to all attached subscriptions
- Subscribers pull messages from subscriptions
- Each subscription maintains its own message queue

## Key Characteristics

### Performance
- **Latency**: Typical message latency around 100 milliseconds
- **Throughput**: Can handle millions of messages per second
- **Scalability**: Automatically scales to meet demand
- **Global**: Available in regions worldwide

### Reliability
- **At-least-once delivery**: Guarantees message delivery
- **Message retention**: Up to 7 days
- **Acknowledgment deadline**: Configurable from 10 seconds to 10 minutes
- **Dead letter topics**: For handling failed messages

### Flexibility
- **Per-message parallelism**: Process messages independently
- **Multiple consumer patterns**: Pull and push subscriptions
- **Filtering**: Message attribute-based filtering
- **Ordering**: Optional message ordering with ordering keys

## Messaging Patterns

### 1. Publish-Subscribe (Fan-out)
```python
# One publisher, multiple subscribers
# Publisher
publisher.publish(topic_path, b'Event data')

# Multiple subscriptions receive the same message
# Subscription A: Analytics pipeline
# Subscription B: Real-time dashboard
# Subscription C: Data warehouse loader
```

### 2. Load Balancing (Fan-in)
```python
# Multiple publishers, one subscription with multiple subscribers
# Publishers: Web servers sending logs
# Subscription: Log processing service with auto-scaling workers
```

### 3. Request-Reply
```python
# Synchronous-like communication over async infrastructure
# Include reply_to topic in message attributes
message = {
    'data': request_data,
    'attributes': {
        'reply_to': 'reply-topic-123',
        'correlation_id': 'req-456'
    }
}
```

### 4. Event Sourcing
```python
# Capture all state changes as events
# Topic: user-events
# Messages: created, updated, deleted events
# Subscribers: Read model builders, audit log, analytics
```

## Primary Use Cases

### 1. Real-time Event Distribution
- User activity tracking
- System monitoring events
- Application state changes
- IoT device telemetry

### 2. Data Pipeline Integration
```python
# Streaming ETL pipeline
# Source → Pub/Sub → Dataflow → BigQuery
publisher.publish(
    topic_path,
    json.dumps(event).encode('utf-8')
)
```

### 3. Microservices Communication
- Service decoupling
- Asynchronous workflows
- Event-driven architecture
- Service mesh integration

### 4. Database Replication
- Change data capture (CDC)
- Cross-region replication
- Cache invalidation
- Data synchronization

### 5. Task Distribution
```python
# Distributed work queue
def create_task(task_data):
    future = publisher.publish(
        task_topic,
        json.dumps(task_data).encode('utf-8')
    )
    return future.result()
```

### 6. IoT Data Ingestion
- Device telemetry collection
- Command distribution
- Firmware updates
- Real-time monitoring

### 7. Enterprise Event Bus
- System integration
- Legacy modernization
- Partner data exchange
- Audit logging

## Integration Ecosystem

### Google Cloud Services

#### Dataflow Integration
```python
# Stream processing pipeline
pipeline | 'Read' >> beam.io.ReadFromPubSub(topic=topic_path)
         | 'Process' >> beam.Map(process_message)
         | 'Write' >> beam.io.WriteToBigQuery(table_spec)
```

#### Cloud Functions
```python
# Event-triggered function
def process_pubsub(event, context):
    """Triggered by Pub/Sub message."""
    message = base64.b64decode(event['data']).decode('utf-8')
    # Process message
```

#### Cloud Run
```yaml
# Push subscription to Cloud Run service
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  annotations:
    run.googleapis.com/launch-stage: BETA
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/execution-environment: gen2
```

#### BigQuery
- Streaming inserts via Dataflow
- Batch loads via Cloud Storage
- Real-time analytics pipelines

#### Cloud Storage
- Archive messages for batch processing
- Trigger processing on file uploads
- Data lake integration

## Message Structure

### Basic Message Format
```json
{
  "data": "base64-encoded-string",
  "attributes": {
    "key1": "value1",
    "key2": "value2"
  },
  "messageId": "unique-message-id",
  "publishTime": "2024-01-15T12:00:00Z",
  "orderingKey": "optional-ordering-key"
}
```

### Message Attributes
- Key-value pairs for metadata
- Used for filtering
- Maximum 100 attributes per message
- Keys: 256 bytes max
- Values: 1024 bytes max

### Message Size Limits
- Maximum message size: 10 MB
- Includes data and attributes
- Consider chunking for larger payloads

## Delivery Guarantees

### At-Least-Once Delivery
- Messages delivered at least once
- May receive duplicates
- Implement idempotent processing
- Use messageId for deduplication

### Message Ordering
```python
# Enable message ordering
publisher_options = pubsub_v1.types.PublisherOptions(
    enable_message_ordering=True
)
publisher = pubsub_v1.PublisherClient(
    publisher_options=publisher_options
)

# Publish with ordering key
future = publisher.publish(
    topic_path,
    data,
    ordering_key='user-123'
)
```

### Exactly-Once Processing
- Not guaranteed by Pub/Sub alone
- Implement using:
  - Dataflow exactly-once semantics
  - Transaction processing
  - Idempotency keys

## Subscription Types

### Pull Subscriptions
```python
# Synchronous pull
response = subscriber.pull(
    request={
        "subscription": subscription_path,
        "max_messages": 10,
    }
)

# Process and acknowledge
ack_ids = [msg.ack_id for msg in response.received_messages]
subscriber.acknowledge(
    request={
        "subscription": subscription_path,
        "ack_ids": ack_ids,
    }
)
```

### Push Subscriptions
```json
{
  "pushConfig": {
    "pushEndpoint": "https://myapp.com/push-handler",
    "attributes": {
      "x-goog-version": "v1"
    },
    "oidcToken": {
      "serviceAccountEmail": "<EMAIL>"
    }
  }
}
```

### Streaming Pull
```python
# Asynchronous streaming
def callback(message):
    print(f"Received: {message.data}")
    message.ack()

streaming_pull_future = subscriber.subscribe(
    subscription_path, 
    callback=callback
)
```

## Advanced Features

### Message Filtering
```python
# Create subscription with filter
filter_expression = 'attributes.department="sales" AND attributes.priority="high"'
subscriber.create_subscription(
    request={
        "name": subscription_path,
        "topic": topic_path,
        "filter": filter_expression,
    }
)
```

### Dead Letter Topics
```python
# Configure dead letter policy
dead_letter_policy = {
    "dead_letter_topic": dead_letter_topic_path,
    "max_delivery_attempts": 5,
}
```

### Retry Policies
```python
# Configure exponential backoff
retry_policy = {
    "minimum_backoff": {"seconds": 10},
    "maximum_backoff": {"seconds": 600},
}
```

### Seek and Replay
```python
# Replay messages from timestamp
seek_time = datetime.now() - timedelta(hours=1)
subscriber.seek(
    request={
        "subscription": subscription_path,
        "time": seek_time,
    }
)
```

## Best Practices

### 1. Message Design
- Keep messages small and focused
- Use attributes for filtering metadata
- Include version information
- Consider schema evolution

### 2. Error Handling
- Implement exponential backoff
- Use dead letter topics
- Log failed messages
- Monitor acknowledgment rates

### 3. Performance Optimization
- Batch publish operations
- Use appropriate acknowledgment deadlines
- Configure flow control
- Monitor publish/subscribe latencies

### 4. Security
- Use service accounts with minimal permissions
- Enable audit logging
- Encrypt sensitive data
- Use VPC Service Controls

### 5. Cost Optimization
- Set appropriate message retention
- Clean up unused subscriptions
- Monitor message volumes
- Use filtering to reduce processing

## Monitoring and Operations

### Key Metrics
1. **Publishing metrics**
   - Publish requests
   - Publish latency
   - Publish errors

2. **Subscription metrics**
   - Undelivered messages
   - Oldest unacked message age
   - Pull request count
   - Push request latency

3. **System health**
   - Subscription/backlog quota
   - Throughput rates
   - Error rates

### Alerting Examples
```yaml
# High backlog alert
- alert: HighPubSubBacklog
  expr: |
    pubsub_subscription_num_undelivered_messages > 100000
  for: 5m
  annotations:
    summary: "High message backlog on {{ $labels.subscription_id }}"

# Old unacked messages
- alert: OldUnackedMessages
  expr: |
    pubsub_subscription_oldest_unacked_message_age > 3600
  for: 10m
  annotations:
    summary: "Old unacked messages in {{ $labels.subscription_id }}"
```

## Common Architectures

### 1. Event-Driven Microservices
```
Services → Topics → Subscriptions → Services
                ↓
          Cloud Functions (for simple processing)
```

### 2. Data Pipeline
```
Applications → Pub/Sub → Dataflow → BigQuery
                    ↓
              Cloud Storage (archive)
```

### 3. IoT Platform
```
Devices → IoT Core → Pub/Sub → Cloud Functions
                          ↓
                    Time-series DB
```

### 4. Multi-Region Replication
```
Region A Services → Topic A ←→ Topic B ← Region B Services
```

## Migration Strategies

### From Other Message Queues
1. **Parallel run**: Publish to both systems
2. **Gradual migration**: Move one service at a time
3. **Adapter pattern**: Build compatibility layer
4. **Big bang**: Switch all at once (risky)

### Considerations
- Message format compatibility
- Delivery guarantee differences
- API compatibility
- Performance characteristics

## Conclusion
Pub/Sub provides a robust, scalable foundation for building event-driven architectures on Google Cloud. Its integration with other GCP services, combined with strong delivery guarantees and flexible subscription models, makes it suitable for a wide range of use cases from simple task queues to complex event streaming pipelines.