# Google Cloud Pub/Sub Publisher Guide

## Overview
Publishing is the process of sending messages to Pub/Sub topics. This guide covers publishing workflows, configuration options, best practices, and optimization techniques for high-throughput scenarios.

## Publishing Workflow

### Basic Publishing Steps
1. **Create a message** containing your data
2. **Configure optional attributes** for metadata
3. **Set ordering key** if message order matters
4. **Send publish request** to Pub/Sub server
5. **Handle response** including message ID

### Message Composition

#### Core Message Structure
```python
message = {
    "data": b"Your message data here",  # Required: byte string
    "attributes": {                      # Optional: metadata
        "key1": "value1",
        "key2": "value2"
    },
    "ordering_key": "user-123"          # Optional: for ordered delivery
}
```

#### Automatic Fields
Pub/Sub automatically adds:
- **Message ID**: Unique identifier for the message
- **Publish timestamp**: When Pub/Sub received the message

## Publishing Methods

### 1. Client Libraries (Recommended)
```python
from google.cloud import pubsub_v1

# Initialize publisher
publisher = pubsub_v1.PublisherClient()
topic_path = publisher.topic_path(project_id, topic_id)

# Publish message
future = publisher.publish(
    topic_path,
    b"Hello, World!",
    origin="python-sample",
    username="gcp-user"
)

# Get message ID
message_id = future.result()
print(f"Published message ID: {message_id}")
```

### 2. gcloud CLI
```bash
# Basic publish
gcloud pubsub topics publish my-topic --message="Hello, World!"

# With attributes
gcloud pubsub topics publish my-topic \
    --message="Hello, World!" \
    --attribute="origin=cli,env=prod"

# From file
gcloud pubsub topics publish my-topic \
    --message-file=message.json
```

### 3. REST API
```bash
curl -X POST \
    -H "Authorization: Bearer $(gcloud auth print-access-token)" \
    -H "Content-Type: application/json" \
    -d '{
        "messages": [{
            "data": "SGVsbG8sIFdvcmxkIQ==",
            "attributes": {
                "origin": "rest-api"
            }
        }]
    }' \
    "https://pubsub.googleapis.com/v1/projects/PROJECT_ID/topics/TOPIC_ID:publish"
```

### 4. Google Cloud Console
1. Navigate to Pub/Sub topics
2. Select your topic
3. Click "Publish message"
4. Enter message data and attributes
5. Click "Publish"

## Message Attributes

### Attribute Constraints
- **Maximum attributes**: 100 per message
- **Key constraints**:
  - Cannot start with "goog"
  - Maximum 256 bytes
  - Must be UTF-8 strings
- **Value constraints**:
  - Maximum 1024 bytes
  - Must be UTF-8 strings

### Common Attribute Patterns
```python
# Event metadata
attributes = {
    "event_type": "user_signup",
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "1.0"
}

# Routing information
attributes = {
    "region": "us-central1",
    "environment": "production",
    "service": "user-service"
}

# Processing hints
attributes = {
    "priority": "high",
    "retry_count": "0",
    "content_type": "application/json"
}
```

## Message Ordering

### Enabling Ordered Delivery
```python
# Configure publisher for ordering
publisher_options = pubsub_v1.types.PublisherOptions(
    enable_message_ordering=True
)

publisher = pubsub_v1.PublisherClient(
    publisher_options=publisher_options
)

# Publish with ordering key
future = publisher.publish(
    topic_path,
    b"Message 1",
    ordering_key="conversation-123"
)
```

### Ordering Key Best Practices
1. **Use consistent keys**: Same key for related messages
2. **Limit key cardinality**: Too many keys reduce throughput
3. **Regional consistency**: Ordering works within single region
4. **Handle failures**: Failed ordered messages block subsequent ones

### Ordering Failure Handling
```python
def resume_publish(publisher, topic_path, ordering_key):
    """Resume publishing after ordering key failure."""
    publisher.resume_publish(topic_path, ordering_key)

# Handle ordering failures
try:
    future = publisher.publish(
        topic_path,
        data,
        ordering_key=ordering_key
    )
    future.result()
except Exception as e:
    print(f"Publishing failed: {e}")
    # Must resume before publishing with same key
    resume_publish(publisher, topic_path, ordering_key)
```

## Batching Configuration

### Default Batching Behavior
Client libraries automatically batch messages for efficiency:
- **Max messages**: 1000 per batch
- **Max bytes**: 10 MB per batch
- **Max latency**: 10 ms

### Custom Batch Settings
```python
# Configure batch settings
batch_settings = pubsub_v1.types.BatchSettings(
    max_messages=100,      # Messages per batch
    max_bytes=1024 * 1024, # 1 MB
    max_latency=0.01,      # 10 ms
)

publisher = pubsub_v1.PublisherClient(
    batch_settings=batch_settings
)
```

### Batch Optimization Strategies
```python
# High throughput settings
high_throughput_settings = pubsub_v1.types.BatchSettings(
    max_messages=1000,
    max_bytes=10 * 1024 * 1024,  # 10 MB
    max_latency=0.1,              # 100 ms
)

# Low latency settings
low_latency_settings = pubsub_v1.types.BatchSettings(
    max_messages=10,
    max_bytes=1024,      # 1 KB
    max_latency=0.001,   # 1 ms
)
```

## Flow Control

### Publisher Flow Control
```python
# Configure flow control
flow_control_settings = pubsub_v1.types.PublishFlowControl(
    message_limit=1000,           # Max outstanding messages
    byte_limit=100 * 1024 * 1024, # 100 MB
    limit_exceeded_behavior=pubsub_v1.types.LimitExceededBehavior.BLOCK,
)

publisher_options = pubsub_v1.types.PublisherOptions(
    flow_control=flow_control_settings
)

publisher = pubsub_v1.PublisherClient(
    publisher_options=publisher_options
)
```

### Flow Control Behaviors
1. **BLOCK**: Wait when limits exceeded (default)
2. **ERROR**: Raise exception when limits exceeded
3. **IGNORE**: No flow control (not recommended)

## Error Handling

### Retry Configuration
```python
from google.api_core import retry

# Custom retry configuration
custom_retry = retry.Retry(
    initial=0.1,        # Initial delay
    maximum=60.0,       # Max delay
    multiplier=1.3,     # Backoff multiplier
    deadline=120.0,     # Total timeout
)

# Publish with custom retry
future = publisher.publish(
    topic_path,
    data,
    retry=custom_retry
)
```

### Error Types and Handling
```python
from google.api_core import exceptions

try:
    future = publisher.publish(topic_path, data)
    message_id = future.result(timeout=30)
except exceptions.DeadlineExceeded:
    print("Publishing timed out")
except exceptions.GoogleAPICallError as e:
    print(f"API error: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

### Handling Publish Failures
```python
def publish_with_error_handling(publisher, topic_path, data, max_retries=3):
    """Publish with comprehensive error handling."""
    for attempt in range(max_retries):
        try:
            future = publisher.publish(topic_path, data)
            return future.result(timeout=30)
        except Exception as e:
            if attempt == max_retries - 1:
                # Log to dead letter queue or alternative storage
                log_failed_message(data, str(e))
                raise
            time.sleep(2 ** attempt)  # Exponential backoff
```

## High-Throughput Publishing

### Optimization Techniques

#### 1. Connection Pooling
```python
# Increase gRPC channel count
from google.api_core import grpc_helpers

# Create publisher with more channels
channel_count = 10  # Default is 1
channels = [
    grpc_helpers.create_channel(
        publisher.api.transport._host,
        credentials=publisher.api.transport._credentials,
    )
    for _ in range(channel_count)
]
```

#### 2. Parallel Publishing
```python
import concurrent.futures
import threading

class HighThroughputPublisher:
    def __init__(self, project_id, topic_id, num_publishers=4):
        self.publishers = [
            pubsub_v1.PublisherClient()
            for _ in range(num_publishers)
        ]
        self.topic_path = f"projects/{project_id}/topics/{topic_id}"
        self.current = 0
        self.lock = threading.Lock()
    
    def publish(self, data, **kwargs):
        """Round-robin across publishers."""
        with self.lock:
            publisher = self.publishers[self.current]
            self.current = (self.current + 1) % len(self.publishers)
        
        return publisher.publish(self.topic_path, data, **kwargs)
```

#### 3. Asynchronous Publishing
```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def async_publish_batch(publisher, topic_path, messages):
    """Publish messages asynchronously."""
    loop = asyncio.get_event_loop()
    executor = ThreadPoolExecutor(max_workers=10)
    
    futures = []
    for message in messages:
        future = loop.run_in_executor(
            executor,
            publisher.publish,
            topic_path,
            message['data'],
            **message.get('attributes', {})
        )
        futures.append(future)
    
    return await asyncio.gather(*futures)
```

### Performance Monitoring
```python
import time
from dataclasses import dataclass
from typing import List

@dataclass
class PublishMetrics:
    total_messages: int = 0
    total_bytes: int = 0
    total_errors: int = 0
    start_time: float = 0
    
    def add_success(self, message_size):
        self.total_messages += 1
        self.total_bytes += message_size
    
    def add_error(self):
        self.total_errors += 1
    
    def get_throughput(self):
        elapsed = time.time() - self.start_time
        return {
            'messages_per_second': self.total_messages / elapsed,
            'mb_per_second': (self.total_bytes / 1024 / 1024) / elapsed,
            'error_rate': self.total_errors / (self.total_messages + self.total_errors)
        }
```

## Best Practices

### 1. Message Design
- Keep messages focused and single-purpose
- Use protobuf or JSON for structured data
- Include version information
- Compress large payloads

### 2. Error Handling
- Always handle publish futures
- Implement exponential backoff
- Log failed publishes
- Monitor error rates

### 3. Performance
- Batch messages when possible
- Use appropriate flow control
- Monitor publish latencies
- Scale publishers horizontally

### 4. Reliability
- Use idempotent message IDs
- Implement dead letter queues
- Monitor acknowledgment rates
- Test failure scenarios

### 5. Security
- Use service accounts with minimal permissions
- Encrypt sensitive data before publishing
- Audit publish access
- Use VPC Service Controls

## Monitoring and Debugging

### Key Metrics
```yaml
# Prometheus queries
- record: pubsub:publish:rate
  expr: rate(pubsub_topic_send_request_count[5m])

- record: pubsub:publish:errors
  expr: rate(pubsub_topic_send_request_count{response_code!="200"}[5m])

- record: pubsub:publish:latency_p99
  expr: histogram_quantile(0.99, rate(pubsub_topic_send_request_latency_bucket[5m]))
```

### Debug Logging
```python
import logging

# Enable debug logging
logging.getLogger('google.cloud.pubsub_v1').setLevel(logging.DEBUG)

# Custom publish wrapper with logging
def publish_with_logging(publisher, topic_path, data, **kwargs):
    start = time.time()
    try:
        future = publisher.publish(topic_path, data, **kwargs)
        message_id = future.result()
        duration = time.time() - start
        logging.info(f"Published {message_id} in {duration:.3f}s")
        return message_id
    except Exception as e:
        duration = time.time() - start
        logging.error(f"Publish failed after {duration:.3f}s: {e}")
        raise
```

### Common Issues

#### 1. High Latency
- Check batch settings
- Verify network connectivity
- Monitor CPU usage
- Review message sizes

#### 2. Ordering Failures
- Check for blocked ordering keys
- Verify regional consistency
- Monitor ordering key cardinality
- Implement proper error recovery

#### 3. Throughput Limitations
- Increase publisher instances
- Optimize batch settings
- Use regional endpoints
- Monitor quota usage

## Example: Production Publisher
```python
class ProductionPublisher:
    """Production-ready publisher with monitoring and error handling."""
    
    def __init__(self, project_id, topic_id):
        # Configure batch settings for throughput
        batch_settings = pubsub_v1.types.BatchSettings(
            max_messages=1000,
            max_bytes=10 * 1024 * 1024,  # 10 MB
            max_latency=0.05,             # 50 ms
        )
        
        # Configure flow control
        flow_control = pubsub_v1.types.PublishFlowControl(
            message_limit=10000,
            byte_limit=1024 * 1024 * 1024,  # 1 GB
            limit_exceeded_behavior=pubsub_v1.types.LimitExceededBehavior.BLOCK,
        )
        
        publisher_options = pubsub_v1.types.PublisherOptions(
            flow_control=flow_control
        )
        
        self.publisher = pubsub_v1.PublisherClient(
            batch_settings=batch_settings,
            publisher_options=publisher_options
        )
        
        self.topic_path = self.publisher.topic_path(project_id, topic_id)
        self.metrics = PublishMetrics(start_time=time.time())
    
    def publish(self, data, **attributes):
        """Publish with monitoring and error handling."""
        try:
            future = self.publisher.publish(
                self.topic_path,
                data,
                **attributes
            )
            
            # Add callback for metrics
            future.add_done_callback(
                lambda f: self._handle_publish_result(f, len(data))
            )
            
            return future
        
        except Exception as e:
            self.metrics.add_error()
            logging.error(f"Publish error: {e}")
            raise
    
    def _handle_publish_result(self, future, message_size):
        """Handle publish result for metrics."""
        try:
            future.result()
            self.metrics.add_success(message_size)
        except Exception:
            self.metrics.add_error()
    
    def get_stats(self):
        """Get publisher statistics."""
        return self.metrics.get_throughput()
```