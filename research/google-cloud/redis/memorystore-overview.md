# Memorystore for Redis - Comprehensive Guide

## Overview
Memorystore for Redis is Google Cloud's fully managed Redis service, providing a highly scalable, available, and secure Redis deployment without the operational overhead of managing complex Redis infrastructure.

## Key Features

### Managed Service Benefits
- **Fully managed**: Automated provisioning, replication, failover, and patching
- **High availability**: Built-in replication and automatic failover
- **Security**: VPC-native, encryption at rest and in transit
- **Monitoring**: Integrated with Cloud Monitoring
- **Backup**: Automated backups and point-in-time recovery

### Performance Characteristics
- **Extreme performance**: Optimized for Google Cloud applications
- **Low latency**: Sub-millisecond response times
- **High throughput**: Handles millions of operations per second
- **Scalability**: Seamless scaling up to 300 GB

### Redis Compatibility
- Supports Redis versions up to 7.2
- Compatible with open-source Redis protocols
- Supports most Redis commands (with some limitations)
- Works with existing Redis client libraries

## Getting Started

### Prerequisites
1. Google Cloud project with billing enabled
2. Compute Engine API enabled
3. VPC network configured
4. Appropriate IAM permissions

### Instance Creation Methods

#### 1. Using gcloud CLI
```bash
# Create a basic Redis instance
gcloud redis instances create my-redis-instance \
    --size=1 \
    --region=us-central1 \
    --redis-version=redis_7_2

# Create with specific configuration
gcloud redis instances create my-redis-instance \
    --size=5 \
    --region=us-central1 \
    --redis-version=redis_7_2 \
    --memory-size=10GB \
    --network=projects/PROJECT_ID/global/networks/NETWORK_NAME
```

#### 2. Using Google Cloud Console
1. Navigate to Memorystore for Redis
2. Click "Create Instance"
3. Configure:
   - Instance ID
   - Region and zone
   - Redis version
   - Memory size
   - Network settings
4. Click "Create"

### Instance Tiers

#### Basic Tier
- Cost-effective option
- No replication
- Best for development and testing
- Manual backups only

#### Standard Tier
- High availability with replication
- Automatic failover
- Read replicas
- Automated backups

## Connection Setup

### 1. VPC Network Configuration
```bash
# Ensure your application is in the same VPC
gcloud compute instances create app-instance \
    --zone=us-central1-a \
    --network=NETWORK_NAME \
    --subnet=SUBNET_NAME
```

### 2. Get Instance Details
```bash
# Get Redis instance IP
gcloud redis instances describe my-redis-instance \
    --region=us-central1 \
    --format="get(host)"
```

### 3. Connect from Application
```python
# Python example
import redis

# Create connection
r = redis.Redis(
    host='********',  # Internal IP from instance details
    port=6379,
    decode_responses=True
)

# Test connection
r.ping()
```

## Basic Operations

### Key-Value Operations
```python
# Set and get values
r.set('key', 'value')
value = r.get('key')

# Set with expiration
r.setex('temp_key', 300, 'temporary_value')  # 5 minutes

# Increment/decrement
r.incr('counter')
r.decr('counter')

# Check existence
exists = r.exists('key')
```

### Data Structures

#### Lists
```python
# Push/pop operations
r.lpush('mylist', 'first', 'second', 'third')
r.rpush('mylist', 'last')
first = r.lpop('mylist')
last = r.rpop('mylist')

# Get range
items = r.lrange('mylist', 0, -1)
```

#### Sets
```python
# Add members
r.sadd('myset', 'member1', 'member2', 'member3')

# Check membership
is_member = r.sismember('myset', 'member1')

# Get all members
members = r.smembers('myset')
```

#### Sorted Sets
```python
# Add with scores
r.zadd('leaderboard', {'player1': 100, 'player2': 200})

# Get top players
top_players = r.zrevrange('leaderboard', 0, 9, withscores=True)
```

#### Hashes
```python
# Set hash fields
r.hset('user:1000', mapping={
    'name': 'John Doe',
    'email': '<EMAIL>',
    'age': 30
})

# Get specific field
name = r.hget('user:1000', 'name')

# Get all fields
user_data = r.hgetall('user:1000')
```

## Configuration Best Practices

### 1. Memory Management
```bash
# Configure maxmemory policy
gcloud redis instances update my-redis-instance \
    --region=us-central1 \
    --update-redis-config maxmemory-policy=allkeys-lru
```

Common eviction policies:
- `noeviction`: Return errors when memory limit reached
- `allkeys-lru`: Evict least recently used keys
- `volatile-lru`: Evict least recently used keys with TTL
- `allkeys-random`: Evict random keys

### 2. Persistence Configuration
```bash
# Enable RDB persistence
gcloud redis instances update my-redis-instance \
    --region=us-central1 \
    --enable-persistence
```

### 3. Performance Tuning
```python
# Connection pooling
import redis

pool = redis.ConnectionPool(
    host='********',
    port=6379,
    max_connections=50,
    decode_responses=True
)
r = redis.Redis(connection_pool=pool)
```

### 4. Security Configuration
- Use VPC-native networking
- Implement AUTH password
- Use Private Service Connect
- Enable encryption at rest

## Common Use Cases

### 1. Session Storage
```python
# Store session data
session_id = 'sess_123456'
session_data = {
    'user_id': 1000,
    'username': 'johndoe',
    'login_time': '2024-01-15T10:30:00Z'
}
r.hset(f'session:{session_id}', mapping=session_data)
r.expire(f'session:{session_id}', 3600)  # 1 hour expiry
```

### 2. Caching
```python
# Cache database query results
def get_user_data(user_id):
    # Check cache first
    cached = r.get(f'user:{user_id}')
    if cached:
        return json.loads(cached)
    
    # Fetch from database
    user_data = fetch_from_database(user_id)
    
    # Cache for 5 minutes
    r.setex(f'user:{user_id}', 300, json.dumps(user_data))
    
    return user_data
```

### 3. Rate Limiting
```python
# Implement rate limiting
def check_rate_limit(user_id, max_requests=100, window=60):
    key = f'rate_limit:{user_id}'
    
    try:
        current = r.incr(key)
        if current == 1:
            r.expire(key, window)
        
        if current > max_requests:
            return False, current
        return True, current
    except redis.RedisError:
        return True, 0
```

### 4. Real-time Analytics
```python
# Track page views
def track_page_view(page_id):
    # Increment total views
    r.hincrby('page_views:total', page_id, 1)
    
    # Track hourly views
    hour_key = f'page_views:hourly:{datetime.now().strftime("%Y%m%d%H")}'
    r.hincrby(hour_key, page_id, 1)
    r.expire(hour_key, 86400)  # Keep for 24 hours
```

### 5. Pub/Sub Messaging
```python
# Publisher
def publish_message(channel, message):
    r.publish(channel, json.dumps(message))

# Subscriber
def subscribe_to_channel(channel):
    pubsub = r.pubsub()
    pubsub.subscribe(channel)
    
    for message in pubsub.listen():
        if message['type'] == 'message':
            data = json.loads(message['data'])
            process_message(data)
```

## Monitoring and Maintenance

### Key Metrics to Monitor
1. **Memory usage**: Track percentage of allocated memory used
2. **CPU utilization**: Monitor processing load
3. **Network throughput**: Bytes in/out per second
4. **Operation latency**: Response time for commands
5. **Connected clients**: Number of active connections
6. **Evicted keys**: Keys removed due to memory pressure

### Setting Up Alerts
```yaml
# Example alert policy
alertPolicy:
  displayName: "Redis High Memory Usage"
  conditions:
    - displayName: "Memory usage above 80%"
      conditionThreshold:
        filter: |
          resource.type="redis_instance"
          metric.type="redis.googleapis.com/stats/memory/usage_percentage"
        comparison: COMPARISON_GT
        thresholdValue: 0.8
        duration: 300s
```

### Backup Strategies
```bash
# Create manual backup
gcloud redis instances export my-redis-instance \
    --region=us-central1 \
    --destination=gs://my-bucket/redis-backup.rdb

# Schedule automated backups (Standard tier)
gcloud redis instances update my-redis-instance \
    --region=us-central1 \
    --backup-schedule-frequency=daily \
    --backup-schedule-time=03:00
```

## Scaling Strategies

### Vertical Scaling
```bash
# Increase instance size
gcloud redis instances update my-redis-instance \
    --region=us-central1 \
    --memory-size=10GB
```

### Read Replica Scaling
```bash
# Add read replicas (Standard tier)
gcloud redis instances update my-redis-instance \
    --region=us-central1 \
    --replica-count=2
```

### Sharding Strategies
For workloads exceeding single instance limits:
1. **Client-side sharding**: Distribute keys across multiple instances
2. **Proxy-based sharding**: Use Redis Cluster proxy
3. **Application-level partitioning**: Separate by data type or tenant

## Troubleshooting

### Common Issues

#### 1. Connection Timeouts
- Verify VPC network configuration
- Check firewall rules
- Ensure client and Redis instance are in same region
- Validate instance is running

#### 2. High Memory Usage
- Review eviction policy
- Analyze key patterns
- Implement TTL for temporary data
- Consider scaling instance

#### 3. Performance Degradation
- Monitor slow queries
- Check for blocking operations
- Review client connection pooling
- Analyze command patterns

#### 4. Replication Lag
- Monitor replication metrics
- Check network latency
- Review write patterns
- Consider regional placement

### Debugging Commands
```python
# Get instance info
info = r.info()

# Monitor commands in real-time
r.monitor()

# Check slow queries
slow_queries = r.slowlog_get()

# Memory analysis
memory_stats = r.memory_stats()
```

## Best Practices Summary

1. **Design for failure**: Implement retry logic and circuit breakers
2. **Use connection pooling**: Reuse connections efficiently
3. **Set appropriate TTLs**: Prevent unbounded memory growth
4. **Monitor actively**: Set up alerts for key metrics
5. **Test at scale**: Validate performance under load
6. **Plan capacity**: Account for growth and peak usage
7. **Secure access**: Use VPC and AUTH when available
8. **Regular backups**: Implement backup and recovery procedures
9. **Keep Redis updated**: Use latest stable version
10. **Document patterns**: Maintain clear documentation of data structures and access patterns