# Spanner CPU Utilization and Optimization Guide

## Overview
CPU utilization is a critical metric for Spanner performance. Understanding and optimizing CPU usage ensures consistent application performance and cost efficiency.

## Key CPU Utilization Metrics

### 1. Smoothed CPU Utilization
- **Definition**: 24-hour rolling average of total CPU usage
- **Purpose**: Provides stable view of overall system load
- **Use case**: Capacity planning and trend analysis

### 2. CPU Utilization by Priority
Breakdown by task priority levels:
- **High Priority**: User-initiated operations (queries, transactions)
- **Medium Priority**: Background maintenance tasks
- **Low Priority**: System housekeeping operations

### 3. CPU Utilization by Operation Type
- **Read operations**: SELECT queries
- **Write operations**: INSERT, UPDATE, DELETE
- **System operations**: Internal maintenance
- **Background operations**: Compaction, statistics

## Task Classification

### By Source
1. **User-initiated tasks**
   - Direct queries
   - Transactions
   - Batch operations
   - API calls

2. **System-initiated tasks**
   - Compaction
   - Statistics collection
   - Replication
   - Backup operations

### By Priority
1. **High Priority**
   - User queries
   - Transactional writes
   - Critical system operations

2. **Medium Priority**
   - Background index maintenance
   - Non-critical replication
   - Statistics updates

3. **Low Priority**
   - Cleanup operations
   - Long-term maintenance
   - Optional optimizations

## Recommended CPU Thresholds

### Regional Instances
```yaml
thresholds:
  high_priority_total: 65%  # Maximum for user operations
  smoothed_aggregate: 90%   # 24-hour average limit
  
alerts:
  warning: 55%  # High priority usage
  critical: 65% # High priority usage
```

### Multi-Regional Instances
```yaml
thresholds:
  high_priority_per_region: 45%  # Per-region limit
  smoothed_aggregate: 90%        # Global average
  
alerts:
  warning: 35%  # Per-region high priority
  critical: 45% # Per-region high priority
```

### Important Considerations
- Lower thresholds for multi-regional due to replication overhead
- CPU can temporarily exceed 100% but not sustained
- Performance degradation likely above thresholds

## Monitoring CPU Utilization

### Cloud Console Monitoring
1. Navigate to Spanner instance
2. Select "System Insights" tab
3. View CPU utilization graphs
4. Filter by time range and metric type

### Programmatic Monitoring
```python
from google.cloud import monitoring_v3

client = monitoring_v3.MetricServiceClient()
project_name = f"projects/{project_id}"

# Query CPU utilization metrics
interval = monitoring_v3.TimeInterval(
    {
        "end_time": {"seconds": int(time.time())},
        "start_time": {"seconds": int(time.time() - 3600)},
    }
)

results = client.list_time_series(
    request={
        "name": project_name,
        "filter": 'metric.type="spanner.googleapis.com/instance/cpu/utilization"',
        "interval": interval,
    }
)
```

### Creating Alerts
```yaml
# Alert policy configuration
alertPolicy:
  displayName: "Spanner High CPU Alert"
  conditions:
    - displayName: "CPU above 65%"
      conditionThreshold:
        filter: |
          resource.type="spanner_instance"
          metric.type="spanner.googleapis.com/instance/cpu/utilization_by_priority"
          metric.label.priority="high"
        comparison: COMPARISON_GT
        thresholdValue: 0.65
        duration: 300s
```

## CPU Optimization Strategies

### 1. Query Optimization
```sql
-- Bad: Full table scan
SELECT * FROM large_table WHERE status = 'active';

-- Good: Use index
CREATE INDEX idx_status ON large_table(status);
SELECT * FROM large_table@{FORCE_INDEX=idx_status} WHERE status = 'active';
```

### 2. Batch Operations
```python
# Bad: Individual operations
for row in rows:
    database.run_in_transaction(insert_row, row)

# Good: Batch operations
def batch_insert(transaction):
    transaction.batch_insert(
        table='my_table',
        columns=['id', 'data'],
        values=[(row['id'], row['data']) for row in rows]
    )

database.run_in_transaction(batch_insert)
```

### 3. Read Optimization
```python
# Bad: Unbounded reads
results = database.execute_sql("SELECT * FROM huge_table")

# Good: Paginated reads
query = """
    SELECT * FROM huge_table 
    WHERE id > @last_id 
    ORDER BY id 
    LIMIT 1000
"""
```

### 4. Connection Pooling
```python
# Configure connection pool
from google.cloud.spanner_v1 import pool

pool = pool.TransactionPingingPool(
    size=10,
    ping_interval=300,  # 5 minutes
    database=database
)
```

## Scaling Decisions

### When to Scale Up
1. **Consistent high CPU** (>65% for hours)
2. **Query latency increases**
3. **Transaction retries increase**
4. **Throughput plateaus**

### Scaling Strategies
```bash
# Manual scaling
gcloud spanner instances update my-instance --nodes=5

# Or use processing units for finer control
gcloud spanner instances update my-instance --processing-units=500
```

### Autoscaling Configuration
```yaml
autoscalingConfig:
  autoscalingLimits:
    minNodes: 1
    maxNodes: 10
  autoscalingTargets:
    highPriorityCpuUtilizationPercent: 65
    storageUtilizationPercent: 75
```

## Troubleshooting High CPU

### 1. Identify Problem Queries
```sql
SELECT 
  text,
  execution_count,
  avg_latency_seconds,
  avg_cpu_seconds
FROM 
  spanner_sys.query_stats_top_hour
WHERE 
  interval_end = (
    SELECT MAX(interval_end) 
    FROM spanner_sys.query_stats_top_hour
  )
ORDER BY 
  avg_cpu_seconds DESC
LIMIT 10;
```

### 2. Check Index Usage
```sql
SELECT 
  t.table_name,
  i.index_name,
  i.index_type
FROM 
  information_schema.tables t
LEFT JOIN 
  information_schema.indexes i
ON 
  t.table_name = i.table_name
WHERE 
  t.table_schema = ''
ORDER BY 
  t.table_name;
```

### 3. Monitor Active Operations
```sql
SELECT 
  operation_id,
  statement_text,
  start_time,
  cpu_seconds
FROM 
  spanner_sys.active_queries
ORDER BY 
  cpu_seconds DESC;
```

## Best Practices

### 1. Query Design
- Use parameters to enable query plan caching
- Avoid SELECT * - specify needed columns
- Use appropriate indexes
- Limit result sets

### 2. Transaction Management
- Keep transactions short
- Avoid long-running read-write transactions
- Use read-only transactions when possible
- Batch related operations

### 3. Schema Design
- Design efficient primary keys
- Use interleaved tables for related data
- Create covering indexes for frequent queries
- Avoid hot spots in key ranges

### 4. Application Design
- Implement connection pooling
- Use stale reads when appropriate
- Cache frequently accessed data
- Implement retry logic with backoff

### 5. Monitoring Strategy
- Set up proactive alerts
- Review query statistics regularly
- Monitor trends, not just current values
- Correlate CPU with other metrics

## Advanced Optimization

### 1. Partitioned Operations
```python
# Partition large operations
def partitioned_query():
    batch_partition_options = {
        "partition_size_bytes": 100 * 1024 * 1024,  # 100MB
        "max_partitions": 10
    }
    
    partitions = database.batch_snapshot(
        read_timestamp=datetime.utcnow()
    ).generate_read_batches(
        table="large_table",
        columns=["id", "data"],
        batch_partition_options=batch_partition_options
    )
    
    # Process partitions in parallel
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [
            executor.submit(process_partition, partition) 
            for partition in partitions
        ]
```

### 2. Query Hints
```sql
-- Force specific index
SELECT * FROM orders@{FORCE_INDEX=orders_by_date}
WHERE order_date > '2024-01-01';

-- Disable automatic index selection
SELECT /*@ disable_auto_index */ * FROM products
WHERE category = 'electronics';
```

### 3. Read Options
```python
# Stale reads for lower CPU usage
with database.snapshot(exact_staleness=datetime.timedelta(seconds=15)) as snapshot:
    results = snapshot.execute_sql("SELECT * FROM inventory")

# Strong reads when necessary
with database.snapshot() as snapshot:
    results = snapshot.execute_sql("SELECT balance FROM accounts WHERE id = @id")
```

## Capacity Planning

### Estimating CPU Requirements
1. **Baseline measurements**
   - Measure CPU per operation type
   - Calculate operations per second
   - Add 30% headroom

2. **Growth projections**
   - Account for data growth
   - Consider query complexity increase
   - Plan for traffic spikes

3. **Testing methodology**
   - Use production-like data
   - Simulate peak loads
   - Monitor all metrics

### Cost Optimization
1. **Right-sizing**
   - Start small and scale up
   - Use processing units for precision
   - Review usage patterns monthly

2. **Regional vs Multi-regional**
   - Regional for single-region apps
   - Multi-regional for global apps
   - Consider replication overhead

3. **Committed use discounts**
   - Analyze stable workload patterns
   - Commit to baseline capacity
   - Keep headroom for growth