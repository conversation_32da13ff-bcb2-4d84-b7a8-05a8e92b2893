# Backoff Crate - Retry Strategies Documentation

## Core Functionality
- Implements exponential backoff algorithm with randomization
- Supports both synchronous and asynchronous retry strategies
- Handles transient and permanent errors explicitly
- Configurable retry intervals and maximum elapsed time

## Key Retry Mechanics
- Calculates backoff using: `retry_interval * (random value between 1 ± randomization_factor)`
- Exponentially increases retry intervals
- Caps maximum retry interval
- Stops retrying after maximum elapsed time

## Error Handling Patterns
- Explicit error categorization:
  - `Error::Permanent`: Stops retrying immediately
  - `Error::Transient`: Continues retry attempts
  - `Error::retry_after()`: Retry with specific delay (useful for rate limiting)

## Async Support
- Requires enabling feature flags:
  - `tokio`
  - `async-std`

## Example Use Cases
- Network request retries
- Database connection recovery
- Resilient API interactions

## Configuration Flexibility
- Customizable randomization factor
- Configurable multiplier
- Maximum elapsed time setting

## Practical Example
```rust
let backoff = ExponentialBackoff::default();
retry(backoff, || {
    // Retry operation with exponential backoff
})
```

The library provides a sophisticated, configurable retry mechanism with strong error handling capabilities across synchronous and asynchronous contexts.