# Spanner Rust Implementation Patterns

## Connection Setup Pattern

```rust
use google_cloud_spanner::{Client, ClientConfig};

// Basic client setup with default authentication
let config = ClientConfig::default().with_auth().await?;
let client = Client::new("projects/my-project/instances/my-instance/databases/my-database", config).await?;
```

## Connection Pooling Pattern

### Using bb8
```rust
use bb8::Pool;
use bb8_google_cloud_spanner::SpannerConnectionManager;

// Create connection manager
let manager = SpannerConnectionManager::new(database_uri, config);

// Build pool with configuration
let pool = Pool::builder()
    .max_size(30)
    .min_idle(Some(5))
    .connection_timeout(Duration::from_secs(30))
    .build(manager)
    .await?;

// Use pooled connection
let conn = pool.get().await?;
```

### Using deadpool
```rust
use deadpool::managed::Pool;
use deadpool_google_cloud_spanner::{Manager, Config};

// Create managed pool
let config = Config::new(database_uri);
let manager = Manager::new(config);
let pool = Pool::builder(manager).max_size(30).build()?;

// Get connection from pool
let conn = pool.get().await?;
```

## Transaction Patterns

### Read-Only Transaction
```rust
// Single read transaction
let mut tx = client.single().await?;
let mut stmt = Statement::new("SELECT * FROM Users WHERE active = @active");
stmt.add_param("active", &true);
let mut iter = tx.query(stmt).await?;

while let Some(row) = iter.next().await? {
    let user_id: String = row.get(0)?;
    let name: String = row.get(1)?;
}
```

### Read-Write Transaction
```rust
// Read-write transaction with retry
use tokio_retry::{Retry, strategy::ExponentialBackoff};

let retry_strategy = ExponentialBackoff::from_millis(100)
    .map(jitter)
    .take(5);

let result = Retry::spawn(retry_strategy, || async {
    let mut tx = client.read_write().await?;
    
    // Read data
    let mut stmt = Statement::new("SELECT balance FROM Accounts WHERE id = @id");
    stmt.add_param("id", &account_id);
    let mut iter = tx.query(stmt).await?;
    
    // Process and update
    if let Some(row) = iter.next().await? {
        let balance: f64 = row.get(0)?;
        let new_balance = balance + amount;
        
        // Update with mutation
        let mutation = update("Accounts", 
            &[("id", &account_id), ("balance", &new_balance)]);
        tx.buffer_write(vec![mutation]);
    }
    
    // Commit transaction
    tx.commit().await
}).await?;
```

## Error Handling Pattern

```rust
use google_cloud_spanner::Error;
use backoff::{Error as BackoffError, ExponentialBackoff};

async fn execute_with_retry<F, T>(operation: F) -> Result<T, Error>
where
    F: Fn() -> Result<T, Error>,
{
    let backoff = ExponentialBackoff::default();
    
    backoff::retry(backoff, || {
        match operation() {
            Ok(result) => Ok(result),
            Err(e) => {
                if is_retryable(&e) {
                    Err(BackoffError::transient(e))
                } else {
                    Err(BackoffError::permanent(e))
                }
            }
        }
    }).await
}

fn is_retryable(error: &Error) -> bool {
    // Check if error is retryable based on error code
    matches!(error.code(), 
        Code::Unavailable | 
        Code::DeadlineExceeded | 
        Code::Aborted)
}
```

## Batch Operations Pattern

```rust
// Batch mutations
let mutations = vec![
    insert("Users", &[("id", &"user1"), ("name", &"Alice")]),
    insert("Users", &[("id", &"user2"), ("name", &"Bob")]),
    update("Users", &[("id", &"user3"), ("name", &"Charlie")]),
    delete("Users", &[&"user4"]),
];

// Apply batch
let commit_timestamp = client.apply(mutations).await?;
```

## Query Building Pattern

```rust
use google_cloud_spanner::Statement;

// Parameterized query builder
struct QueryBuilder {
    base_query: String,
    params: Vec<(String, Value)>,
}

impl QueryBuilder {
    fn new(base: &str) -> Self {
        Self {
            base_query: base.to_string(),
            params: Vec::new(),
        }
    }
    
    fn add_param<T: Into<Value>>(mut self, name: &str, value: T) -> Self {
        self.params.push((name.to_string(), value.into()));
        self
    }
    
    fn build(self) -> Statement {
        let mut stmt = Statement::new(&self.base_query);
        for (name, value) in self.params {
            stmt.add_param(&name, value);
        }
        stmt
    }
}

// Usage
let query = QueryBuilder::new("SELECT * FROM Users WHERE age > @min_age AND active = @active")
    .add_param("min_age", 18)
    .add_param("active", true)
    .build();
```

## Session Management Pattern

```rust
// Custom session pool wrapper
struct SpannerSessionPool {
    client: Client,
    sessions: Arc<Mutex<Vec<Session>>>,
    max_sessions: usize,
}

impl SpannerSessionPool {
    async fn get_session(&self) -> Result<Session, Error> {
        let mut sessions = self.sessions.lock().await;
        
        if let Some(session) = sessions.pop() {
            // Validate session is still alive
            if session.is_valid().await {
                return Ok(session);
            }
        }
        
        // Create new session if needed
        if sessions.len() < self.max_sessions {
            self.client.create_session().await
        } else {
            // Wait for available session
            // Implementation depends on specific requirements
            Err(Error::ResourceExhausted)
        }
    }
    
    async fn return_session(&self, session: Session) {
        let mut sessions = self.sessions.lock().await;
        if sessions.len() < self.max_sessions {
            sessions.push(session);
        }
    }
}
```

## Performance Optimization Patterns

### 1. Stale Reads for Performance
```rust
use google_cloud_spanner::TimestampBound;

// Read with 15-second staleness for better performance
let bound = TimestampBound::exact_staleness(Duration::from_secs(15));
let mut tx = client.single_with_timestamp_bound(bound).await?;
let iter = tx.query(stmt).await?;
```

### 2. Batch Reading
```rust
// Partition query for parallel processing
let partitions = client.partition_query(
    Statement::new("SELECT * FROM LargeTable"),
    PartitionOptions::default()
).await?;

// Process partitions in parallel
let handles: Vec<_> = partitions.into_iter()
    .map(|partition| {
        let client = client.clone();
        tokio::spawn(async move {
            process_partition(client, partition).await
        })
    })
    .collect();

// Wait for all partitions
let results = futures::future::join_all(handles).await;
```

### 3. Connection Keep-Alive
```rust
// Periodic session refresh to maintain connection pool health
async fn maintain_sessions(pool: Arc<SpannerSessionPool>) {
    let mut interval = tokio::time::interval(Duration::from_secs(300)); // 5 minutes
    
    loop {
        interval.tick().await;
        
        // Refresh sessions
        if let Ok(session) = pool.get_session().await {
            // Execute lightweight query to keep session alive
            let _ = session.execute_sql("SELECT 1").await;
            pool.return_session(session).await;
        }
    }
}
```

## Monitoring Pattern

```rust
use prometheus::{Counter, Histogram};

lazy_static! {
    static ref QUERY_COUNTER: Counter = Counter::new("spanner_queries_total", "Total Spanner queries").unwrap();
    static ref QUERY_DURATION: Histogram = Histogram::new("spanner_query_duration_seconds", "Spanner query duration").unwrap();
}

async fn monitored_query(client: &Client, stmt: Statement) -> Result<Vec<Row>, Error> {
    QUERY_COUNTER.inc();
    let timer = QUERY_DURATION.start_timer();
    
    let result = client.single().await?
        .query(stmt).await?
        .collect().await;
    
    timer.observe_duration();
    result
}
```