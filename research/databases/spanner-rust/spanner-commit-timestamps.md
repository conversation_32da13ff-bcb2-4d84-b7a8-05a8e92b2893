# Google Cloud Spanner Commit Timestamps Documentation

## Key Characteristics
- Commit timestamps represent the exact moment a transaction is committed in the database
- Based on Google's TrueTime technology
- Have microsecond granularity, stored as nanoseconds
- Can be used to create changelogs and optimize recent-data queries

## Implementation Steps
1. Create a timestamp column with `allow_commit_timestamp=true`
2. Use `PENDING_COMMIT_TIMESTAMP()` in DML or `spanner.commit_timestamp()` in mutations
3. Timestamps must be in the past

## Best Practices
- Avoid using commit timestamps as the first part of primary keys to prevent hotspots
- Write commit timestamps as the last statement in a transaction
- Timestamps are not guaranteed to be unique across all transactions

## Example Schema
```sql
CREATE TABLE Documents (
  UserId INT64 NOT NULL,
  DocumentId INT64 NOT NULL,
  Ts TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true)
) PRIMARY KEY (UserId, DocumentId);
```

## Optimization Tip
Queries comparing commit timestamps with constant expressions can benefit from reduced I/O, like:
```sql
SELECT * FROM Performances 
WHERE LastUpdateTime > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY);
```

The documentation provides comprehensive code examples for multiple programming languages demonstrating commit timestamp usage.