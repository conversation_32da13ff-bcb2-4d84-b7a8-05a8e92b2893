# Google Cloud Spanner Rust Client Documentation

## Library Overview
The `google-cloud-spanner` is a Rust client library for interacting with Google Cloud Spanner, providing robust database interaction capabilities.

## Key Features
- Supports authentication via environment variables or manual credentials
- Provides comprehensive transaction management
- Supports read-only and read-write transactions
- Handles complex query and mutation operations
- Offers both synchronous and asynchronous database interactions

## Main Modules
- `client`: Primary client interaction
- `mutation`: Database mutation operations
- `key`: Key and key range management
- `statement`: SQL statement handling
- `transaction`: Transaction management
- `row`: Row data extraction
- `value`: Type conversion and handling

## Client Creation Example
```rust
let config = ClientConfig::default().with_auth().await.unwrap();
let client = Client::new("projects/.../databases/...", config).await.unwrap();
```

## Transaction Types
1. Single Read Transaction
2. Read-Only Transaction
3. Read-Write Transaction

## Query Execution
```rust
let mut stmt = Statement::new("SELECT * FROM Users WHERE UserId = @UserID");
stmt.add_param("UserId", &"user_id");
let mut tx = client.single().await?;
let iter = tx.query(stmt).await?;
```

## Mutation Example
```rust
let mutation = insert_or_update("Users", 
    &["UserId", "Name"], 
    &[&"user1", &"John Doe"]
);
client.apply(vec![mutation]).await?;
```

## Key Characteristics
- Async-first design
- Automatic session management
- Supports partitioned DML
- Comprehensive error handling
- Flexible authentication methods

The library aims to provide a Rust-idiomatic interface to Google Cloud Spanner, closely mirroring the Go client's design while leveraging Rust's type system and async capabilities.