# Deadpool Async Connection Pooling Documentation

## Deadpool Overview
- A "dead simple async pool for connections and objects of any type"
- Supports two primary pool implementations:
  1. Managed Pool (`deadpool::managed::Pool`)
  2. Unmanaged Pool (`deadpool::unmanaged::Pool`)

## Key Features
- Runtime agnostic
- Compatible with multiple database backends
- No background threads or tasks required
- Objects returned via `Drop` trait
- Minimal API surface
- Supports runtime resizing
- Provides object lifecycle metrics

## Pool Types

### 1. Managed Pool
- Automatically creates and recycles objects
- Ideal for database connection pools
- Requires implementing a `Manager` trait
- Supports customization via hooks (`post_create`, `pre_recycle`)

### 2. Unmanaged Pool
- User manually manages object creation
- Slightly faster than managed pool
- Useful when custom management isn't feasible

## Runtime Support
- Optional runtime features for:
  - Tokio
  - Async-std
- Timeouts require specifying a runtime

## Unique Design Principles
- Identical startup and runtime behavior
- No automatic startup failures
- Minimal locking
- Extensible architecture

## Supported Databases
- PostgreSQL
- Redis
- RabbitMQ (AMQP)
- SQLite
- And several others via specialized crates

## Licensing
- Dual-licensed under Apache 2.0 and MIT

## Example (Managed Pool)

```rust
use deadpool::managed;

struct Computer {}
impl managed::Manager for Manager {
    type Type = Computer;
    type Error = Error;
    
    async fn create(&self) -> Result<Computer, Error> {
        Ok(Computer {})
    }
}
```

Recommended for developers seeking a lightweight, flexible async connection pooling solution across various runtimes and object types.