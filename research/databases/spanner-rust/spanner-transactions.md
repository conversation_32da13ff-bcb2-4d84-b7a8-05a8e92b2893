# Google Cloud Spanner Transactions Documentation

## Transaction Types

### 1. Read-Write Transactions
- Provide "pessimistic locking and, if needed a two-phase commit"
- Can modify data across multiple tables within a single database
- Require explicit commit or rollback
- Offer strong ACID properties

### 2. Read-Only Transactions
- Guarantee data consistency across multiple read operations
- Do not permit data modifications
- Execute at a system-determined or user-configured timestamp
- Do not require locks or commit operations

### 3. Partitioned DML Transactions
- Optimized for large-scale data updates and deletions
- Execute DML statements by partitioning the key space
- Useful for bulk data operations without full table locking

## Key Transaction Characteristics

- **Serializability**: Transactions appear to execute sequentially
- **External Consistency**: Commit timestamps align with real-world time
- **Atomicity**: Transactions are all-or-nothing operations
- **Isolation**: Concurrent transactions do not interfere with each other

## Performance Considerations

- Minimize transaction duration
- Use single read methods when possible
- Prefer read-only transactions for read-only operations
- Be aware of distributed execution overhead

## Best Practices

- Use read-only transactions for read-only operations
- Keep transactions short to reduce contention
- Use appropriate timestamp bounds for read-only transactions
- Handle potential transaction retries
- Understand lock acquisition and release mechanisms

## Code Example Highlights

Most language implementations follow a similar pattern:
- Create a transaction
- Perform reads or writes
- Commit or rollback the transaction
- Handle potential errors and retries

## Unique Spanner Features

- TrueTime API for external consistency
- Distributed transaction management
- Automatic lock management
- Timestamp-based concurrency control