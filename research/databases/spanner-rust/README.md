# Spanner + Rust Integration Research

This directory contains comprehensive documentation for integrating Google Cloud Spanner with Rust applications.

## Documentation Structure

### Core Client Documentation
- **[google-cloud-spanner-docs.md](./google-cloud-spanner-docs.md)** - Official Rust client library documentation
- **[spanner-rust-client-readme.md](./spanner-rust-client-readme.md)** - Client library README with quickstart examples

### Connection Management
- **[bb8-connection-pooling.md](./bb8-connection-pooling.md)** - Async connection pooling with bb8
- **[deadpool-connection-pooling.md](./deadpool-connection-pooling.md)** - Alternative pooling with deadpool
- **[spanner-sessions.md](./spanner-sessions.md)** - Session management best practices

### Transaction & Data Operations
- **[spanner-transactions.md](./spanner-transactions.md)** - Transaction types and management
- **[spanner-commit-timestamps.md](./spanner-commit-timestamps.md)** - Working with commit timestamps
- **[spanner-reads.md](./spanner-reads.md)** - Read operations and optimization

### Error Handling & Resilience
- **[backoff-retry-strategies.md](./backoff-retry-strategies.md)** - Exponential backoff implementation
- **[tokio-retry.md](./tokio-retry.md)** - Tokio-specific retry patterns

### Performance
- **[spanner-performance-optimization.md](./spanner-performance-optimization.md)** - Performance tuning guidelines
- **[implementation-patterns.md](./implementation-patterns.md)** - Practical code patterns and examples

## Key Takeaways

### 1. Client Setup
- Use `google-cloud-spanner` crate (or `gcloud-spanner` package name)
- Supports async/await patterns with tokio
- Authentication via environment variables or explicit config

### 2. Connection Pooling
- Essential for performance - session creation is expensive
- bb8 or deadpool both work well with Spanner
- Configure pool size based on concurrent transaction needs

### 3. Transaction Management
- Three types: read-only, read-write, and partitioned DML
- Keep transactions short to reduce contention
- Implement retry logic for transient failures

### 4. Error Handling
- Use exponential backoff for retries
- Distinguish between permanent and transient errors
- Consider using `backoff` or `tokio-retry` crates

### 5. Performance Best Practices
- Use stale reads when absolute consistency isn't required
- Batch mutations for bulk operations
- Monitor and optimize session pool usage
- Leverage parallel reads for large datasets

## Quick Start Example

```rust
use google_cloud_spanner::{Client, ClientConfig, Statement};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Setup client
    let config = ClientConfig::default().with_auth().await?;
    let client = Client::new("projects/my-project/instances/my-instance/databases/my-db", config).await?;
    
    // Execute query
    let mut stmt = Statement::new("SELECT * FROM Users WHERE active = @active");
    stmt.add_param("active", &true);
    
    let mut tx = client.single().await?;
    let mut iter = tx.query(stmt).await?;
    
    while let Some(row) = iter.next().await? {
        println!("User: {:?}", row);
    }
    
    Ok(())
}
```

## Integration Considerations

1. **Authentication**: Set up Google Cloud credentials via environment variables or service account
2. **Dependencies**: Add required crates to `Cargo.toml`:
   ```toml
   [dependencies]
   google-cloud-spanner = "0.x"
   tokio = { version = "1", features = ["full"] }
   bb8 = "0.8"  # Optional: for connection pooling
   backoff = { version = "0.4", features = ["tokio"] }  # Optional: for retry logic
   ```
3. **Error Handling**: Implement comprehensive error handling with retries
4. **Monitoring**: Add metrics for query performance and pool utilization
5. **Testing**: Use Spanner emulator for local development and testing