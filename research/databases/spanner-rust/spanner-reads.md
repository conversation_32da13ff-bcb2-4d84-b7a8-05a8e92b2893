# Google Cloud Spanner Read Operations Documentation

## Read Types

### 1. Strong Reads
- "A _strong read_ is a read at a current timestamp and is guaranteed to see all data that has been committed"
- Default read type for Spanner
- Provides most consistent view of data

### 2. Stale Reads
- "A _stale read_ is read at a timestamp in the past"
- Provides performance benefits for latency-sensitive applications
- Recommended staleness is around 15 seconds

## Read Best Practices

- "Choose strong reads whenever possible"
- Use stale reads when absolute recency isn't critical
- Consider performance trade-offs between read types

## Read Methods

Spanner supports multiple read approaches:
- Single read methods
- SQL query statements
- Read from single/multiple rows
- Read from tables or secondary indexes
- Batch/parallel reads

## Parallel Reading Strategies

Key parallel reading techniques:
- Use `PartitionQuery` API for bulk operations
- Distribute query partitions across multiple workers
- Enable Data Boost for independent compute resources
- Optimize by running partitions in parallel

## Performance Considerations

- Use strong reads as default
- Select appropriate timestamp bounds
- Consider Data Boost for large analytical queries
- Distribute partitions across machines for efficiency

## Code Example Highlights

Most client libraries offer similar read patterns:
- Create client/transaction
- Define read parameters
- Execute read
- Process results

Example (Python) parallel read structure:
```python
def run_batch_query(instance_id, database_id):
    snapshot = database.batch_snapshot()
    partitions = snapshot.generate_read_batches(
        table="Singers",
        columns=("SingerId", "FirstName", "LastName"),
        data_boost_enabled=True
    )
```

## Key Recommendations

- Prioritize strong reads
- Use stale reads judiciously
- Leverage parallel reading for large datasets
- Choose appropriate timestamp bounds
- Consider Data Boost for complex queries