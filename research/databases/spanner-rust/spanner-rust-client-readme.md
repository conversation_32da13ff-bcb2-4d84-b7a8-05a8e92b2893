# Google Cloud Spanner Rust Client README Documentation

## Overview
- A Rust library for interacting with Google Cloud Spanner
- Provides similar functionality to the Google Cloud Go Spanner library

## Installation
```toml
[dependencies]
google-cloud-spanner = { package = "gcloud-spanner", version = "1.0.0" }
```

## Quickstart Example
```rust
// Create Spanner client
let config = ClientConfig::default().with_auth().await.unwrap();
let client = Client::new(DATABASE, config).await.unwrap();

// Insert data
let mutation = insert("Guild", &["GuildId", "OwnerUserID", "UpdatedAt"], 
                      &[&"guildId", &"ownerId", &CommitTimestamp::new()]);
let commit_timestamp = client.apply(vec![mutation]).await?;

// Read with query
let mut stmt = Statement::new("SELECT GuildId FROM Guild WHERE OwnerUserID = @OwnerUserID");
stmt.add_param("OwnerUserID", &"ownerId");
let tx = client.single().await?;
let mut iter = tx.query(stmt).await?;
```

## Key Features
- Async/await support
- Transaction management
- Parameterized queries
- Commit timestamp handling

## Performance
- Comparable performance to Google Cloud Go library
- Load test results show similar RPS (Requests Per Second)
- Efficient CPU usage (0.37 ~ 0.42 vCPU)

## Related Project
- Companion library: "google-cloud-spanner-derive"

## Test Conditions
- 2.0 vCPU GKE Autopilot Pod
- 1 Node Spanner database server
- 100 Users

The library aims to provide a Rust-native, performant interface to Google Cloud Spanner with an API similar to the Go implementation.