# Google Cloud Eventarc

## Overview
Eventarc is Google Cloud's serverless, fully managed eventing solution that enables building event-driven architectures. It provides asynchronous message routing between event sources and destinations, supporting both simple and complex event management scenarios.

## Editions

### Eventarc Standard
Recommended for simple event delivery scenarios:
- Basic event filtering by source, type, and attributes
- Direct routing to destinations
- Lower complexity requirements
- Cost-effective for straightforward use cases

### Eventarc Advanced
Designed for complex event management:
- Central event bus architecture
- Advanced filtering and transformation
- Cross-project event delivery
- Event collection and redistribution
- Enterprise-scale event processing

## Core Concepts

### Event Sources
Eventarc can receive events from multiple sources:

#### Google Cloud Services
- Cloud Storage (object changes)
- Cloud Pub/Sub (message publishing)
- BigQuery (dataset/table changes)
- Cloud Build (build status)
- Cloud SQL (database events)
- Firebase (Realtime Database, Authentication)
- Over 130+ Google event sources

#### Direct Event Publishing
- Custom applications via Eventarc Publishing API
- HTTP endpoints with authentication
- CloudEvents format support

### Event Format
All events use CloudEvents v1.0 specification:
```json
{
  "specversion": "1.0",
  "type": "google.cloud.storage.object.v1.finalized",
  "source": "//storage.googleapis.com/projects/_/buckets/my-bucket",
  "subject": "objects/my-file.txt",
  "id": "1234567890",
  "time": "2024-01-15T12:00:00Z",
  "datacontenttype": "application/json",
  "data": {
    "name": "my-file.txt",
    "bucket": "my-bucket",
    "size": "1024"
  }
}
```

### Event Destinations

#### Cloud Run
- Functions and services
- Automatic scaling based on events
- HTTP endpoint invocation

#### Workflows
- Orchestrate complex event processing
- Multi-step event handling
- Conditional logic and error handling

#### Cloud Pub/Sub
- Topic-based messaging
- Fan-out to multiple subscribers
- Message persistence and replay

#### HTTP Endpoints
- Internal and external endpoints
- Public and private GKE services
- Custom authentication support

## Architecture Patterns

### Simple Event Routing (Standard)
```
Event Source → Eventarc Trigger → Destination
     ↓              ↓                   ↓
Cloud Storage    Filter           Cloud Run
                Conditions         Function
```

### Central Event Bus (Advanced)
```
Multiple Sources → Event Bus → Multiple Destinations
       ↓               ↓              ↓
   Publishers      Transform      Subscribers
                   & Filter
```

### Cross-Project Delivery
```
Project A         →    Project B
Event Source           Event Destination
    ↓                       ↓
Eventarc          →    Cloud Run
(Publisher)            (Consumer)
```

## Creating Triggers

### Basic Trigger Configuration
```bash
# Cloud Storage trigger
gcloud eventarc triggers create storage-trigger \
  --location=us-central1 \
  --destination-run-service=my-service \
  --destination-run-region=us-central1 \
  --event-filters="type=google.cloud.storage.object.v1.finalized" \
  --event-filters="bucket=my-bucket" \
  --service-account=<EMAIL>
```

### Advanced Filtering
```bash
# Multiple filters
gcloud eventarc triggers create filtered-trigger \
  --location=us-central1 \
  --destination-run-service=process-orders \
  --event-filters="type=google.cloud.pubsub.topic.v1.messagePublished" \
  --event-filters-path-pattern="subject=orders/*" \
  --event-filters="customAttribute=priority:high"
```

### Cross-Project Trigger
```bash
# In destination project
gcloud eventarc triggers create cross-project-trigger \
  --location=us-central1 \
  --destination-run-service=event-processor \
  --destination-project=destination-project \
  --event-filters="type=custom.event.v1" \
  --transport-topic=projects/source-project/topics/event-topic
```

## Event Filtering

### Filter Types

#### Type Filters
Match specific event types:
```yaml
event-filters:
  - attribute: type
    value: google.cloud.storage.object.v1.finalized
```

#### Attribute Filters
Match event attributes:
```yaml
event-filters:
  - attribute: bucket
    value: my-bucket
  - attribute: customAttribute
    value: production
```

#### Path Pattern Filters
Pattern matching for hierarchical data:
```yaml
event-filters-path-pattern:
  - attribute: subject
    value: "users/*/orders/*"
```

### Complex Filtering (Advanced)
```json
{
  "filter": {
    "and": [
      {
        "attribute": "type",
        "value": "order.created"
      },
      {
        "or": [
          {
            "attribute": "priority",
            "value": "high"
          },
          {
            "attribute": "amount",
            "operator": ">",
            "value": "1000"
          }
        ]
      }
    ]
  }
}
```

## Security Model

### Authentication
- Service account-based authentication
- IAM roles for access control
- Automatic token generation for destinations

### Required IAM Roles
```yaml
# For trigger creation
roles:
  - eventarc.eventReceiver
  - run.invoker  # For Cloud Run destinations
  - pubsub.publisher  # For Pub/Sub transport

# For event sources
roles:
  - eventarc.eventProducer
  - storage.objectViewer  # For Cloud Storage events
```

### Service Account Configuration
```bash
# Create service account
gcloud iam service-accounts create eventarc-trigger-sa \
  --display-name="Eventarc Trigger Service Account"

# Grant necessary permissions
gcloud projects add-iam-policy-binding PROJECT_ID \
  --member="serviceAccount:eventarc-trigger-sa@PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/eventarc.eventReceiver"

gcloud run services add-iam-policy-binding my-service \
  --member="serviceAccount:eventarc-trigger-sa@PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/run.invoker"
```

## Event Processing Patterns

### Fan-Out Pattern
```yaml
# Multiple triggers from single source
triggers:
  - name: process-images
    filters:
      - type: google.cloud.storage.object.v1.finalized
      - contentType: image/*
    destination: image-processor
  
  - name: process-documents
    filters:
      - type: google.cloud.storage.object.v1.finalized
      - contentType: application/pdf
    destination: document-processor
```

### Event Transformation (Advanced)
```python
# Cloud Run function for transformation
import functions_framework
import json

@functions_framework.cloud_event
def transform_event(cloud_event):
    # Extract original data
    original_data = cloud_event.data
    
    # Transform event
    transformed_data = {
        "orderId": original_data.get("id"),
        "amount": float(original_data.get("amount", 0)),
        "priority": calculate_priority(original_data),
        "timestamp": cloud_event["time"]
    }
    
    # Publish transformed event
    publish_to_eventarc(transformed_data)
```

### Error Handling
```python
import functions_framework
from google.cloud import error_reporting

error_client = error_reporting.Client()

@functions_framework.cloud_event
def process_with_retry(cloud_event):
    try:
        # Process event
        result = process_event(cloud_event)
        return result
    except Exception as e:
        error_client.report_exception()
        
        # Check retry count
        retry_count = int(cloud_event.get("retryCount", 0))
        if retry_count < 3:
            # Retry by publishing to retry topic
            publish_retry_event(cloud_event, retry_count + 1)
        else:
            # Send to dead letter queue
            publish_to_dlq(cloud_event, str(e))
```

## Monitoring and Observability

### Metrics
Key metrics to monitor:
- Trigger execution count
- Event delivery success/failure rates
- Processing latency
- Error rates by trigger

### Cloud Logging
```python
import logging
import json

def process_event(cloud_event):
    # Structured logging
    logging.info(json.dumps({
        "message": "Processing event",
        "eventId": cloud_event["id"],
        "eventType": cloud_event["type"],
        "source": cloud_event["source"],
        "trigger": os.environ.get("K_SERVICE", "unknown")
    }))
```

### Monitoring Query
```sql
-- Cloud Logging query for Eventarc events
resource.type="cloud_run_revision"
resource.labels.service_name="my-service"
jsonPayload.eventType="google.cloud.storage.object.v1.finalized"
severity>=WARNING
```

## Best Practices

### 1. Event Design
- Use CloudEvents format consistently
- Include correlation IDs for tracing
- Keep event payloads small (< 512KB)
- Use references for large data

### 2. Trigger Configuration
- Use specific filters to reduce noise
- Implement idempotent event handlers
- Set appropriate service account permissions
- Use path patterns for hierarchical filtering

### 3. Error Handling
- Implement retry logic with backoff
- Use dead letter queues for failed events
- Monitor and alert on failures
- Log events for debugging

### 4. Performance
- Process events asynchronously
- Use batch processing where applicable
- Implement circuit breakers
- Monitor processing latency

### 5. Security
- Use least-privilege service accounts
- Encrypt sensitive event data
- Audit event access
- Implement event validation

## Use Cases

### 1. File Processing Pipeline
```yaml
trigger:
  source: Cloud Storage
  event: object.finalized
  destination: Cloud Run
  
flow:
  1. File uploaded to bucket
  2. Eventarc triggers Cloud Run
  3. Process file (resize, convert, analyze)
  4. Store results
```

### 2. Real-time Data Processing
```yaml
trigger:
  source: Pub/Sub
  event: message.published
  destination: Cloud Run
  
flow:
  1. Stream data to Pub/Sub
  2. Eventarc routes to processor
  3. Transform and enrich data
  4. Write to BigQuery
```

### 3. Microservices Communication
```yaml
trigger:
  source: Custom Application
  event: order.created
  destination: Multiple Services
  
flow:
  1. Order service publishes event
  2. Eventarc fans out to:
     - Inventory service
     - Payment service
     - Notification service
```

### 4. Workflow Orchestration
```yaml
trigger:
  source: Cloud Build
  event: build.complete
  destination: Workflows
  
flow:
  1. Build completes
  2. Trigger deployment workflow
  3. Run tests
  4. Deploy to production
```

## Limitations and Considerations

### Technical Limitations
- No guaranteed in-order delivery
- 24-hour default retention
- Event size limits (1MB Advanced, 512KB Standard)
- Regional service (no global triggers)

### Design Considerations
- Plan for eventual consistency
- Implement idempotency
- Handle duplicate events
- Consider event ordering requirements

### Cost Considerations
- Charged per event delivery
- Additional costs for destinations
- Network egress charges
- Monitoring and logging costs

## Migration from Pub/Sub

### Comparison
| Feature | Pub/Sub | Eventarc |
|---------|---------|----------|
| Event Sources | Manual publishing | 130+ automatic sources |
| Filtering | Subscription filters | Advanced attribute filtering |
| Format | Custom | CloudEvents standard |
| Destinations | Pull/Push | Managed integrations |

### Migration Strategy
1. Identify event sources
2. Map to Eventarc providers
3. Update event consumers
4. Implement CloudEvents format
5. Create Eventarc triggers
6. Test and validate
7. Migrate gradually

## Summary
Google Cloud Eventarc provides a comprehensive event-driven architecture solution with flexible routing, filtering, and processing capabilities. Choose Standard edition for simple use cases and Advanced for complex enterprise requirements. Follow best practices for event design, error handling, and monitoring to build robust event-driven systems.