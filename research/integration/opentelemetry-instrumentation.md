# OpenTelemetry Instrumentation Guide

## Overview
OpenTelemetry instrumentation is the process of adding observability to applications by emitting traces, metrics, and logs. There are two primary approaches: manual (code-based) and automatic (zero-code).

## Instrumentation Approaches

### 1. Manual/Code-based Instrumentation
Direct integration of OpenTelemetry API/SDK into application code for maximum control.

#### Advantages
- Fine-grained control over telemetry
- Custom business metrics and traces
- Optimized performance
- Precise attribute selection

#### Disadvantages
- Requires code changes
- More development effort
- Maintenance overhead
- Risk of incomplete coverage

### 2. Automatic/Zero-code Instrumentation
Adds instrumentation without modifying source code using agents or libraries.

#### Advantages
- No code changes required
- Quick implementation
- Consistent instrumentation
- Covers common frameworks

#### Disadvantages
- Less control over telemetry
- Potential performance overhead
- May miss business-specific metrics
- Language/framework limitations

## Language-Specific Implementation

### Java
Most mature support with comprehensive automatic instrumentation.

#### Automatic Instrumentation
```bash
# Download agent
wget https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar

# Run with agent
java -javaagent:opentelemetry-javaagent.jar \
     -Dotel.service.name=my-service \
     -Dotel.exporter.otlp.endpoint=http://localhost:4317 \
     -jar myapp.jar
```

#### Manual Instrumentation
```java
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.context.Scope;

public class OrderService {
    private static final Tracer tracer = 
        GlobalOpenTelemetry.getTracer("order-service", "1.0");
    
    public Order processOrder(String orderId) {
        Span span = tracer.spanBuilder("processOrder")
            .setAttribute("order.id", orderId)
            .startSpan();
            
        try (Scope scope = span.makeCurrent()) {
            // Business logic
            Order order = fetchOrder(orderId);
            validateOrder(order);
            return order;
        } finally {
            span.end();
        }
    }
}
```

### Python
Strong support for both manual and automatic instrumentation.

#### Automatic Instrumentation
```bash
# Install instrumentation packages
pip install opentelemetry-distro[otlp]
pip install opentelemetry-instrumentation

# Auto-instrument and run
opentelemetry-instrument \
    --service_name my-service \
    --exporter_otlp_endpoint http://localhost:4317 \
    python app.py
```

#### Manual Instrumentation
```python
from opentelemetry import trace, metrics
from opentelemetry.trace import Status, StatusCode

tracer = trace.get_tracer(__name__)
meter = metrics.get_meter(__name__)

# Create metrics
request_counter = meter.create_counter(
    "requests",
    description="Number of requests",
    unit="1"
)
request_duration = meter.create_histogram(
    "request_duration",
    description="Request duration",
    unit="ms"
)

@tracer.start_as_current_span("process_order")
def process_order(order_id: str):
    span = trace.get_current_span()
    span.set_attribute("order.id", order_id)
    
    try:
        # Record metric
        request_counter.add(1, {"endpoint": "/orders"})
        
        # Business logic
        with tracer.start_as_current_span("validate_order"):
            validate(order_id)
            
        with tracer.start_as_current_span("save_order"):
            result = save_to_db(order_id)
            
        span.set_status(Status(StatusCode.OK))
        return result
        
    except Exception as e:
        span.set_status(Status(StatusCode.ERROR, str(e)))
        span.record_exception(e)
        raise
```

### JavaScript/Node.js
Supports both browser and server-side instrumentation.

#### Automatic Instrumentation (Node.js)
```bash
# Install packages
npm install @opentelemetry/api
npm install @opentelemetry/auto-instrumentations-node

# Run with instrumentation
node --require @opentelemetry/auto-instrumentations-node/register app.js
```

#### Manual Instrumentation
```javascript
const { trace, metrics, context } = require('@opentelemetry/api');

const tracer = trace.getTracer('order-service', '1.0.0');
const meter = metrics.getMeter('order-service', '1.0.0');

// Create metrics
const requestCounter = meter.createCounter('requests', {
  description: 'Count of requests'
});

const requestDuration = meter.createHistogram('request_duration', {
  description: 'Duration of requests',
  unit: 'ms'
});

async function processOrder(orderId) {
  const span = tracer.startSpan('processOrder');
  span.setAttribute('order.id', orderId);
  
  // Use context to ensure proper span propagation
  return context.with(trace.setSpan(context.active(), span), async () => {
    const startTime = Date.now();
    
    try {
      requestCounter.add(1, { endpoint: '/orders' });
      
      // Nested span
      const validationSpan = tracer.startSpan('validateOrder');
      const isValid = await validateOrder(orderId);
      validationSpan.end();
      
      if (!isValid) {
        throw new Error('Invalid order');
      }
      
      const result = await saveOrder(orderId);
      
      requestDuration.record(Date.now() - startTime, {
        status: 'success'
      });
      
      return result;
    } catch (error) {
      span.recordException(error);
      span.setStatus({
        code: trace.SpanStatusCode.ERROR,
        message: error.message
      });
      throw error;
    } finally {
      span.end();
    }
  });
}
```

### Go
Requires manual instrumentation but provides comprehensive SDK.

#### Manual Instrumentation
```go
package main

import (
    "context"
    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/attribute"
    "go.opentelemetry.io/otel/metric"
    "go.opentelemetry.io/otel/trace"
)

var (
    tracer = otel.Tracer("order-service")
    meter  = otel.Meter("order-service")
)

func init() {
    // Create metrics
    requestCounter, _ := meter.Int64Counter(
        "requests",
        metric.WithDescription("Number of requests"),
    )
    
    requestDuration, _ := meter.Float64Histogram(
        "request_duration",
        metric.WithDescription("Request duration in ms"),
        metric.WithUnit("ms"),
    )
}

func processOrder(ctx context.Context, orderID string) error {
    ctx, span := tracer.Start(ctx, "processOrder",
        trace.WithAttributes(attribute.String("order.id", orderID)))
    defer span.End()
    
    // Record metrics
    requestCounter.Add(ctx, 1,
        metric.WithAttributes(attribute.String("endpoint", "/orders")))
    
    // Nested span
    ctx, validationSpan := tracer.Start(ctx, "validateOrder")
    err := validateOrder(ctx, orderID)
    validationSpan.End()
    
    if err != nil {
        span.RecordError(err)
        span.SetStatus(codes.Error, "Order validation failed")
        return err
    }
    
    // Continue processing
    return nil
}
```

### .NET
Strong support for both automatic and manual instrumentation.

#### Automatic Instrumentation
```bash
# Install as NuGet package
dotnet add package OpenTelemetry.AutoInstrumentation

# Set environment variables
export OTEL_SERVICE_NAME=my-service
export OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317

# Run with instrumentation
dotnet run
```

#### Manual Instrumentation
```csharp
using OpenTelemetry;
using OpenTelemetry.Trace;
using OpenTelemetry.Metrics;
using System.Diagnostics;
using System.Diagnostics.Metrics;

public class OrderService
{
    private static readonly ActivitySource ActivitySource = 
        new("OrderService", "1.0.0");
    
    private static readonly Meter Meter = 
        new("OrderService", "1.0.0");
    
    private static readonly Counter<int> RequestCounter = 
        Meter.CreateCounter<int>("requests", description: "Number of requests");
    
    private static readonly Histogram<double> RequestDuration = 
        Meter.CreateHistogram<double>("request_duration", "ms", "Request duration");
    
    public async Task<Order> ProcessOrderAsync(string orderId)
    {
        using var activity = ActivitySource.StartActivity("ProcessOrder");
        activity?.SetTag("order.id", orderId);
        
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            RequestCounter.Add(1, new("endpoint", "/orders"));
            
            using (var validationActivity = ActivitySource.StartActivity("ValidateOrder"))
            {
                await ValidateOrderAsync(orderId);
            }
            
            var result = await SaveOrderAsync(orderId);
            
            RequestDuration.Record(stopwatch.ElapsedMilliseconds, 
                new("status", "success"));
            
            return result;
        }
        catch (Exception ex)
        {
            activity?.RecordException(ex);
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            
            RequestDuration.Record(stopwatch.ElapsedMilliseconds, 
                new("status", "error"));
            
            throw;
        }
    }
}
```

## Instrumentation Libraries

### Common Libraries Instrumented
Most automatic instrumentation covers:

#### Web Frameworks
- Spring Boot (Java)
- Flask/Django (Python)
- Express (Node.js)
- ASP.NET Core (.NET)
- Gin/Echo (Go)

#### HTTP Clients
- Apache HttpClient (Java)
- Requests/urllib3 (Python)
- Axios/fetch (JavaScript)
- net/http (Go)
- HttpClient (.NET)

#### Databases
- JDBC (Java)
- psycopg2/pymongo (Python)
- mongodb/postgres (Node.js)
- database/sql (Go)
- Entity Framework (.NET)

#### Message Queues
- Kafka
- RabbitMQ
- Redis
- AWS SQS
- Azure Service Bus

#### Cloud SDKs
- AWS SDK
- Google Cloud Client Libraries
- Azure SDK

## Best Practices

### 1. Start with Automatic Instrumentation
```yaml
# Example: Kubernetes OpenTelemetry Operator
apiVersion: opentelemetry.io/v1alpha1
kind: Instrumentation
metadata:
  name: my-instrumentation
spec:
  exporter:
    endpoint: http://otel-collector:4317
  propagators:
    - tracecontext
    - baggage
  sampler:
    type: parentbased_traceidratio
    argument: "0.1"  # Sample 10%
```

### 2. Add Custom Business Metrics
```python
# Track business KPIs
order_value = meter.create_histogram(
    "order.value",
    unit="USD",
    description="Order value in USD"
)

@tracer.start_as_current_span("checkout")
def checkout(cart):
    total = calculate_total(cart)
    order_value.record(total, {"currency": "USD"})
    # Process checkout
```

### 3. Use Semantic Conventions
```java
// Use standard attribute names
span.setAttribute(SemanticAttributes.HTTP_METHOD, "GET");
span.setAttribute(SemanticAttributes.HTTP_URL, "/api/orders");
span.setAttribute(SemanticAttributes.HTTP_STATUS_CODE, 200);
span.setAttribute(SemanticAttributes.USER_ID, userId);
```

### 4. Implement Proper Error Handling
```go
func processRequest(ctx context.Context) error {
    ctx, span := tracer.Start(ctx, "processRequest")
    defer span.End()
    
    if err := doWork(ctx); err != nil {
        span.RecordError(err)
        span.SetStatus(codes.Error, "Processing failed")
        
        // Add error details
        span.SetAttributes(
            attribute.String("error.type", fmt.Sprintf("%T", err)),
            attribute.String("error.message", err.Error()),
        )
        
        return err
    }
    
    span.SetStatus(codes.Ok, "Success")
    return nil
}
```

### 5. Optimize Performance
```javascript
// Batch span processing
const batchSpanProcessor = new BatchSpanProcessor(exporter, {
  maxQueueSize: 2048,
  maxExportBatchSize: 512,
  scheduledDelayMillis: 5000,
});

// Use sampling to reduce overhead
const sampler = new TraceIdRatioBasedSampler(0.1); // 10% sampling
```

## Common Patterns

### Service Entry Points
```python
# Instrument all entry points
@app.route('/api/orders/<order_id>')
@tracer.start_as_current_span("get_order_endpoint")
def get_order(order_id):
    span = trace.get_current_span()
    span.set_attributes({
        "http.method": "GET",
        "http.route": "/api/orders/{order_id}",
        "order.id": order_id
    })
    return process_order(order_id)
```

### Database Operations
```java
@WithSpan("database.query")
public User findUserById(String userId) {
    Span span = Span.current();
    span.setAttribute("db.system", "postgresql");
    span.setAttribute("db.operation", "SELECT");
    span.setAttribute("db.statement", "SELECT * FROM users WHERE id = ?");
    
    return jdbcTemplate.queryForObject(
        "SELECT * FROM users WHERE id = ?",
        new UserRowMapper(),
        userId
    );
}
```

### External API Calls
```go
func callExternalAPI(ctx context.Context, url string) (*Response, error) {
    ctx, span := tracer.Start(ctx, "external_api_call",
        trace.WithAttributes(
            attribute.String("http.url", url),
            attribute.String("http.method", "GET"),
        ))
    defer span.End()
    
    req, _ := http.NewRequestWithContext(ctx, "GET", url, nil)
    
    // Inject trace context
    otel.GetTextMapPropagator().Inject(ctx, 
        propagation.HeaderCarrier(req.Header))
    
    resp, err := http.DefaultClient.Do(req)
    if err != nil {
        span.RecordError(err)
        return nil, err
    }
    
    span.SetAttributes(
        attribute.Int("http.status_code", resp.StatusCode),
    )
    
    return resp, nil
}
```

## Summary
OpenTelemetry instrumentation provides flexible options for adding observability to applications. Start with automatic instrumentation for quick wins, then add manual instrumentation for business-specific insights. Follow semantic conventions and best practices to ensure consistent, valuable telemetry across your distributed system.