# API Versioning Strategies (Google AIP-185)

## Overview
All APIs must provide a major version number as part of their protobuf package and URI path. Only major versions are exposed to users (e.g., "v1", not "v1.0" or "v1.1").

## Core Principles

### Version Independence
- New major versions must NOT depend on previous versions
- Each version is self-contained
- Allows independent evolution and eventual deprecation

### Version Numbering
- Format: `v{major}` (e.g., v1, v2, v3)
- No minor or patch versions exposed in API
- Internal minor changes handled without version change

### Protobuf Package Structure
```protobuf
// Correct
package example.library.v1;

// Incorrect - no minor versions
package example.library.v1.2;
```

## Versioning Strategies

### 1. Channel-based Versioning (Recommended)

Most suitable for APIs with incremental feature development.

#### Channel Structure
```
v1alpha → v1beta → v1 (stable)
v2alpha → v2beta → v2 (stable)
```

#### Channel Rules
- **Maximum 3 channels**: alpha, beta, stable
- **Stability suffix**: Added to version except for stable
  - Alpha: `v1alpha`
  - Beta: `v1beta`
  - Stable: `v1` (no suffix)
- **Feature superset requirement**:
  - Beta must include all stable features
  - Alpha must include all beta features

#### API Structure Example
```
/v1/books          (stable)
/v1beta/books      (beta - includes v1 + new features)
/v1alpha/books     (alpha - includes v1beta + experimental)
```

#### Migration Pattern
```
Feature lifecycle:
v1alpha (experimental) → v1beta (preview) → v1 (GA)
```

### 2. Release-based Versioning

Best for APIs requiring multiple preview iterations.

#### Release Structure
```
v1alpha1 → v1alpha2 → ... → v1alphaN
v1beta1 → v1beta2 → ... → v1betaN
v1 (stable)
```

#### Release Rules
- Multiple alpha/beta releases allowed
- Sequential numbering (1, 2, 3...)
- Beta releases can receive backwards-compatible updates
- Each release is a snapshot

#### Example Evolution
```
v1alpha1: Initial API design
v1alpha2: Major redesign based on feedback
v1beta1: Stabilized design, minor changes only
v1beta2: Bug fixes and minor improvements
v1: General availability
```

### 3. Visibility-based Versioning

For fine-grained feature control within a version.

#### Visibility Labels
- `PREVIEW`: Early access features
- `INTERNAL`: Internal testing only
- `TRUSTED_TESTER`: Limited external testing
- `PUBLIC`: Generally available

#### Implementation
```protobuf
service Library {
  option (google.api.api_visibility) = {
    restriction: "PREVIEW"
  };
  
  rpc CreateBook(CreateBookRequest) returns (Book) {
    option (google.api.method_visibility) = {
      restriction: "TRUSTED_TESTER"
    };
  }
}
```

## Breaking vs Non-Breaking Changes

### Non-Breaking Changes (Allowed in same version)
- Adding new methods
- Adding new fields with sensible defaults
- Adding new enum values
- Adding optional request fields
- Making required fields optional

### Breaking Changes (Require new major version)
- Removing or renaming methods
- Removing or renaming fields
- Changing field types
- Changing method signatures
- Making optional fields required
- Changing behavior significantly

### Stability Level Guidelines

#### Stable APIs
- No breaking changes allowed
- Only backwards-compatible additions
- Long deprecation periods (6-12 months)

#### Beta APIs
- Breaking changes allowed with notice
- Shorter deprecation periods (3-6 months)
- Should converge toward stability

#### Alpha APIs
- Breaking changes without notice
- No compatibility guarantees
- Rapid iteration expected

## Version Lifecycle Management

### Version Timeline
```
Launch → Growth → Maturity → Deprecation → Sunset
  |         |         |           |          |
  v1alpha   v1beta    v1       v1 deprecated v1 removed
                      |
                      v2alpha launches
```

### Deprecation Process

#### 1. Announcement
```protobuf
service Library {
  option deprecated = true;
  option (google.api.deprecation_info) = {
    deprecation_date: "2024-12-31"
    replacement: "library.v2.Library"
  };
}
```

#### 2. Migration Period
- Maintain both versions
- Provide migration guides
- Support data migration tools

#### 3. Sunset
- Remove deprecated version
- Redirect remaining traffic
- Archive documentation

## Migration Strategies

### 1. Side-by-Side Operation
```
Client → Load Balancer → v1 Service (90% traffic)
                      ↘ v2 Service (10% traffic)
```

### 2. Gradual Migration
```python
# Client code supporting multiple versions
if api_version == "v2":
    client = library_v2.LibraryClient()
else:
    client = library_v1.LibraryClient()
```

### 3. Feature Flags
```python
def get_books(use_v2=False):
    if use_v2 and feature_flags.is_enabled("library_v2"):
        return library_v2_client.list_books()
    return library_v1_client.list_books()
```

## Best Practices

### Version Planning
1. **Start with v1beta** for new APIs
2. **Gather feedback** during beta period
3. **Stabilize API** before v1 release
4. **Plan for v2** early if major changes expected

### Documentation
```markdown
# Library API Versions

## Current Versions
- v2: Current stable version (recommended)
- v1: Previous version (deprecated, removal: 2025-01-01)

## Version History
- 2024-01: v2 released
- 2023-06: v2beta released
- 2023-01: v1 released
- 2022-06: v1beta released
```

### Client Libraries
```python
# Support multiple versions
from google.example import library_v1
from google.example import library_v2

# Version selection
def create_client(version="v2"):
    if version == "v2":
        return library_v2.LibraryClient()
    elif version == "v1":
        return library_v1.LibraryClient()
    else:
        raise ValueError(f"Unsupported version: {version}")
```

### URL Structure
```
# Stable versions
https://library.googleapis.com/v1/books
https://library.googleapis.com/v2/books

# Preview versions
https://library.googleapis.com/v3beta/books
https://library.googleapis.com/v3alpha/books
```

## Common Anti-Patterns

### ❌ Don't Do This
1. **Minor versions in URLs**: `/v1.2/books`
2. **Breaking changes without version**: Changing behavior in v1
3. **Too many versions**: v1 through v10 all active
4. **No migration path**: Removing v1 without warning
5. **Version in method names**: `GetBookV2()`

### ✅ Do This Instead
1. **Major versions only**: `/v1/books`, `/v2/books`
2. **New major version**: Breaking changes in v2
3. **Limited active versions**: Support 2-3 versions max
4. **Graceful deprecation**: 6-12 month migration period
5. **Version in URL only**: `GET /v2/books/{id}`

## Summary
Effective API versioning enables evolution while maintaining stability. Choose the appropriate strategy based on your API's needs, follow stability guarantees, and provide clear migration paths for users.