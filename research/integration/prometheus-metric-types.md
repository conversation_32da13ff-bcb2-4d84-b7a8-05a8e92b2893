# Prometheus Metric Types

## Overview
Prometheus supports four core metric types that form the foundation of its monitoring capabilities. Each type serves specific use cases and has distinct characteristics.

## 1. Counter

### Definition
A counter is a cumulative metric that represents a single monotonically increasing value that can only increase or be reset to zero on restart.

### Characteristics
- Always increases (never decreases)
- Resets to zero only on restart
- Ideal for tracking counts and rates
- Value itself is less meaningful than rate of change

### Use Cases
- Number of requests served
- Tasks completed
- Errors occurred
- Bytes transmitted
- Records processed

### Implementation Example
```go
// Go implementation
var (
    httpRequestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests processed",
        },
        []string{"method", "endpoint", "status"},
    )
    
    processedBytes = prometheus.NewCounter(
        prometheus.CounterOpts{
            Name: "processed_bytes_total",
            Help: "Total number of bytes processed",
        },
    )
)

// Usage
httpRequestsTotal.WithLabelValues("GET", "/api/users", "200").Inc()
processedBytes.Add(float64(len(data)))
```

```python
# Python implementation
from prometheus_client import Counter

http_requests_total = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

bytes_processed = Counter(
    'bytes_processed_total',
    'Total bytes processed'
)

# Usage
http_requests_total.labels(method='GET', endpoint='/api/users', status='200').inc()
bytes_processed.inc(len(data))
```

### Common PromQL Queries
```promql
# Rate of requests per second over 5 minutes
rate(http_requests_total[5m])

# Increase in errors over the last hour
increase(errors_total[1h])

# Percentage of failed requests
100 * sum(rate(http_requests_total{status=~"5.."}[5m])) 
/ sum(rate(http_requests_total[5m]))
```

### Anti-Patterns
```go
// ❌ Wrong: Using counter for values that can decrease
activeConnections := prometheus.NewCounter(...) // Should be Gauge

// ❌ Wrong: Setting specific values
requestsCounter.Set(100) // Counters can only increment

// ✅ Correct: Only increment
requestsCounter.Inc()
requestsCounter.Add(5)
```

## 2. Gauge

### Definition
A gauge represents a single numerical value that can arbitrarily go up and down.

### Characteristics
- Can increase or decrease
- Represents current state
- Absolute value is meaningful
- Suitable for snapshots of current conditions

### Use Cases
- Temperature readings
- Current memory usage
- Number of concurrent requests
- Queue sizes
- Number of active connections
- Disk space available

### Implementation Example
```go
// Go implementation
var (
    memoryUsageBytes = prometheus.NewGauge(
        prometheus.GaugeOpts{
            Name: "memory_usage_bytes",
            Help: "Current memory usage in bytes",
        },
    )
    
    activeConnections = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "active_connections",
            Help: "Number of active connections",
        },
        []string{"type"},
    )
)

// Usage
memoryUsageBytes.Set(float64(getCurrentMemory()))
activeConnections.WithLabelValues("websocket").Inc()
activeConnections.WithLabelValues("websocket").Dec()
```

```python
# Python implementation
from prometheus_client import Gauge

temperature = Gauge(
    'temperature_celsius',
    'Current temperature in Celsius',
    ['location']
)

queue_size = Gauge(
    'queue_size',
    'Number of items in queue'
)

# Usage
temperature.labels(location='server_room').set(23.5)
queue_size.inc()  # +1
queue_size.dec()  # -1
queue_size.set(10)  # Set to specific value
```

### Common PromQL Queries
```promql
# Current memory usage
memory_usage_bytes

# Average temperature over last hour
avg_over_time(temperature_celsius[1h])

# Max concurrent connections in last 5 minutes
max_over_time(active_connections[5m])

# Alert when queue is too large
queue_size > 1000
```

### Best Practices
```go
// Track both current and max values
var (
    currentConnections = prometheus.NewGauge(...)
    maxConnections = prometheus.NewGauge(...)
)

func handleConnection() {
    currentConnections.Inc()
    if current := currentConnections.Get(); current > maxConnections.Get() {
        maxConnections.Set(current)
    }
    defer currentConnections.Dec()
    // Handle connection
}
```

## 3. Histogram

### Definition
A histogram samples observations (usually things like request durations or response sizes) and counts them in configurable buckets. It also provides a sum of all observed values.

### Characteristics
- Cumulative buckets (each bucket includes all lower buckets)
- Provides `_bucket`, `_count`, and `_sum` series
- Server-side quantile calculation
- Good for aggregation across instances

### Use Cases
- Request latencies
- Response sizes
- Processing times
- Any distribution of values

### Implementation Example
```go
// Go implementation
var (
    httpDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "http_request_duration_seconds",
            Help:    "HTTP request latencies in seconds",
            Buckets: prometheus.DefBuckets, // 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10
        },
        []string{"method", "endpoint"},
    )
    
    responseSize = prometheus.NewHistogram(
        prometheus.HistogramOpts{
            Name:    "http_response_size_bytes",
            Help:    "HTTP response size in bytes",
            Buckets: prometheus.ExponentialBuckets(100, 10, 8), // 100, 1000, 10000, ...
        },
    )
)

// Usage
start := time.Now()
// Process request
duration := time.Since(start).Seconds()
httpDuration.WithLabelValues("GET", "/api/users").Observe(duration)
responseSize.Observe(float64(len(responseBody)))
```

```python
# Python implementation
from prometheus_client import Histogram

request_latency = Histogram(
    'request_latency_seconds',
    'Request latency in seconds',
    ['method', 'endpoint'],
    buckets=(0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0)
)

task_duration = Histogram(
    'task_duration_seconds',
    'Task processing time',
    buckets=[0.1, 0.5, 1.0, 5.0, 10.0, 30.0, 60.0]
)

# Usage with decorator
@request_latency.labels(method='GET', endpoint='/api/users').time()
def handle_request():
    # Process request
    pass

# Manual timing
with task_duration.time():
    # Process task
    pass
```

### Exposed Metrics
For a histogram `http_request_duration_seconds`, Prometheus exposes:
```
# Buckets
http_request_duration_seconds_bucket{le="0.005"} 24054
http_request_duration_seconds_bucket{le="0.01"} 33444
http_request_duration_seconds_bucket{le="0.025"} 100392
http_request_duration_seconds_bucket{le="+Inf"} 144320

# Total count
http_request_duration_seconds_count 144320

# Sum of all observations
http_request_duration_seconds_sum 53423.23
```

### Common PromQL Queries
```promql
# 95th percentile latency
histogram_quantile(0.95, 
  sum by (le) (
    rate(http_request_duration_seconds_bucket[5m])
  )
)

# Average request duration
rate(http_request_duration_seconds_sum[5m]) 
/ rate(http_request_duration_seconds_count[5m])

# Apdex score (target: 0.5s, tolerable: 2s)
(
  sum(rate(http_request_duration_seconds_bucket{le="0.5"}[5m])) +
  sum(rate(http_request_duration_seconds_bucket{le="2"}[5m]))
) / 2 / sum(rate(http_request_duration_seconds_count[5m]))
```

### Bucket Configuration Best Practices
```go
// Linear buckets: 0.1, 0.2, 0.3, ..., 1.0
prometheus.LinearBuckets(0.1, 0.1, 10)

// Exponential buckets: 0.001, 0.002, 0.004, ..., 1.024
prometheus.ExponentialBuckets(0.001, 2, 11)

// Custom buckets for SLO monitoring
httpDuration := prometheus.NewHistogram(
    prometheus.HistogramOpts{
        Name: "http_request_duration_seconds",
        Help: "HTTP request latencies",
        Buckets: []float64{
            0.05,  // 50ms  - excellent
            0.1,   // 100ms - good
            0.25,  // 250ms - acceptable
            0.5,   // 500ms - slow
            1.0,   // 1s    - very slow
            2.5,   // 2.5s  - timeout threshold
            5.0,   // 5s    - definitely timed out
        },
    },
)
```

## 4. Summary

### Definition
Similar to histogram, a summary samples observations and provides configurable quantiles over a sliding time window. Unlike histograms, summaries calculate quantiles client-side.

### Characteristics
- Client-side quantile calculation
- Provides `_count` and `_sum` series
- Configurable φ-quantiles
- Cannot be aggregated across instances
- More accurate quantiles than histograms

### Use Cases
- When you need accurate quantiles
- When you cannot define buckets upfront
- Single-instance deployments
- Not recommended for most use cases (prefer histograms)

### Implementation Example
```go
// Go implementation
var (
    requestDuration = prometheus.NewSummaryVec(
        prometheus.SummaryOpts{
            Name:       "http_request_duration_seconds",
            Help:       "HTTP request latencies",
            Objectives: map[float64]float64{
                0.5:  0.05,  // 50th percentile with 5% error
                0.9:  0.01,  // 90th percentile with 1% error
                0.99: 0.001, // 99th percentile with 0.1% error
            },
            MaxAge: 10 * time.Minute, // Sliding window
        },
        []string{"method", "endpoint"},
    )
)

// Usage
start := time.Now()
// Process request
duration := time.Since(start).Seconds()
requestDuration.WithLabelValues("GET", "/api/users").Observe(duration)
```

```python
# Python implementation
from prometheus_client import Summary

request_latency = Summary(
    'request_latency_seconds',
    'Request latency',
    ['method', 'endpoint']
)

processing_time = Summary(
    'processing_time_seconds',
    'Time spent processing',
    quantiles=[0.5, 0.9, 0.99]  # Default error margins
)

# Usage
@request_latency.labels(method='GET', endpoint='/api').time()
def handle_request():
    # Process request
    pass
```

### Exposed Metrics
For a summary `http_request_duration_seconds`, Prometheus exposes:
```
# Quantiles
http_request_duration_seconds{quantile="0.5"} 0.232
http_request_duration_seconds{quantile="0.9"} 0.821
http_request_duration_seconds{quantile="0.99"} 1.528

# Total count
http_request_duration_seconds_count 144320

# Sum of all observations
http_request_duration_seconds_sum 53423.23
```

### Common PromQL Queries
```promql
# Get 90th percentile (pre-calculated)
http_request_duration_seconds{quantile="0.9"}

# Average request duration
rate(http_request_duration_seconds_sum[5m]) 
/ rate(http_request_duration_seconds_count[5m])

# Cannot aggregate quantiles across instances!
# ❌ This gives incorrect results:
avg(http_request_duration_seconds{quantile="0.9"})
```

## Histogram vs Summary Comparison

| Feature | Histogram | Summary |
|---------|-----------|----------|
| Quantile calculation | Server-side | Client-side |
| Quantile accuracy | Approximate | Accurate (within error margin) |
| Aggregation | ✅ Supported | ❌ Not supported |
| Performance impact | Low | Higher (quantile calculation) |
| Configuration | Pre-defined buckets | Quantiles with error margins |
| Use case | Most scenarios | Single-instance, need accuracy |

### When to Use Each

#### Use Histogram when:
- You need to aggregate across multiple instances
- You can define reasonable buckets upfront
- You want to calculate Apdex scores
- You need server-side flexibility in quantile calculation
- This is the recommended default choice

#### Use Summary when:
- You need accurate quantiles for a single instance
- You cannot determine buckets beforehand
- Aggregation is not required
- Client-side CPU usage is acceptable

## Best Practices

### 1. Naming Conventions
```go
// Include unit in metric name
http_request_duration_seconds     // ✅ Good
http_request_duration            // ❌ Bad

// Use _total suffix for counters
http_requests_total              // ✅ Good
http_requests                    // ❌ Bad

// Be descriptive but concise
database_connection_pool_size    // ✅ Good
db_conn_pool                     // ❌ Too abbreviated
```

### 2. Label Usage
```go
// ✅ Good: Bounded cardinality
httpRequests.WithLabelValues("GET", "/api/users", "200")

// ❌ Bad: Unbounded cardinality
httpRequests.WithLabelValues("GET", "/api/users/"+userID, "200")

// ✅ Good: Use static labels for environment
httpRequests.WithLabelValues("production", "us-east-1")
```

### 3. Bucket Selection
```go
// For latencies: cover typical SLO boundaries
latencyBuckets := []float64{0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0}

// For sizes: use exponential growth
sizeBuckets := prometheus.ExponentialBuckets(100, 10, 8)

// For counts: linear progression
countBuckets := prometheus.LinearBuckets(0, 10, 20)
```

### 4. Initialization
```go
// Initialize all label combinations to avoid missing series
func init() {
    for _, method := range []string{"GET", "POST", "PUT", "DELETE"} {
        for _, status := range []string{"200", "400", "500"} {
            httpRequests.WithLabelValues(method, "/api/users", status).Add(0)
        }
    }
}
```

## Summary
Understanding Prometheus metric types is crucial for effective monitoring. Choose counters for rates, gauges for current states, histograms for distributions (preferred), and summaries only when accurate single-instance quantiles are required. Follow naming conventions and be mindful of cardinality to maintain a performant monitoring system.