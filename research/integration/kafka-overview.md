# Apache Kafka Overview

## Introduction
Apache Kafka is an event streaming platform designed to capture, store, process, and route event streams in real-time. It enables a continuous flow and interpretation of data so that the right information is at the right place, at the right time.

## Core Concepts

### Events
An event records that "something happened" in the world or business:
- Also called records or messages
- Contains a key, value, timestamp, and optional metadata headers
- Example: payment transaction, GPS location update, shipping order

### Topics
- Named collections of events (similar to folders in a filesystem)
- Multi-producer and multi-subscriber
- Events in topics can be read as often as needed
- Retention is configurable (time-based or size-based)
- Topics are partitioned for scalability

### Partitions
- Topics are spread over multiple "buckets" called partitions
- Events with the same key go to the same partition
- Guarantees ordering within a partition
- Enables parallel processing

### Producers
- Client applications that publish events to Kafka topics
- Can choose which partition to send to
- Support various delivery guarantees

### Consumers
- Applications that subscribe to topics and process events
- Can be part of consumer groups for scalability
- Track their position (offset) in each partition

### Brokers
- Kafka servers that store data and serve clients
- Form a Kafka cluster for high availability
- Use KRaft protocol (replacing <PERSON><PERSON><PERSON><PERSON>) for metadata management

## Architecture

### Distributed System
```
Producer → Kafka Cluster → Consumer
    ↓          ↓              ↓
  Events    Brokers      Process
           (Replicated)
```

### Topic Partitioning
```
Topic: user-activity
├── Partition 0: [E1, E4, E7, ...]
├── Partition 1: [E2, E5, E8, ...]
└── Partition 2: [E3, E6, E9, ...]
```

### Replication
- Each partition has multiple replicas across brokers
- One leader replica handles all reads/writes
- Follower replicas stay synchronized
- Automatic failover if leader fails

## Message Delivery Guarantees

### Producer Guarantees
1. **At most once** (acks=0)
   - Fire and forget
   - Possible message loss
   - Highest throughput

2. **At least once** (acks=1)
   - Leader acknowledgment
   - Possible duplicates on retry
   - Default setting

3. **Exactly once** (acks=all + idempotence)
   - All replicas acknowledge
   - No duplicates or loss
   - Highest latency

### Consumer Guarantees
1. **At most once**
   - Commit offset before processing
   - Message loss possible on failure

2. **At least once**
   - Commit offset after processing
   - Duplicates possible on failure
   - Most common pattern

3. **Exactly once**
   - Transactional processing
   - Atomic commits
   - Complex but guaranteed

## Producer Configuration

### Basic Producer
```java
Properties props = new Properties();
props.put("bootstrap.servers", "localhost:9092");
props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");

Producer<String, String> producer = new KafkaProducer<>(props);

ProducerRecord<String, String> record = new ProducerRecord<>(
    "user-events",           // topic
    "user123",              // key
    "{\"action\":\"login\"}" // value
);

producer.send(record);
producer.close();
```

### Advanced Producer Settings
```java
// Idempotent producer (exactly-once semantics)
props.put("enable.idempotence", "true");
props.put("acks", "all");
props.put("retries", Integer.MAX_VALUE);
props.put("max.in.flight.requests.per.connection", "5");

// Performance tuning
props.put("batch.size", "16384");
props.put("linger.ms", "10");
props.put("compression.type", "snappy");
props.put("buffer.memory", "33554432");

// Custom partitioner
props.put("partitioner.class", "com.example.CustomPartitioner");
```

### Async Producer with Callback
```java
producer.send(record, new Callback() {
    @Override
    public void onCompletion(RecordMetadata metadata, Exception exception) {
        if (exception != null) {
            logger.error("Failed to send message", exception);
        } else {
            logger.info("Message sent to partition {} with offset {}",
                metadata.partition(), metadata.offset());
        }
    }
});
```

## Consumer Configuration

### Basic Consumer
```java
Properties props = new Properties();
props.put("bootstrap.servers", "localhost:9092");
props.put("group.id", "my-consumer-group");
props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");

KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
consumer.subscribe(Arrays.asList("user-events"));

while (true) {
    ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(100));
    for (ConsumerRecord<String, String> record : records) {
        System.out.printf("offset = %d, key = %s, value = %s%n",
            record.offset(), record.key(), record.value());
    }
}
```

### Consumer Group Configuration
```java
// Manual offset management
props.put("enable.auto.commit", "false");

// Session timeout and heartbeat
props.put("session.timeout.ms", "30000");
props.put("heartbeat.interval.ms", "3000");

// Max records per poll
props.put("max.poll.records", "500");
props.put("max.poll.interval.ms", "300000");

// Starting position
props.put("auto.offset.reset", "earliest"); // or "latest"
```

### Manual Offset Commit
```java
try {
    while (true) {
        ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(100));
        for (ConsumerRecord<String, String> record : records) {
            processRecord(record);
        }
        // Commit after processing
        consumer.commitSync();
    }
} catch (CommitFailedException e) {
    logger.error("Commit failed", e);
} finally {
    consumer.close();
}
```

## Consumer Groups

### How Consumer Groups Work
```
Topic: orders (3 partitions)
├── Partition 0 → Consumer A (Group 1)
├── Partition 1 → Consumer B (Group 1)
└── Partition 2 → Consumer C (Group 1)

Same topic, different group:
├── Partition 0 → Consumer X (Group 2)
├── Partition 1 → Consumer X (Group 2)
└── Partition 2 → Consumer Y (Group 2)
```

### Rebalancing
- Automatic redistribution when consumers join/leave
- Configurable strategies:
  - Range (default)
  - RoundRobin
  - Sticky
  - CooperativeSticky

### Consumer Group Management
```bash
# List consumer groups
kafka-consumer-groups.sh --bootstrap-server localhost:9092 --list

# Describe group
kafka-consumer-groups.sh --bootstrap-server localhost:9092 \
  --describe --group my-consumer-group

# Reset offsets
kafka-consumer-groups.sh --bootstrap-server localhost:9092 \
  --group my-consumer-group --reset-offsets --to-earliest \
  --topic user-events --execute
```

## Kafka Streams

### Stream Processing Application
```java
Properties props = new Properties();
props.put(StreamsConfig.APPLICATION_ID_CONFIG, "user-activity-processor");
props.put(StreamsConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");

StreamsBuilder builder = new StreamsBuilder();

// Process stream
KStream<String, String> source = builder.stream("user-events");

source
    .filter((key, value) -> value.contains("login"))
    .mapValues(value -> value.toUpperCase())
    .to("user-logins");

// Aggregation
KTable<String, Long> loginCounts = source
    .groupByKey()
    .count(Materialized.as("login-counts-store"));

loginCounts.toStream().to("user-login-counts");

KafkaStreams streams = new KafkaStreams(builder.build(), props);
streams.start();
```

### Windowed Aggregations
```java
// Tumbling window
source
    .groupByKey()
    .windowedBy(TimeWindows.of(Duration.ofMinutes(5)))
    .count()
    .toStream()
    .map((key, value) -> new KeyValue<>(
        key.key() + "@" + key.window().start(),
        value
    ))
    .to("windowed-counts");

// Sliding window
source
    .groupByKey()
    .windowedBy(SlidingWindows.withTimeDifferenceAndGrace(
        Duration.ofMinutes(5),
        Duration.ofMinutes(1)
    ))
    .count();
```

## Kafka Connect

### Source Connector Configuration
```json
{
  "name": "jdbc-source",
  "config": {
    "connector.class": "io.confluent.connect.jdbc.JdbcSourceConnector",
    "tasks.max": "1",
    "connection.url": "*************************************",
    "connection.user": "user",
    "connection.password": "password",
    "table.whitelist": "users,orders",
    "mode": "incrementing",
    "incrementing.column.name": "id",
    "topic.prefix": "postgres-"
  }
}
```

### Sink Connector Configuration
```json
{
  "name": "elasticsearch-sink",
  "config": {
    "connector.class": "io.confluent.connect.elasticsearch.ElasticsearchSinkConnector",
    "tasks.max": "1",
    "topics": "user-events",
    "connection.url": "http://localhost:9200",
    "type.name": "_doc",
    "key.ignore": "true",
    "schema.ignore": "true"
  }
}
```

## Configuration Best Practices

### Broker Configuration
```properties
# Server basics
broker.id=1
listeners=PLAINTEXT://localhost:9092
log.dirs=/var/kafka-logs

# Replication
default.replication.factor=3
min.insync.replicas=2
unclean.leader.election.enable=false

# Log retention
log.retention.hours=168
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000

# Performance
num.network.threads=8
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600

# Compression
compression.type=producer

# Group coordination
offsets.topic.replication.factor=3
transaction.state.log.replication.factor=3
transaction.state.log.min.isr=2
```

### Topic Configuration
```bash
# Create topic with specific settings
kafka-topics.sh --create \
  --bootstrap-server localhost:9092 \
  --topic user-events \
  --partitions 6 \
  --replication-factor 3 \
  --config retention.ms=604800000 \
  --config compression.type=snappy \
  --config min.insync.replicas=2
```

### Performance Tuning
```properties
# Producer performance
batch.size=32768
linger.ms=20
compression.type=lz4
buffer.memory=67108864

# Consumer performance
fetch.min.bytes=1
fetch.max.wait.ms=500
max.partition.fetch.bytes=1048576
```

## Monitoring and Operations

### Key Metrics to Monitor
1. **Broker Metrics**
   - Under-replicated partitions
   - Leader election rate
   - Request handler idle ratio
   - Network request rate
   - Log flush latency

2. **Producer Metrics**
   - Record send rate
   - Record error rate
   - Request latency
   - Buffer utilization

3. **Consumer Metrics**
   - Lag (offset difference)
   - Fetch rate
   - Commit latency
   - Rebalance rate

### JMX Monitoring
```java
// Enable JMX
export JMX_PORT=9999
export KAFKA_JMX_OPTS="-Dcom.sun.management.jmxremote \
  -Dcom.sun.management.jmxremote.authenticate=false \
  -Dcom.sun.management.jmxremote.ssl=false"
```

### Health Checks
```bash
# Check cluster health
kafka-broker-api-versions.sh --bootstrap-server localhost:9092

# Verify topic health
kafka-topics.sh --describe --topic user-events \
  --bootstrap-server localhost:9092

# Check consumer lag
kafka-consumer-groups.sh --bootstrap-server localhost:9092 \
  --describe --group my-consumer-group
```

## Production Deployment

### Hardware Recommendations
- **CPU**: 24+ cores for production
- **Memory**: 32-64 GB RAM
- **Storage**: SSDs with high IOPS
- **Network**: 10 Gbps+ network

### Cluster Sizing
```
# Minimum production setup
- 3 brokers (allows 1 failure)
- 3 ZooKeeper nodes (or KRaft controllers)
- Replication factor 3
- Min in-sync replicas 2

# Scaling formula
Brokers = max(
  Total throughput / Broker throughput,
  Total storage / Broker storage,
  Partition count / Partitions per broker
)
```

### Security Configuration
```properties
# SSL/TLS
listeners=SSL://localhost:9093
ssl.keystore.location=/var/kafka/keystore.jks
ssl.keystore.password=password
ssl.key.password=password
ssl.truststore.location=/var/kafka/truststore.jks
ssl.truststore.password=password

# SASL
sasl.enabled.mechanisms=PLAIN,SCRAM-SHA-256
sasl.mechanism.inter.broker.protocol=PLAIN

# ACLs
authorizer.class.name=kafka.security.authorizer.AclAuthorizer
allow.everyone.if.no.acl.found=false
```

## Best Practices Summary

### Design Patterns
1. **Event Sourcing**: Store all changes as events
2. **CQRS**: Separate command and query models
3. **Saga Pattern**: Distributed transactions via events
4. **Outbox Pattern**: Reliable event publishing

### Anti-Patterns to Avoid
1. Using Kafka as a database
2. Unlimited retention periods
3. Too many partitions per topic
4. Synchronous request-response patterns
5. Ignoring consumer lag

### Operational Guidelines
1. Monitor consumer lag continuously
2. Set appropriate retention policies
3. Use compression for large messages
4. Plan for capacity growth
5. Regular backup of topic configurations
6. Test disaster recovery procedures

## Summary
Apache Kafka provides a robust, scalable platform for event streaming. Its distributed architecture, flexible APIs, and strong delivery guarantees make it suitable for mission-critical data pipelines. Success with Kafka requires understanding its core concepts, careful configuration, and adherence to operational best practices.