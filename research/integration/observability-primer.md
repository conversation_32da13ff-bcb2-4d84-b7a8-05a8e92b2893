# Observability Primer

## What is Observability?

Observability lets you understand a system from the outside by letting you ask questions about that system without knowing its inner workings. Unlike traditional monitoring, observability enables you to troubleshoot "unknown unknowns" - problems you didn't anticipate.

### Key Characteristics
- **External understanding**: Infer internal states from external outputs
- **Exploratory**: Ask arbitrary questions about system behavior
- **Data-driven**: Based on telemetry signals from instrumented code
- **Proactive**: Discover issues before they become incidents

## Three Pillars of Observability

### 1. Metrics
Aggregations of numeric data about infrastructure or application:
- **System metrics**: CPU, memory, disk, network
- **Application metrics**: Request rate, error rate, latency
- **Business metrics**: User signups, revenue, conversion rates

#### Metric Types
- **Counter**: Monotonically increasing value (e.g., total requests)
- **Gauge**: Value that can go up or down (e.g., current temperature)
- **Histogram**: Distribution of values (e.g., request durations)

#### Example Metrics
```
http_requests_total{method="GET", status="200"} 12423
http_request_duration_seconds{quantile="0.99"} 0.865
active_users{region="us-east"} 3421
```

### 2. Logs
Timestamped text records of discrete events:
- **Structured logs**: JSON or key-value format
- **Unstructured logs**: Plain text messages
- **Contextual logs**: Include trace/span IDs for correlation

#### Modern Log Format
```json
{
  "timestamp": "2024-01-15T10:23:45Z",
  "level": "ERROR",
  "message": "Failed to process payment",
  "trace_id": "1234567890abcdef",
  "span_id": "abcdef1234",
  "user_id": "user-123",
  "payment_id": "pay-456",
  "error": "insufficient_funds"
}
```

### 3. Traces
Records of request paths through distributed systems:
- **Trace**: Complete journey of a request
- **Span**: Single operation within a trace
- **Context**: Metadata propagated between services

## Distributed Tracing Concepts

### Trace Structure
```
Trace (trace_id: abc123)
├── Span A: Frontend (root span)
│   ├── Span B: API Gateway
│   │   ├── Span C: Auth Service
│   │   └── Span D: User Service
│   │       └── Span E: Database Query
│   └── Span F: Cache Lookup
```

### Span Components
1. **Operation name**: What the span represents
2. **Start/end time**: Duration of operation
3. **Status**: Success, error, or cancelled
4. **Attributes**: Key-value metadata
5. **Events**: Timestamped occurrences during span
6. **Links**: References to other spans

### Span Example
```json
{
  "trace_id": "1234567890abcdef",
  "span_id": "abcdef1234",
  "parent_span_id": "parentspan123",
  "operation_name": "GET /api/users/{id}",
  "start_time": "2024-01-15T10:23:45.123Z",
  "end_time": "2024-01-15T10:23:45.456Z",
  "status": "OK",
  "attributes": {
    "http.method": "GET",
    "http.url": "/api/users/123",
    "http.status_code": 200,
    "user.id": "123"
  },
  "events": [{
    "time": "2024-01-15T10:23:45.234Z",
    "name": "cache_hit",
    "attributes": {
      "cache.key": "user:123"
    }
  }]
}
```

## Context Propagation

### What is Context?
Context carries execution-scoped values across API boundaries and between processes. It includes:
- **Trace context**: Trace ID, span ID, trace flags
- **Baggage**: User-defined key-value pairs
- **Correlation context**: Links between telemetry signals

### Propagation Formats

#### W3C Trace Context (Recommended)
```
traceparent: 00-1234567890abcdef1234567890abcdef-abcdef1234567890-01
tracestate: vendor1=value1,vendor2=value2
```

#### B3 Propagation (Zipkin)
```
X-B3-TraceId: 1234567890abcdef
X-B3-SpanId: abcdef1234
X-B3-ParentSpanId: parentspan123
X-B3-Sampled: 1
```

### Implementation Example
```python
# Injecting context
def make_request(url):
    headers = {}
    # Inject trace context into headers
    propagator.inject(headers)
    return requests.get(url, headers=headers)

# Extracting context
def handle_request(request):
    # Extract trace context from incoming headers
    context = propagator.extract(request.headers)
    with tracer.start_as_current_span("handle_request", context=context):
        # Process request
        pass
```

## Sampling Strategies

### Why Sample?
- Reduce data volume and costs
- Minimize performance overhead
- Maintain statistical accuracy

### Sampling Types

#### 1. Head-based Sampling
Decision made at trace start:
```python
sampler = TraceIdRatioBased(0.1)  # Sample 10% of traces
```

**Pros**: Simple, predictable costs
**Cons**: May miss important traces

#### 2. Tail-based Sampling
Decision made after trace completion:
```yaml
processors:
  tail_sampling:
    policies:
      - name: error-traces
        type: status_code
        status_codes: [ERROR]
      - name: slow-traces
        type: latency
        latency: {threshold_ms: 1000}
      - name: probabilistic
        type: probabilistic
        probabilistic: {sampling_percentage: 10}
```

**Pros**: Keeps important traces
**Cons**: Requires buffering, complex

#### 3. Adaptive Sampling
Adjusts rate based on traffic:
```python
class AdaptiveSampler:
    def should_sample(self, trace_id):
        current_rate = get_request_rate()
        if current_rate > threshold:
            return random() < 0.01  # 1% when high traffic
        return random() < 0.10     # 10% normal traffic
```

### Sampling Best Practices
1. **Always sample errors**: 100% of error traces
2. **Sample slow requests**: Latency > threshold
3. **Preserve trace integrity**: All or nothing
4. **Use consistent decisions**: Same trace ID = same decision
5. **Document sampling rate**: Include in dashboards

## OpenTelemetry Implementation

### Instrumentation Levels

#### 1. Automatic Instrumentation
```bash
# Zero-code instrumentation
opentelemetry-instrument \
    --traces_exporter console \
    --metrics_exporter console \
    python app.py
```

#### 2. Manual Instrumentation
```python
from opentelemetry import trace, metrics

tracer = trace.get_tracer(__name__)
meter = metrics.get_meter(__name__)

request_counter = meter.create_counter(
    "requests", 
    description="Number of requests"
)

@tracer.start_as_current_span("process_order")
def process_order(order_id):
    span = trace.get_current_span()
    span.set_attribute("order.id", order_id)
    
    request_counter.add(1, {"endpoint": "/orders"})
    
    # Business logic
    return result
```

#### 3. Library Instrumentation
```python
# Automatic instrumentation for popular libraries
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.flask import FlaskInstrumentor

RequestsInstrumentor().instrument()
FlaskInstrumentor().instrument_app(app)
```

### Correlating Signals

#### Logs with Traces
```python
import logging
from opentelemetry import trace

# Configure log correlation
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - [trace_id=%(otelTraceID)s span_id=%(otelSpanID)s] - %(message)s'
)

def process_request():
    span = trace.get_current_span()
    logging.info("Processing request", extra={
        "otelTraceID": format(span.get_span_context().trace_id, "032x"),
        "otelSpanID": format(span.get_span_context().span_id, "016x")
    })
```

#### Metrics with Traces
```python
histogram = meter.create_histogram("request_duration")

with tracer.start_as_current_span("handle_request") as span:
    start_time = time.time()
    
    # Process request
    result = process()
    
    duration = time.time() - start_time
    histogram.record(duration, {
        "trace_id": format(span.get_span_context().trace_id, "032x"),
        "status": "success"
    })
```

## Benefits of Observability

### Operational Benefits
- **Faster debugging**: Pinpoint issues quickly
- **Proactive monitoring**: Detect anomalies early
- **Performance optimization**: Identify bottlenecks
- **Capacity planning**: Data-driven scaling decisions

### Business Benefits
- **Improved reliability**: Reduce MTTR
- **Better user experience**: Fix issues before users complain
- **Cost optimization**: Right-size infrastructure
- **Data-driven decisions**: Understand actual usage patterns

### Development Benefits
- **Faster development**: Understand system behavior
- **Better testing**: Trace test executions
- **Easier onboarding**: Visual system understanding
- **Continuous improvement**: Identify optimization opportunities

## Summary
Observability transforms how we understand and operate complex distributed systems. By instrumenting applications to emit traces, metrics, and logs, teams gain deep insights into system behavior, enabling faster problem resolution and better decision-making. OpenTelemetry provides the standardized framework to implement these observability practices effectively.