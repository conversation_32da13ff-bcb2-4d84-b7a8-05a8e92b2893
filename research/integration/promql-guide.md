# PromQL (Prometheus Query Language) Guide

## Overview
PromQL is a functional query language that allows you to select and aggregate time series data in real-time. It's designed to be powerful yet intuitive for exploring metrics and creating alerts.

## Data Types

### 1. Instant Vector
A set of time series containing a single sample for each time series, all sharing the same timestamp.
```promql
http_requests_total
# Returns current value for all series matching this metric
```

### 2. Range Vector
A set of time series containing a range of data points over time for each time series.
```promql
http_requests_total[5m]
# Returns all values from the last 5 minutes
```

### 3. Scalar
A simple numeric floating point value.
```promql
100
3.14159
```

### 4. String
A simple string value (currently unused in PromQL).
```promql
"hello world"
```

## Time Series Selectors

### Basic Selection
```promql
# Select all series for a metric
http_requests_total

# Select with exact label match
http_requests_total{method="GET"}

# Multiple label matches
http_requests_total{method="GET", status="200"}
```

### Label Matching Operators

#### Equality Matching
```promql
# Exact match
http_requests_total{environment="production"}

# Not equal
http_requests_total{environment!="development"}
```

#### Regex Matching
```promql
# Regex match (matches any 2xx status)
http_requests_total{status=~"2.."}

# Regex non-match (exclude 5xx errors)
http_requests_total{status!~"5.."}

# Match multiple values
http_requests_total{method=~"GET|POST"}
```

### Advanced Selectors
```promql
# Select all metrics (use with caution!)
{__name__=~".+"}

# Select metrics by pattern
{__name__=~"http_.*_total"}

# Complex label matching
http_requests_total{environment="production", method!="GET", status=~"2.."}
```

## Time Modifiers

### Offset Modifier
Query data from the past:
```promql
# Current value
http_requests_total

# Value 5 minutes ago
http_requests_total offset 5m

# Rate from 1 hour ago
rate(http_requests_total[5m] offset 1h)
```

### @ Modifier
Query at specific timestamp:
```promql
# Value at specific Unix timestamp
http_requests_total @ 1609459200

# Rate at specific time
rate(http_requests_total[5m] @ 1609459200)

# Combine with offset
http_requests_total @ 1609459200 offset 5m
```

## Operators

### Arithmetic Operators
```promql
# Basic math
prometheus_http_requests_total * 2
prometheus_http_requests_total / 100
prometheus_http_requests_total + 10
prometheus_http_requests_total - 5
prometheus_http_requests_total % 10
prometheus_http_requests_total ^ 2

# Between vectors
(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100
```

### Comparison Operators
```promql
# Filter by value
http_requests_total > 100
memory_usage_bytes <= 1000000000

# Boolean output (0 or 1)
http_requests_total > bool 100
```

### Logical Operators
```promql
# Vector intersection (AND)
up{job="api"} and up{env="prod"}

# Vector union (OR)
up{job="api"} or up{job="web"}

# Vector complement (UNLESS)
up{job="api"} unless up{env="test"}
```

## Functions

### Rate Functions
```promql
# Per-second rate (for counters)
rate(http_requests_total[5m])

# Per-second increase (like rate, but only for counters)
irate(http_requests_total[5m])

# Absolute increase over time window
increase(http_requests_total[1h])

# Derivative (for gauges)
deriv(temperature_celsius[1h])
```

### Aggregation Functions
```promql
# Sum across all series
sum(http_requests_total)

# Sum by label
sum by (method) (http_requests_total)

# Average
avg(node_cpu_seconds_total)

# Min/Max
min(node_filesystem_free_bytes)
max(http_request_duration_seconds)

# Count series
count(up)

# Standard deviation
stddev(http_request_duration_seconds)
```

### Time Functions
```promql
# Current timestamp
time()

# Days in month
day_of_month()
day_of_week()
days_in_month()

# Hour of day
hour()
```

### Math Functions
```promql
# Absolute value
abs(node_cpu_seconds_total - 100)

# Ceiling/Floor
ceil(memory_usage_ratio)
floor(cpu_usage_percent)

# Natural logarithm
ln(http_requests_total)

# Square root
sqrt(variance_value)

# Clamp values
clamp_min(cpu_usage_percent, 10)
clamp_max(cpu_usage_percent, 90)
```

### Histogram Functions
```promql
# Calculate quantile from histogram
histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))

# 90th percentile by endpoint
histogram_quantile(0.9, 
  sum by (endpoint, le) (
    rate(http_request_duration_seconds_bucket[5m])
  )
)
```

## Aggregation Operators

### Basic Aggregations
```promql
# Sum all series
sum(http_requests_total)

# Group by specific labels
sum by (method, status) (http_requests_total)

# Keep all labels except specified
sum without (instance) (http_requests_total)
```

### Statistical Aggregations
```promql
# Average response time by service
avg by (service) (http_request_duration_seconds)

# 95th percentile across instances
quantile by (service) (0.95, http_request_duration_seconds)

# Standard deviation of CPU usage
stddev(rate(node_cpu_seconds_total[5m]))
```

### Counting and Sorting
```promql
# Count unique label values
count by (status) (http_requests_total)

# Count total number of series
count(up)

# Top 5 endpoints by request rate
topk(5, sum by (endpoint) (rate(http_requests_total[5m])))

# Bottom 3 nodes by available memory
bottomk(3, node_memory_MemAvailable_bytes)
```

## Vector Matching

### One-to-One Matching
```promql
# Match on all labels
error_rate / total_rate

# Ignore specific labels
error_rate / ignoring(instance) total_rate

# Match only on specific labels
error_rate / on(method, endpoint) total_rate
```

### Many-to-One/One-to-Many
```promql
# Left side many-to-one
sum by (service) (rate(errors_total[5m])) 
  / ignoring(service) group_left 
    sum(rate(requests_total[5m]))

# With label inheritance
method:http_requests:rate5m 
  / ignoring(method) group_left(version) 
    group by (job) (build_version)
```

## Common Query Patterns

### Error Rate Calculation
```promql
# Error percentage
100 * sum(rate(http_requests_total{status=~"5.."}[5m])) 
  / sum(rate(http_requests_total[5m]))

# Error rate by endpoint
100 * sum by (endpoint) (rate(http_requests_total{status=~"5.."}[5m])) 
  / sum by (endpoint) (rate(http_requests_total[5m]))
```

### Resource Utilization
```promql
# CPU usage percentage
100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)

# Memory usage percentage
100 * (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes))

# Disk usage percentage
100 - (100 * node_filesystem_avail_bytes / node_filesystem_size_bytes)
```

### Latency Percentiles
```promql
# 95th percentile latency
histogram_quantile(0.95,
  sum by (le) (
    rate(http_request_duration_seconds_bucket[5m])
  )
)

# Multiple percentiles
histogram_quantile(0.5, ...) # median
histogram_quantile(0.95, ...) # 95th
histogram_quantile(0.99, ...) # 99th
```

### Availability/Uptime
```promql
# Service availability (percentage)
avg_over_time(up[1d]) * 100

# Uptime in hours
(time() - process_start_time_seconds) / 3600
```

### Predictions
```promql
# Predict disk full in 4 hours
predict_linear(node_filesystem_free_bytes[1h], 4 * 3600) < 0

# Days until disk full
node_filesystem_free_bytes / predict_linear(node_filesystem_free_bytes[1d], 0)
```

## Advanced Techniques

### Recording Rules
Pre-compute expensive queries:
```yaml
groups:
  - name: example
    interval: 30s
    rules:
    - record: instance:node_cpu:rate5m
      expr: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
    
    - record: service:error_rate:ratio
      expr: |
        sum by (service) (rate(errors_total[5m]))
        /
        sum by (service) (rate(requests_total[5m]))
```

### Subqueries
```promql
# Max rate over last hour's 5-minute rates
max_over_time(rate(http_requests_total[5m])[1h:])

# Average of 5-minute rates over last hour
avg_over_time(rate(http_requests_total[5m])[1h:5m])
```

### Label Manipulation
```promql
# Add labels
label_join(up, "foo", ",", "instance", "job")

# Replace labels
label_replace(up, "hostname", "$1", "instance", "(.*):.*")

# Remove labels
sum without (instance, job) (up)
```

### Time-based Comparisons
```promql
# Week-over-week comparison
rate(http_requests_total[5m]) 
/ rate(http_requests_total[5m] offset 1w)

# Day-over-day growth
(sum(http_requests_total) - sum(http_requests_total offset 1d)) 
/ sum(http_requests_total offset 1d) * 100
```

## Best Practices

### 1. Use Recording Rules
For frequently-used or computationally expensive queries:
```promql
# Instead of computing this every time
histogram_quantile(0.95, sum by (le, method) (rate(http_request_duration_seconds_bucket[5m])))

# Pre-compute as recording rule
method:http_request_duration:p95_5m
```

### 2. Avoid High Cardinality
```promql
# ❌ Bad: Can produce thousands of series
rate(http_requests_total[5m])

# ✅ Good: Aggregate to reduce series
sum by (method, status) (rate(http_requests_total[5m]))
```

### 3. Use Appropriate Time Windows
```promql
# For volatile metrics: shorter windows
rate(http_requests_total[1m])

# For stable metrics: longer windows
rate(http_requests_total[5m])

# Rule of thumb: window should be at least 4x scrape interval
```

### 4. Label Best Practices
```promql
# Be specific with label selectors
up{job="api-server", env="production"}

# Use regex carefully (can be slow)
up{job=~"api-.*"}  # Only when necessary
```

### 5. Handle Missing Data
```promql
# Use 'or' for default values
rate(custom_metric[5m]) or 0

# Check for missing series
up or absent(up)
```

## Common Pitfalls

### 1. Rate vs Increase
```promql
# ✅ rate() returns per-second values
rate(http_requests_total[5m])

# ✅ increase() returns absolute increase
increase(http_requests_total[5m])

# ❌ Don't use rate() on gauges
rate(memory_usage_bytes[5m])  # Meaningless!
```

### 2. Histogram Quantiles
```promql
# ❌ Wrong: Averaging percentiles
avg(http_request_duration{quantile="0.95"})

# ✅ Correct: Calculate from buckets
histogram_quantile(0.95, sum by (le) (rate(http_request_duration_bucket[5m])))
```

### 3. Counter Resets
```promql
# rate() and increase() handle counter resets automatically
rate(http_requests_total[5m])

# Direct subtraction doesn't handle resets
http_requests_total - http_requests_total offset 5m  # ❌ Incorrect
```

## Summary
PromQL provides a powerful and flexible way to query Prometheus metrics. Master the basics of selectors, operators, and functions, then build up to complex aggregations and time-based analysis. Use recording rules for efficiency and follow best practices to avoid common pitfalls.