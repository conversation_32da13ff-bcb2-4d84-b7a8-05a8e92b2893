# Saga Pattern for Distributed Transactions

## Definition
A saga is a sequence of local transactions that maintains data consistency across multiple microservices without using distributed ACID transactions. Each local transaction updates the database and publishes a message or event to trigger the next local transaction in the saga.

## Core Problem
In microservices architecture, each service has its own database, making it impossible to use traditional ACID transactions across services. The saga pattern solves this by coordinating a series of local transactions.

## Types of Sagas

### 1. Choreography-based Saga
- **How it works**: Services publish domain events that trigger local transactions in other services
- **Communication**: Event-driven, using publish/subscribe
- **Coordination**: Decentralized - each service knows what to do when receiving events
- **Example Flow**:
  ```
  Order Service -> publishes OrderCreated event
  Customer Service -> receives event, reserves credit, publishes CreditReserved
  Order Service -> receives event, approves order
  ```

### 2. Orchestration-based Saga
- **How it works**: Central orchestrator coordinates the saga
- **Communication**: Command/reply style
- **Coordination**: Centralized - orchestrator tells each service what to do
- **Example Flow**:
  ```
  Order Orchestrator -> sends ReserveCredit command to Customer Service
  Customer Service -> replies with credit reserved/rejected
  Order Orchestrator -> sends ApproveOrder or RejectOrder command
  ```

## Key Components

### Local Transactions
- Each service performs its own database transaction
- Must be designed to be reversible via compensating transactions

### Compensating Transactions
- Undo the effects of a local transaction
- Must be idempotent
- Example: If "Reserve Credit" fails, run "Release Credit" compensation

### Message/Event Publishing
- Must atomically update database and publish messages
- Use patterns like:
  - Transactional Outbox
  - Event Sourcing
  - Database triggers

## Implementation Patterns

### Choreography Implementation
```java
// Order Service
public void createOrder(Order order) {
    // Local transaction
    orderRepository.save(order);
    
    // Publish event
    eventPublisher.publish(new OrderCreatedEvent(order));
}

// Customer Service event handler
@EventHandler
public void handle(OrderCreatedEvent event) {
    // Local transaction
    boolean creditReserved = customerService.reserveCredit(event.getCustomerId(), event.getAmount());
    
    // Publish result
    if (creditReserved) {
        eventPublisher.publish(new CreditReservedEvent(event.getOrderId()));
    } else {
        eventPublisher.publish(new CreditReservationFailedEvent(event.getOrderId()));
    }
}
```

### Orchestration Implementation
```java
public class OrderSaga {
    public void createOrder(CreateOrderCommand command) {
        // Step 1: Create pending order
        Order order = orderService.createPendingOrder(command);
        
        // Step 2: Reserve credit
        ReserveCreditReply reply = customerService.reserveCredit(
            new ReserveCreditCommand(command.getCustomerId(), command.getAmount())
        );
        
        // Step 3: Handle result
        if (reply.isSuccess()) {
            orderService.approveOrder(order.getId());
        } else {
            orderService.rejectOrder(order.getId());
            // No compensation needed as order was in pending state
        }
    }
}
```

## Benefits
- **Data consistency**: Maintains eventual consistency across services
- **No distributed locks**: Avoids distributed transaction complexity
- **Service autonomy**: Each service manages its own data
- **Flexibility**: Can handle complex business workflows

## Drawbacks
- **Complexity**: More complex than ACID transactions
- **No isolation**: Other transactions can see intermediate states
- **Debugging difficulty**: Hard to trace distributed flows
- **Compensation design**: Requires careful design of rollback logic
- **Eventual consistency**: Not suitable for strong consistency requirements

## Best Practices

1. **Design for idempotency**: Ensure operations can be safely retried
2. **Use correlation IDs**: Track saga instances across services
3. **Implement timeouts**: Handle stuck sagas
4. **Monitor saga completion**: Track success/failure rates
5. **Test compensation logic**: Ensure rollbacks work correctly
6. **Consider saga state machines**: Use frameworks like Eventuate or Axon

## When to Use
- Cross-service business transactions
- Long-running business processes
- When eventual consistency is acceptable
- When services need to remain loosely coupled

## When NOT to Use
- Strong consistency requirements
- Simple transactions within single service
- High-frequency, low-latency operations

## Related Patterns
- **Event Sourcing**: Store events as source of truth
- **CQRS**: Separate read and write models
- **Transactional Outbox**: Reliable event publishing
- **API Composition**: Query data from multiple services