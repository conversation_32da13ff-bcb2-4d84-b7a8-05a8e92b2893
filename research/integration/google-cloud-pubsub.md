# Google Cloud Pub/Sub

## Overview
Pub/Sub is an asynchronous and scalable messaging service that decouples services producing messages from services processing those messages. It enables reliable, many-to-many, asynchronous messaging between applications.

## Core Concepts

### Topics
- Named resources to which messages are sent
- Act as message channels or feeds
- Support multiple publishers and subscribers
- Regional or multi-regional resources

### Subscriptions
- Named resources representing message streams
- Each subscription belongs to a single topic
- Multiple subscriptions can exist for one topic
- Define how messages are delivered to subscribers

### Messages
- Data payload plus attributes
- Maximum size: 10 MB
- Automatically assigned unique message ID
- Include publish timestamp

### Publishers
- Applications that create and send messages to topics
- Can be any application with proper credentials
- Support batch publishing for efficiency

### Subscribers
- Applications that receive messages from subscriptions
- Two delivery mechanisms: Pull and Push
- Process messages asynchronously

## Message Flow Architecture

```
Publishers → Topic → Subscriptions → Subscribers
     ↓         ↓           ↓              ↓
  App A    Channel    Message Stream   App X
  App B               Message Stream   App Y
  App C               Message Stream   App Z
```

### Detailed Flow
1. **Publishing**: Publisher sends message to topic
2. **Fan-out**: Pub/Sub forwards to all subscriptions
3. **Delivery**: Messages delivered via push or pull
4. **Processing**: Subscriber processes message
5. **Acknowledgment**: Subscriber confirms completion
6. **Cleanup**: Pub/Sub removes acknowledged messages

## Publishing Patterns

### Basic Publishing
```python
from google.cloud import pubsub_v1

publisher = pubsub_v1.PublisherClient()
topic_path = publisher.topic_path(project_id, topic_id)

# Simple publish
future = publisher.publish(
    topic_path,
    b"Hello, World!",
    origin="python-sample",
    username="gcp"
)
print(f"Published message ID: {future.result()}")
```

### Batch Publishing
```python
# Automatic batching (recommended)
publisher = pubsub_v1.PublisherClient(
    batch_settings=pubsub_v1.types.BatchSettings(
        max_messages=100,  # Max messages per batch
        max_bytes=1024 * 1024,  # Max 1 MB per batch
        max_latency=0.01,  # 10ms max delay
    )
)

# Publish multiple messages
futures = []
for i in range(1000):
    future = publisher.publish(
        topic_path,
        f"Message {i}".encode("utf-8")
    )
    futures.append(future)

# Wait for all publishes
for future in futures:
    future.result()
```

### Publishing with Attributes
```python
# Message with metadata
future = publisher.publish(
    topic_path,
    b'{"orderId": "12345", "amount": 99.99}',
    eventType="order.created",
    priority="high",
    timestamp=str(int(time.time()))
)
```

### Error Handling
```python
from google.api_core import retry

# Publish with retry
@retry.Retry(
    initial=0.1,
    maximum=60.0,
    multiplier=1.3,
    deadline=300.0
)
def publish_with_retry(publisher, topic_path, data):
    future = publisher.publish(topic_path, data)
    return future.result()
```

## Subscription Patterns

### Pull Subscription
```python
from google.cloud import pubsub_v1

subscriber = pubsub_v1.SubscriberClient()
subscription_path = subscriber.subscription_path(
    project_id, subscription_id
)

def callback(message):
    print(f"Received: {message.data.decode('utf-8')}")
    print(f"Attributes: {message.attributes}")
    
    # Process message
    try:
        process_message(message)
        message.ack()  # Acknowledge success
    except Exception as e:
        print(f"Error: {e}")
        message.nack()  # Negative acknowledgment

# Start pulling
flow_control = pubsub_v1.types.FlowControl(max_messages=100)
streaming_pull_future = subscriber.subscribe(
    subscription_path,
    callback=callback,
    flow_control=flow_control
)

# Keep running
with subscriber:
    try:
        streaming_pull_future.result()
    except KeyboardInterrupt:
        streaming_pull_future.cancel()
```

### Synchronous Pull
```python
# Pull specific number of messages
response = subscriber.pull(
    request={
        "subscription": subscription_path,
        "max_messages": 10,
        "return_immediately": True,
    }
)

# Process messages
ack_ids = []
for message in response.received_messages:
    print(f"Received: {message.message.data}")
    ack_ids.append(message.ack_id)

# Acknowledge in batch
if ack_ids:
    subscriber.acknowledge(
        request={
            "subscription": subscription_path,
            "ack_ids": ack_ids,
        }
    )
```

### Push Subscription
```python
# Configure push endpoint (during subscription creation)
from google.cloud import pubsub_v1

publisher = pubsub_v1.PublisherClient()
subscriber = pubsub_v1.SubscriberClient()

topic_path = publisher.topic_path(project_id, topic_id)
subscription_path = subscriber.subscription_path(
    project_id, subscription_id
)

push_config = pubsub_v1.types.PushConfig(
    push_endpoint="https://myapp.com/pubsub/push",
    oidc_token=pubsub_v1.types.PushConfig.OidcToken(
        service_account_email="<EMAIL>"
    )
)

subscription = subscriber.create_subscription(
    request={
        "name": subscription_path,
        "topic": topic_path,
        "push_config": push_config,
    }
)
```

### Push Endpoint Handler
```python
from flask import Flask, request, jsonify
import base64
import json

app = Flask(__name__)

@app.route('/pubsub/push', methods=['POST'])
def pubsub_push():
    envelope = request.get_json()
    if not envelope:
        return 'Bad Request', 400
    
    # Parse Pub/Sub message
    pubsub_message = envelope['message']
    data = base64.b64decode(pubsub_message['data']).decode('utf-8')
    attributes = pubsub_message.get('attributes', {})
    
    # Process message
    try:
        process_message(data, attributes)
        return '', 204  # Success
    except Exception as e:
        print(f"Error processing: {e}")
        return 'Internal Server Error', 500  # Retry
```

## Delivery Guarantees

### At-Least-Once Delivery
- Default guarantee
- Messages delivered one or more times
- Requires idempotent message processing

### Exactly-Once Processing
```python
# Enable exactly-once delivery on subscription
subscriber.update_subscription(
    request={
        "subscription": {
            "name": subscription_path,
            "enable_exactly_once_delivery": True,
        },
        "update_mask": {"paths": ["enable_exactly_once_delivery"]},
    }
)

# Handle with exactly-once semantics
def callback(message):
    # Check if already processed
    if not is_duplicate(message.message_id):
        process_message(message)
        record_processed(message.message_id)
    
    message.ack()  # Always acknowledge
```

### Message Ordering
```python
# Enable message ordering on subscription
subscription = subscriber.create_subscription(
    request={
        "name": subscription_path,
        "topic": topic_path,
        "enable_message_ordering": True,
    }
)

# Publish with ordering key
future = publisher.publish(
    topic_path,
    b"Ordered message",
    ordering_key="user-123"  # Messages with same key delivered in order
)
```

## Message Filtering

### Subscription Filters
```python
# Create subscription with filter
filter_expression = 'attributes.eventType = "order.created" AND attributes.priority = "high"'

subscription = subscriber.create_subscription(
    request={
        "name": subscription_path,
        "topic": topic_path,
        "filter": filter_expression,
    }
)
```

### Filter Syntax Examples
```sql
-- Attribute filters
attributes.department = "sales"
attributes.urgent = "true"
attributes.customerId = "12345"

-- Numeric comparisons
attributes.price > "100"
attributes.quantity >= "10"

-- String operations
hasPrefix(attributes.region, "us-")
attributes:eventType  -- Check existence

-- Boolean operations
attributes.department = "sales" AND attributes.urgent = "true"
attributes.priority = "high" OR attributes.amount > "1000"
NOT attributes.test = "true"
```

## Dead Letter Topics

### Configuration
```python
# Create dead letter policy
dead_letter_policy = pubsub_v1.types.DeadLetterPolicy(
    dead_letter_topic=dead_letter_topic_path,
    max_delivery_attempts=5
)

subscription = subscriber.create_subscription(
    request={
        "name": subscription_path,
        "topic": topic_path,
        "dead_letter_policy": dead_letter_policy,
    }
)
```

### Monitoring Dead Letters
```python
# Subscribe to dead letter topic
def dead_letter_callback(message):
    print(f"Dead letter received: {message.data}")
    print(f"Delivery attempts: {message.attributes.get('CloudPubSubDeadLetterSourceDeliveryCount')}")
    print(f"Original subscription: {message.attributes.get('CloudPubSubDeadLetterSourceSubscription')}")
    
    # Log or alert
    log_dead_letter(message)
    message.ack()

dead_letter_future = subscriber.subscribe(
    dead_letter_subscription_path,
    callback=dead_letter_callback
)
```

## Flow Control

### Subscriber Flow Control
```python
# Configure flow control
flow_control = pubsub_v1.types.FlowControl(
    max_messages=1000,  # Max outstanding messages
    max_bytes=100 * 1024 * 1024,  # Max 100 MB
    max_lease_duration=3600,  # 1 hour max
    max_duration_per_lease_extension=600,  # 10 min extensions
)

streaming_pull_future = subscriber.subscribe(
    subscription_path,
    callback=callback,
    flow_control=flow_control
)
```

### Publisher Flow Control
```python
# Limit publisher memory usage
publisher = pubsub_v1.PublisherClient(
    publisher_options=pubsub_v1.types.PublisherOptions(
        flow_control=pubsub_v1.types.PublishFlowControl(
            message_limit=1000,
            byte_limit=10 * 1024 * 1024,  # 10 MB
            limit_exceeded_behavior=pubsub_v1.types.LimitExceededBehavior.BLOCK,
        ),
    ),
)
```

## Monitoring and Observability

### Key Metrics
```python
# Using Cloud Monitoring API
from google.cloud import monitoring_v3

client = monitoring_v3.MetricServiceClient()
project_name = f"projects/{project_id}"

# Query subscription metrics
interval = monitoring_v3.TimeInterval(
    {
        "end_time": {"seconds": int(time.time())},
        "start_time": {"seconds": int(time.time() - 3600)},
    }
)

results = client.list_time_series(
    request={
        "name": project_name,
        "filter": 'metric.type="pubsub.googleapis.com/subscription/num_undelivered_messages"',
        "interval": interval,
    }
)
```

### Important Metrics
- `subscription/num_undelivered_messages` - Message backlog
- `subscription/oldest_unacked_message_age` - Processing lag
- `subscription/push_request_latencies` - Push endpoint latency
- `topic/send_message_operation_count` - Publish rate
- `subscription/pull_message_operation_count` - Pull rate

### Logging
```python
import logging
from google.cloud import logging as cloud_logging

# Setup structured logging
cloud_logging.Client().setup_logging()

def callback(message):
    logging.info(
        "Processing message",
        extra={
            "message_id": message.message_id,
            "publish_time": message.publish_time.isoformat(),
            "attributes": dict(message.attributes),
            "subscription": subscription_path,
        }
    )
```

## Best Practices

### 1. Message Design
- Keep messages small (< 1 MB ideal)
- Use attributes for filtering metadata
- Include idempotency keys
- Consider message versioning

### 2. Publisher Best Practices
- Use batching for throughput
- Implement retry logic
- Monitor publish errors
- Use flow control for large volumes

### 3. Subscriber Best Practices
- Process messages idempotently
- Set appropriate ack deadlines
- Use flow control to prevent overload
- Monitor processing lag

### 4. Error Handling
```python
def robust_callback(message):
    max_retries = 3
    retry_count = int(message.attributes.get('retry_count', 0))
    
    try:
        process_message(message)
        message.ack()
    except RetriableError as e:
        if retry_count < max_retries:
            # Republish with retry count
            publisher.publish(
                topic_path,
                message.data,
                retry_count=str(retry_count + 1),
                **message.attributes
            )
            message.ack()  # Remove from current subscription
        else:
            message.nack()  # Send to dead letter
    except NonRetriableError:
        message.ack()  # Don't retry
        log_error(message)
```

### 5. Performance Optimization
- Use regional topics when possible
- Configure appropriate batch settings
- Implement connection pooling
- Use concurrent message processing

### 6. Security
- Use service accounts with minimal permissions
- Enable audit logging
- Encrypt sensitive message data
- Use VPC Service Controls for network security

## High-Throughput Patterns

### Parallel Processing
```python
from concurrent.futures import ThreadPoolExecutor
import threading

executor = ThreadPoolExecutor(max_workers=10)
lock = threading.Lock()

def callback(message):
    # Submit to thread pool
    future = executor.submit(process_message_async, message)
    future.add_done_callback(
        lambda f: message.ack() if f.result() else message.nack()
    )

def process_message_async(message):
    try:
        # Process message
        return True
    except Exception:
        return False
```

### Sharding Strategy
```python
# Publish to multiple topics for parallelism
num_shards = 10
shard = hash(message_key) % num_shards
topic_path = publisher.topic_path(project_id, f"topic-shard-{shard}")

publisher.publish(topic_path, message_data)
```

### Batch Processing
```python
from collections import deque
import threading
import time

class BatchProcessor:
    def __init__(self, batch_size=100, max_latency=1.0):
        self.batch = deque()
        self.batch_size = batch_size
        self.max_latency = max_latency
        self.lock = threading.Lock()
        self.timer = None
        
    def add_message(self, message):
        with self.lock:
            self.batch.append(message)
            if len(self.batch) >= self.batch_size:
                self.process_batch()
            elif not self.timer:
                self.timer = threading.Timer(
                    self.max_latency,
                    self.process_batch
                )
                self.timer.start()
    
    def process_batch(self):
        with self.lock:
            if self.timer:
                self.timer.cancel()
                self.timer = None
            
            if self.batch:
                messages = list(self.batch)
                self.batch.clear()
                
                # Process all messages together
                try:
                    batch_process(messages)
                    for msg in messages:
                        msg.ack()
                except Exception:
                    for msg in messages:
                        msg.nack()
```

## Common Use Cases

### 1. Event-Driven Architecture
- Microservice communication
- Event sourcing
- CQRS implementation
- Webhook distribution

### 2. Data Pipeline
- Stream processing with Dataflow
- Real-time ETL
- Change data capture
- Log aggregation

### 3. Task Distribution
- Work queues
- Batch job triggering
- Parallel processing
- Load balancing

### 4. Real-time Systems
- IoT data ingestion
- Live notifications
- Chat systems
- Gaming events

## Summary
Google Cloud Pub/Sub provides a robust, scalable messaging infrastructure for building event-driven architectures. With features like exactly-once delivery, message ordering, and dead letter queues, it supports both simple and complex messaging patterns. Follow best practices for message design, error handling, and monitoring to build reliable distributed systems.