# Prometheus Instrumentation Best Practices

## Core Principles

### Instrument Everything
"Every library, subsystem and service should have at least a few metrics to give you insight into how it is performing." - Prometheus documentation

- Make metrics an integral part of your code
- Instrument early in development, not as an afterthought
- Include metrics in code reviews
- Instantiate metrics in the same file where they're used

## Service Instrumentation Patterns

### 1. Online-Serving Systems (APIs, Web Services)

#### Essential Metrics
```go
var (
    // RED Method: Rate, Errors, Duration
    requestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
    
    requestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "http_request_duration_seconds",
            Help:    "HTTP request latencies",
            Buckets: []float64{.005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10},
        },
        []string{"method", "endpoint"},
    )
    
    requestsInFlight = prometheus.NewGauge(
        prometheus.GaugeOpts{
            Name: "http_requests_in_flight",
            Help: "Number of HTTP requests currently being processed",
        },
    )
)
```

#### Implementation Pattern
```go
func instrumentHandler(pattern string, handler http.HandlerFunc) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        start := time.Now()
        requestsInFlight.Inc()
        defer requestsInFlight.Dec()
        
        wrapped := &responseWriter{ResponseWriter: w, statusCode: http.StatusOK}
        handler(wrapped, r)
        
        duration := time.Since(start).Seconds()
        requestsTotal.WithLabelValues(r.Method, pattern, strconv.Itoa(wrapped.statusCode)).Inc()
        requestDuration.WithLabelValues(r.Method, pattern).Observe(duration)
    }
}
```

#### Client-Side Instrumentation
```python
import time
from prometheus_client import Counter, Histogram

external_request_total = Counter(
    'external_requests_total',
    'Total external API requests',
    ['service', 'method', 'status']
)

external_request_duration = Histogram(
    'external_request_duration_seconds',
    'External API request duration',
    ['service', 'method']
)

def make_api_call(service, method, url):
    start_time = time.time()
    try:
        response = requests.get(url)
        status = str(response.status_code)
        return response
    except Exception as e:
        status = 'error'
        raise
    finally:
        duration = time.time() - start_time
        external_request_total.labels(service, method, status).inc()
        external_request_duration.labels(service, method).observe(duration)
```

### 2. Offline Processing Systems

#### Queue/Pipeline Metrics
```go
var (
    queueSize = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "processing_queue_size",
            Help: "Number of items in processing queue",
        },
        []string{"queue", "priority"},
    )
    
    itemsProcessed = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "items_processed_total",
            Help: "Total number of items processed",
        },
        []string{"type", "status"},
    )
    
    processingDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "item_processing_duration_seconds",
            Help:    "Time taken to process each item",
            Buckets: prometheus.ExponentialBuckets(0.1, 2, 10),
        },
        []string{"type"},
    )
    
    lastProcessedTimestamp = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "last_processed_timestamp_seconds",
            Help: "Unix timestamp of last successfully processed item",
        },
        []string{"type"},
    )
)
```

#### Heartbeat Pattern
```python
import time
from prometheus_client import Gauge

heartbeat = Gauge(
    'pipeline_heartbeat_timestamp_seconds',
    'Last heartbeat timestamp',
    ['pipeline', 'stage']
)

def processing_loop():
    while True:
        # Update heartbeat
        heartbeat.labels(pipeline='data_processing', stage='extract').set_to_current_time()
        
        # Process data
        process_batch()
        
        # Update completion heartbeat
        heartbeat.labels(pipeline='data_processing', stage='complete').set_to_current_time()
        
        time.sleep(60)
```

### 3. Batch Jobs

#### Job Metrics Pattern
```go
var (
    jobLastSuccess = prometheus.NewGauge(
        prometheus.GaugeOpts{
            Name: "batch_job_last_success_timestamp_seconds",
            Help: "Unix timestamp of last successful job completion",
        },
    )
    
    jobDuration = prometheus.NewHistogram(
        prometheus.HistogramOpts{
            Name:    "batch_job_duration_seconds",
            Help:    "Duration of batch job execution",
            Buckets: prometheus.ExponentialBuckets(10, 2, 10),
        },
    )
    
    jobRecordsProcessed = prometheus.NewCounter(
        prometheus.CounterOpts{
            Name: "batch_job_records_processed_total",
            Help: "Total records processed by batch job",
        },
    )
)

func runBatchJob() {
    start := time.Now()
    defer func() {
        duration := time.Since(start).Seconds()
        jobDuration.Observe(duration)
    }()
    
    records := processRecords()
    jobRecordsProcessed.Add(float64(records))
    jobLastSuccess.SetToCurrentTime()
    
    // Push to gateway for short-lived jobs
    pusher := push.New(pushgatewayURL, "batch_job").
        Collector(jobLastSuccess).
        Collector(jobDuration).
        Collector(jobRecordsProcessed)
    
    if err := pusher.Push(); err != nil {
        log.Printf("Failed to push metrics: %v", err)
    }
}
```

## Naming Conventions

### Metric Names
```
# Format: library_subsystem_metric_unit
prometheus_http_requests_total
process_cpu_seconds_total
http_request_duration_seconds
node_memory_usage_bytes
```

### Rules:
1. Use snake_case
2. Include unit suffix (`_seconds`, `_bytes`, `_total`)
3. Use `_total` suffix for counters
4. Be descriptive but concise
5. Use standard prefixes for common patterns

### Common Patterns
```
# Counters
*_total           # http_requests_total
*_created         # process_start_time_created
*_failures_total  # job_failures_total

# Gauges
*_size           # queue_size
*_bytes          # memory_usage_bytes
*_ratio          # cpu_usage_ratio
*_timestamp      # last_success_timestamp

# Histograms/Summaries
*_duration_seconds    # request_duration_seconds
*_size_bytes         # response_size_bytes
*_latency_seconds    # db_query_latency_seconds
```

## Label Usage Best Practices

### Good Label Usage
```go
// ✅ Bounded, meaningful labels
httpRequests.WithLabelValues("GET", "/api/users", "200").Inc()

// ✅ Consolidate related metrics
databaseConnections.WithLabelValues("mysql", "primary", "active").Set(10)
databaseConnections.WithLabelValues("mysql", "replica", "active").Set(5)

// ✅ Use labels for aggregation
cpuUsage.WithLabelValues("user").Set(0.75)
cpuUsage.WithLabelValues("system").Set(0.15)
cpuUsage.WithLabelValues("idle").Set(0.10)
```

### Bad Label Usage
```go
// ❌ Unbounded cardinality
httpRequests.WithLabelValues(userID, sessionID, requestID).Inc()

// ❌ Should be separate metrics
systemMetric.WithLabelValues("cpu_usage", "0.75").Set(1)
systemMetric.WithLabelValues("memory_usage", "1024").Set(1)

// ❌ Too many labels
metric.WithLabelValues(a, b, c, d, e, f, g, h, i, j).Inc()
```

### Label Guidelines
1. Keep cardinality under control (< 10 label combinations ideal)
2. Labels should partition data, not store different types
3. Use static labels for dimensions that don't change
4. Avoid labels with high variance (IDs, timestamps)
5. Initialize known label combinations at startup

## Avoiding High Cardinality

### Common Cardinality Mistakes
```go
// ❌ User IDs - potentially millions
requestsTotal.WithLabelValues(userID, endpoint).Inc()

// ❌ Dynamic paths
requestsTotal.WithLabelValues(method, r.URL.Path).Inc()

// ❌ Timestamps
eventCounter.WithLabelValues(event, time.Now().String()).Inc()

// ❌ Session/Request IDs
requestDuration.WithLabelValues(sessionID, requestID).Observe(duration)
```

### Cardinality Solutions
```go
// ✅ Bucket dynamic values
func getEndpointLabel(path string) string {
    switch {
    case strings.HasPrefix(path, "/api/users/"):
        return "/api/users/{id}"
    case strings.HasPrefix(path, "/api/orders/"):
        return "/api/orders/{id}"
    default:
        return path
    }
}

// ✅ Use static status codes
func getStatusBucket(code int) string {
    switch {
    case code >= 200 && code < 300:
        return "2xx"
    case code >= 300 && code < 400:
        return "3xx"
    case code >= 400 && code < 500:
        return "4xx"
    case code >= 500:
        return "5xx"
    default:
        return "unknown"
    }
}

// ✅ Limit label values
func getPriorityLabel(priority int) string {
    switch {
    case priority >= 0 && priority < 3:
        return "low"
    case priority >= 3 && priority < 7:
        return "medium"
    case priority >= 7:
        return "high"
    default:
        return "unknown"
    }
}
```

## Performance Considerations

### Instrumentation in Hot Paths
```go
// ❌ Avoid in tight loops
for i := 0; i < 1000000; i++ {
    someMetric.Inc()  // Too expensive
    processItem(i)
}

// ✅ Batch updates
processed := 0
for i := 0; i < 1000000; i++ {
    processItem(i)
    processed++
}
someMetric.Add(float64(processed))

// ✅ Sample in hot paths
for i := 0; i < 1000000; i++ {
    if i%1000 == 0 {  // Sample 0.1%
        someMetric.Inc()
    }
    processItem(i)
}
```

### Efficient Histogram Usage
```go
// ✅ Reuse histogram observations
var (
    // Pre-allocate timer
    timer = prometheus.NewTimer(nil)
)

func processRequest() {
    // Efficient: reuse timer
    timer = prometheus.NewTimer(requestDuration.WithLabelValues("GET", "/api"))
    defer timer.ObserveDuration()
    
    // Process request
}
```

## Common Anti-Patterns

### 1. Taking rate() of Gauges
```promql
# ❌ NEVER do this
rate(memory_usage_bytes[5m])

# ✅ Gauges don't need rate
memory_usage_bytes
avg_over_time(memory_usage_bytes[5m])
```

### 2. Missing Metric Initialization
```go
// ❌ Can cause missing series
func init() {
    prometheus.MustRegister(httpRequests)
}

// ✅ Initialize all known label combinations
func init() {
    prometheus.MustRegister(httpRequests)
    
    for _, method := range []string{"GET", "POST", "PUT", "DELETE"} {
        for _, status := range []string{"200", "400", "500"} {
            httpRequests.WithLabelValues(method, "/api", status).Add(0)
        }
    }
}
```

### 3. Metric Naming Anti-Patterns
```go
// ❌ Procedurally generated names
for _, table := range tables {
    gauge := prometheus.NewGauge(prometheus.GaugeOpts{
        Name: fmt.Sprintf("table_%s_size", table),
    })
}

// ✅ Use labels instead
tableSize := prometheus.NewGaugeVec(
    prometheus.GaugeOpts{
        Name: "table_size_rows",
        Help: "Number of rows in table",
    },
    []string{"table"},
)
```

### 4. Wrong Metric Types
```go
// ❌ Using gauge for monotonic values
connectionsTotal := prometheus.NewGauge(...)  // Should be Counter

// ❌ Using counter for current state
activeConnections := prometheus.NewCounter(...)  // Should be Gauge

// ❌ Using histogram for counts
errorCounts := prometheus.NewHistogram(...)  // Should be Counter
```

## Testing Instrumentation

### Unit Testing Metrics
```go
func TestInstrumentation(t *testing.T) {
    // Reset metrics
    requestsTotal.Reset()
    
    // Execute code
    handleRequest()
    
    // Verify metrics
    expected := `
        # HELP http_requests_total Total HTTP requests
        # TYPE http_requests_total counter
        http_requests_total{method="GET",endpoint="/api",status="200"} 1
    `
    
    if err := testutil.CollectAndCompare(
        requestsTotal, 
        strings.NewReader(expected),
    ); err != nil {
        t.Errorf("Unexpected metrics: %v", err)
    }
}
```

### Integration Testing
```python
def test_metrics_endpoint():
    # Make request
    response = client.get('/metrics')
    
    # Parse metrics
    families = text_string_to_metric_families(response.text)
    
    # Verify specific metrics exist
    assert 'http_requests_total' in families
    assert 'http_request_duration_seconds' in families
    
    # Verify metric values
    for sample in families['http_requests_total'].samples:
        assert sample.value >= 0
```

## Summary
Effective Prometheus instrumentation requires thoughtful metric design, careful label usage, and awareness of cardinality implications. Follow these best practices to create observable systems that provide valuable insights without overwhelming your monitoring infrastructure. Remember: instrument liberally but thoughtfully, and always consider the operational impact of your metrics.