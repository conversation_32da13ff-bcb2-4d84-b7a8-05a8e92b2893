# CQRS (Command Query Responsibility Segregation) Pattern

## Core Concept
CQRS separates read and write operations by defining a view database - a read-only replica designed specifically to support queries in a microservices architecture.

## Context and Problem
In microservices with "Database per Service" pattern:
- Each service owns its data
- Cannot directly query across service databases
- Traditional joins across services are impossible
- API composition can be inefficient for complex queries

## Solution Architecture

### Write Side (Commands)
- Services handle commands that modify data
- Each service owns its database
- Publishes domain events when data changes

### Read Side (Queries)
- Separate view database optimized for queries
- Updated by subscribing to domain events
- Can denormalize data from multiple services
- Often uses NoSQL databases (document, key-value stores)

```
┌─────────────┐     Events      ┌──────────────┐
│  Service A  │ ─────────────>  │              │
│  (Write DB) │                 │  View        │
└─────────────┘                 │  Database    │
                               │  (Read-only)  │
┌─────────────┐     Events      │              │
│  Service B  │ ─────────────>  │              │
│  (Write DB) │                 └──────────────┘
└─────────────┘                         ↑
                                       │
                                    Queries
```

## Implementation Approaches

### 1. Event-Driven View Updates
```java
// Order Service publishes event
public class OrderService {
    public void createOrder(Order order) {
        orderRepository.save(order);
        eventPublisher.publish(new OrderCreatedEvent(order));
    }
}

// View Service subscribes to events
@EventHandler
public class OrderHistoryProjection {
    public void on(OrderCreatedEvent event) {
        OrderHistoryView view = new OrderHistoryView(
            event.getOrderId(),
            event.getCustomerId(),
            event.getOrderDetails()
        );
        viewRepository.save(view);
    }
}
```

### 2. Query Service Implementation
```java
@RestController
public class OrderHistoryController {
    @Autowired
    private OrderHistoryViewRepository viewRepository;
    
    @GetMapping("/customers/{customerId}/orderhistory")
    public List<OrderHistoryView> getOrderHistory(@PathVariable String customerId) {
        return viewRepository.findByCustomerId(customerId);
    }
}
```

### 3. View Database Schema Design
```javascript
// Document store example (MongoDB)
{
    "_id": "order-123",
    "customerId": "customer-456",
    "customerName": "John Doe",
    "orderDate": "2024-01-15",
    "items": [
        {
            "productId": "product-789",
            "productName": "Widget",
            "quantity": 2,
            "price": 29.99
        }
    ],
    "totalAmount": 59.98,
    "status": "DELIVERED"
}
```

## Benefits

### Performance
- Optimized read models for specific queries
- No need for complex joins at query time
- Can use specialized databases for queries

### Scalability
- Read and write sides scale independently
- Multiple denormalized views possible
- Can use caching effectively

### Flexibility
- Different storage technologies for reads/writes
- Query models tailored to UI needs
- Easy to add new query models

### Separation of Concerns
- Clear boundary between commands and queries
- Simpler, focused models
- Easier to reason about

## Drawbacks

### Complexity
- More moving parts in the system
- Additional infrastructure required
- Event handling logic needed

### Eventual Consistency
- Views lag behind source data
- Must handle stale data scenarios
- UI must accommodate delays

### Development Overhead
- Potential code duplication
- More models to maintain
- Synchronization logic required

### Operational Complexity
- More databases to manage
- Monitoring view synchronization
- Handling failed event processing

## When to Use CQRS

### Good Fit
- Microservices with separate databases
- Complex query requirements
- High read/write ratio
- Different scaling needs for reads/writes
- Event-sourced systems
- When eventual consistency is acceptable

### Poor Fit
- Simple CRUD applications
- Strong consistency requirements
- Small, monolithic applications
- Low query complexity

## Best Practices

### 1. Event Design
- Include all data needed for views
- Make events immutable
- Version events for evolution

### 2. View Synchronization
- Handle out-of-order events
- Implement idempotent handlers
- Monitor synchronization lag

### 3. Query Design
- Design views for specific use cases
- Avoid over-generalization
- Consider query performance early

### 4. Error Handling
- Implement retry mechanisms
- Handle poison messages
- Monitor failed projections

### 5. Testing
- Test event handlers thoroughly
- Verify view consistency
- Test eventual consistency scenarios

## Common Technologies

### Message Brokers
- Apache Kafka
- RabbitMQ
- Amazon Kinesis
- Azure Service Bus

### View Databases
- MongoDB (document store)
- Elasticsearch (search)
- Redis (key-value)
- PostgreSQL (with JSONB)

### Frameworks
- Axon Framework
- Eventuate
- Lagom
- Akka Persistence

## Example: Order History Service

```java
// Domain Event
public class OrderCreatedEvent {
    private String orderId;
    private String customerId;
    private List<OrderItem> items;
    private BigDecimal totalAmount;
    // ... getters/setters
}

// View Model
@Document
public class OrderHistoryView {
    @Id
    private String orderId;
    private String customerId;
    private String customerName;
    private List<OrderItemView> items;
    private BigDecimal totalAmount;
    private OrderStatus status;
    private LocalDateTime orderDate;
    // ... getters/setters
}

// Event Handler
@Component
public class OrderHistoryProjection {
    @Autowired
    private OrderHistoryViewRepository repository;
    
    @Autowired
    private CustomerService customerService;
    
    @EventHandler
    public void on(OrderCreatedEvent event) {
        CustomerInfo customer = customerService.getCustomer(event.getCustomerId());
        
        OrderHistoryView view = OrderHistoryView.builder()
            .orderId(event.getOrderId())
            .customerId(event.getCustomerId())
            .customerName(customer.getName())
            .items(mapItems(event.getItems()))
            .totalAmount(event.getTotalAmount())
            .status(OrderStatus.PENDING)
            .orderDate(LocalDateTime.now())
            .build();
            
        repository.save(view);
    }
}
```

## Related Patterns
- **Event Sourcing**: Store events as source of truth
- **Database per Service**: Each service owns its data
- **API Composition**: Alternative query pattern
- **Domain Event**: Communication mechanism
- **Saga**: Distributed transaction management