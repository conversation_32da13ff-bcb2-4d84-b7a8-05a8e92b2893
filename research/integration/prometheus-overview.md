# Prometheus Monitoring Overview

## Introduction
Prometheus is an open-source systems monitoring and alerting toolkit originally built at SoundCloud. Since 2012, it has become the de facto standard for metrics in cloud-native environments, joining the Cloud Native Computing Foundation in 2016.

## Core Features

### Multi-Dimensional Data Model
- Time series identified by metric name and key/value pairs (labels)
- Enables flexible and powerful queries
- Example: `http_requests_total{method="GET", endpoint="/api/users", status="200"}`

### Pull-Based Architecture
- Prometheus actively scrapes metrics from targets
- Targets expose metrics via HTTP endpoints
- More reliable than push-based systems during network issues

### Flexible Query Language (PromQL)
- Powerful query language for slicing and dicing data
- Supports aggregations, functions, and operators
- Real-time and historical data analysis

### Service Discovery
- Automatic target discovery
- Integrations with Kubernetes, Consul, AWS, etc.
- Dynamic environment support

### Standalone Architecture
- No dependency on distributed storage
- Local time series database
- Designed for reliability over availability

## Architecture Components

### 1. Prometheus Server
Core component that:
- Scrapes metrics from configured targets
- Stores time series data locally
- Runs rules over stored data
- Provides PromQL query interface

### 2. Client Libraries
Official libraries for:
- Go
- Java/Scala
- Python
- Ruby

### 3. Push Gateway
- Supports metrics from short-lived jobs
- Acts as intermediary for batch jobs
- Jobs push metrics to gateway, Prometheus scrapes gateway

### 4. Exporters
Bridge services that don't natively expose Prometheus metrics:
- Node Exporter (hardware/OS metrics)
- MySQL Exporter
- Redis Exporter
- Blackbox Exporter (probing endpoints)
- Custom exporters for any service

### 5. Alertmanager
- Handles alerts from Prometheus
- Deduplication, grouping, and routing
- Integrations with PagerDuty, Slack, email, etc.

## Data Model

### Time Series Structure
```
metric_name{label1="value1", label2="value2"} value timestamp
```

Example:
```
http_requests_total{method="POST", endpoint="/api/orders", status="200"} 1234 **********
```

### Metric Types

#### 1. Counter
Cumulative metric that only increases:
```go
httpRequestsTotal := prometheus.NewCounterVec(
    prometheus.CounterOpts{
        Name: "http_requests_total",
        Help: "Total number of HTTP requests",
    },
    []string{"method", "endpoint", "status"},
)
```

#### 2. Gauge
Metric that can go up or down:
```go
currentConnections := prometheus.NewGauge(
    prometheus.GaugeOpts{
        Name: "current_connections",
        Help: "Current number of open connections",
    },
)
```

#### 3. Histogram
Samples observations and counts them in configurable buckets:
```go
requestDuration := prometheus.NewHistogramVec(
    prometheus.HistogramOpts{
        Name:    "http_request_duration_seconds",
        Help:    "HTTP request latencies in seconds",
        Buckets: prometheus.DefBuckets, // [.005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10]
    },
    []string{"method", "endpoint"},
)
```

#### 4. Summary
Similar to histogram but calculates quantiles:
```go
requestSize := prometheus.NewSummaryVec(
    prometheus.SummaryOpts{
        Name:       "http_request_size_bytes",
        Help:       "HTTP request sizes in bytes",
        Objectives: map[float64]float64{0.5: 0.05, 0.9: 0.01, 0.99: 0.001},
    },
    []string{"method", "endpoint"},
)
```

## Pull vs Push Model

### Pull Model (Prometheus Default)
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'api-server'
    static_configs:
      - targets: ['api1:9090', 'api2:9090']
    scrape_interval: 15s
```

**Advantages:**
- Centralized configuration
- Easy to detect down targets
- No need for service registry on targets
- Better security (targets don't need to know about Prometheus)

**Disadvantages:**
- Requires network accessibility
- Not suitable for short-lived jobs
- Can be complex in dynamic environments

### Push Model (via Push Gateway)
```bash
# Push metrics to gateway
echo "batch_job_duration_seconds 185.4" | curl --data-binary @- http://pushgateway:9091/metrics/job/batch_job
```

**Use Cases:**
- Batch jobs
- Serverless functions
- Jobs behind strict firewalls

## Service Discovery

### Kubernetes Service Discovery
```yaml
scrape_configs:
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      # Only scrape pods with annotation
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      # Get metrics path from annotation
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
```

### Static Configuration
```yaml
scrape_configs:
  - job_name: 'node'
    static_configs:
      - targets:
        - 'node1:9100'
        - 'node2:9100'
        labels:
          environment: 'production'
          region: 'us-east-1'
```

### File-Based Discovery
```yaml
scrape_configs:
  - job_name: 'dynamic-targets'
    file_sd_configs:
      - files:
        - '/etc/prometheus/targets/*.yml'
        refresh_interval: 30s
```

## PromQL Basics

### Instant Queries
```promql
# Current value
http_requests_total

# With label filter
http_requests_total{status="200"}

# Multiple conditions
http_requests_total{status=~"2..", method!="GET"}
```

### Range Queries
```promql
# Rate of increase over 5 minutes
rate(http_requests_total[5m])

# Average request duration
avg(http_request_duration_seconds)

# 95th percentile latency
histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))
```

### Aggregations
```promql
# Sum by endpoint
sum by (endpoint) (rate(http_requests_total[5m]))

# Top 5 endpoints by request rate
topk(5, sum by (endpoint) (rate(http_requests_total[5m])))

# Error rate percentage
100 * sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m]))
```

### Functions
```promql
# Predict disk full in 4 hours
predict_linear(node_filesystem_free_bytes[1h], 4 * 3600) < 0

# Alert on sudden changes
abs(deriv(rate(http_requests_total[5m])[5m])) > 0.1

# Day-over-day comparison
rate(http_requests_total[5m]) / rate(http_requests_total[5m] offset 1d)
```

## Alerting

### Alert Rules
```yaml
# alerting_rules.yml
groups:
  - name: example
    rules:
    - alert: HighErrorRate
      expr: |
        100 * sum(rate(http_requests_total{status=~"5.."}[5m])) 
        / sum(rate(http_requests_total[5m])) > 5
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High error rate detected"
        description: "Error rate is {{ $value }}% for {{ $labels.instance }}"
    
    - alert: InstanceDown
      expr: up == 0
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "Instance {{ $labels.instance }} down"
```

### Alertmanager Configuration
```yaml
# alertmanager.yml
global:
  resolve_timeout: 5m
  slack_api_url: 'YOUR_SLACK_WEBHOOK'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 12h
  receiver: 'team-notifications'
  routes:
  - match:
      severity: critical
    receiver: pagerduty-critical

receivers:
  - name: 'team-notifications'
    slack_configs:
      - channel: '#alerts'
        title: 'Alert: {{ .GroupLabels.alertname }}'
        text: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
  
  - name: 'pagerduty-critical'
    pagerduty_configs:
      - service_key: 'YOUR_PAGERDUTY_KEY'
```

## Best Practices for Microservices

### 1. Standard Metrics
Implement RED method (Rate, Errors, Duration):
```go
var (
    requestRate = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
    
    requestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "HTTP request duration",
        },
        []string{"method", "endpoint"},
    )
    
    requestsInFlight = prometheus.NewGauge(
        prometheus.GaugeOpts{
            Name: "http_requests_in_flight",
            Help: "Current number of HTTP requests being processed",
        },
    )
)
```

### 2. Service-Level Objectives (SLOs)
```yaml
- record: slo:availability
  expr: |
    sum(rate(http_requests_total{status!~"5.."}[5m])) 
    / sum(rate(http_requests_total[5m]))

- alert: SLOViolation
  expr: slo:availability < 0.999
  for: 5m
  labels:
    severity: critical
```

### 3. Label Best Practices
```go
// Good: bounded cardinality
httpRequestsTotal.WithLabelValues("GET", "/api/users", "200").Inc()

// Bad: unbounded cardinality
httpRequestsTotal.WithLabelValues("GET", "/api/users/"+userID, "200").Inc()
```

### 4. Metric Naming Conventions
- Use base units: `seconds` not `milliseconds`
- Include unit in name: `http_request_duration_seconds`
- Use `_total` suffix for counters
- Be consistent across services

### 5. Efficient Queries
```promql
# Good: pre-aggregate in recording rules
- record: service:http_request_rate
  expr: sum by (service) (rate(http_requests_total[5m]))

# Use recording rule
service:http_request_rate{service="api"}

# Instead of computing every time
sum by (service) (rate(http_requests_total[5m])){service="api"}
```

## Common Use Cases

### Application Monitoring
- Request rates and latencies
- Error rates
- Resource usage (CPU, memory)
- Business metrics

### Infrastructure Monitoring
- Node metrics (CPU, memory, disk, network)
- Container metrics
- Kubernetes cluster health
- Database performance

### Alerting Scenarios
- Service availability
- Performance degradation
- Resource exhaustion
- Error rate spikes
- SLO violations

## Summary
Prometheus provides a robust, reliable monitoring solution ideal for cloud-native environments. Its pull-based model, powerful query language, and extensive ecosystem make it the standard choice for monitoring microservices and dynamic infrastructure. When properly configured with appropriate metrics, service discovery, and alerting rules, it enables teams to maintain high availability and quickly diagnose issues.