# API Error Handling (Google AIP-193)

## Overview
Services must return a `google.rpc.Status` message when an API error occurs, using canonical error codes and providing helpful, actionable error messages.

## Error Message Structure

### Core Components
```protobuf
message Status {
  // Canonical error code from google.rpc.Code
  int32 code = 1;
  
  // Developer-facing error message in English
  string message = 2;
  
  // Additional error information
  repeated google.protobuf.Any details = 3;
}
```

### Status Fields

#### 1. Error Code (`code`)
- Must use canonical codes from `google.rpc.Code`
- Common codes:
  - `OK` (0): Success
  - `INVALID_ARGUMENT` (3): Client provided invalid argument
  - `NOT_FOUND` (5): Resource not found
  - `PERMISSION_DENIED` (7): No permission
  - `RESOURCE_EXHAUSTED` (8): Quota exceeded
  - `FAILED_PRECONDITION` (9): Operation rejected
  - `INTERNAL` (13): Internal server error
  - `UNAVAILABLE` (14): Service unavailable

#### 2. Error Message (`message`)
- Developer-facing debug message
- Always in English
- Should be:
  - Brief and informative
  - Actionable
  - Free of technical jargon
  - Clear about the problem

#### 3. Error Details (`details`)
- Additional structured error information
- Common detail types listed below

## Error Detail Types

### ErrorInfo (Recommended)
Machine-readable error details for programmatic handling:

```protobuf
message ErrorInfo {
  // Snake_case error identifier (max 63 chars)
  string reason = 1;
  
  // Globally unique error domain
  string domain = 2;
  
  // Additional error context
  map<string, string> metadata = 3;
}
```

Example:
```json
{
  "@type": "type.googleapis.com/google.rpc.ErrorInfo",
  "reason": "account_disabled",
  "domain": "auth.example.com",
  "metadata": {
    "account_id": "12345",
    "disabled_reason": "payment_failure"
  }
}
```

### LocalizedMessage
Provides error text in user's language:

```protobuf
message LocalizedMessage {
  // BCP47 locale (e.g., "en-US", "fr-FR")
  string locale = 1;
  
  // Localized error message
  string message = 2;
}
```

Example:
```json
{
  "@type": "type.googleapis.com/google.rpc.LocalizedMessage",
  "locale": "es-ES",
  "message": "La cuenta ha sido desactivada"
}
```

### Help
Links to documentation for troubleshooting:

```protobuf
message Help {
  message Link {
    string description = 1;
    string url = 2;
  }
  repeated Link links = 1;
}
```

Example:
```json
{
  "@type": "type.googleapis.com/google.rpc.Help",
  "links": [{
    "description": "Account recovery",
    "url": "https://example.com/docs/account-recovery"
  }]
}
```

### Other Detail Types
- `BadRequest`: Field-level validation errors
- `PreconditionFailure`: Failed preconditions
- `QuotaFailure`: Quota violations
- `RequestInfo`: Request tracking information
- `ResourceInfo`: Resource-specific error details
- `DebugInfo`: Debugging information (development only)

## Best Practices

### Error Message Guidelines

#### DO:
- Keep messages concise and clear
- Explain what went wrong
- Suggest how to fix the issue
- Use consistent terminology

#### DON'T:
- Include sensitive information
- Use technical implementation details
- Blame the user
- Use error codes in message text

### Good vs Bad Examples

**Bad:**
```
"Error 5023: Object reference not set to an instance of an object"
```

**Good:**
```
"The book 'books/123' was not found. Check that the book ID is correct."
```

### Permission Checking
- Check permissions before checking resource existence
- Prevents information disclosure about resource existence

### Partial Errors
- Avoid partial errors when possible
- If unavoidable, clearly indicate which operations succeeded/failed

## Implementation Examples

### Basic Error Response
```json
{
  "error": {
    "code": 404,
    "message": "Book 'books/123' not found",
    "details": [{
      "@type": "type.googleapis.com/google.rpc.ErrorInfo",
      "reason": "book_not_found",
      "domain": "library.example.com",
      "metadata": {
        "book_id": "123"
      }
    }]
  }
}
```

### Validation Error
```json
{
  "error": {
    "code": 400,
    "message": "Invalid book data",
    "details": [{
      "@type": "type.googleapis.com/google.rpc.BadRequest",
      "field_violations": [{
        "field": "book.title",
        "description": "Title must be between 1 and 200 characters"
      }, {
        "field": "book.isbn",
        "description": "ISBN must be 13 digits"
      }]
    }]
  }
}
```

### Quota Error
```json
{
  "error": {
    "code": 429,
    "message": "Book creation quota exceeded",
    "details": [{
      "@type": "type.googleapis.com/google.rpc.QuotaFailure",
      "violations": [{
        "subject": "books_per_minute",
        "description": "Book creation limit of 10 per minute exceeded"
      }]
    }, {
      "@type": "type.googleapis.com/google.rpc.ErrorInfo",
      "reason": "quota_exceeded",
      "domain": "library.example.com",
      "metadata": {
        "quota_type": "books_per_minute",
        "limit": "10",
        "retry_after": "60"
      }
    }]
  }
}
```

## Error Mapping

### HTTP to gRPC Status Codes
| HTTP Status | gRPC Code | Use Case |
|-------------|-----------|----------|
| 200 | OK | Success |
| 400 | INVALID_ARGUMENT | Bad request |
| 401 | UNAUTHENTICATED | No credentials |
| 403 | PERMISSION_DENIED | No permission |
| 404 | NOT_FOUND | Resource not found |
| 409 | ABORTED | Conflict |
| 429 | RESOURCE_EXHAUSTED | Rate limit |
| 500 | INTERNAL | Server error |
| 503 | UNAVAILABLE | Service down |

## Retry Logic Guidelines

### Retryable Errors
- `UNAVAILABLE`: Service temporarily down
- `RESOURCE_EXHAUSTED`: With retry-after info
- `DEADLINE_EXCEEDED`: Request timeout

### Non-Retryable Errors
- `INVALID_ARGUMENT`: Fix request first
- `NOT_FOUND`: Resource doesn't exist
- `PERMISSION_DENIED`: Need authorization
- `FAILED_PRECONDITION`: Precondition not met

### Retry Strategy
```python
def should_retry(error):
    retryable_codes = [
        grpc.StatusCode.UNAVAILABLE,
        grpc.StatusCode.RESOURCE_EXHAUSTED,
        grpc.StatusCode.DEADLINE_EXCEEDED
    ]
    return error.code() in retryable_codes

def get_retry_delay(error, attempt):
    if error.code() == grpc.StatusCode.RESOURCE_EXHAUSTED:
        # Check for retry_after in metadata
        for detail in error.details():
            if detail.HasField("error_info"):
                retry_after = detail.error_info.metadata.get("retry_after")
                if retry_after:
                    return int(retry_after)
    
    # Exponential backoff with jitter
    base_delay = 1.0
    max_delay = 60.0
    delay = min(base_delay * (2 ** attempt), max_delay)
    jitter = random.uniform(0, delay * 0.1)
    return delay + jitter
```

## Testing Error Handling

### Unit Tests
```python
def test_book_not_found():
    response = library_client.get_book("books/999")
    assert response.error.code == 404
    assert "not found" in response.error.message
    
    error_info = get_error_detail(response.error, "ErrorInfo")
    assert error_info.reason == "book_not_found"
    assert error_info.metadata["book_id"] == "999"
```

### Integration Tests
- Test all error codes your API can return
- Verify error details are properly populated
- Test localization if supported
- Validate retry behavior

## Summary
Effective error handling is crucial for API usability. Follow these guidelines to create clear, actionable, and programmatically useful error responses that help developers quickly understand and resolve issues.