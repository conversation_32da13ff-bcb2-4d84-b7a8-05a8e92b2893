# OpenTelemetry Collector Architecture & Configuration

## Overview
The OpenTelemetry Collector is a vendor-agnostic way to receive, process, and export telemetry data. It removes the need to run, operate, and maintain multiple agents/collectors, providing a unified solution for traces, metrics, and logs.

## Core Objectives
1. **Usability**: Reasonable default configurations
2. **Performance**: Stable under varying loads and configurations
3. **Observability**: Exemplar of an observable service
4. **Extensibility**: Customizable without core code changes
5. **Unification**: Single codebase for all telemetry signals

## Architecture

### Component Pipeline
```
Receivers → Processors → Exporters
    ↓           ↓            ↓
  Ingest    Transform    Send Data
```

### Core Components

#### 1. Receivers
Accept telemetry data in various formats:
```yaml
receivers:
  # OTLP receiver for OpenTelemetry Protocol
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318
  
  # Prometheus scraping
  prometheus:
    config:
      scrape_configs:
        - job_name: 'node-exporter'
          static_configs:
            - targets: ['localhost:9100']
  
  # Jaeger receiver for legacy support
  jaeger:
    protocols:
      thrift_http:
        endpoint: 0.0.0.0:14268
      grpc:
        endpoint: 0.0.0.0:14250
  
  # Zipkin receiver
  zipkin:
    endpoint: 0.0.0.0:9411
```

#### 2. Processors
Transform and enhance telemetry data:
```yaml
processors:
  # Batch processing for efficiency
  batch:
    timeout: 10s
    send_batch_size: 1024
    send_batch_max_size: 2048
  
  # Add resource attributes
  resource:
    attributes:
      - key: environment
        value: production
        action: insert
      - key: team
        value: platform
        action: insert
  
  # Memory limiter to prevent OOM
  memory_limiter:
    check_interval: 1s
    limit_mib: 512
    spike_limit_mib: 128
  
  # Filter telemetry
  filter:
    traces:
      span:
        - 'attributes["http.status_code"] >= 500'
        - 'resource.attributes["service.name"] == "critical-service"'
  
  # Sampling
  probabilistic_sampler:
    sampling_percentage: 10
```

#### 3. Exporters
Send data to backends:
```yaml
exporters:
  # OTLP exporter
  otlp:
    endpoint: otel-backend:4317
    compression: gzip
    headers:
      api-key: ${API_KEY}
  
  # Prometheus exporter
  prometheus:
    endpoint: 0.0.0.0:8889
    namespace: otel
  
  # Jaeger exporter
  jaeger:
    endpoint: jaeger-collector:14250
    tls:
      insecure: true
  
  # Multiple backends
  otlp/backend1:
    endpoint: backend1:4317
  
  otlp/backend2:
    endpoint: backend2:4317
```

### Extensions
Additional capabilities:
```yaml
extensions:
  # Health check
  health_check:
    endpoint: 0.0.0.0:13133
    path: /health
  
  # Performance profiling
  pprof:
    endpoint: 0.0.0.0:1777
  
  # Metrics about the collector
  zpages:
    endpoint: 0.0.0.0:55679
  
  # Authentication
  oauth2client:
    client_id: ${CLIENT_ID}
    client_secret: ${CLIENT_SECRET}
    token_url: https://auth.example.com/token
```

## Deployment Models

### 1. Agent Mode (Sidecar/DaemonSet)
Runs alongside applications:
```yaml
# Kubernetes DaemonSet
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: otel-agent
spec:
  template:
    spec:
      containers:
      - name: otel-agent
        image: otel/opentelemetry-collector:latest
        resources:
          limits:
            memory: 200Mi
            cpu: 200m
        volumeMounts:
        - name: config
          mountPath: /etc/otel-agent
```

**Agent Configuration**:
```yaml
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317

processors:
  batch:
    timeout: 1s
    send_batch_size: 512
  
  memory_limiter:
    limit_mib: 100
    spike_limit_mib: 20

exporters:
  otlp:
    endpoint: otel-gateway:4317
    compression: gzip

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [memory_limiter, batch]
      exporters: [otlp]
```

### 2. Gateway Mode (Standalone)
Central collector for multiple agents:
```yaml
# Kubernetes Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: otel-gateway
spec:
  replicas: 3
  template:
    spec:
      containers:
      - name: otel-gateway
        image: otel/opentelemetry-collector:latest
        resources:
          limits:
            memory: 2Gi
            cpu: 1000m
```

**Gateway Configuration**:
```yaml
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
        max_recv_msg_size_mib: 10

processors:
  batch:
    timeout: 5s
    send_batch_size: 2048
  
  # Advanced processing
  attributes:
    actions:
      - key: sensitive_data
        action: delete
      - key: environment
        from_attribute: deployment.environment
        action: insert
  
  tail_sampling:
    policies:
      - name: error-sampling
        type: status_code
        status_codes: [ERROR]
      - name: slow-traces
        type: latency
        latency:
          threshold_ms: 1000
      - name: probabilistic
        type: probabilistic
        probabilistic:
          sampling_percentage: 10

exporters:
  # Multiple backends with retry
  otlp/jaeger:
    endpoint: jaeger:4317
    retry_on_failure:
      enabled: true
      initial_interval: 5s
      max_interval: 30s
      max_elapsed_time: 300s
  
  prometheusremotewrite:
    endpoint: https://prometheus:9090/api/v1/write
    timeout: 30s

service:
  extensions: [health_check, pprof, zpages]
  pipelines:
    traces:
      receivers: [otlp]
      processors: [memory_limiter, batch, tail_sampling]
      exporters: [otlp/jaeger]
    metrics:
      receivers: [otlp]
      processors: [memory_limiter, batch, attributes]
      exporters: [prometheusremotewrite]
```

## Configuration Patterns

### 1. Multi-Pipeline Configuration
```yaml
service:
  pipelines:
    # Traces pipeline
    traces/production:
      receivers: [otlp]
      processors: [memory_limiter, batch]
      exporters: [jaeger, otlp/backup]
    
    # Metrics pipeline
    metrics/production:
      receivers: [otlp, prometheus]
      processors: [memory_limiter, batch, filter]
      exporters: [prometheusremotewrite]
    
    # Logs pipeline
    logs/production:
      receivers: [otlp]
      processors: [memory_limiter, batch]
      exporters: [loki]
    
    # Debug pipeline
    traces/debug:
      receivers: [otlp]
      processors: [memory_limiter]
      exporters: [logging]
```

### 2. Environment-Specific Config
```yaml
# Use environment variables
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: ${OTLP_ENDPOINT:-0.0.0.0:4317}

exporters:
  otlp:
    endpoint: ${BACKEND_ENDPOINT}
    headers:
      api-key: ${API_KEY}

processors:
  resource:
    attributes:
      - key: environment
        value: ${ENVIRONMENT:-development}
        action: insert
```

### 3. Security Configuration
```yaml
extensions:
  bearertokenauth:
    token: ${BEARER_TOKEN}

receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
        tls:
          cert_file: /etc/otel/cert.pem
          key_file: /etc/otel/key.pem
          ca_file: /etc/otel/ca.pem

exporters:
  otlp:
    endpoint: backend:4317
    auth:
      authenticator: bearertokenauth
    tls:
      insecure: false
      ca_file: /etc/ssl/certs/ca-certificates.crt
```

## Scaling Strategies

### 1. Horizontal Scaling
```yaml
# Load balancer configuration
apiVersion: v1
kind: Service
metadata:
  name: otel-gateway-lb
spec:
  type: LoadBalancer
  ports:
  - port: 4317
    name: otlp-grpc
  - port: 4318
    name: otlp-http
  selector:
    app: otel-gateway
```

### 2. Sharding by Service
```yaml
# Route specific services to different collectors
processors:
  routing:
    from_attribute: service.name
    table:
      - value: frontend
        exporters: [otlp/frontend-backend]
      - value: backend
        exporters: [otlp/backend-backend]
      - value: database
        exporters: [otlp/database-backend]
```

### 3. Resource-Based Autoscaling
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: otel-gateway-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: otel-gateway
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## Production Best Practices

### 1. Reliability
```yaml
# Configure retries and queuing
exporters:
  otlp:
    endpoint: backend:4317
    sending_queue:
      enabled: true
      num_consumers: 10
      queue_size: 5000
    retry_on_failure:
      enabled: true
      initial_interval: 5s
      max_interval: 30s
      max_elapsed_time: 300s
```

### 2. Performance Optimization
```yaml
processors:
  # Batch for efficiency
  batch:
    timeout: 10s
    send_batch_size: 1024
    send_batch_max_size: 2048
  
  # Prevent OOM
  memory_limiter:
    check_interval: 1s
    limit_percentage: 80
    spike_limit_percentage: 25
```

### 3. Monitoring the Collector
```yaml
# Self-monitoring pipeline
receivers:
  prometheus:
    config:
      scrape_configs:
        - job_name: 'otel-collector'
          scrape_interval: 10s
          static_configs:
            - targets: ['0.0.0.0:8888']

service:
  telemetry:
    logs:
      level: info
      output_paths: [stdout]
    metrics:
      level: detailed
      address: 0.0.0.0:8888
```

### 4. Data Privacy
```yaml
processors:
  # Remove sensitive data
  attributes:
    actions:
      - key: user.email
        action: delete
      - key: user.ssn
        action: delete
      - key: credit_card
        action: hash
  
  # Redact span names
  span:
    name:
      from_attributes: [http.target]
      separator: "?"
      # Removes query parameters
```

### 5. Cost Optimization
```yaml
processors:
  # Aggressive sampling for high-volume services
  probabilistic_sampler:
    sampling_percentage: 1  # 1% for high volume
  
  # Drop unnecessary attributes
  attributes:
    actions:
      - pattern: internal.*
        action: delete
  
  # Filter out noise
  filter:
    traces:
      span:
        - 'name == "health-check" and attributes["http.status_code"] == 200'
```

## When to Use Collector

### Recommended Scenarios
1. **Production environments**: Centralized control and processing
2. **Multi-backend export**: Send to multiple observability platforms
3. **Data transformation**: Enrich, filter, or sample telemetry
4. **Protocol translation**: Convert between formats
5. **Security requirements**: TLS termination, authentication
6. **Resource constraints**: Offload processing from applications

### Direct Export Scenarios
Consider direct export (no collector) when:
- Development/testing environments
- Simple architectures
- Single backend
- Minimal processing needs
- Extreme latency sensitivity

## Summary
The OpenTelemetry Collector provides a flexible, scalable solution for telemetry data management. Its modular architecture enables organizations to build robust observability pipelines that can adapt to changing requirements while maintaining vendor neutrality.