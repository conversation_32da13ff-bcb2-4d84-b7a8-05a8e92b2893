# Google Cloud API Design Guide

## Overview
Google's API design guide applies to both REST and RPC APIs, with specific focus on gRPC APIs. It uses Protocol Buffers for API surface definition and follows resource-oriented design principles.

## Core Principles

### Resource-Oriented Design
- APIs should be designed around **resources** (nouns) rather than actions (verbs)
- Resources are named entities, and operations are performed on these resources
- Each resource has a unique identifier and supports standard methods

### API Architecture
```
Collection: /publishers
Resource: /publishers/{publisher}
Sub-collection: /publishers/{publisher}/books
Sub-resource: /publishers/{publisher}/books/{book}
```

## Standard Methods

### Core CRUD Operations
1. **Get** - Retrieve a specific resource
   ```
   GET /v1/publishers/{publisher}/books/{book}
   ```

2. **List** - Retrieve multiple resources
   ```
   GET /v1/publishers/{publisher}/books
   ```

3. **Create** - Generate a new resource
   ```
   POST /v1/publishers/{publisher}/books
   ```

4. **Update** - Modify an existing resource
   ```
   PATCH /v1/publishers/{publisher}/books/{book}
   ```

5. **Delete** - Remove a resource
   ```
   DELETE /v1/publishers/{publisher}/books/{book}
   ```

## Resource Naming Conventions

### Collection Names
- Use plural form: `books` not `book`
- Use American English: `organizations` not `organisations`
- Avoid overly general names: use `book_shelf` not `shelf`

### Resource ID Patterns
- Use meaningful identifiers when possible
- Support both system-generated and user-provided IDs
- Format: `{collection}/{id}` or `{parent}/{collection}/{id}`

### Field Names
- Use lowercase with underscores: `created_time`
- Avoid prepositions in names: `error_code` not `code_for_error`
- Use singular for single values, plural for arrays

## Protocol Buffer Best Practices

### Proto3 Syntax
```protobuf
syntax = "proto3";

package myapi.v1;

message Book {
  string name = 1;
  string title = 2;
  string author = 3;
  google.protobuf.Timestamp created_time = 4;
}
```

### Service Definition
```protobuf
service Library {
  rpc GetBook(GetBookRequest) returns (Book);
  rpc ListBooks(ListBooksRequest) returns (ListBooksResponse);
  rpc CreateBook(CreateBookRequest) returns (Book);
  rpc UpdateBook(UpdateBookRequest) returns (Book);
  rpc DeleteBook(DeleteBookRequest) returns (google.protobuf.Empty);
}
```

## Error Handling

### Standard Error Model
```protobuf
message Status {
  int32 code = 1;
  string message = 2;
  repeated google.protobuf.Any details = 3;
}
```

### Error Codes
- Use standard gRPC status codes
- Provide detailed error information in `details` field
- Include actionable error messages

### Example Error Response
```json
{
  "error": {
    "code": 404,
    "message": "Book not found",
    "details": [{
      "@type": "type.googleapis.com/google.rpc.ResourceInfo",
      "resourceType": "Book",
      "resourceName": "books/123"
    }]
  }
}
```

## Versioning Strategies

### API Versioning
- Use semantic versioning: `v1`, `v2`, etc.
- Include version in API path: `/v1/books`
- Maintain backward compatibility within major versions

### Breaking Changes
- Increment major version for breaking changes
- Support multiple versions simultaneously
- Provide migration guides

### Non-Breaking Changes
- Adding new fields (with defaults)
- Adding new methods
- Adding new resources

## Common Patterns

### Pagination
```protobuf
message ListBooksRequest {
  string parent = 1;
  int32 page_size = 2;
  string page_token = 3;
}

message ListBooksResponse {
  repeated Book books = 1;
  string next_page_token = 2;
}
```

### Filtering
```protobuf
message ListBooksRequest {
  string parent = 1;
  string filter = 2;  // e.g., "author='Tolkien' AND year>2000"
}
```

### Field Masks
```protobuf
message UpdateBookRequest {
  Book book = 1;
  google.protobuf.FieldMask update_mask = 2;
}
```

### Long-Running Operations
```protobuf
service Library {
  rpc ImportBooks(ImportBooksRequest) returns (google.longrunning.Operation);
}
```

## Best Practices

### API Documentation
- Use clear, descriptive comments in proto files
- Document all public methods and fields
- Provide usage examples

### Performance
- Design for pagination from the start
- Use field masks for partial updates
- Implement proper caching strategies

### Security
- Use proper authentication (OAuth 2.0, API keys)
- Implement authorization checks
- Validate all inputs

### Consistency
- Follow naming conventions strictly
- Use consistent patterns across APIs
- Maintain style guide compliance

## Common Anti-Patterns to Avoid

1. **Verbs in Resource Names**: `/getBook` → `/books/{id}`
2. **Nested Resources Too Deep**: Limit to 2-3 levels
3. **Inconsistent Naming**: Maintain consistent style
4. **Missing Pagination**: Always paginate list operations
5. **Exposing Internal IDs**: Use meaningful resource names

## Related Resources
- API Improvement Proposals (AIPs)
- Protocol Buffers documentation
- gRPC best practices
- Google API linter tools

## Evolution and Maintenance
This guide is a living document that evolves with:
- New design patterns
- Community feedback
- Technology advances
- Real-world API experiences