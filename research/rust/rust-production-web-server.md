# Rust Production Web Server Guide

## Overview

Building a production-ready web server in Rust requires careful consideration of concurrency, error handling, resource management, and graceful shutdown. This guide covers best practices for deploying Rust web services.

## Handling Concurrent Connections

### Basic Thread Pool Implementation

```rust
use std::{
    sync::{mpsc, Arc, Mutex},
    thread,
};

pub struct ThreadPool {
    workers: Vec<Worker>,
    sender: Option<mpsc::Sender<Job>>,
}

type Job = Box<dyn FnOnce() + Send + 'static>;

impl ThreadPool {
    pub fn new(size: usize) -> ThreadPool {
        assert!(size > 0);

        let (sender, receiver) = mpsc::channel();
        let receiver = Arc::new(Mutex::new(receiver));

        let mut workers = Vec::with_capacity(size);

        for id in 0..size {
            workers.push(Worker::new(id, Arc::clone(&receiver)));
        }

        ThreadPool {
            workers,
            sender: Some(sender),
        }
    }

    pub fn execute<F>(&self, f: F)
    where
        F: FnOnce() + Send + 'static,
    {
        let job = Box::new(f);
        self.sender.as_ref().unwrap().send(job).unwrap();
    }
}

impl Drop for ThreadPool {
    fn drop(&mut self) {
        drop(self.sender.take());

        for worker in &mut self.workers {
            if let Some(thread) = worker.thread.take() {
                thread.join().unwrap();
            }
        }
    }
}

struct Worker {
    id: usize,
    thread: Option<thread::JoinHandle<()>>,
}

impl Worker {
    fn new(id: usize, receiver: Arc<Mutex<mpsc::Receiver<Job>>>) -> Worker {
        let thread = thread::spawn(move || loop {
            let message = receiver.lock().unwrap().recv();

            match message {
                Ok(job) => {
                    job();
                }
                Err(_) => {
                    break;
                }
            }
        });

        Worker {
            id,
            thread: Some(thread),
        }
    }
}
```

### Using Tokio for Async Concurrency

```rust
use tokio::net::{TcpListener, TcpStream};
use tokio::io::{AsyncReadExt, AsyncWriteExt};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let listener = TcpListener::bind("127.0.0.1:8080").await?;
    
    loop {
        let (socket, _) = listener.accept().await?;
        
        // Spawn a new task for each connection
        tokio::spawn(async move {
            handle_connection(socket).await;
        });
    }
}

async fn handle_connection(mut socket: TcpStream) {
    let mut buffer = [0; 1024];
    
    match socket.read(&mut buffer).await {
        Ok(_) => {
            let response = "HTTP/1.1 200 OK\r\n\r\nHello, World!";
            let _ = socket.write_all(response.as_bytes()).await;
        }
        Err(e) => eprintln!("Failed to read from socket: {}", e),
    }
}
```

## Graceful Shutdown

### Signal Handling

```rust
use tokio::signal;
use tokio::sync::broadcast;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let (shutdown_tx, _) = broadcast::channel(1);
    let shutdown_flag = Arc::new(AtomicBool::new(false));
    
    // Spawn signal handler
    let shutdown_tx_clone = shutdown_tx.clone();
    let shutdown_flag_clone = shutdown_flag.clone();
    
    tokio::spawn(async move {
        match signal::ctrl_c().await {
            Ok(()) => {
                println!("Received shutdown signal");
                shutdown_flag_clone.store(true, Ordering::SeqCst);
                let _ = shutdown_tx_clone.send(());
            }
            Err(err) => {
                eprintln!("Unable to listen for shutdown signal: {}", err);
            }
        }
    });
    
    // Main server loop
    let listener = TcpListener::bind("127.0.0.1:8080").await?;
    let mut shutdown_rx = shutdown_tx.subscribe();
    
    loop {
        tokio::select! {
            Ok((socket, _)) = listener.accept() => {
                if !shutdown_flag.load(Ordering::SeqCst) {
                    let shutdown_rx = shutdown_tx.subscribe();
                    tokio::spawn(handle_connection_with_shutdown(socket, shutdown_rx));
                }
            }
            _ = shutdown_rx.recv() => {
                println!("Shutting down server...");
                break;
            }
        }
    }
    
    // Wait for existing connections to finish
    tokio::time::sleep(std::time::Duration::from_secs(5)).await;
    println!("Server shutdown complete");
    
    Ok(())
}

async fn handle_connection_with_shutdown(
    mut socket: TcpStream,
    mut shutdown_rx: broadcast::Receiver<()>,
) {
    tokio::select! {
        _ = handle_connection(socket) => {}
        _ = shutdown_rx.recv() => {
            println!("Connection handler shutting down");
        }
    }
}
```

## Error Handling in Production

### Comprehensive Error Types

```rust
use thiserror::Error;

#[derive(Error, Debug)]
pub enum ServerError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Invalid request: {0}")]
    InvalidRequest(String),
    
    #[error("Internal server error")]
    Internal,
    
    #[error("Service unavailable")]
    ServiceUnavailable,
    
    #[error("Timeout")]
    Timeout,
}

impl ServerError {
    pub fn status_code(&self) -> u16 {
        match self {
            ServerError::InvalidRequest(_) => 400,
            ServerError::Timeout => 408,
            ServerError::ServiceUnavailable => 503,
            _ => 500,
        }
    }
}
```

### Request Handling with Error Recovery

```rust
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Router,
    routing::get,
};

async fn fallible_handler() -> Result<String, ServerError> {
    // Simulate some operation that might fail
    if rand::random::<bool>() {
        Ok("Success!".to_string())
    } else {
        Err(ServerError::Internal)
    }
}

impl IntoResponse for ServerError {
    fn into_response(self) -> Response {
        let status = StatusCode::from_u16(self.status_code()).unwrap();
        let body = format!("Error: {}", self);
        
        (status, body).into_response()
    }
}

// Middleware for catching panics
use tower::ServiceBuilder;
use tower_http::catch_panic::CatchPanicLayer;

let app = Router::new()
    .route("/", get(fallible_handler))
    .layer(
        ServiceBuilder::new()
            .layer(CatchPanicLayer::new())
    );
```

## Performance Considerations

### Connection Pooling

```rust
use deadpool_postgres::{Config, Manager, Pool};
use tokio_postgres::NoTls;

pub async fn create_pool() -> Result<Pool, Box<dyn std::error::Error>> {
    let mut cfg = Config::new();
    cfg.host = Some("localhost".to_string());
    cfg.port = Some(5432);
    cfg.dbname = Some("mydb".to_string());
    cfg.user = Some("postgres".to_string());
    cfg.password = Some("password".to_string());
    
    // Connection pool settings
    cfg.pool = Some(deadpool_postgres::PoolConfig {
        max_size: 32,
        timeouts: deadpool_postgres::Timeouts {
            wait: Some(std::time::Duration::from_secs(5)),
            create: Some(std::time::Duration::from_secs(5)),
            recycle: Some(std::time::Duration::from_secs(5)),
        },
    });
    
    let pool = cfg.create_pool(NoTls)?;
    Ok(pool)
}
```

### Request Timeouts

```rust
use tower::timeout::TimeoutLayer;
use std::time::Duration;

let app = Router::new()
    .route("/", get(handler))
    .layer(TimeoutLayer::new(Duration::from_secs(30)));
```

### Response Compression

```rust
use tower_http::compression::CompressionLayer;

let app = Router::new()
    .route("/", get(handler))
    .layer(CompressionLayer::new());
```

## Resource Management

### File Descriptor Limits

```rust
use rlimit::{Resource, setrlimit};

fn increase_fd_limit() -> std::io::Result<()> {
    let (soft, hard) = rlimit::getrlimit(Resource::NOFILE)?;
    println!("Current FD limits - soft: {}, hard: {}", soft, hard);
    
    // Try to set soft limit to hard limit
    if soft < hard {
        setrlimit(Resource::NOFILE, hard, hard)?;
        println!("Increased FD limit to {}", hard);
    }
    
    Ok(())
}
```

### Memory Management

```rust
use std::alloc::{GlobalAlloc, System, Layout};
use std::sync::atomic::{AtomicUsize, Ordering};

struct MemoryTracker;

static ALLOCATED: AtomicUsize = AtomicUsize::new(0);

unsafe impl GlobalAlloc for MemoryTracker {
    unsafe fn alloc(&self, layout: Layout) -> *mut u8 {
        let ret = System.alloc(layout);
        if !ret.is_null() {
            ALLOCATED.fetch_add(layout.size(), Ordering::SeqCst);
        }
        ret
    }

    unsafe fn dealloc(&self, ptr: *mut u8, layout: Layout) {
        System.dealloc(ptr, layout);
        ALLOCATED.fetch_sub(layout.size(), Ordering::SeqCst);
    }
}

#[global_allocator]
static GLOBAL: MemoryTracker = MemoryTracker;

// Monitor memory usage
fn get_allocated_memory() -> usize {
    ALLOCATED.load(Ordering::SeqCst)
}
```

## Health Checks and Monitoring

```rust
use axum::{
    routing::get,
    Json,
    Router,
};
use serde::Serialize;
use std::sync::Arc;
use tokio::sync::RwLock;

#[derive(Serialize)]
struct HealthStatus {
    status: String,
    version: String,
    uptime: u64,
    connections: usize,
}

struct ServerState {
    start_time: std::time::Instant,
    active_connections: Arc<RwLock<usize>>,
}

async fn health_check(state: Arc<ServerState>) -> Json<HealthStatus> {
    let connections = state.active_connections.read().await;
    
    Json(HealthStatus {
        status: "healthy".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        uptime: state.start_time.elapsed().as_secs(),
        connections: *connections,
    })
}

let state = Arc::new(ServerState {
    start_time: std::time::Instant::now(),
    active_connections: Arc::new(RwLock::new(0)),
});

let app = Router::new()
    .route("/health", get({
        let state = state.clone();
        move || health_check(state)
    }));
```

## Production Configuration

### Environment-based Configuration

```rust
use serde::Deserialize;
use config::{Config, ConfigError, Environment, File};

#[derive(Debug, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub workers: usize,
    pub max_connections: usize,
    pub timeout_seconds: u64,
    pub database_url: String,
    pub redis_url: String,
}

impl ServerConfig {
    pub fn from_env() -> Result<Self, ConfigError> {
        let environment = std::env::var("RUN_ENV").unwrap_or_else(|_| "development".into());
        
        let settings = Config::builder()
            // Start with defaults
            .add_source(File::with_name("config/default"))
            // Layer on the environment-specific values
            .add_source(File::with_name(&format!("config/{}", environment)).required(false))
            // Add in settings from the environment
            .add_source(Environment::with_prefix("APP").separator("_"))
            .build()?;
        
        settings.try_deserialize()
    }
}
```

## Logging and Observability

```rust
use tracing::{info, error, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

fn init_tracing() {
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();
}

// In handlers
async fn handler() -> Result<String, ServerError> {
    info!("Processing request");
    
    match process_request().await {
        Ok(result) => {
            info!("Request processed successfully");
            Ok(result)
        }
        Err(e) => {
            error!("Request failed: {:?}", e);
            Err(e)
        }
    }
}
```

## Best Practices Summary

1. **Use async runtime** (Tokio) for high concurrency
2. **Implement graceful shutdown** with signal handling
3. **Use connection pooling** for database connections
4. **Add comprehensive error handling** with proper status codes
5. **Implement health checks** for monitoring
6. **Use structured logging** with tracing
7. **Configure timeouts** at multiple levels
8. **Monitor resource usage** (memory, file descriptors)
9. **Use environment-based configuration**
10. **Enable response compression** for better performance
11. **Handle panics gracefully** with catch_panic middleware
12. **Set up proper observability** with metrics and traces