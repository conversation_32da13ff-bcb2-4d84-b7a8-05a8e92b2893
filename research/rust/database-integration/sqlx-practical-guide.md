# SQLx Practical Guide

## Installation and Setup

### Basic Installation
```toml
[dependencies]
# Choose runtime and TLS backend
sqlx = { 
    version = "0.8", 
    features = [
        "runtime-tokio",     # or "runtime-async-std"
        "tls-native-tls",    # or "tls-rustls"
        "postgres",          # and/or "mysql", "sqlite"
        "uuid",              # optional: UUID support
        "chrono",            # optional: datetime support
        "migrate"            # optional: migration support
    ]
}
tokio = { version = "1", features = ["full"] }
```

### Environment Setup
```bash
# Install SQLx CLI for migrations and offline mode
cargo install sqlx-cli --no-default-features --features postgres

# Set database URL
export DATABASE_URL="postgres://user:password@localhost/mydb"
```

## Quick Start Examples

### Basic Connection and Query
```rust
use sqlx::postgres::PgPoolOptions;

#[tokio::main]
async fn main() -> Result<(), sqlx::Error> {
    // Create connection pool
    let pool = PgPoolOptions::new()
        .max_connections(5)
        .connect("postgres://postgres:password@localhost/test")
        .await?;

    // Simple query
    let row: (i64,) = sqlx::query_as("SELECT $1")
        .bind(150_i64)
        .fetch_one(&pool)
        .await?;

    assert_eq!(row.0, 150);
    Ok(())
}
```

### Compile-Time Checked Queries
```rust
// Define your struct
#[derive(Debug)]
struct Country {
    country: String,
    count: i64,
}

// Use query_as! macro for compile-time checking
async fn get_country_stats(
    pool: &PgPool,
    organization: &str
) -> Result<Vec<Country>, sqlx::Error> {
    let countries = sqlx::query_as!(
        Country,
        r#"
        SELECT country, COUNT(*) as count 
        FROM users 
        WHERE organization = $1
        GROUP BY country 
        ORDER BY count DESC
        "#,
        organization
    )
    .fetch_all(pool)
    .await?;

    Ok(countries)
}
```

## Key Differentiators

### 1. Pure Rust Implementation
- No C dependencies (except SQLite which uses libsqlite3)
- Zero unsafe code in core library
- Full async/await support

### 2. Compile-Time SQL Validation
```rust
// This will fail at compile time if:
// - The table doesn't exist
// - Column names are wrong
// - Types don't match
let user = sqlx::query_as!(
    User,
    "SELECT id, name, email FROM users WHERE id = $1",
    user_id
)
.fetch_one(&pool)
.await?;
```

### 3. Runtime Agnostic
```toml
# async-std runtime
sqlx = { version = "0.8", features = ["runtime-async-std-native-tls"] }

# Tokio runtime
sqlx = { version = "0.8", features = ["runtime-tokio-native-tls"] }

# Actix runtime
sqlx = { version = "0.8", features = ["runtime-actix-native-tls"] }
```

### 4. Flexible Query Execution

#### Prepared Queries (Default)
```rust
// Automatically prepared and cached
let users = sqlx::query!("SELECT * FROM users WHERE active = $1", true)
    .fetch_all(&pool)
    .await?;
```

#### Unprepared Queries
```rust
// For one-off queries or dynamic SQL
use sqlx::raw_sql;

let users = raw_sql("SELECT * FROM users WHERE active = true")
    .fetch_all(&pool)
    .await?;
```

## Production Patterns

### Connection Pool Configuration
```rust
use std::time::Duration;

let pool = PgPoolOptions::new()
    // Connection limits
    .max_connections(32)
    .min_connections(5)
    
    // Timeouts
    .acquire_timeout(Duration::from_secs(3))
    .idle_timeout(Duration::from_secs(600))
    .max_lifetime(Duration::from_secs(1800))
    
    // Connection testing
    .test_before_acquire(true)
    
    // Connect with options
    .connect_with(
        PgConnectOptions::from_str(&database_url)?
            .application_name("my_app")
            .log_statements(log::LevelFilter::Debug)
            .log_slow_statements(log::LevelFilter::Warn, Duration::from_secs(1))
    )
    .await?;
```

### Error Handling
```rust
use sqlx::Error;

async fn get_user(pool: &PgPool, id: i32) -> Result<User, AppError> {
    match sqlx::query_as!(User, "SELECT * FROM users WHERE id = $1", id)
        .fetch_one(pool)
        .await
    {
        Ok(user) => Ok(user),
        Err(Error::RowNotFound) => Err(AppError::NotFound),
        Err(Error::Database(db_err)) if db_err.is_unique_violation() => {
            Err(AppError::Conflict("User already exists"))
        }
        Err(e) => {
            log::error!("Database error: {}", e);
            Err(AppError::Internal)
        }
    }
}
```

### Transaction Patterns
```rust
use sqlx::Acquire;

async fn transfer_funds(
    pool: &PgPool,
    from_id: i32,
    to_id: i32,
    amount: Decimal,
) -> Result<(), TransferError> {
    // Start transaction
    let mut tx = pool.begin().await?;
    
    // Use RAII pattern for automatic rollback
    let result = async {
        // Debit from account
        sqlx::query!(
            "UPDATE accounts SET balance = balance - $1 WHERE id = $2 AND balance >= $1",
            amount,
            from_id
        )
        .execute(&mut *tx)
        .await?;
        
        // Credit to account
        sqlx::query!(
            "UPDATE accounts SET balance = balance + $1 WHERE id = $2",
            amount,
            to_id
        )
        .execute(&mut *tx)
        .await?;
        
        // Log transaction
        sqlx::query!(
            "INSERT INTO transfers (from_id, to_id, amount) VALUES ($1, $2, $3)",
            from_id,
            to_id,
            amount
        )
        .execute(&mut *tx)
        .await?;
        
        Ok::<_, sqlx::Error>(())
    }.await;
    
    match result {
        Ok(_) => {
            tx.commit().await?;
            Ok(())
        }
        Err(e) => {
            // Automatic rollback on drop
            Err(TransferError::from(e))
        }
    }
}
```

### Batch Operations
```rust
// Efficient bulk insert using COPY
use sqlx::postgres::PgPool;
use sqlx::copy::CopyIn;

async fn bulk_insert_users(
    pool: &PgPool,
    users: Vec<NewUser>,
) -> Result<u64, sqlx::Error> {
    let mut copy_in = pool.copy_in_raw(
        "COPY users (name, email, created_at) FROM STDIN WITH (FORMAT CSV)"
    ).await?;
    
    for user in users {
        copy_in.send(format!("{},{},{}\n", 
            user.name, 
            user.email, 
            user.created_at
        ).as_bytes()).await?;
    }
    
    let rows_inserted = copy_in.finish().await?;
    Ok(rows_inserted)
}
```

### Query Building
```rust
use sqlx::QueryBuilder;

async fn search_users(
    pool: &PgPool,
    filters: UserFilters,
) -> Result<Vec<User>, sqlx::Error> {
    let mut query = QueryBuilder::new("SELECT * FROM users WHERE 1=1");
    
    if let Some(name) = filters.name {
        query.push(" AND name ILIKE ");
        query.push_bind(format!("%{}%", name));
    }
    
    if let Some(email) = filters.email {
        query.push(" AND email = ");
        query.push_bind(email);
    }
    
    if let Some(active) = filters.active {
        query.push(" AND active = ");
        query.push_bind(active);
    }
    
    query.push(" ORDER BY created_at DESC");
    
    if let Some(limit) = filters.limit {
        query.push(" LIMIT ");
        query.push_bind(limit);
    }
    
    let users = query.build_query_as::<User>()
        .fetch_all(pool)
        .await?;
    
    Ok(users)
}
```

## Performance Optimization

### 1. Prepared Statement Caching
```rust
// Statements are automatically cached, but you can control it
let statement = sqlx::query!("SELECT * FROM users WHERE id = $1", user_id)
    .persistent(true); // Explicitly cache

// Reuse the cached statement
for id in user_ids {
    let user = statement.bind(id).fetch_one(&pool).await?;
}
```

### 2. Connection Pool Tuning
```rust
// For high-traffic applications
let pool = PgPoolOptions::new()
    .max_connections(num_cpus::get() as u32 * 4)
    .min_connections(num_cpus::get() as u32)
    .acquire_timeout(Duration::from_millis(500))
    .test_before_acquire(false) // Disable for performance
    .connect(&database_url)
    .await?;
```

### 3. Lazy Connections
```rust
// Don't connect until first use
let pool = PgPoolOptions::new()
    .max_connections(5)
    .connect_lazy(&database_url)?;
```

### 4. Statement Batching
```rust
// Execute multiple statements in one round trip
let results = sqlx::query!(
    r#"
    WITH inserted AS (
        INSERT INTO users (name, email) VALUES ($1, $2)
        RETURNING id
    )
    INSERT INTO profiles (user_id, bio) 
    SELECT id, $3 FROM inserted
    RETURNING user_id
    "#,
    name,
    email,
    bio
)
.fetch_one(&pool)
.await?;
```

## Offline Mode for CI/CD

### 1. Generate Offline Query Data
```bash
# In development with database access
cargo sqlx prepare

# This creates .sqlx directory with query metadata
```

### 2. Build Without Database
```bash
# In CI/CD without database
cargo build --features offline
```

### 3. Check Mode
```rust
// Cargo.toml for dual mode support
[features]
default = []
offline = ["sqlx/offline"]

# Build script can detect environment
[package.metadata.sqlx]
offline = true
```

## Migration Management

### Create and Run Migrations
```bash
# Create new migration
sqlx migrate add create_users_table

# Write your SQL in migrations/{timestamp}_create_users_table.sql

# Run migrations
sqlx migrate run

# Revert last migration
sqlx migrate revert

# Get migration info
sqlx migrate info
```

### Embedded Migrations
```rust
// Include migrations in binary
sqlx::migrate!("./migrations")
    .run(&pool)
    .await?;
```

### Reversible Migrations
```sql
-- migrations/{timestamp}_create_users_table.up.sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL
);

-- migrations/{timestamp}_create_users_table.down.sql
DROP TABLE users;
```

## Database-Specific Features

### PostgreSQL
```rust
// LISTEN/NOTIFY
use sqlx::postgres::PgListener;

let mut listener = PgListener::connect(&database_url).await?;
listener.listen("user_updates").await?;

while let Some(notification) = listener.recv().await? {
    println!("Received: {:?}", notification);
}

// Custom types
#[derive(sqlx::Type)]
#[sqlx(type_name = "user_role", rename_all = "lowercase")]
enum UserRole {
    Admin,
    User,
    Guest,
}
```

### MySQL
```rust
// SSL/TLS configuration
use sqlx::mysql::MySqlConnectOptions;

let options = MySqlConnectOptions::from_str(&database_url)?
    .ssl_mode(MySslMode::Required)
    .ssl_ca("path/to/ca.pem");
```

### SQLite
```rust
// In-memory database for testing
let pool = SqlitePoolOptions::new()
    .connect("sqlite::memory:")
    .await?;

// Custom collation
use sqlx::sqlite::SqliteConnectOptions;

let options = SqliteConnectOptions::from_str("sqlite:data.db")?
    .collation("NOCASE", |a, b| a.to_lowercase().cmp(&b.to_lowercase()));
```

## Best Practices Summary

1. **Use compile-time checking** whenever possible with `query!` macros
2. **Configure connection pools** appropriately for your workload
3. **Handle errors gracefully** with pattern matching on error types
4. **Use transactions** for data consistency
5. **Prepare for offline mode** in CI/CD environments
6. **Monitor performance** with logging and metrics
7. **Test with real databases** not just mocks
8. **Use migrations** for schema management
9. **Leverage database-specific features** when needed
10. **Keep queries simple** and let the database optimize