# Sea-ORM Documentation

## Overview

Sea-ORM is a relational ORM for Rust that helps build web services with the familiarity of dynamic languages. It features:
- **Async-first design** - Built on async/await
- **Dynamic query building** - Flexible query construction
- **Service-oriented architecture** - Clean separation of concerns
- **Production-ready** - Used in real-world applications

## Key Features

### Database Support
- PostgreSQL
- MySQL/MariaDB  
- SQLite
- SQL Server (via SQLx)

### Core Capabilities
1. **Async Database Operations** - All operations are async by default
2. **Dynamic Querying** - Build queries programmatically
3. **Relationship Mapping** - Define and navigate entity relationships
4. **Pagination Support** - Built-in pagination helpers
5. **Migration System** - Schema versioning and management
6. **Mock Testing** - Test without a real database

## Entity Definition

### Basic Entity
```rust
use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "users")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub name: String,
    pub email: String,
    #[sea_orm(column_type = "Text", nullable)]
    pub bio: Option<String>,
    pub created_at: DateTime,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::post::Entity")]
    Posts,
}

impl ActiveModelBehavior for ActiveModel {}
```

### Column Attributes
```rust
#[derive(DeriveEntityModel)]
#[sea_orm(table_name = "products")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub uuid: Uuid,
    
    #[sea_orm(column_name = "product_name")]
    pub name: String,
    
    #[sea_orm(column_type = "Decimal(Some((10, 2)))")]
    pub price: Decimal,
    
    #[sea_orm(indexed)]
    pub sku: String,
    
    #[sea_orm(unique)]
    pub barcode: String,
    
    #[sea_orm(default_value = "true")]
    pub active: bool,
}
```

## Database Connection

### Basic Connection
```rust
use sea_orm::{Database, DatabaseConnection};

let db: DatabaseConnection = Database::connect("postgres://user:pass@localhost/db")
    .await?;
```

### Connection Options
```rust
use sea_orm::{ConnectOptions, Database};
use std::time::Duration;

let mut opt = ConnectOptions::new("postgres://user:pass@localhost/db");
opt.max_connections(100)
    .min_connections(5)
    .connect_timeout(Duration::from_secs(8))
    .acquire_timeout(Duration::from_secs(8))
    .idle_timeout(Duration::from_secs(8))
    .max_lifetime(Duration::from_secs(8))
    .sqlx_logging(true)
    .sqlx_logging_level(log::LevelFilter::Debug);

let db = Database::connect(opt).await?;
```

## Query Operations

### Basic Queries
```rust
use sea_orm::EntityTrait;

// Find all
let users: Vec<user::Model> = User::find().all(&db).await?;

// Find by primary key
let user: Option<user::Model> = User::find_by_id(1).one(&db).await?;

// Find with filter
let active_users: Vec<user::Model> = User::find()
    .filter(user::Column::Active.eq(true))
    .all(&db)
    .await?;

// Find with multiple conditions
use sea_orm::Condition;

let users: Vec<user::Model> = User::find()
    .filter(
        Condition::all()
            .add(user::Column::Age.gte(18))
            .add(user::Column::Name.contains("John"))
    )
    .all(&db)
    .await?;
```

### Advanced Queries
```rust
use sea_orm::{QueryFilter, QueryOrder, QuerySelect};

// Complex query
let results = User::find()
    .filter(user::Column::Email.like("%@example.com"))
    .filter(user::Column::CreatedAt.gt(date))
    .order_by_desc(user::Column::CreatedAt)
    .limit(10)
    .offset(20)
    .all(&db)
    .await?;

// Select specific columns
let names: Vec<String> = User::find()
    .select_only()
    .column(user::Column::Name)
    .into_tuple()
    .all(&db)
    .await?;

// Raw SQL
use sea_orm::{FromQueryResult, Statement};

#[derive(FromQueryResult)]
struct UserStat {
    count: i64,
    avg_age: Option<f64>,
}

let stats = UserStat::find_by_statement(
    Statement::from_sql_and_values(
        DbBackend::Postgres,
        "SELECT COUNT(*) as count, AVG(age) as avg_age FROM users WHERE active = $1",
        vec![true.into()]
    )
)
.one(&db)
.await?;
```

## Active Model Pattern

### Insert
```rust
use sea_orm::{ActiveModelTrait, Set};

let new_user = user::ActiveModel {
    name: Set("John Doe".to_owned()),
    email: Set("<EMAIL>".to_owned()),
    ..Default::default()
};

let user: user::Model = new_user.insert(&db).await?;
```

### Update
```rust
// Update by primary key
let mut user: user::ActiveModel = User::find_by_id(1)
    .one(&db)
    .await?
    .unwrap()
    .into();

user.name = Set("Jane Doe".to_owned());
let updated_user: user::Model = user.update(&db).await?;

// Bulk update
User::update_many()
    .filter(user::Column::Active.eq(false))
    .set(user::ActiveModel {
        deleted_at: Set(Some(chrono::Utc::now())),
        ..Default::default()
    })
    .exec(&db)
    .await?;
```

### Delete
```rust
// Delete by primary key
let user = User::find_by_id(1).one(&db).await?.unwrap();
user.delete(&db).await?;

// Bulk delete
User::delete_many()
    .filter(user::Column::CreatedAt.lt(cutoff_date))
    .exec(&db)
    .await?;
```

## Relations

### Define Relations
```rust
#[derive(DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::user::Entity",
        from = "Column::UserId",
        to = "super::user::Column::Id"
    )]
    User,
    
    #[sea_orm(has_many = "super::comment::Entity")]
    Comments,
}

// Implement reverse relations
impl Related<super::user::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::User.def()
    }
}
```

### Query with Relations
```rust
// Find with related data
let users_with_posts: Vec<(user::Model, Vec<post::Model>)> = User::find()
    .find_with_related(Post)
    .all(&db)
    .await?;

// Lazy loading
let user = User::find_by_id(1).one(&db).await?.unwrap();
let posts: Vec<post::Model> = user.find_related(Post).all(&db).await?;

// Join queries
use sea_orm::JoinType;

let results = User::find()
    .join(JoinType::InnerJoin, user::Relation::Posts.def())
    .filter(post::Column::Published.eq(true))
    .all(&db)
    .await?;
```

### Loader Pattern (N+1 Prevention)
```rust
use sea_orm::LoaderTrait;

let users = User::find().all(&db).await?;
let posts: Vec<Vec<post::Model>> = users.load_many(Post, &db).await?;

for (user, user_posts) in users.iter().zip(posts.iter()) {
    println!("{} has {} posts", user.name, user_posts.len());
}
```

## Pagination

### Basic Pagination
```rust
use sea_orm::{PaginatorTrait, ItemsAndPagesNumber};

let paginator = User::find()
    .filter(user::Column::Active.eq(true))
    .paginate(&db, 50); // 50 items per page

let page1 = paginator.fetch_page(0).await?;
let num_pages = paginator.num_pages().await?;
let num_items = paginator.num_items().await?;
```

### Cursor Pagination
```rust
use sea_orm::CursorTrait;

let mut cursor = User::find().cursor_by(user::Column::Id);

// First page
cursor.after(0).first(10);
let users = cursor.all(&db).await?;

// Next page
if let Some(last_user) = users.last() {
    cursor.after(last_user.id).first(10);
    let next_page = cursor.all(&db).await?;
}
```

## Transactions

### Basic Transaction
```rust
use sea_orm::TransactionTrait;

let txn = db.begin().await?;

let user = user::ActiveModel {
    name: Set("New User".to_owned()),
    ..Default::default()
}
.insert(&txn)
.await?;

post::ActiveModel {
    user_id: Set(user.id),
    title: Set("First Post".to_owned()),
    ..Default::default()
}
.insert(&txn)
.await?;

txn.commit().await?;
```

### Transaction with Error Handling
```rust
use sea_orm::{DatabaseTransaction, DbErr};

async fn create_user_with_profile(
    db: &DatabaseConnection,
    user_data: UserData,
) -> Result<user::Model, DbErr> {
    db.transaction::<_, user::Model, DbErr>(|txn| {
        Box::pin(async move {
            let user = user::ActiveModel {
                name: Set(user_data.name),
                email: Set(user_data.email),
                ..Default::default()
            }
            .insert(txn)
            .await?;

            profile::ActiveModel {
                user_id: Set(user.id),
                bio: Set(user_data.bio),
                ..Default::default()
            }
            .insert(txn)
            .await?;

            Ok(user)
        })
    })
    .await
}
```

## Migration System

### Create Migration
```bash
# Install sea-orm-cli
cargo install sea-orm-cli

# Create new migration
sea-orm-cli migrate generate create_users_table
```

### Migration Structure
```rust
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(User::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(User::Id)
                            .integer()
                            .not_null()
                            .auto_increment()
                            .primary_key(),
                    )
                    .col(ColumnDef::new(User::Name).string().not_null())
                    .col(ColumnDef::new(User::Email).string().not_null().unique())
                    .col(
                        ColumnDef::new(User::CreatedAt)
                            .timestamp()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .to_owned(),
            )
            .await
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(User::Table).to_owned())
            .await
    }
}

#[derive(Iden)]
enum User {
    Table,
    Id,
    Name,
    Email,
    CreatedAt,
}
```

### Run Migrations
```rust
use sea_orm_migration::MigratorTrait;

// Run all pending migrations
Migrator::up(&db, None).await?;

// Rollback last migration
Migrator::down(&db, Some(1)).await?;

// Get migration status
let applied = Migrator::get_applied_migrations(&db).await?;
let pending = Migrator::get_pending_migrations(&db).await?;
```

## Testing with Mock

### Mock Database
```rust
use sea_orm::{DatabaseBackend, MockDatabase, MockExecResult};

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_find_user() {
        let db = MockDatabase::new(DatabaseBackend::Postgres)
            .append_query_results(vec![vec![user::Model {
                id: 1,
                name: "John".to_owned(),
                email: "<EMAIL>".to_owned(),
            }]])
            .into_connection();

        let user = User::find_by_id(1).one(&db).await.unwrap();
        assert_eq!(user.unwrap().name, "John");
    }

    #[tokio::test]
    async fn test_insert_user() {
        let db = MockDatabase::new(DatabaseBackend::Postgres)
            .append_exec_results(vec![
                MockExecResult {
                    last_insert_id: 15,
                    rows_affected: 1,
                },
            ])
            .into_connection();

        let result = user::ActiveModel {
            name: Set("New User".to_owned()),
            ..Default::default()
        }
        .insert(&db)
        .await;

        assert!(result.is_ok());
    }
}
```

## Production Patterns

### Service Layer
```rust
pub struct UserService {
    db: DatabaseConnection,
}

impl UserService {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }

    pub async fn create_user(&self, input: CreateUserInput) -> Result<user::Model, ServiceError> {
        // Validate input
        input.validate()?;

        // Check for duplicates
        if User::find()
            .filter(user::Column::Email.eq(&input.email))
            .one(&self.db)
            .await?
            .is_some()
        {
            return Err(ServiceError::DuplicateEmail);
        }

        // Create user
        let user = user::ActiveModel {
            name: Set(input.name),
            email: Set(input.email),
            password_hash: Set(hash_password(&input.password)?),
            ..Default::default()
        }
        .insert(&self.db)
        .await?;

        Ok(user)
    }

    pub async fn find_by_email(&self, email: &str) -> Result<Option<user::Model>, ServiceError> {
        Ok(User::find()
            .filter(user::Column::Email.eq(email))
            .one(&self.db)
            .await?)
    }
}
```

### Repository Pattern
```rust
#[async_trait]
pub trait UserRepository {
    async fn find_by_id(&self, id: i32) -> Result<Option<user::Model>, DbErr>;
    async fn find_by_email(&self, email: &str) -> Result<Option<user::Model>, DbErr>;
    async fn create(&self, user: user::ActiveModel) -> Result<user::Model, DbErr>;
    async fn update(&self, user: user::ActiveModel) -> Result<user::Model, DbErr>;
    async fn delete(&self, id: i32) -> Result<(), DbErr>;
}

pub struct SeaOrmUserRepository {
    db: DatabaseConnection,
}

#[async_trait]
impl UserRepository for SeaOrmUserRepository {
    async fn find_by_id(&self, id: i32) -> Result<Option<user::Model>, DbErr> {
        User::find_by_id(id).one(&self.db).await
    }

    async fn find_by_email(&self, email: &str) -> Result<Option<user::Model>, DbErr> {
        User::find()
            .filter(user::Column::Email.eq(email))
            .one(&self.db)
            .await
    }

    // ... other methods
}
```

### Query Optimization
```rust
// Use select_only for read-only queries
let users = User::find()
    .select_only()
    .column(user::Column::Id)
    .column(user::Column::Name)
    .into_model::<UserSummary>()
    .all(&db)
    .await?;

// Batch operations
let users_to_create: Vec<user::ActiveModel> = data.into_iter()
    .map(|d| user::ActiveModel {
        name: Set(d.name),
        email: Set(d.email),
        ..Default::default()
    })
    .collect();

User::insert_many(users_to_create)
    .exec(&db)
    .await?;

// Connection pooling
let mut opt = ConnectOptions::new(database_url);
opt.max_connections(num_cpus::get() as u32 * 4)
    .min_connections(num_cpus::get() as u32)
    .acquire_timeout(Duration::from_secs(3));
```

### Error Handling
```rust
use sea_orm::DbErr;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum ServiceError {
    #[error("Database error: {0}")]
    Database(#[from] DbErr),
    
    #[error("Not found")]
    NotFound,
    
    #[error("Duplicate email")]
    DuplicateEmail,
}

pub async fn get_user_safe(db: &DatabaseConnection, id: i32) -> Result<user::Model, ServiceError> {
    User::find_by_id(id)
        .one(db)
        .await?
        .ok_or(ServiceError::NotFound)
}
```

## Integration Examples

### With Axum
```rust
use axum::{Extension, Json};
use sea_orm::DatabaseConnection;

async fn list_users(
    Extension(db): Extension<DatabaseConnection>,
) -> Result<Json<Vec<user::Model>>, AppError> {
    let users = User::find()
        .filter(user::Column::Active.eq(true))
        .all(&db)
        .await?;
    
    Ok(Json(users))
}
```

### With GraphQL (Seaography)
```rust
use seaography::{Builder, BuilderContext};

let schema = Builder::new(&db, BuilderContext::default())
    .register_entity::<user::Entity>()
    .register_entity::<post::Entity>()
    .build();
```

## Performance Tips

1. **Use Connection Pooling** - Configure pool size based on workload
2. **Select Only Needed Columns** - Use `select_only()` and `column()`
3. **Batch Operations** - Use `insert_many()`, `update_many()`
4. **Lazy Loading Prevention** - Use loader pattern for related data
5. **Index Columns** - Add database indexes for frequently queried columns
6. **Raw SQL When Needed** - Use raw queries for complex operations
7. **Cache Queries** - Implement caching layer for frequently accessed data

## Resources

- [Official Documentation](https://www.sea-ql.org/SeaORM/)
- [API Documentation](https://docs.rs/sea-orm/latest/sea_orm/)
- [Examples Repository](https://github.com/SeaQL/sea-orm/tree/master/examples)
- [SeaORM Cookbook](https://www.sea-ql.org/sea-orm-cookbook/)
- [SeaORM Pro](https://www.sea-ql.org/sea-orm-pro/) - Commercial admin panel