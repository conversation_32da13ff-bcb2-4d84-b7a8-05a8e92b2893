# SQLx Documentation

## Overview

SQLx is an async, pure Rust SQL crate featuring:
- **Compile-time checked queries** - No DSL required
- **Async/await** - First-class async support
- **Pure Rust** - No C dependencies
- **Runtime Agnostic** - Works with Tokio and async-std

## Key Features

### Compile-Time Query Verification
- Queries are checked against your database at compile time
- Type-safe without a DSL
- Catches SQL errors before runtime

### Database Support
- PostgreSQL
- MySQL/MariaDB
- SQLite
- MSSQL (in development)

### Runtime Support
Choose your async runtime via feature flags:
- `runtime-tokio` - Tokio runtime support
- `runtime-async-std` - async-std runtime support
- `runtime-actix` - Actix runtime support

### TLS Support
Two TLS backend options:
1. **`tls-native-tls`** - Uses OS-native TLS (default)
   - Windows: SChannel
   - macOS: Secure Transport
   - Linux: OpenSSL

2. **`tls-rustls`** - Pure Rust TLS implementation
   - Cross-platform consistency
   - Supports TLS 1.2 and 1.3

## Core Components

### Query Macros

#### `query!` - Basic Query
```rust
let row = sqlx::query!(
    "SELECT id, name, email FROM users WHERE id = $1",
    user_id
)
.fetch_one(&pool)
.await?;

// Access fields with compile-time checking
println!("User: {} ({})", row.name, row.email);
```

#### `query_as!` - Query into Struct
```rust
#[derive(Debug)]
struct User {
    id: i32,
    name: String,
    email: String,
}

let user = sqlx::query_as!(
    User,
    "SELECT id, name, email FROM users WHERE id = $1",
    user_id
)
.fetch_one(&pool)
.await?;
```

#### `query_file!` - External SQL Files
```rust
let users = sqlx::query_file!("queries/get_active_users.sql")
    .fetch_all(&pool)
    .await?;
```

#### `query_file_as!` - External File into Struct
```rust
let users = sqlx::query_file_as!(User, "queries/get_all_users.sql")
    .fetch_all(&pool)
    .await?;
```

### Dynamic Queries

For runtime-constructed queries:
```rust
use sqlx::QueryBuilder;

let mut query_builder = QueryBuilder::new(
    "SELECT * FROM users WHERE active = true"
);

if let Some(name) = filter.name {
    query_builder.push(" AND name = ");
    query_builder.push_bind(name);
}

let query = query_builder.build();
let users = query.fetch_all(&pool).await?;
```

## Connection Management

### Single Connection
```rust
use sqlx::postgres::PgConnection;
use sqlx::Connection;

let mut conn = PgConnection::connect("postgres://user:pass@localhost/db").await?;
```

### Connection Pool
```rust
use sqlx::postgres::PgPoolOptions;

let pool = PgPoolOptions::new()
    .max_connections(5)
    .acquire_timeout(Duration::from_secs(3))
    .connect("postgres://user:pass@localhost/db")
    .await?;
```

### Pool Configuration
```rust
let pool = PgPoolOptions::new()
    .max_connections(10)
    .min_connections(2)
    .max_lifetime(Duration::from_secs(30 * 60)) // 30 min
    .idle_timeout(Duration::from_secs(10 * 60)) // 10 min
    .acquire_timeout(Duration::from_secs(3))
    .test_before_acquire(true)
    .connect(&database_url)
    .await?;
```

## Transactions

### Basic Transaction
```rust
let mut tx = pool.begin().await?;

sqlx::query!("INSERT INTO users (name) VALUES ($1)", name)
    .execute(&mut *tx)
    .await?;

sqlx::query!("UPDATE stats SET user_count = user_count + 1")
    .execute(&mut *tx)
    .await?;

tx.commit().await?;
```

### Transaction with Rollback
```rust
let mut tx = pool.begin().await?;

let result = async {
    // Multiple operations
    sqlx::query!("...").execute(&mut *tx).await?;
    sqlx::query!("...").execute(&mut *tx).await?;
    Ok::<_, sqlx::Error>(())
}.await;

match result {
    Ok(_) => tx.commit().await?,
    Err(e) => {
        tx.rollback().await?;
        return Err(e);
    }
}
```

## Type Mappings

### PostgreSQL Types
- `BOOLEAN` → `bool`
- `SMALLINT` → `i16`
- `INTEGER` → `i32`
- `BIGINT` → `i64`
- `REAL` → `f32`
- `DOUBLE PRECISION` → `f64`
- `TEXT/VARCHAR` → `String`
- `BYTEA` → `Vec<u8>`
- `TIMESTAMP` → `chrono::NaiveDateTime`
- `TIMESTAMPTZ` → `chrono::DateTime<Utc>`
- `UUID` → `uuid::Uuid`
- `JSON/JSONB` → `serde_json::Value` or custom type

### Nullable Types
SQL `NULL` values map to Rust `Option<T>`:
```rust
let row = sqlx::query!(
    "SELECT name, email, phone FROM users WHERE id = $1",
    user_id
)
.fetch_one(&pool)
.await?;

// phone is Option<String> if nullable in database
if let Some(phone) = row.phone {
    println!("Phone: {}", phone);
}
```

## Migrations

### Setup
```bash
# Install sqlx-cli
cargo install sqlx-cli

# Create migration
sqlx migrate add create_users_table
```

### Migration Files
Created in `migrations/` directory:
- `{timestamp}_create_users_table.sql`

### Running Migrations
```rust
// Run migrations programmatically
sqlx::migrate!("./migrations")
    .run(&pool)
    .await?;
```

Or via CLI:
```bash
sqlx migrate run
sqlx migrate revert
sqlx migrate info
```

## Offline Mode

For CI/CD environments without database access:

1. **Prepare offline data**:
   ```bash
   cargo sqlx prepare
   ```

2. **Build with offline mode**:
   ```bash
   cargo build --features offline
   ```

## Error Handling

### Error Types
```rust
use sqlx::Error;

match result {
    Err(Error::RowNotFound) => {
        // Handle missing data
    }
    Err(Error::Database(db_err)) => {
        // Handle database errors
        if db_err.is_unique_violation() {
            // Handle unique constraint
        }
    }
    Err(Error::PoolTimedOut) => {
        // Handle connection pool timeout
    }
    Err(e) => {
        // Handle other errors
    }
    Ok(data) => {
        // Process data
    }
}
```

## Best Practices

### 1. Use Compile-Time Checking
```rust
// Preferred: Compile-time checked
let user = sqlx::query_as!(User, "SELECT * FROM users WHERE id = $1", id)
    .fetch_one(&pool)
    .await?;

// Avoid: Runtime only
let user = sqlx::query("SELECT * FROM users WHERE id = $1")
    .bind(id)
    .fetch_one(&pool)
    .await?;
```

### 2. Connection Pool Configuration
```rust
// Production-ready pool config
let pool = PgPoolOptions::new()
    .max_connections(32)
    .min_connections(5)
    .acquire_timeout(Duration::from_secs(3))
    .idle_timeout(Duration::from_secs(600))
    .max_lifetime(Duration::from_secs(1800))
    .test_before_acquire(true)
    .connect_with(
        PgConnectOptions::from_str(&database_url)?
            .application_name("my_app")
            .log_statements(LevelFilter::Debug)
    )
    .await?;
```

### 3. Prepared Statements
```rust
// Reuse prepared statements
let stmt = sqlx::query!(
    "SELECT * FROM users WHERE created_at > $1",
    since_date
)
.persistent(true); // Cache prepared statement

// Execute multiple times efficiently
for _ in 0..100 {
    let users = stmt.fetch_all(&pool).await?;
}
```

### 4. Batch Operations
```rust
// Efficient bulk insert
let values: Vec<_> = users.iter()
    .map(|u| format!("('{}', '{}')", u.name, u.email))
    .collect();

let query = format!(
    "INSERT INTO users (name, email) VALUES {}",
    values.join(", ")
);

sqlx::query(&query).execute(&pool).await?;
```

### 5. Health Checks
```rust
async fn health_check(pool: &PgPool) -> Result<(), sqlx::Error> {
    sqlx::query("SELECT 1")
        .fetch_one(pool)
        .await?;
    Ok(())
}
```

## Production Patterns

### Retry Logic
```rust
use backoff::{ExponentialBackoff, future::retry};

async fn execute_with_retry<F, Fut, T>(
    f: F,
) -> Result<T, sqlx::Error>
where
    F: Fn() -> Fut,
    Fut: Future<Output = Result<T, sqlx::Error>>,
{
    let backoff = ExponentialBackoff::default();
    retry(backoff, || async {
        f().await.map_err(|e| match e {
            sqlx::Error::PoolTimedOut => backoff::Error::transient(e),
            sqlx::Error::Io(_) => backoff::Error::transient(e),
            e => backoff::Error::permanent(e),
        })
    }).await
}
```

### Circuit Breaker Pattern
```rust
use std::sync::Arc;
use tokio::sync::RwLock;

struct CircuitBreaker {
    failures: Arc<RwLock<u32>>,
    threshold: u32,
}

impl CircuitBreaker {
    async fn call<F, Fut, T>(&self, f: F) -> Result<T, Error>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = Result<T, sqlx::Error>>,
    {
        let failures = self.failures.read().await;
        if *failures >= self.threshold {
            return Err(Error::CircuitOpen);
        }
        drop(failures);
        
        match f().await {
            Ok(result) => {
                *self.failures.write().await = 0;
                Ok(result)
            }
            Err(e) => {
                *self.failures.write().await += 1;
                Err(e.into())
            }
        }
    }
}
```

### Monitoring Integration
```rust
use prometheus::{Counter, Histogram};

lazy_static! {
    static ref DB_QUERY_DURATION: Histogram = register_histogram!(
        "db_query_duration_seconds",
        "Database query duration in seconds"
    ).unwrap();
    
    static ref DB_QUERY_ERRORS: Counter = register_counter!(
        "db_query_errors_total",
        "Total number of database query errors"
    ).unwrap();
}

async fn monitored_query<T>(
    pool: &PgPool,
    query: &str,
) -> Result<T, sqlx::Error> {
    let timer = DB_QUERY_DURATION.start_timer();
    
    let result = sqlx::query(query)
        .fetch_one(pool)
        .await;
    
    timer.observe_duration();
    
    if result.is_err() {
        DB_QUERY_ERRORS.inc();
    }
    
    result
}
```

## Feature Flags

### Database Features
- `postgres` - PostgreSQL support
- `mysql` - MySQL/MariaDB support
- `sqlite` - SQLite support
- `mssql` - Microsoft SQL Server support
- `any` - Generic database driver

### Runtime Features
- `runtime-tokio` - Tokio runtime
- `runtime-async-std` - async-std runtime
- `runtime-actix` - Actix runtime

### TLS Features
- `tls-native-tls` - Native TLS support
- `tls-rustls` - Rustls TLS support

### Additional Features
- `offline` - Offline mode support
- `migrate` - Migration support
- `uuid` - UUID type support
- `chrono` - Date/time support
- `decimal` - Decimal type support
- `json` - JSON support
- `macros` - Enable derive macros

## Example Cargo.toml
```toml
[dependencies]
sqlx = { 
    version = "0.7", 
    features = [
        "runtime-tokio",
        "tls-rustls",
        "postgres",
        "uuid",
        "chrono",
        "migrate",
        "macros"
    ]
}
tokio = { version = "1", features = ["full"] }
```

## Resources

- [Official Documentation](https://docs.rs/sqlx/latest/sqlx/)
- [GitHub Repository](https://github.com/launchbadge/sqlx)
- [SQLx Book](https://github.com/launchbadge/sqlx/tree/main/sqlx-cli)
- [Examples](https://github.com/launchbadge/sqlx/tree/main/examples)