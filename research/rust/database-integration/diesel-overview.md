# Diesel ORM Documentation

## Overview

Diesel is a Safe, Extensible ORM and Query Builder for Rust that provides:
- Compile-time query validation
- Strongly typed query builder
- Support for PostgreSQL, SQLite, and MySQL
- Zero-cost abstractions

## Key Features

### Type Safety
- Strongly typed query builder prevents SQL injection
- Compile-time query construction validation
- Extensive type mapping between Rust and SQL types
- Schema declaration via `table!` macro

### Core Modules

1. **`prelude`**: Exports commonly used traits and types
   ```rust
   use diesel::prelude::*;
   ```

2. **`query_dsl`**: Query construction methods
   - Select, insert, update, delete operations
   - Join operations
   - Filtering and ordering

3. **`expression_methods`**: Column and value manipulation
   - String operations (like, not_like)
   - Numeric operations
   - Boolean operations

4. **`sql_types`**: SQL type representations
   - Integer, Text, Boolean, Timestamp
   - Custom type support
   - Nullable type handling

5. **`backend`**: Database backend abstractions
   - PostgreSQL specific features
   - SQLite specific features
   - MySQL specific features

## Query Construction Patterns

### Basic Query Building
```rust
// Start queries from tables
users::table
    .filter(users::name.eq("<PERSON>"))
    .select(users::id)
    .first::<i32>(&mut conn)

// Update operations
update(users::table)
    .filter(users::id.eq(1))
    .set(users::name.eq("Jane"))
    .execute(&mut conn)
```

### Three Query Building Categories

1. **Query Builder Methods**
   - Methods that modify the query structure
   - Examples: `select()`, `filter()`, `order()`

2. **Expression Methods**
   - Methods on columns and values
   - Examples: `eq()`, `like()`, `gt()`

3. **Bare SQL Functions**
   - Direct SQL execution when needed
   - `sql_query()` for complex queries

## Connection Management

### Basic Connection
```rust
use diesel::prelude::*;
use diesel::pg::PgConnection;

let database_url = std::env::var("DATABASE_URL")
    .expect("DATABASE_URL must be set");
let mut connection = PgConnection::establish(&database_url)
    .expect(&format!("Error connecting to {}", database_url));
```

### Connection Pooling (r2d2)
```rust
use diesel::r2d2::{self, ConnectionManager};
use diesel::PgConnection;

type DbPool = r2d2::Pool<ConnectionManager<PgConnection>>;

let manager = ConnectionManager::<PgConnection>::new(database_url);
let pool = r2d2::Pool::builder()
    .build(manager)
    .expect("Failed to create pool.");
```

## Schema Definition

### Using `table!` Macro
```rust
table! {
    users (id) {
        id -> Int4,
        name -> Varchar,
        email -> Varchar,
        created_at -> Timestamp,
    }
}
```

### Schema Generation
```bash
# Generate schema from existing database
diesel print-schema > src/schema.rs
```

## Migrations

### Creating Migrations
```bash
diesel migration generate create_users
```

### Migration Files
- `up.sql`: Apply migration
- `down.sql`: Revert migration

### Running Migrations
```bash
diesel migration run
diesel migration revert
diesel migration redo
```

## Type System

### SQL to Rust Type Mapping
- `Int4` → `i32`
- `Int8` → `i64`
- `Varchar/Text` → `String`
- `Bool` → `bool`
- `Timestamp` → `chrono::NaiveDateTime`
- `Nullable<T>` → `Option<T>`

### Custom Types
```rust
#[derive(Debug, Clone, Copy, SqlType)]
#[diesel(postgres_type(name = "my_enum"))]
pub struct MyEnum;
```

## Associations and Joins

### Belonging To
```rust
#[derive(Queryable, Identifiable, Associations)]
#[diesel(belongs_to(User))]
#[diesel(table_name = posts)]
pub struct Post {
    pub id: i32,
    pub user_id: i32,
    pub title: String,
}
```

### Joins
```rust
users::table
    .inner_join(posts::table)
    .select((users::name, posts::title))
    .load::<(String, String)>(&mut conn)
```

## Transactions

### Basic Transaction
```rust
conn.transaction::<_, diesel::result::Error, _>(|conn| {
    diesel::insert_into(users::table)
        .values(&new_user)
        .execute(conn)?;
    
    diesel::update(posts::table)
        .set(posts::user_id.eq(new_user.id))
        .execute(conn)?;
    
    Ok(())
})
```

## Best Practices

1. **Use Schema Generation**
   - Let Diesel generate your schema from the database
   - Keep migrations in version control

2. **Type Safety**
   - Leverage Diesel's type system for compile-time guarantees
   - Use NewType pattern for domain types

3. **Connection Pooling**
   - Always use connection pooling in production
   - Configure pool size based on workload

4. **Error Handling**
   - Use `Result<T, diesel::result::Error>` for database operations
   - Map errors to application-specific types

5. **Query Optimization**
   - Use `select()` to limit columns
   - Use `limit()` and pagination
   - Profile queries with `debug_query()`

## Feature Flags

### Backend Features
- `postgres`: PostgreSQL support
- `sqlite`: SQLite support
- `mysql`: MySQL support

### Optional Features
- `chrono`: Date/time support
- `uuid`: UUID type support
- `serde_json`: JSON/JSONB support
- `r2d2`: Connection pooling

### Column Limits
- `32-column-tables` (default)
- `64-column-tables`
- `128-column-tables`

## Common Patterns

### Pagination
```rust
fn paginate<T>(page: i64) -> Paginated<T> {
    use diesel::query_dsl::methods::LimitDsl;
    
    const PER_PAGE: i64 = 10;
    
    T::table
        .limit(PER_PAGE)
        .offset((page - 1) * PER_PAGE)
}
```

### Filtering with Options
```rust
let mut query = users::table.into_boxed();

if let Some(name) = &filter.name {
    query = query.filter(users::name.eq(name));
}

if let Some(email) = &filter.email {
    query = query.filter(users::email.eq(email));
}

query.load::<User>(&mut conn)
```

### Upsert Operations
```rust
diesel::insert_into(users::table)
    .values(&new_user)
    .on_conflict(users::email)
    .do_update()
    .set(&new_user)
    .execute(&mut conn)
```

## Production Considerations

1. **Connection Pool Configuration**
   ```rust
   let pool = r2d2::Pool::builder()
       .max_size(15)
       .min_idle(Some(5))
       .connection_timeout(Duration::from_secs(5))
       .build(manager)?;
   ```

2. **Query Logging**
   ```rust
   // Enable query logging
   use diesel::debug_query;
   let debug_string = debug_query::<DB, _>(&query).to_string();
   ```

3. **Health Checks**
   ```rust
   fn health_check(pool: &DbPool) -> Result<(), Error> {
       let mut conn = pool.get()?;
       diesel::sql_query("SELECT 1")
           .execute(&mut conn)?;
       Ok(())
   }
   ```

4. **Migration Management**
   - Run migrations on deployment
   - Test rollback procedures
   - Keep migration files small and focused

## Resources

- [Official Getting Started Guide](https://diesel.rs/guides/getting-started/)
- [API Documentation](https://docs.rs/diesel/latest/diesel/)
- [GitHub Discussions](https://github.com/diesel-rs/diesel/discussions)
- [Examples Repository](https://github.com/diesel-rs/diesel/tree/master/examples)