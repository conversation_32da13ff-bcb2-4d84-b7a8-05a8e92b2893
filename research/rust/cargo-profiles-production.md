# Cargo Profiles for Production

## Overview

Profiles provide a way to alter the compiler settings, influencing things like optimizations and debugging symbols. Cargo has 4 built-in profiles: `dev`, `release`, `test`, and `bench`. Additionally, custom profiles can be defined.

## Built-in Profiles

### Profile Selection

Profile selected automatically based on command:
- `cargo build` → `dev` profile
- `cargo build --release` → `release` profile
- `cargo test` → `test` profile
- `cargo bench` → `bench` profile

### Default Settings

| Profile | opt-level | debug | debug-assertions | overflow-checks |
|---------|-----------|-------|------------------|-----------------|
| dev     | 0         | true  | true             | true            |
| release | 3         | false | false            | false           |
| test    | 0         | true  | true             | true            |
| bench   | 3         | false | false            | false           |

## Configuration Options

### opt-level

Controls optimization level:

```toml
[profile.release]
opt-level = 3  # Options: 0, 1, 2, 3, "s", "z"
```

- `0`: No optimizations (fastest compile time)
- `1`: Basic optimizations
- `2`: Some optimizations
- `3`: All optimizations (default for release)
- `"s"`: Optimize for binary size
- `"z"`: Optimize for binary size, even more than `"s"`

### debug

Controls debug information inclusion:

```toml
[profile.release]
debug = false  # Options: 0/false, "line-tables-only", 1/"limited", 2/true
```

- `0` or `false`: No debug info
- `"line-tables-only"`: Line numbers only for panics
- `1` or `"limited"`: Line tables and limited debug info
- `2` or `true`: Full debug info

### strip

Controls symbol stripping:

```toml
[profile.release]
strip = "symbols"  # Options: "none", "debuginfo", "symbols"
```

- `"none"`: No stripping
- `"debuginfo"`: Strip debug info only
- `"symbols"`: Strip all symbols (reduces binary size)

### lto

Link Time Optimization:

```toml
[profile.release]
lto = true  # Options: false, true/"fat", "thin", "off"
```

- `false` or `"off"`: No LTO
- `true` or `"fat"`: Full LTO (slower builds, better optimization)
- `"thin"`: Thin LTO (faster than fat, good optimization)

### codegen-units

Controls parallelization vs optimization:

```toml
[profile.release]
codegen-units = 1  # Default: 16 for opt-level 0, 1 for opt-level > 0
```

- Lower values: Better optimization, slower compilation
- Higher values: Faster compilation, less optimization

### panic

Panic strategy:

```toml
[profile.release]
panic = "abort"  # Options: "unwind", "abort"
```

- `"unwind"`: Stack unwinding (default)
- `"abort"`: Immediate termination (smaller binary)

### incremental

Incremental compilation:

```toml
[profile.release]
incremental = false  # Default: true for dev, false for release
```

### overflow-checks

Integer overflow checking:

```toml
[profile.release]
overflow-checks = false  # Default: true for dev, false for release
```

### rpath

Runtime library search path:

```toml
[profile.release]
rpath = false  # Default: false
```

### debug-assertions

Debug assertions in code:

```toml
[profile.release]
debug-assertions = false  # Default: true for dev, false for release
```

## Production Release Profile

### Optimized for Performance

```toml
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = "symbols"
```

### Optimized for Binary Size

```toml
[profile.release]
opt-level = "z"
lto = true
codegen-units = 1
panic = "abort"
strip = "symbols"
```

### Balanced Production Profile

```toml
[profile.release]
opt-level = 3
lto = "thin"
codegen-units = 1
debug = false
panic = "unwind"
strip = "debuginfo"
overflow-checks = false
debug-assertions = false
```

## Custom Profiles

### Creating Custom Profiles

```toml
[profile.production]
inherits = "release"
lto = true
codegen-units = 1

[profile.release-with-debug]
inherits = "release"
debug = true
strip = "none"
```

Use custom profiles:
```bash
cargo build --profile production
```

### Staging Profile Example

```toml
[profile.staging]
inherits = "release"
debug = 1  # Some debug info for troubleshooting
lto = "thin"  # Faster builds than full LTO
```

## Profile Overrides

### Package-specific Overrides

```toml
# Optimize dependencies even in dev mode
[profile.dev.package."*"]
opt-level = 2

# Specific package override
[profile.release.package.image]
opt-level = "z"  # Optimize image crate for size

# Using build-override for build scripts
[profile.dev.build-override]
opt-level = 3
```

### Override Precedence

1. `[profile.<name>.package.<name>]` (highest priority)
2. `[profile.<name>.package."*"]`
3. `[profile.<name>.build-override]`
4. `[profile.<name>]`
5. Built-in defaults (lowest priority)

## Benchmarking Profiles

```toml
[profile.bench]
debug = true  # Enable debug symbols for profiling

[profile.bench.package."*"]
opt-level = 3  # Ensure dependencies are optimized
```

## CI/CD Profiles

### Fast CI Builds

```toml
[profile.ci]
inherits = "dev"
debug = false
incremental = false  # Better for clean builds
```

### Release CI Builds

```toml
[profile.ci-release]
inherits = "release"
lto = true
codegen-units = 1
strip = "symbols"
```

## Best Practices

### 1. Profile for Your Use Case

```toml
# Web service (performance critical)
[profile.release]
opt-level = 3
lto = true
codegen-units = 1

# Embedded system (size critical)
[profile.release]
opt-level = "z"
panic = "abort"
strip = "symbols"
```

### 2. Development Optimization

```toml
# Faster dev builds with optimized dependencies
[profile.dev]
opt-level = 0

[profile.dev.package."*"]
opt-level = 2
```

### 3. Testing Profiles

```toml
# Faster test compilation
[profile.test]
opt-level = 0
debug = true

# But optimize test dependencies
[profile.test.package."*"]
opt-level = 2
```

### 4. Debugging Production Issues

```toml
[profile.release-debug]
inherits = "release"
debug = true
strip = "none"
```

## Platform-Specific Considerations

### Linux Production

```toml
[profile.release]
strip = "symbols"  # Use external debug symbols if needed
```

### Windows Production

```toml
[profile.release]
# PDB files are generated separately
strip = "none"  # Stripping handled differently on Windows
```

### macOS Production

```toml
[profile.release]
strip = "symbols"
# Consider split-debuginfo for separate dSYM files
split-debuginfo = "packed"
```

## Measuring Impact

### Build Time vs Runtime Performance

```bash
# Measure build time
time cargo build --release

# Measure binary size
ls -lh target/release/myapp

# Profile runtime performance
cargo build --profile bench
```

### Common Trade-offs

1. **LTO**: +20-30% build time, 5-20% performance gain
2. **codegen-units=1**: +50% build time, 5-15% performance gain
3. **opt-level="z"**: 20-40% size reduction, 5-10% performance loss

## Summary

Key recommendations for production:
1. Always use release profile or custom production profile
2. Enable LTO for best performance
3. Set codegen-units=1 for maximum optimization
4. Strip symbols for smaller binaries
5. Consider panic="abort" for embedded/size-critical apps
6. Test performance impact of different settings
7. Document your profile choices