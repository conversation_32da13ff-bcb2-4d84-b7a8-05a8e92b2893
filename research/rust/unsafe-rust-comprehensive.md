# Unsafe Rust - Comprehensive Guide

## Overview

Rust's memory safety guarantees are enforced at compile time through its ownership system. However, there are cases where you need to perform operations that the compiler cannot verify as safe. This is where `unsafe` comes in.

## The Five Unsafe Superpowers

When you use the `unsafe` keyword, you can:

1. Dereference raw pointers
2. Call unsafe functions or methods
3. Access or modify mutable static variables
4. Implement unsafe traits
5. Access fields of unions

## 1. Dereferencing Raw Pointers

### Raw Pointer Types

```rust
// Immutable raw pointer
let x = 5;
let r1 = &x as *const i32;

// Mutable raw pointer
let mut y = 10;
let r2 = &mut y as *mut i32;

// Creating raw pointers is safe
// Dereferencing them requires unsafe
unsafe {
    println!("r1 is: {}", *r1);
    println!("r2 is: {}", *r2);
}
```

### Common Raw Pointer Patterns

```rust
// Working with C APIs
extern "C" {
    fn some_c_function(ptr: *const u8, len: usize);
}

let data = vec![1, 2, 3, 4, 5];
unsafe {
    some_c_function(data.as_ptr(), data.len());
}

// Manual memory management
use std::alloc::{alloc, dealloc, Layout};

unsafe {
    let layout = Layout::new::<u32>();
    let ptr = alloc(layout) as *mut u32;
    
    // SAFETY: We just allocated this memory and haven't freed it
    *ptr = 42;
    println!("Value: {}", *ptr);
    
    dealloc(ptr as *mut u8, layout);
}
```

### Null Pointer Checks

```rust
fn safe_deref(ptr: *const i32) -> Option<i32> {
    if ptr.is_null() {
        None
    } else {
        // SAFETY: We've checked that ptr is not null
        unsafe { Some(*ptr) }
    }
}
```

## 2. Calling Unsafe Functions

### Unsafe Function Declaration

```rust
unsafe fn dangerous() {
    // Unsafe operations here
}

// Must be called within unsafe block
unsafe {
    dangerous();
}
```

### FFI (Foreign Function Interface)

```rust
// Declaring external functions
extern "C" {
    fn abs(input: i32) -> i32;
    fn strlen(s: *const c_char) -> size_t;
}

// Using external functions
use std::ffi::CString;

let input = -5;
let output = unsafe { abs(input) };
println!("Absolute value of {} is {}", input, output);

// Working with C strings
let c_string = CString::new("Hello, world!").expect("CString::new failed");
let length = unsafe { strlen(c_string.as_ptr()) };
println!("String length: {}", length);
```

### Creating Safe Abstractions

```rust
use std::slice;

fn split_at_mut(values: &mut [i32], mid: usize) -> (&mut [i32], &mut [i32]) {
    let len = values.len();
    let ptr = values.as_mut_ptr();

    assert!(mid <= len);

    unsafe {
        (
            // SAFETY: We ensure mid <= len, so these slices don't overlap
            slice::from_raw_parts_mut(ptr, mid),
            slice::from_raw_parts_mut(ptr.add(mid), len - mid),
        )
    }
}
```

## 3. Accessing Mutable Static Variables

### Static Variables

```rust
static HELLO_WORLD: &str = "Hello, world!"; // Immutable static - safe to access

static mut COUNTER: u32 = 0; // Mutable static - unsafe to access

fn add_to_count(inc: u32) {
    unsafe {
        // SAFETY: No data races in single-threaded context
        COUNTER += inc;
    }
}

fn get_count() -> u32 {
    unsafe {
        // SAFETY: No data races in single-threaded context
        COUNTER
    }
}
```

### Thread-Safe Static Variables

```rust
use std::sync::atomic::{AtomicUsize, Ordering};

static ATOMIC_COUNTER: AtomicUsize = AtomicUsize::new(0);

// Safe to access from multiple threads
fn increment_atomic() {
    ATOMIC_COUNTER.fetch_add(1, Ordering::SeqCst);
}

fn get_atomic_count() -> usize {
    ATOMIC_COUNTER.load(Ordering::SeqCst)
}
```

## 4. Implementing Unsafe Traits

### Unsafe Trait Declaration

```rust
unsafe trait Foo {
    // Methods that have safety requirements
    fn dangerous(&self);
}

unsafe impl Foo for i32 {
    fn dangerous(&self) {
        // Implementation that upholds safety contract
    }
}
```

### Common Unsafe Traits

```rust
// Send and Sync
struct MyType {
    data: *const u8,
}

// SAFETY: MyType can be safely sent between threads because...
unsafe impl Send for MyType {}

// SAFETY: MyType can be safely shared between threads because...
unsafe impl Sync for MyType {}

// GlobalAlloc for custom allocators
use std::alloc::{GlobalAlloc, Layout};

struct MyAllocator;

unsafe impl GlobalAlloc for MyAllocator {
    unsafe fn alloc(&self, layout: Layout) -> *mut u8 {
        // SAFETY: Implementation must return valid memory or null
        std::alloc::System.alloc(layout)
    }

    unsafe fn dealloc(&self, ptr: *mut u8, layout: Layout) {
        // SAFETY: ptr must have been allocated by this allocator
        std::alloc::System.dealloc(ptr, layout)
    }
}
```

## 5. Accessing Union Fields

### Union Definition and Usage

```rust
#[repr(C)]
union MyUnion {
    f1: u32,
    f2: f32,
}

let u = MyUnion { f1: 1 };

// Accessing union fields is unsafe
let f = unsafe { u.f1 };

// Pattern matching on unions
unsafe {
    match u {
        MyUnion { f1: 10 } => println!("ten"),
        MyUnion { f2 } => println!("float {}", f2),
    }
}
```

### Practical Union Example

```rust
use std::mem;

// A union for type punning
#[repr(C)]
union FloatInt {
    f: f32,
    i: u32,
}

fn float_bits(f: f32) -> u32 {
    let fi = FloatInt { f };
    // SAFETY: f32 and u32 have the same size and alignment
    unsafe { fi.i }
}

fn bits_to_float(i: u32) -> f32 {
    let fi = FloatInt { i };
    // SAFETY: f32 and u32 have the same size and alignment
    unsafe { fi.f }
}
```

## Best Practices for Unsafe Code

### 1. Document Safety Invariants

```rust
/// Splits the slice into two mutable slices at the given index.
///
/// # Safety
///
/// The caller must ensure that `mid <= slice.len()`
unsafe fn split_at_mut_unchecked<T>(slice: &mut [T], mid: usize) -> (&mut [T], &mut [T]) {
    let len = slice.len();
    let ptr = slice.as_mut_ptr();
    
    // SAFETY: Caller guarantees mid <= len
    (
        std::slice::from_raw_parts_mut(ptr, mid),
        std::slice::from_raw_parts_mut(ptr.add(mid), len - mid),
    )
}
```

### 2. Minimize Unsafe Blocks

```rust
// Bad: Large unsafe block
unsafe {
    let ptr = some_function();
    // lots of safe code...
    let value = *ptr;
    // more safe code...
}

// Good: Minimal unsafe blocks
let ptr = some_function();
// safe code...
let value = unsafe { *ptr };
// more safe code...
```

### 3. Create Safe Abstractions

```rust
pub struct SafeWrapper {
    ptr: *mut u8,
    len: usize,
}

impl SafeWrapper {
    pub fn new(size: usize) -> Option<Self> {
        let layout = Layout::array::<u8>(size).ok()?;
        let ptr = unsafe { std::alloc::alloc(layout) };
        
        if ptr.is_null() {
            None
        } else {
            Some(SafeWrapper { ptr, len: size })
        }
    }
    
    pub fn get(&self, index: usize) -> Option<u8> {
        if index < self.len {
            // SAFETY: We've bounds-checked the index
            unsafe { Some(*self.ptr.add(index)) }
        } else {
            None
        }
    }
}

impl Drop for SafeWrapper {
    fn drop(&mut self) {
        let layout = Layout::array::<u8>(self.len).unwrap();
        unsafe {
            // SAFETY: ptr was allocated with this layout
            std::alloc::dealloc(self.ptr, layout);
        }
    }
}
```

### 4. Use Debug Assertions

```rust
unsafe fn unchecked_index<T>(slice: &[T], index: usize) -> &T {
    debug_assert!(index < slice.len(), "Index out of bounds");
    
    // SAFETY: In release builds, caller must ensure index is valid
    &*slice.as_ptr().add(index)
}
```

### 5. Test Unsafe Code Thoroughly

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_safe_wrapper() {
        let wrapper = SafeWrapper::new(10).unwrap();
        assert_eq!(wrapper.get(0), Some(0)); // Uninitialized memory!
        assert_eq!(wrapper.get(10), None);
    }
    
    #[test]
    #[should_panic]
    fn test_unchecked_index_debug() {
        let slice = &[1, 2, 3];
        unsafe { unchecked_index(slice, 10) };
    }
}
```

## Common Unsafe Patterns

### Pin and Unpin

```rust
use std::pin::Pin;
use std::marker::PhantomPinned;

struct SelfReferential {
    data: String,
    pointer: *const String,
    _pin: PhantomPinned,
}

impl SelfReferential {
    fn new(data: String) -> Pin<Box<Self>> {
        let mut boxed = Box::new(SelfReferential {
            data,
            pointer: std::ptr::null(),
            _pin: PhantomPinned,
        });
        
        let ptr = &boxed.data as *const String;
        unsafe {
            // SAFETY: We're pinning the box right after this
            let mut_ref = Pin::as_mut(&mut Pin::new_unchecked(boxed));
            Pin::get_unchecked_mut(mut_ref).pointer = ptr;
        }
        
        Box::into_pin(boxed)
    }
}
```

### Variance and PhantomData

```rust
use std::marker::PhantomData;

struct Invariant<T> {
    marker: PhantomData<fn(T) -> T>,
}

struct Covariant<T> {
    marker: PhantomData<T>,
}

struct Contravariant<T> {
    marker: PhantomData<fn(T)>,
}
```

## Safety Checklist

When writing unsafe code, always verify:

1. **Memory Safety**
   - No use after free
   - No double free
   - No uninitialized memory access
   - Proper alignment

2. **Thread Safety**
   - No data races
   - Proper synchronization
   - Send/Sync implementations are correct

3. **Type Safety**
   - No type confusion
   - Correct transmutations
   - Valid discriminants for enums

4. **Documentation**
   - Clear safety requirements
   - Documented invariants
   - Examples of correct usage

5. **Testing**
   - Unit tests for edge cases
   - Miri tests for undefined behavior
   - Fuzzing for robustness

## Summary

Unsafe Rust is a powerful tool that should be used judiciously. Always:
- Minimize unsafe code
- Document safety requirements
- Create safe abstractions
- Test thoroughly
- Consider alternatives before using unsafe