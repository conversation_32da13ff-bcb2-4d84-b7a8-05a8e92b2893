# Bumpalo - Fast Arena Allocation for Rust

## Overview

Bumpalo is a fast bump allocation arena for Rust that provides extremely fast allocation and bulk deallocation. It's ideal for phase-oriented allocations where many objects are allocated together and deallocated at once.

## Key Concepts

### Bump Allocation
- **Simple pointer increment** for allocation
- **No individual deallocation** - only bulk reset
- **Extremely fast** - just moves a pointer
- **No fragmentation** within active chunk
- **Ideal for temporary allocations**

### Performance Benefits
- **10-100x faster** than system allocator for small allocations
- **Predictable performance** - no malloc overhead
- **Cache-friendly** - sequential memory layout
- **Low overhead** - minimal bookkeeping

## Basic Usage

### Creating an Arena
```rust
use bumpalo::Bump;

// Create a new bump arena
let bump = Bump::new();

// Allocate objects
let x = bump.alloc(42);
let y = bump.alloc("hello");
let z = bump.alloc([1, 2, 3, 4, 5]);
```

### Allocating Different Types
```rust
#[derive(Debug)]
struct Person {
    name: String,
    age: u32,
}

let bump = Bump::new();

// Allocate primitive types
let number = bump.alloc(42i32);
let text = bump.alloc_str("Hello, world!");

// Allocate structs
let person = bump.alloc(Person {
    name: String::from("Alice"),
    age: 30,
});

// Allocate arrays
let array = bump.alloc([1, 2, 3, 4, 5]);

// Allocate slices
let slice = bump.alloc_slice_copy(&[1, 2, 3, 4, 5]);
```

### Allocation Methods

#### `alloc` - Single Value
```rust
let value = bump.alloc(42);
assert_eq!(*value, 42);
```

#### `alloc_slice_copy` - Copy Slice
```rust
let original = vec![1, 2, 3, 4, 5];
let copy = bump.alloc_slice_copy(&original);
assert_eq!(copy, &[1, 2, 3, 4, 5]);
```

#### `alloc_slice_fill_copy` - Fill with Copies
```rust
let slice = bump.alloc_slice_fill_copy(5, 42);
assert_eq!(slice, &[42, 42, 42, 42, 42]);
```

#### `alloc_slice_fill_with` - Fill with Function
```rust
let mut counter = 0;
let slice = bump.alloc_slice_fill_with(5, |_| {
    counter += 1;
    counter
});
assert_eq!(slice, &[1, 2, 3, 4, 5]);
```

#### `alloc_str` - String Slices
```rust
let s = bump.alloc_str("Hello, world!");
assert_eq!(s, "Hello, world!");
```

## Collections Support

### Bumpalo Collections
```rust
use bumpalo::collections::{Vec, String};

let bump = Bump::new();

// Vec in bump allocator
let mut vec = Vec::new_in(&bump);
vec.push(1);
vec.push(2);
vec.push(3);

// String in bump allocator
let mut string = String::new_in(&bump);
string.push_str("Hello, ");
string.push_str("world!");
```

### Collection Types
```rust
use bumpalo::collections::{Vec, String, CollectIn};

let bump = Bump::new();

// Collect iterator into bump Vec
let vec: Vec<_> = (0..10).collect_in(&bump);

// Format into bump String
let string = bumpalo::format!(in &bump, "Hello, {}!", "world");
```

## Advanced Features

### Scoped Allocations
```rust
let bump = Bump::new();

// Create a scope for temporary allocations
bump.reset();

// Allocate temporary data
let temp_data = bump.alloc_slice_fill_copy(1000, 0u8);
process_data(temp_data);

// Reset arena to reclaim all memory
bump.reset();
```

### Custom Drop with Box
```rust
use bumpalo::boxed::Box as BumpBox;

let bump = Bump::new();

// Regular allocation - no drop
let no_drop = bump.alloc(String::from("not dropped"));

// Boxed allocation - will drop
let will_drop = BumpBox::new_in(String::from("will be dropped"), &bump);

// will_drop's destructor runs when it goes out of scope
```

### Try Allocation
```rust
use bumpalo::Bump;

let bump = Bump::with_capacity(1024); // Limited capacity

// Try to allocate, may fail
match bump.try_alloc(large_data) {
    Ok(ptr) => println!("Allocation succeeded"),
    Err(_) => println!("Allocation failed - out of space"),
}
```

### Allocation Layout
```rust
use std::alloc::Layout;

let bump = Bump::new();

// Allocate with specific layout
let layout = Layout::array::<u32>(10).unwrap();
let ptr = bump.alloc_layout(layout);

// Cast and use
let array = unsafe { 
    std::slice::from_raw_parts_mut(ptr.as_ptr() as *mut u32, 10) 
};
```

## Memory Management Patterns

### Phase-Based Allocation
```rust
struct Compiler<'a> {
    bump: &'a Bump,
}

impl<'a> Compiler<'a> {
    fn compile(&self, source: &str) -> Result<(), Error> {
        // Lexing phase
        let tokens = self.lex(source)?;
        
        // Parsing phase
        let ast = self.parse(tokens)?;
        
        // Type checking phase
        let typed_ast = self.type_check(ast)?;
        
        // Code generation phase
        let code = self.generate_code(typed_ast)?;
        
        // All intermediate allocations cleaned up at once
        Ok(())
    }
    
    fn lex(&self, source: &str) -> Result<&'a [Token<'a>], Error> {
        let mut tokens = bumpalo::vec![in self.bump];
        // Tokenize and store in bump-allocated vec
        Ok(tokens.into_bump_slice())
    }
}
```

### Request-Scoped Allocation
```rust
async fn handle_request(req: Request) -> Response {
    // Create arena for this request
    let bump = Bump::new();
    
    // All allocations for this request use the arena
    let parsed = parse_request(&bump, req);
    let validated = validate(&bump, parsed);
    let result = process(&bump, validated);
    
    // Convert to owned response
    let response = create_response(result);
    
    // Arena automatically cleaned up
    response
}
```

### Tree Building
```rust
#[derive(Debug)]
enum Expr<'a> {
    Number(i32),
    Add(&'a Expr<'a>, &'a Expr<'a>),
    Mul(&'a Expr<'a>, &'a Expr<'a>),
}

fn build_expr_tree<'a>(bump: &'a Bump, input: &str) -> &'a Expr<'a> {
    // Parse and build tree using arena
    let left = bump.alloc(Expr::Number(2));
    let right = bump.alloc(Expr::Number(3));
    let add = bump.alloc(Expr::Add(left, right));
    
    let factor = bump.alloc(Expr::Number(4));
    bump.alloc(Expr::Mul(add, factor))
}
```

## Performance Optimization

### Pre-allocation
```rust
// Pre-allocate arena with expected size
let bump = Bump::with_capacity(1024 * 1024); // 1MB

// Bulk operations without reallocation
for i in 0..10000 {
    bump.alloc(i);
}
```

### Chunk Size Configuration
```rust
use bumpalo::{Bump, ChunkSize};

// Configure chunk size for allocation pattern
let bump = Bump::with_capacity(ChunkSize::new(64 * 1024)); // 64KB chunks
```

### Reusing Arenas
```rust
let mut bump = Bump::new();

for request in requests {
    // Process request
    handle_request(&bump, request);
    
    // Reset for next request
    bump.reset();
}
```

### Allocation Strategies
```rust
// Strategy 1: Many small allocations
fn process_tokens<'a>(bump: &'a Bump, text: &str) -> Vec<&'a str> {
    text.split_whitespace()
        .map(|word| bump.alloc_str(word))
        .collect()
}

// Strategy 2: Single large allocation
fn process_tokens_optimized<'a>(bump: &'a Bump, text: &str) -> &'a [&'a str] {
    let words: Vec<_> = text.split_whitespace().collect();
    let slice = bump.alloc_slice_fill_with(words.len(), |i| {
        bump.alloc_str(words[i])
    });
    slice
}
```

## Integration Examples

### With Serde
```rust
use serde::{Deserialize, Serialize};
use bumpalo::Bump;

#[derive(Serialize, Deserialize)]
struct Data {
    name: String,
    values: Vec<i32>,
}

fn deserialize_in_arena<'a>(bump: &'a Bump, json: &str) -> Result<&'a Data, Error> {
    // Deserialize directly into arena
    let data: Data = serde_json::from_str(json)?;
    Ok(bump.alloc(data))
}
```

### With Parser Combinators
```rust
use nom::IResult;

fn parse_in_arena<'a>(bump: &'a Bump, input: &'a str) -> IResult<&'a str, Expr<'a>> {
    // All parser allocations use the arena
    let (input, left) = parse_term(bump, input)?;
    let (input, _) = tag("+")(input)?;
    let (input, right) = parse_term(bump, input)?;
    
    Ok((input, *bump.alloc(Expr::Add(left, right))))
}
```

### With Async Code
```rust
async fn process_async<'a>(bump: &'a Bump, data: &[u8]) -> Result<&'a ProcessedData, Error> {
    // Arena persists across await points
    let parsed = parse_data(bump, data).await?;
    let validated = validate(bump, parsed).await?;
    let processed = bump.alloc(process(validated));
    Ok(processed)
}
```

## Best Practices

### 1. Lifetime Management
```rust
// Good: Clear lifetime bounds
fn process<'a>(bump: &'a Bump, input: &str) -> &'a str {
    bump.alloc_str(&input.to_uppercase())
}

// Bad: Unclear lifetimes
fn process_bad(bump: &Bump, input: &str) -> &str {
    bump.alloc_str(&input.to_uppercase()) // Lifetime error
}
```

### 2. Reset Points
```rust
fn process_batches(batches: Vec<Batch>) {
    let bump = Bump::new();
    
    for batch in batches {
        process_batch(&bump, batch);
        
        // Reset after each batch
        bump.reset();
    }
}
```

### 3. Avoid Unnecessary Allocations
```rust
// Good: Reuse arena allocation
let bump = Bump::new();
let buffer = bump.alloc_slice_fill_copy(1024, 0u8);
for i in 0..100 {
    process_with_buffer(buffer);
}

// Bad: Allocate in loop
for i in 0..100 {
    let buffer = bump.alloc_slice_fill_copy(1024, 0u8);
    process_with_buffer(buffer);
}
```

### 4. Size Estimation
```rust
fn estimate_arena_size(input: &Input) -> usize {
    let base_size = std::mem::size_of::<Node>() * input.expected_nodes();
    let string_size = input.total_string_length();
    let overhead = (base_size + string_size) / 10; // 10% overhead
    
    base_size + string_size + overhead
}

let bump = Bump::with_capacity(estimate_arena_size(&input));
```

### 5. Error Handling
```rust
fn safe_allocation<'a>(bump: &'a Bump, size: usize) -> Result<&'a mut [u8], Error> {
    bump.try_alloc_slice_fill_copy(size, 0)
        .map_err(|_| Error::OutOfMemory)
}
```

## Common Use Cases

### 1. Compilers and Interpreters
- AST nodes
- Symbol tables
- Intermediate representations
- Type information

### 2. Parsers
- Parse trees
- Token storage
- Temporary buffers
- Error messages

### 3. Request Processing
- HTTP request parsing
- Response building
- Temporary computations
- Session data

### 4. Game Development
- Frame allocations
- Temporary geometry
- Particle systems
- UI layout

### 5. Data Processing
- Batch processing
- Stream processing
- Temporary transformations
- Intermediate results

## Performance Comparison

### Benchmark Example
```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn bench_allocators(c: &mut Criterion) {
    let mut group = c.benchmark_group("allocators");
    
    // System allocator
    group.bench_function("system", |b| {
        b.iter(|| {
            let mut vec = Vec::new();
            for i in 0..1000 {
                vec.push(i);
            }
            black_box(vec);
        })
    });
    
    // Bump allocator
    group.bench_function("bump", |b| {
        let bump = Bump::new();
        b.iter(|| {
            let mut vec = bumpalo::vec![in &bump];
            for i in 0..1000 {
                vec.push(i);
            }
            black_box(vec);
            bump.reset();
        })
    });
    
    group.finish();
}
```

## Summary

Bumpalo provides:
- **Extremely fast allocation** through pointer bumping
- **Bulk deallocation** for phase-oriented memory use
- **Zero overhead** for allocation-heavy workloads
- **Simple API** that integrates well with Rust
- **Predictable performance** characteristics

Use Bumpalo when:
- Many allocations with similar lifetimes
- Performance is critical
- Allocation pattern is phase-based
- Individual deallocation isn't needed
- Working with trees or graphs