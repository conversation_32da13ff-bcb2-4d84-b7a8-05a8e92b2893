# Rust Memory Optimization Guide

## Overview

Memory optimization in Rust involves understanding allocation patterns, choosing appropriate data structures, and leveraging Rust's ownership system for efficient memory usage. This guide covers strategies, tools, and patterns for optimizing memory in Rust applications.

## Memory Layout and Alignment

### Understanding Size and Alignment
```rust
use std::mem;

#[repr(C)]
struct Aligned {
    a: u8,    // 1 byte
    b: u32,   // 4 bytes (needs 4-byte alignment)
    c: u8,    // 1 byte
} // Total: 12 bytes (with padding)

#[repr(packed)]
struct Packed {
    a: u8,    // 1 byte
    b: u32,   // 4 bytes
    c: u8,    // 1 byte  
} // Total: 6 bytes (no padding)

fn analyze_layout() {
    println!("Aligned size: {}", mem::size_of::<Aligned>()); // 12
    println!("Packed size: {}", mem::size_of::<Packed>());   // 6
    
    // Check alignment
    println!("u32 alignment: {}", mem::align_of::<u32>());   // 4
}
```

### Optimizing Struct Layout
```rust
// Bad: Poor field ordering causes padding
struct Inefficient {
    a: u8,     // 1 byte + 7 padding
    b: u64,    // 8 bytes
    c: u8,     // 1 byte + 7 padding
    d: u64,    // 8 bytes
} // Total: 32 bytes

// Good: Optimal field ordering
struct Efficient {
    b: u64,    // 8 bytes
    d: u64,    // 8 bytes
    a: u8,     // 1 byte
    c: u8,     // 1 byte + 6 padding
} // Total: 24 bytes

// Use #[repr(C)] for predictable layout
#[repr(C)]
struct Predictable {
    header: u32,
    data: [u8; 64],
    footer: u32,
}
```

## Small String Optimization

### Custom Small String
```rust
use std::mem::MaybeUninit;

const SMALL_STRING_CAPACITY: usize = 23;

enum SmallString {
    Inline {
        len: u8,
        data: [u8; SMALL_STRING_CAPACITY],
    },
    Heap(String),
}

impl SmallString {
    fn new(s: &str) -> Self {
        let bytes = s.as_bytes();
        if bytes.len() <= SMALL_STRING_CAPACITY {
            let mut data = [0u8; SMALL_STRING_CAPACITY];
            data[..bytes.len()].copy_from_slice(bytes);
            SmallString::Inline {
                len: bytes.len() as u8,
                data,
            }
        } else {
            SmallString::Heap(s.to_string())
        }
    }
    
    fn as_str(&self) -> &str {
        match self {
            SmallString::Inline { len, data } => {
                unsafe { std::str::from_utf8_unchecked(&data[..*len as usize]) }
            }
            SmallString::Heap(s) => s.as_str(),
        }
    }
}
```

### Using SmallVec
```rust
use smallvec::{SmallVec, smallvec};

// Store up to 32 items inline
type SmallBuffer = SmallVec<[u8; 32]>;

fn process_data() {
    // No heap allocation for small data
    let mut buffer: SmallBuffer = smallvec![1, 2, 3, 4, 5];
    
    // Grows to heap if needed
    for i in 6..100 {
        buffer.push(i);
    }
}
```

## Zero-Copy Techniques

### Cow (Clone on Write)
```rust
use std::borrow::Cow;

fn process_string(input: &str) -> Cow<str> {
    if input.contains("old") {
        // Only allocates when modification needed
        Cow::Owned(input.replace("old", "new"))
    } else {
        // No allocation
        Cow::Borrowed(input)
    }
}

// Zero-copy parsing
struct Parser<'a> {
    input: &'a str,
}

impl<'a> Parser<'a> {
    fn parse_word(&mut self) -> Option<&'a str> {
        let start = self.position;
        while self.position < self.input.len() 
            && !self.input.as_bytes()[self.position].is_ascii_whitespace() {
            self.position += 1;
        }
        
        if start < self.position {
            Some(&self.input[start..self.position])
        } else {
            None
        }
    }
}
```

### Memory Mapping
```rust
use memmap2::{Mmap, MmapOptions};
use std::fs::File;

fn process_large_file(path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let file = File::open(path)?;
    
    // Memory map the file - no copying
    let mmap = unsafe { MmapOptions::new().map(&file)? };
    
    // Process data without loading into memory
    for chunk in mmap.chunks(1024 * 1024) {
        process_chunk(chunk);
    }
    
    Ok(())
}
```

## Custom Allocators

### Using jemalloc
```toml
[dependencies]
jemallocator = "0.5"

[profile.release]
lto = true
```

```rust
use jemallocator::Jemalloc;

#[global_allocator]
static GLOBAL: Jemalloc = Jemalloc;

fn main() {
    // Application now uses jemalloc
}
```

### Arena Allocators Comparison
```rust
// Bumpalo - Multiple types, reset capability
use bumpalo::Bump;

fn with_bumpalo() {
    let bump = Bump::new();
    let x = bump.alloc(42);
    let y = bump.alloc("hello");
    let z = bump.alloc([1, 2, 3]);
    // All freed at once when bump drops
}

// Typed-arena - Single type, safe cycles
use typed_arena::Arena;

fn with_typed_arena() {
    let arena = Arena::new();
    let nodes: Vec<_> = (0..100)
        .map(|i| arena.alloc(Node { value: i, next: None }))
        .collect();
    // All nodes freed when arena drops
}
```

## Lazy Initialization

### Once Cell
```rust
use once_cell::sync::Lazy;
use std::collections::HashMap;

static GLOBAL_DATA: Lazy<HashMap<String, String>> = Lazy::new(|| {
    let mut m = HashMap::new();
    m.insert("key".to_string(), "value".to_string());
    m
});

fn use_global_data() {
    // Initialized on first access
    println!("{:?}", GLOBAL_DATA.get("key"));
}
```

### Lazy Static
```rust
use lazy_static::lazy_static;
use regex::Regex;

lazy_static! {
    static ref EMAIL_REGEX: Regex = Regex::new(
        r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    ).unwrap();
}

fn validate_email(email: &str) -> bool {
    EMAIL_REGEX.is_match(email)
}
```

## Memory Pooling

### Object Pool Pattern
```rust
use std::sync::{Arc, Mutex};

struct Pool<T> {
    items: Arc<Mutex<Vec<T>>>,
    factory: Box<dyn Fn() -> T + Send + Sync>,
}

impl<T> Pool<T> {
    fn new(factory: impl Fn() -> T + Send + Sync + 'static) -> Self {
        Pool {
            items: Arc::new(Mutex::new(Vec::new())),
            factory: Box::new(factory),
        }
    }
    
    fn get(&self) -> PoolGuard<T> {
        let item = self.items.lock().unwrap().pop()
            .unwrap_or_else(|| (self.factory)());
        
        PoolGuard {
            item: Some(item),
            pool: Arc::clone(&self.items),
        }
    }
}

struct PoolGuard<T> {
    item: Option<T>,
    pool: Arc<Mutex<Vec<T>>>,
}

impl<T> Drop for PoolGuard<T> {
    fn drop(&mut self) {
        if let Some(item) = self.item.take() {
            self.pool.lock().unwrap().push(item);
        }
    }
}

impl<T> std::ops::Deref for PoolGuard<T> {
    type Target = T;
    
    fn deref(&self) -> &Self::Target {
        self.item.as_ref().unwrap()
    }
}
```

## Profiling and Measurement

### Using Allocator Stats
```rust
use std::alloc::{GlobalAlloc, Layout, System};
use std::sync::atomic::{AtomicUsize, Ordering};

struct CountingAllocator;

static ALLOCATED: AtomicUsize = AtomicUsize::new(0);

unsafe impl GlobalAlloc for CountingAllocator {
    unsafe fn alloc(&self, layout: Layout) -> *mut u8 {
        let size = layout.size();
        let ptr = System.alloc(layout);
        if !ptr.is_null() {
            ALLOCATED.fetch_add(size, Ordering::Relaxed);
        }
        ptr
    }
    
    unsafe fn dealloc(&self, ptr: *mut u8, layout: Layout) {
        System.dealloc(ptr, layout);
        ALLOCATED.fetch_sub(layout.size(), Ordering::Relaxed);
    }
}

#[global_allocator]
static GLOBAL: CountingAllocator = CountingAllocator;

fn print_memory_usage() {
    let bytes = ALLOCATED.load(Ordering::Relaxed);
    println!("Currently allocated: {} bytes", bytes);
}
```

### Heap Profiling with DHAT
```rust
#[cfg(feature = "dhat-heap")]
#[global_allocator]
static ALLOC: dhat::Alloc = dhat::Alloc;

fn main() {
    #[cfg(feature = "dhat-heap")]
    let _profiler = dhat::Profiler::new_heap();
    
    // Run application
    
    #[cfg(feature = "dhat-heap")]
    println!("Peak allocated: {} bytes", dhat::Profiler::peak_allocated());
}
```

## Common Optimization Patterns

### 1. String Interning
```rust
use std::collections::HashSet;
use std::sync::RwLock;

lazy_static! {
    static ref INTERNED: RwLock<HashSet<&'static str>> = RwLock::new(HashSet::new());
}

fn intern(s: &str) -> &'static str {
    {
        let set = INTERNED.read().unwrap();
        if let Some(&interned) = set.get(s) {
            return interned;
        }
    }
    
    let s = Box::leak(s.to_string().into_boxed_str());
    INTERNED.write().unwrap().insert(s);
    s
}
```

### 2. Capacity Planning
```rust
// Bad: Multiple reallocations
let mut vec = Vec::new();
for i in 0..1000 {
    vec.push(i);
}

// Good: Pre-allocate capacity
let mut vec = Vec::with_capacity(1000);
for i in 0..1000 {
    vec.push(i);
}

// For HashMaps
let mut map = HashMap::with_capacity(expected_entries);
```

### 3. Avoid Unnecessary Clones
```rust
// Bad: Unnecessary clone
fn process_bad(data: Vec<String>) -> Vec<String> {
    data.clone().into_iter().map(|s| s.to_uppercase()).collect()
}

// Good: Take ownership or use references
fn process_good(data: Vec<String>) -> Vec<String> {
    data.into_iter().map(|s| s.to_uppercase()).collect()
}

fn process_ref(data: &[String]) -> Vec<String> {
    data.iter().map(|s| s.to_uppercase()).collect()
}
```

### 4. Buffer Reuse
```rust
struct Processor {
    buffer: Vec<u8>,
}

impl Processor {
    fn process(&mut self, data: &[u8]) {
        self.buffer.clear();
        self.buffer.extend_from_slice(data);
        // Process buffer
    }
}
```

### 5. Compact Data Structures
```rust
// Use bitfields for flags
use bitflags::bitflags;

bitflags! {
    struct Flags: u8 {
        const READ = 0b00000001;
        const WRITE = 0b00000010;
        const EXECUTE = 0b00000100;
    }
}

// Use enums with small discriminants
#[repr(u8)]
enum State {
    Idle = 0,
    Running = 1,
    Stopped = 2,
}
```

## Best Practices

1. **Profile First** - Measure before optimizing
2. **Use Appropriate Data Structures** - Vec vs VecDeque vs LinkedList
3. **Minimize Allocations** - Reuse buffers, use arenas
4. **Leverage Zero-Copy** - Use Cow, slices, and views
5. **Plan Capacity** - Pre-allocate collections
6. **Consider Cache Efficiency** - Keep hot data together
7. **Use Const Generics** - For compile-time sized arrays
8. **Batch Operations** - Reduce allocation frequency

## Tools and Resources

### Profiling Tools
- **valgrind** - Memory leak detection
- **heaptrack** - Heap profiling
- **perf** - System-wide profiling
- **cargo-flamegraph** - Flame graphs
- **dhat** - Heap profiling for Rust

### Useful Crates
- **bumpalo** - Fast bump allocation
- **typed-arena** - Type-safe arena allocation
- **smallvec** - Small vector optimization
- **arrayvec** - Stack-allocated vectors
- **bytes** - Efficient byte buffers
- **memmap2** - Memory-mapped files
- **crossbeam** - Concurrent data structures

## Summary

Memory optimization in Rust involves:
- Understanding memory layout and alignment
- Choosing appropriate allocation strategies
- Using zero-copy techniques
- Leveraging arena allocators for bulk allocation
- Profiling and measuring memory usage
- Following established patterns and best practices

The key is to measure first, optimize strategically, and leverage Rust's ownership system for efficient memory management.