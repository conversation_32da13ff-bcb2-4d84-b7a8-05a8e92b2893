# Typed-Arena - Type-Safe Arena Allocation

## Overview

Typed-arena is a fast, type-safe arena allocator for Rust that allocates objects of a single type. It provides extremely fast allocation at the cost of not being able to deallocate individual objects - all objects are deallocated together when the arena is dropped.

## Key Features

- **Single-type allocation** - Each arena allocates objects of one type
- **Fast allocation** - Usually just a vector push
- **Safe cycles** - Can create reference cycles safely
- **Lifetime safety** - Rust's lifetime system prevents use-after-free
- **Bulk deallocation** - All objects freed at once

## Basic Usage

### Creating an Arena
```rust
use typed_arena::Arena;

#[derive(Debug)]
struct Monster {
    name: String,
    level: u32,
    health: i32,
}

// Create arena for Monster type
let arena = Arena::new();

// Allocate monsters
let goblin = arena.alloc(Monster {
    name: "Goblin".to_string(),
    level: 5,
    health: 50,
});

let dragon = arena.alloc(Monster {
    name: "Dragon".to_string(),
    level: 50,
    health: 5000,
});
```

### Allocation Methods

#### Single Allocation
```rust
let arena = Arena::new();
let value = arena.alloc(42);
assert_eq!(*value, 42);
```

#### Extend from Iterator
```rust
let arena = Arena::new();
let values: Vec<&i32> = arena.alloc_extend(0..10).collect();
assert_eq!(values.len(), 10);
```

#### Uninitialized Allocation
```rust
use std::mem::MaybeUninit;

let arena = Arena::new();
let uninit = arena.alloc_uninitialized(MaybeUninit::<String>::uninit());
let value = unsafe {
    uninit.write("Hello".to_string());
    uninit.assume_init_ref()
};
```

## Advanced Patterns

### Building Trees
```rust
use typed_arena::Arena;

#[derive(Debug)]
enum Expr<'a> {
    Value(i32),
    Add(&'a Expr<'a>, &'a Expr<'a>),
    Mul(&'a Expr<'a>, &'a Expr<'a>),
}

fn build_expression_tree<'a>(arena: &'a Arena<Expr<'a>>) -> &'a Expr<'a> {
    let two = arena.alloc(Expr::Value(2));
    let three = arena.alloc(Expr::Value(3));
    let sum = arena.alloc(Expr::Add(two, three));
    
    let four = arena.alloc(Expr::Value(4));
    arena.alloc(Expr::Mul(sum, four)) // (2 + 3) * 4
}
```

### Creating Safe Cycles
```rust
use std::cell::Cell;

struct Node<'a> {
    value: i32,
    next: Cell<Option<&'a Node<'a>>>,
    prev: Cell<Option<&'a Node<'a>>>,
}

fn create_circular_list<'a>(arena: &'a Arena<Node<'a>>) -> &'a Node<'a> {
    let node1 = arena.alloc(Node {
        value: 1,
        next: Cell::new(None),
        prev: Cell::new(None),
    });
    
    let node2 = arena.alloc(Node {
        value: 2,
        next: Cell::new(None),
        prev: Cell::new(None),
    });
    
    let node3 = arena.alloc(Node {
        value: 3,
        next: Cell::new(None),
        prev: Cell::new(None),
    });
    
    // Create circular references
    node1.next.set(Some(node2));
    node2.next.set(Some(node3));
    node3.next.set(Some(node1));
    
    node1.prev.set(Some(node3));
    node2.prev.set(Some(node1));
    node3.prev.set(Some(node2));
    
    node1
}
```

### Graph Structures
```rust
use std::cell::RefCell;

struct GraphNode<'a> {
    id: usize,
    edges: RefCell<Vec<&'a GraphNode<'a>>>,
}

fn build_graph<'a>(arena: &'a Arena<GraphNode<'a>>) -> Vec<&'a GraphNode<'a>> {
    let nodes: Vec<_> = (0..5)
        .map(|i| arena.alloc(GraphNode {
            id: i,
            edges: RefCell::new(Vec::new()),
        }))
        .collect();
    
    // Add edges
    nodes[0].edges.borrow_mut().push(nodes[1]);
    nodes[0].edges.borrow_mut().push(nodes[2]);
    nodes[1].edges.borrow_mut().push(nodes[3]);
    nodes[2].edges.borrow_mut().push(nodes[3]);
    nodes[3].edges.borrow_mut().push(nodes[4]);
    
    nodes
}
```

## Memory Management

### Pre-allocation
```rust
// Create arena with initial capacity
let arena = Arena::with_capacity(1000);

// Allocate without reallocation for first 1000 items
for i in 0..1000 {
    arena.alloc(i);
}
```

### Getting Allocated Items
```rust
let arena = Arena::new();

// Allocate some items
for i in 0..10 {
    arena.alloc(i * i);
}

// Convert to Vec and take ownership
let values: Vec<i32> = arena.into_vec();
assert_eq!(values.len(), 10);
```

### Memory Chunks
```rust
// Arena allocates in chunks for efficiency
let arena = Arena::new();

// First allocation creates a chunk
let first = arena.alloc(1);

// Subsequent allocations use the same chunk until full
for i in 2..100 {
    arena.alloc(i);
}
// When chunk is full, a new larger chunk is allocated
```

## Performance Optimization

### Benchmarking Arena vs Box
```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};
use typed_arena::Arena;

fn bench_allocation(c: &mut Criterion) {
    let mut group = c.benchmark_group("allocation");
    
    // Box allocation
    group.bench_function("box", |b| {
        b.iter(|| {
            let mut boxes = Vec::new();
            for i in 0..1000 {
                boxes.push(Box::new(i));
            }
            black_box(boxes);
        })
    });
    
    // Arena allocation
    group.bench_function("arena", |b| {
        b.iter(|| {
            let arena = Arena::new();
            let mut refs = Vec::new();
            for i in 0..1000 {
                refs.push(arena.alloc(i));
            }
            black_box(refs);
        })
    });
    
    group.finish();
}
```

### Optimal Chunk Sizes
```rust
// For many small allocations
let arena = Arena::with_capacity(10000);

// For fewer large allocations
let arena = Arena::with_capacity(100);

// Let arena determine chunk size
let arena = Arena::new();
```

## Use Cases

### Compiler AST
```rust
#[derive(Debug)]
enum Statement<'a> {
    Let { name: &'a str, value: &'a Expr<'a> },
    Return(&'a Expr<'a>),
    Block(Vec<&'a Statement<'a>>),
}

#[derive(Debug)]
enum Expr<'a> {
    Literal(i32),
    Variable(&'a str),
    Binary {
        op: BinaryOp,
        left: &'a Expr<'a>,
        right: &'a Expr<'a>,
    },
}

fn parse_program<'a>(arena: &'a Arena<Statement<'a>>, input: &str) -> &'a Statement<'a> {
    // Parse and allocate AST nodes in arena
    // All nodes have the same lifetime as the arena
}
```

### Game Entity System
```rust
struct Entity<'a> {
    id: u64,
    position: (f32, f32),
    components: Vec<&'a dyn Component>,
}

trait Component {
    fn update(&mut self, delta: f32);
}

struct World<'a> {
    entities: Arena<Entity<'a>>,
    components: Arena<Box<dyn Component>>,
}

impl<'a> World<'a> {
    fn spawn_entity(&'a self) -> &'a Entity<'a> {
        self.entities.alloc(Entity {
            id: generate_id(),
            position: (0.0, 0.0),
            components: Vec::new(),
        })
    }
}
```

### String Interning
```rust
use std::collections::HashMap;

struct StringInterner<'a> {
    arena: Arena<String>,
    map: HashMap<&'a str, &'a str>,
}

impl<'a> StringInterner<'a> {
    fn new() -> Self {
        Self {
            arena: Arena::new(),
            map: HashMap::new(),
        }
    }
    
    fn intern(&'a mut self, s: &str) -> &'a str {
        if let Some(&interned) = self.map.get(s) {
            return interned;
        }
        
        let interned = self.arena.alloc(s.to_string());
        self.map.insert(interned, interned);
        interned
    }
}
```

## Comparison with Other Allocators

### vs Bumpalo
```rust
// Bumpalo - Multiple types, very fast
let bump = Bump::new();
let x = bump.alloc(42);
let y = bump.alloc("hello");

// Typed-arena - Single type, type-safe
let arena = Arena::<i32>::new();
let x = arena.alloc(42);
// let y = arena.alloc("hello"); // Error: wrong type
```

### vs Standard Allocation
```rust
// Standard allocation - Individual deallocation
let mut vec = Vec::new();
for i in 0..1000 {
    vec.push(Box::new(i));
}
// Each Box individually deallocated

// Arena - Bulk deallocation
let arena = Arena::new();
for i in 0..1000 {
    arena.alloc(i);
}
// All deallocated when arena drops
```

## Best Practices

### 1. Lifetime Annotations
```rust
// Good: Clear lifetime relationship
fn process<'a>(arena: &'a Arena<Data>) -> &'a Data {
    arena.alloc(Data::new())
}

// Bad: Unclear lifetimes
fn process_bad(arena: &Arena<Data>) -> &Data {
    arena.alloc(Data::new()) // Lifetime error
}
```

### 2. Arena Scope
```rust
fn process_batch(items: Vec<Item>) -> Vec<Result> {
    let arena = Arena::new(); // Arena for this batch
    
    let results = items.iter().map(|item| {
        let temp = arena.alloc(process_item(item));
        calculate_result(temp)
    }).collect();
    
    results // Arena dropped, memory freed
}
```

### 3. Avoid Mixed Allocations
```rust
// Good: Separate arenas for different types
let nodes = Arena::<Node>::new();
let edges = Arena::<Edge>::new();

// Bad: Trying to mix types
// let mixed = Arena::new();
// mixed.alloc(Node { ... }); // Ok
// mixed.alloc(Edge { ... }); // Error: wrong type
```

### 4. Pre-size When Possible
```rust
fn build_tree(size_hint: usize) -> Tree {
    let arena = Arena::with_capacity(size_hint);
    // Build tree without reallocation
}
```

### 5. Use for Temporary Data
```rust
fn compute_result(input: &Input) -> Output {
    let arena = Arena::new();
    
    // Temporary computations
    let temp1 = arena.alloc(expensive_computation1(input));
    let temp2 = arena.alloc(expensive_computation2(temp1));
    let temp3 = arena.alloc(expensive_computation3(temp2));
    
    // Final result (owned)
    create_output(temp3)
} // Arena and temporaries dropped
```

## Common Pitfalls

### 1. Lifetime Confusion
```rust
// Wrong: Trying to outlive the arena
fn bad_pattern() -> &'static i32 {
    let arena = Arena::new();
    arena.alloc(42) // Error: arena dropped
}

// Correct: Arena lives long enough
struct Container<'a> {
    arena: Arena<i32>,
    value: Option<&'a i32>,
}
```

### 2. Forgetting Type Constraints
```rust
// Arena can only hold one type
let arena = Arena::<i32>::new();
arena.alloc(42); // Ok
// arena.alloc(3.14); // Error: expected i32, found f64
```

### 3. Memory Leaks with Cycles
```rust
// Even with cycles, memory is freed when arena drops
struct LeakyNode<'a> {
    data: String,
    next: Cell<Option<&'a LeakyNode<'a>>>,
}

{
    let arena = Arena::new();
    let node1 = arena.alloc(LeakyNode { 
        data: "A".to_string(), 
        next: Cell::new(None) 
    });
    let node2 = arena.alloc(LeakyNode { 
        data: "B".to_string(), 
        next: Cell::new(Some(node1)) 
    });
    node1.next.set(Some(node2)); // Cycle created
} // Everything freed here, no leak
```

## Performance Tips

1. **Batch Allocations** - Allocate related objects together
2. **Reuse Arenas** - Clear and reuse for similar workloads
3. **Size Appropriately** - Use with_capacity when size is known
4. **Minimize Copies** - Allocate final values directly
5. **Profile Usage** - Measure allocation patterns

## Summary

Typed-arena provides:
- **Type-safe** arena allocation
- **Fast allocation** performance
- **Safe cycles** and complex structures
- **Simple API** with lifetime safety
- **Efficient memory** usage

Best for:
- AST and tree structures
- Graph algorithms
- Temporary computations
- Compiler intermediate representations
- Game entity systems