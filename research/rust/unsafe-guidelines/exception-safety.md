# Exception Safety in Unsafe Rust

## Overview
Exception safety ensures that code maintains invariants and memory safety even when panics occur. This is critical for unsafe code that may temporarily violate invariants.

## Two Levels of Exception Safety

### 1. Minimal Exception Safety (Memory Safety)
- **Required for all unsafe code**
- Must not cause undefined behavior during unwinding
- Memory must remain in a valid state
- No use-after-free, double-free, or memory leaks

### 2. Maximal Exception Safety (Logic Safety)
- **Required for safe abstractions**
- Program remains in a logically consistent state
- All invariants are maintained or restored
- Operations are atomic - they either complete or have no effect

## Critical Considerations

### Transient Unsound States
Unsafe code often temporarily violates invariants:
```rust
// BAD: Panic between these operations leaves invalid state
vec.set_len(new_len);  // Temporarily invalid
initialize_elements();  // Could panic!
```

### Panic Points
Identify all operations that could panic:
- User-provided closures or trait implementations
- Arithmetic operations (overflow in debug mode)
- Allocation failures
- Explicit panics or assertions

## Strategies for Exception Safety

### 1. Separate Panic-Prone Operations
```rust
// GOOD: Complete all possibly-panicking work first
let new_elements = compute_elements()?;  // Might panic
unsafe {
    // SAFETY: No panics possible in this block
    ptr::copy_nonoverlapping(src, dst, len);
    vec.set_len(new_len);
}
```

### 2. Use Drop Guards
```rust
struct Guard<'a> {
    vec: &'a mut Vec<u8>,
    old_len: usize,
}

impl Drop for Guard<'_> {
    fn drop(&mut self) {
        unsafe {
            // SAFETY: Restore valid state on panic
            self.vec.set_len(self.old_len);
        }
    }
}
```

### 3. The Hole Pattern (from BinaryHeap)
```rust
struct Hole<'a, T> {
    data: &'a mut [T],
    pos: usize,
    element: ManuallyDrop<T>,
}

impl<T> Drop for Hole<'_, T> {
    fn drop(&mut self) {
        unsafe {
            // SAFETY: Always restore the element on drop
            let element = ManuallyDrop::take(&mut self.element);
            ptr::write(self.data.get_unchecked_mut(self.pos), element);
        }
    }
}
```

## Documentation Requirements

### SAFETY Comments for Panic Safety
```rust
unsafe {
    // SAFETY: Exception safe because:
    // - All panicking operations completed before this block
    // - Guard ensures cleanup on panic
    // - No user code executes during invariant violation
    perform_unsafe_operation();
}
```

### Function Documentation
```rust
/// Inserts an element at position `index`.
///
/// # Panics
/// 
/// Panics if `index > len`.
///
/// # Exception Safety
///
/// If the closure panics, the collection remains in a valid
/// state with its original contents. The operation has no
/// effect if it doesn't complete successfully.
pub fn insert_with<F>(&mut self, index: usize, f: F) -> T 
where
    F: FnOnce() -> T
{
    // Implementation
}
```

## Common Patterns

### Backup and Restore
```rust
unsafe fn risky_operation(&mut self) {
    // Backup state
    let backup = self.clone_state();
    
    // Create drop guard to restore on panic
    let _guard = Guard { target: self, backup };
    
    // Perform operations that might panic
    self.do_risky_stuff();
    
    // Disarm guard on success
    mem::forget(_guard);
}
```

### Two-Phase Operations
```rust
// Phase 1: All fallible operations
let computed = expensive_computation()?;
let validated = validate_input(input)?;

// Phase 2: Infallible unsafe operations
unsafe {
    // SAFETY: No panics possible, all validation done
    commit_changes(computed, validated);
}
```

## Testing Exception Safety

### Panic Injection
```rust
#[cfg(test)]
mod tests {
    #[test]
    #[should_panic]
    fn test_panic_safety() {
        let mut container = Container::new();
        
        // Inject panic
        container.insert_with(|| {
            if true { panic!("injected"); }
            42
        });
    }
    
    #[test]
    fn test_cleanup_after_panic() {
        let mut container = Container::new();
        
        // Attempt panicking operation
        let _ = std::panic::catch_unwind(|| {
            container.risky_operation();
        });
        
        // Verify valid state after panic
        assert!(container.is_valid());
    }
}
```

## Key Rules

1. **Never expose transient invalid states to user code**
2. **Use drop guards to ensure cleanup**
3. **Separate panicking and non-panicking code**
4. **Document panic conditions and guarantees**
5. **Test panic scenarios explicitly**
6. **Prefer strong exception safety when possible**