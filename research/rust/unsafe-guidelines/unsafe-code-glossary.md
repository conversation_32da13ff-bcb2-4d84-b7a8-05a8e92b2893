# Unsafe Code Guidelines Glossary

## Core Safety Concepts

### Undefined Behavior (UB)
- A contract between programmer and compiler
- If UB occurs, "the program produced by the compiler is essentially garbage"
- Safe code cannot cause UB; unsafe code requires programmer diligence
- Key principle: The compiler assumes UB will never occur and optimizes accordingly

### Soundness
- Ensures "well-typed programs cannot cause Undefined Behavior"
- Applies specifically to safe code
- A library/function is sound if safe code cannot trigger UB
- Critical for API design: unsafe functions must document conditions to avoid UB

## Invariants and Safety

### Validity and Safety Invariants
- **Validity Invariant**: Data must be valid when accessed or copied
- **Safety Invariant**: Safe code can assume certain data properties
- Key distinction: "Data must always be valid, but it only must be safe in safe code"
- Documentation requirement: Unsafe code must document which invariants it maintains

### Interior Mutability
- Mutating memory with an existing shared reference
- Requires using `UnsafeCell`
- Involves "mutating memory where there also exists a live shared reference"
- Safety documentation must explain how mutations preserve invariants

## Memory and Pointer Concepts

### Pointer Provenance
- Extra state in Rust's Abstract Machine
- Distinguishes pointers with same memory address
- Tracks allocation origins and potential aliasing rules
- Documentation should clarify provenance assumptions in unsafe code

### Aliasing
- Occurs when pointers/references overlap memory spans
- Depends on memory "span length" of referenced types
- Zero-sized types never alias each other
- Unsafe code must document aliasing assumptions and guarantees

## Documentation Requirements
When writing unsafe code, documentation should:
1. Explain why the code is safe despite being unsafe
2. List all assumptions about validity and safety invariants
3. Document any aliasing or provenance requirements
4. Specify conditions that would cause undefined behavior