# RFC 2585: Unsafe Block in Unsafe Function

## Summary
This RFC proposes changing how unsafe operations are handled within unsafe functions by requiring explicit `unsafe {}` blocks even inside unsafe functions.

## Key Concepts

### Current Behavior vs Proposed Change
- **Current**: The body of an unsafe function is implicitly treated as an unsafe block
- **Proposed**: Explicit `unsafe {}` blocks required for unsafe operations, even in unsafe functions

### Two Roles of `unsafe` Keyword
The RFC identifies that `unsafe` serves two distinct purposes:
1. **Defining proof obligations** - Making something "unsafe to call"
2. **Discharging proof obligations** - Allowing unsafe operations

As stated in the RFC: "Marking a function as `unsafe` is one of Rust's key protections against undefined behavior."

## Safety Requirements

### Explicit Unsafe Blocks
- All unsafe operations inside unsafe functions must be wrapped in `unsafe {}` blocks
- This ensures developers consciously acknowledge potential unsafe operations
- Enforced via the `unsafe_op_in_unsafe_fn` lint

### Example Transformation
```rust
// Before RFC 2585
unsafe fn example(ptr: *const u8) {
    *ptr // Implicitly allowed
}

// After RFC 2585
unsafe fn example(ptr: *const u8) {
    unsafe { 
        *ptr // Explicitly marked as unsafe
    }
}
```

## Documentation Implications

### Safety Comments
With explicit unsafe blocks, each block should document:
- Why the operation is safe in this context
- What invariants are being maintained
- What preconditions must be met

### Example with Documentation
```rust
unsafe fn process_raw_ptr(ptr: *const u8, len: usize) {
    // SAFETY: The caller must ensure:
    // - `ptr` is valid for reads of `len` bytes
    // - `ptr` points to properly initialized memory
    // - The memory at `ptr` is not mutated during this function
    unsafe {
        // Unsafe operation with documented safety requirements
        let slice = std::slice::from_raw_parts(ptr, len);
        // Process slice...
    }
}
```

## Rationale

### Improved Code Safety
- Makes developers more aware of unsafe operations
- Requires explicit acknowledgment of each unsafe operation
- Prevents accidental unsafe operations in unsafe functions

### Least Invasive Approach
- Uses existing lint mechanisms for gradual transition
- Allows projects to adopt at their own pace
- Maintains backward compatibility through lint configuration

## Migration Strategy
1. Enable `unsafe_op_in_unsafe_fn` lint
2. Add explicit `unsafe` blocks where needed
3. Document safety requirements for each block
4. Review and validate all safety assumptions