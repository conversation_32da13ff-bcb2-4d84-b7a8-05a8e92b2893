# Working with Unsafe Code

## Core Principles

### Safety is Non-Local
- Unsafe code's soundness depends on the broader program state
- "Safety is non-local" means that seemingly safe code can introduce unsoundness
- Changes to safe code can break invariants that unsafe code relies upon

### Invariant Management
- Unsafe operations rely on maintaining specific structural invariants
- Modifying core data structures can break safety guarantees, even in "safe" code
- Every unsafe block must document which invariants it depends on

### Privacy is Critical
- Module-level privacy prevents unintended modifications
- Private methods can safely manage complex internal state
- Limit unsafe code's scope through careful API design

## Best Practices

### 1. Validate Preconditions
Always check conditions before unsafe operations:
```rust
fn index(idx: usize, arr: &[u8]) -> Option<u8> {
    if idx < arr.len() {
        unsafe {
            // SAFETY: We verified idx < arr.len() above,
            // so this index is guaranteed to be in bounds
            Some(*arr.get_unchecked(idx))
        }
    } else {
        None
    }
}
```

### 2. Document Safety Requirements
Every unsafe block should have a SAFETY comment explaining:
- What invariants are being relied upon
- Why the operation is safe in this context
- What preconditions have been verified

### 3. Minimize Unsafe Surface Area
- Keep unsafe blocks as small as possible
- Extract unsafe operations into well-documented functions
- Provide safe abstractions over unsafe operations

### 4. Use Privacy for Protection
```rust
mod my_module {
    pub struct SafeWrapper {
        // Private field prevents external modification
        data: Vec<u8>,
    }
    
    impl SafeWrapper {
        pub fn new() -> Self {
            SafeWrapper { data: Vec::new() }
        }
        
        // Safe public API
        pub fn get(&self, idx: usize) -> Option<&u8> {
            if idx < self.data.len() {
                unsafe {
                    // SAFETY: idx bounds checked above
                    Some(self.data.get_unchecked(idx))
                }
            } else {
                None
            }
        }
    }
}
```

## Common Patterns

### Safe Abstraction Pattern
1. Wrap unsafe operations in a safe API
2. Validate all inputs before unsafe operations
3. Maintain invariants through encapsulation
4. Document all safety requirements

### Checked-Then-Unsafe Pattern
```rust
// First check safety conditions
if condition_is_met {
    unsafe {
        // SAFETY: Explain why this is safe given the check above
        perform_unsafe_operation()
    }
} else {
    // Handle the case where it would be unsafe
}
```

### Builder Pattern for Complex Unsafe State
```rust
pub struct UnsafeBuilder {
    // Track state needed for safety
    initialized: bool,
    aligned: bool,
}

impl UnsafeBuilder {
    pub fn build(self) -> Result<SafeType, Error> {
        if self.initialized && self.aligned {
            unsafe {
                // SAFETY: Builder verified all preconditions
                Ok(SafeType::new_unchecked())
            }
        } else {
            Err(Error::InvalidState)
        }
    }
}
```

## Documentation Standards

### SAFETY Comments Template
```rust
unsafe {
    // SAFETY: [Brief explanation of why this is safe]
    // 
    // Preconditions:
    // - List all conditions that must be true
    // - Include any invariants being relied upon
    //
    // This operation is safe because:
    // - Detailed reasoning about safety
    // - References to where invariants are established
}
```

### Function Documentation
```rust
/// Does something potentially dangerous.
///
/// # Safety
///
/// The caller must ensure that:
/// - `ptr` is valid for reads of `len * size_of::<T>()` bytes
/// - `ptr` is properly aligned for type `T`
/// - The memory referenced by `ptr` is initialized
/// - No other references exist to this memory
///
/// # Panics
///
/// This function will panic if [conditions].
unsafe fn dangerous_operation<T>(ptr: *const T, len: usize) {
    // Implementation
}
```

## Key Warnings

### "Safe" Doesn't Mean "Cannot Cause UB"
- Safe code can break invariants that unsafe code depends on
- Always consider how safe APIs might be misused
- Design defensive unsafe code that validates assumptions

### Think Globally
- Consider the entire module when reasoning about safety
- Track all places where invariants could be violated
- Use visibility modifiers to limit access appropriately