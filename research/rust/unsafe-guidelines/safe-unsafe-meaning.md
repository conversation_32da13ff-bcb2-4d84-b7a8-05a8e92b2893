# The Meaning of Safe and Unsafe in Rust

## Fundamental Principle
**"No matter what, Safe Rust can't cause Undefined Behavior."**

This is the core guarantee that Rust provides and the foundation of its memory safety model.

## The Two Purposes of `unsafe`

### 1. <PERSON>lar<PERSON> Contracts
The `unsafe` keyword declares the existence of contracts that the compiler cannot check. This applies to:
- **Functions**: Have requirements that callers must uphold
- **Trait Declarations**: Have requirements that implementors must satisfy

### 2. Verify Contracts
The `unsafe` keyword indicates that a programmer has checked that contracts are upheld:
- **Code Blocks**: Programmer verifies specific unsafe operations are valid
- **Trait Implementations**: Programmer certifies that implementation meets trait contracts

## Where `unsafe` Can Be Used

### Unsafe Functions
```rust
unsafe fn dangerous_operation(ptr: *const u8) {
    // Function body
}
```
- Callers must check documentation to understand requirements
- Documentation must specify all preconditions and invariants

### Unsafe Trait Declarations
```rust
unsafe trait DangerousTrait {
    // Trait methods
}
```
- Implementors must check trait documentation
- Must verify their implementation upholds the trait's contract

### Unsafe Code Blocks
```rust
fn example() {
    unsafe {
        // Programmer verifies this operation is safe
    }
}
```
- Programmer takes responsibility for safety
- Should document why the operation is safe

### Unsafe Trait Implementations
```rust
unsafe impl Send for MyType {}
```
- Declares that the implementation satisfies the trait's safety requirements
- Programmer certifies correctness

## Trust Relationships

### Safe Rust Must Trust Unsafe Rust
- Safe Rust relies on unsafe implementations being correct
- If unsafe code has bugs, safe code can exhibit undefined behavior

### Unsafe Rust Cannot Trust Safe Rust
- Safe code might do unexpected things (within safe bounds)
- Unsafe code must be defensive against all possible safe usage

## Standard Unsafe Traits

### `Send`
- Types that can be safely moved between threads
- Must ensure no data races when transferring ownership

### `Sync` 
- Types that can be safely shared between threads via shared references
- Must ensure thread-safe access patterns

### `GlobalAlloc`
- Allows customizing program memory allocation
- Must correctly implement memory allocation contracts

## Documentation Requirements for Unsafe Code

### Function Documentation Must Include:
1. **Preconditions**: What must be true before calling
2. **Invariants**: What the function maintains
3. **Postconditions**: What is guaranteed after calling
4. **Safety Notes**: Why the implementation is safe

### Example Documentation:
```rust
/// Dereferences a raw pointer and returns the value.
///
/// # Safety
///
/// The caller must ensure that:
/// - `ptr` is non-null
/// - `ptr` is properly aligned for type `T`
/// - `ptr` points to a valid, initialized value of type `T`
/// - The memory `ptr` points to is not mutated during this call
/// - The lifetime of the returned reference is valid
unsafe fn deref_raw<T>(ptr: *const T) -> &T {
    // Implementation
}
```

## Design Philosophy

### Minimize Pervasive Unsafety
- Keep unsafe code isolated and well-bounded
- Provide safe abstractions over unsafe operations
- Make it clear where safety responsibilities lie

### Clear Boundaries
- Safe/unsafe boundary should be obvious
- Documentation must clearly state all safety requirements
- Unsafe code should be minimal and focused

### Shift Responsibility Appropriately
- Put verification burden on implementors when sensible
- Design APIs that are hard to misuse
- Make unsafe requirements explicit and checkable