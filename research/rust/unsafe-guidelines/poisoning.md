# Poisoning in Rust

## Overview
Poisoning is a mechanism to handle potential inconsistent states during panic scenarios, particularly with concurrent data structures. It serves as a safeguard against using data that might be in an unreliable state after a panic.

## Key Concepts

### Definition
"Poisoning doesn't entail anything in particular. Generally it just means preventing normal usage from proceeding."

### Purpose
- Not strictly for memory safety (minimal exception safety)
- Prevents use of potentially inconsistent or incomplete data
- Provides a warning that data might be compromised

## Mutex Poisoning

### How It Works
1. Thread acquires a Mutex lock (MutexGuard)
2. Thread panics while holding the lock
3. MutexGuard is dropped during unwinding
4. Mutex becomes "poisoned"
5. Future lock attempts return an error

### Example
```rust
use std::sync::{Arc, Mutex};
use std::thread;

let mutex = Arc::new(Mutex::new(0));
let mutex_clone = Arc::clone(&mutex);

// Thread that will panic
let handle = thread::spawn(move || {
    let mut guard = mutex_clone.lock().unwrap();
    *guard = 42;
    panic!("oh no!"); // Mutex becomes poisoned
});

// Wait for panic
let _ = handle.join();

// Attempting to lock poisoned mutex
match mutex.lock() {
    Ok(_) => println!("Got lock"),
    Err(poisoned) => {
        println!("Mutex is poisoned!");
        // Can still access data if absolutely necessary
        let mut guard = poisoned.into_inner();
        *guard = 0; // Reset to known good state
    }
}
```

## Implications for Unsafe Code

### Minimal vs Maximal Safety
- **Minimal Safety**: Unsafe code must maintain memory safety
- **Maximal Safety**: Not all types guarantee logical consistency
- Poisoning bridges this gap for shared state

### Documentation Requirements
When implementing types with poisoning:
```rust
/// A thread-safe container that poisons on panic.
///
/// # Poisoning
///
/// If a panic occurs while the container is being modified,
/// it becomes poisoned. Future operations will return an error
/// until the container is explicitly recovered.
///
/// # Example
/// ```
/// let container = PoisonableContainer::new();
/// // ... usage ...
/// ```
pub struct PoisonableContainer<T> {
    // Implementation
}
```

## Implementing Poisoning

### Basic Pattern
```rust
pub struct Poisonable<T> {
    data: T,
    poisoned: AtomicBool,
}

impl<T> Poisonable<T> {
    pub fn new(data: T) -> Self {
        Self {
            data,
            poisoned: AtomicBool::new(false),
        }
    }
    
    pub fn access<F, R>(&self, f: F) -> Result<R, PoisonError>
    where
        F: FnOnce(&T) -> R,
    {
        if self.poisoned.load(Ordering::Acquire) {
            return Err(PoisonError);
        }
        
        // Set up panic handler
        let guard = PoisonGuard {
            poisoned: &self.poisoned,
        };
        
        let result = f(&self.data);
        
        // Disarm guard on success
        mem::forget(guard);
        Ok(result)
    }
}

struct PoisonGuard<'a> {
    poisoned: &'a AtomicBool,
}

impl Drop for PoisonGuard<'_> {
    fn drop(&mut self) {
        // Poison on panic (during unwinding)
        self.poisoned.store(true, Ordering::Release);
    }
}
```

## Recovery from Poisoning

### Escape Hatches
Most poisonable types provide ways to recover:
```rust
// Mutex example
match poisoned_mutex.lock() {
    Err(poisoned) => {
        // Recover the guard despite poisoning
        let mut guard = poisoned.into_inner();
        // Manually verify/fix state
        validate_and_repair(&mut *guard);
    }
    Ok(guard) => {
        // Normal usage
    }
}
```

### Clear Documentation
```rust
/// Attempts to recover from a poisoned state.
///
/// # Safety
///
/// The caller must ensure that the data is in a valid state
/// before calling this method. This typically involves:
/// - Validating all invariants
/// - Resetting to a known good state
/// - Clearing any partial operations
///
/// # Example
/// ```
/// if container.is_poisoned() {
///     container.recover(|data| {
///         data.clear(); // Reset to empty state
///     });
/// }
/// ```
pub fn recover<F>(&self, recovery: F)
where
    F: FnOnce(&mut T)
```

## Best Practices

### 1. Use Poisoning for Shared Mutable State
- Particularly important for concurrent access
- Helps prevent cascade failures

### 2. Provide Recovery Mechanisms
- Allow users to handle poisoned states
- Document recovery requirements clearly

### 3. Consider Alternatives
- Sometimes resetting to default is better
- Consider if poisoning adds value for your type

### 4. Test Poisoning Behavior
```rust
#[test]
fn test_poisoning() {
    let container = Arc::new(Poisonable::new(vec![1, 2, 3]));
    let container_clone = Arc::clone(&container);
    
    // Cause poisoning
    let result = std::panic::catch_unwind(move || {
        container_clone.access(|_data| {
            panic!("poison test");
        })
    });
    
    assert!(result.is_err());
    assert!(container.is_poisoned());
}
```

## When Not to Use Poisoning

- Single-threaded contexts (unless shared via Rc)
- Immutable data structures
- When reset-to-default is more appropriate
- Performance-critical paths where overhead matters