# Parser Library Safety Patterns

## Overview
Parser libraries in Rust (like nom and pest) prioritize safety through careful API design and minimal unsafe code usage. This document covers safety patterns and documentation standards for parser implementations.

## Nom Parser Safety

### Core Safety Principles
- **Zero-copy parsing**: Minimize allocations and copies
- **Type safety**: Strong typing for parser functions
- **Memory safety**: Leverages Rust's ownership system
- **Extensive testing**: Fuzzed with real-world data

### Parser Function Signature
```rust
fn parser(input: I) -> IResult<I, O, E>;
```
- Type-safe input/output/error handling
- Composable through combinators
- Clear error propagation

### Error Handling Pattern
```rust
match parser(input) {
    Ok((remaining, output)) => {
        // Successful parse with remaining input
    }
    Err(Err::Error(e)) => {
        // Recoverable error - can try alternative parser
    }
    Err(Err::Failure(e)) => {
        // Unrecoverable error - parsing must stop
    }
    Err(Err::Incomplete(needed)) => {
        // Need more input for streaming parsers
    }
}
```

### Safety Through Abstraction
Nom provides safe abstractions to avoid error-prone manual parsing:
- Built-in combinators for common patterns
- Automatic bounds checking
- Safe slice manipulation

## Pest Parser Safety

### Safety Features
- **Automatic parser generation**: Reduces manual unsafe code
- **Call limits**: Prevent stack overflow and excessive execution
- **Type-safe API**: Generated parsers use Rust's type system

### Protection Mechanisms
```rust
// Set call limit to prevent runaway parsing
parser.set_call_limit(Some(1000));

// Parse with safety limits
match parser.parse(Rule::program, input) {
    Ok(pairs) => process_pairs(pairs),
    Err(e) => handle_parse_error(e),
}
```

## Common Parser Safety Patterns

### 1. Bounds Checking Pattern
```rust
/// Parse a fixed-size structure safely
fn parse_header(input: &[u8]) -> IResult<&[u8], Header> {
    // Ensure sufficient bytes before accessing
    if input.len() < HEADER_SIZE {
        return Err(nom::Err::Incomplete(Needed::new(HEADER_SIZE)));
    }
    
    // Safe to proceed with parsing
    // ...
}
```

### 2. Lifetime Management
```rust
/// Parser that borrows from input
struct BorrowingParser<'a> {
    input: &'a [u8],
    position: usize,
}

impl<'a> BorrowingParser<'a> {
    /// Get a slice safely with lifetime tied to input
    fn slice(&self, start: usize, end: usize) -> Option<&'a [u8]> {
        self.input.get(start..end)
    }
}
```

### 3. Safe Unsafe Pattern (When Necessary)
```rust
/// Fast parsing with careful unsafe usage
fn parse_u32_unchecked(input: &[u8]) -> u32 {
    debug_assert!(input.len() >= 4);
    
    unsafe {
        // SAFETY: We have verified that input has at least 4 bytes
        // via debug_assert. In release builds, caller must ensure
        // this precondition is met.
        let ptr = input.as_ptr() as *const u32;
        ptr.read_unaligned()
    }
}

/// Safe wrapper that checks bounds
pub fn parse_u32(input: &[u8]) -> Option<u32> {
    if input.len() >= 4 {
        Some(parse_u32_unchecked(input))
    } else {
        None
    }
}
```

## Documentation Standards for Parser Safety

### Function Documentation
```rust
/// Parses a null-terminated string from the input.
///
/// # Safety Invariants
/// - Does not read beyond input bounds
/// - Returns error if no null terminator found
/// - Validates UTF-8 encoding
///
/// # Performance
/// - O(n) where n is string length
/// - Zero-copy when possible
///
/// # Example
/// ```
/// let input = b"hello\0world";
/// let (remaining, string) = parse_cstring(input)?;
/// assert_eq!(string, "hello");
/// assert_eq!(remaining, b"world");
/// ```
pub fn parse_cstring(input: &[u8]) -> IResult<&[u8], &str> {
    // Implementation
}
```

### Unsafe Block Documentation (When Used)
```rust
impl Parser {
    fn read_unaligned<T>(&self, offset: usize) -> Option<T> {
        let size = mem::size_of::<T>();
        
        if offset + size > self.buffer.len() {
            return None;
        }
        
        unsafe {
            // SAFETY: We've verified that:
            // 1. offset + size <= buffer.len() (bounds check above)
            // 2. The buffer is properly aligned for u8 access
            // 3. We're reading a Copy type, so no drop concerns
            // 4. read_unaligned handles alignment requirements
            let ptr = self.buffer.as_ptr().add(offset) as *const T;
            Some(ptr.read_unaligned())
        }
    }
}
```

## Testing Parser Safety

### Fuzzing
```rust
#[cfg(fuzzing)]
fuzz_target!(|data: &[u8]| {
    // Fuzz parser with arbitrary input
    let _ = parser::parse(data);
});
```

### Property Testing
```rust
#[test]
fn test_parser_never_reads_out_of_bounds() {
    proptest!(|(input: Vec<u8>)| {
        match parse_structure(&input) {
            Ok((remaining, _)) => {
                // Verify remaining is a subslice of input
                let remaining_ptr = remaining.as_ptr();
                let input_ptr = input.as_ptr();
                let input_end = unsafe { input_ptr.add(input.len()) };
                
                assert!(remaining_ptr >= input_ptr);
                assert!(remaining_ptr <= input_end);
            }
            Err(_) => {
                // Errors are acceptable
            }
        }
    });
}
```

### Edge Case Testing
```rust
#[test]
fn test_empty_input() {
    assert!(matches!(parser(b""), Err(_)));
}

#[test]
fn test_incomplete_input() {
    let partial = b"incomplete";
    match parser(partial) {
        Err(nom::Err::Incomplete(_)) => {}
        _ => panic!("Expected Incomplete error"),
    }
}
```

## Best Practices

1. **Prefer Safe Abstractions**: Use nom/pest combinators over manual parsing
2. **Validate Early**: Check bounds before any potentially unsafe operations
3. **Document Assumptions**: Clearly state all safety requirements
4. **Test Exhaustively**: Use fuzzing and property testing
5. **Limit Unsafe Scope**: Keep unsafe blocks minimal and focused
6. **Provide Safe Wrappers**: Always wrap unsafe internals in safe APIs