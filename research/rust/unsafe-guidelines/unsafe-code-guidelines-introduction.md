# Unsafe Code Guidelines Introduction

## Purpose
The Unsafe Code Guidelines Reference is a past effort by the Rust Unsafe Code Guidelines Working Group (UCG WG) to provide guidance for writing unsafe code. Its primary goals are to:
- Recommend what unsafe code can and cannot do
- Document guarantees that unsafe code may rely on

## Key Characteristics
- Currently largely abandoned
- Glossary section remains actively maintained
- Recommendations are "subject to change"

## Important Note
The guidelines are primarily recommendations, not strict rules. As stated in the text: "Unless stated otherwise, the information in the guide is mostly a 'recommendation' and still subject to change."

## Scope
The reference covers various technical aspects of unsafe code, including:
- Data layout
- Validity
- Optimizations
- Specific topics like structs, pointers, unions, and function pointers

The document serves as a reference for Rust developers working with unsafe code, providing insights into best practices and potential implementation considerations.