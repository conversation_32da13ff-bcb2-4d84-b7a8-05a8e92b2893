# Rust Unsafe Code Guidelines Research

This directory contains comprehensive documentation on Rust unsafe code guidelines, documentation standards, and best practices. The research was gathered from official Rust documentation, RFCs, and well-established parser libraries.

## 📚 Documentation Index

### Core Unsafe Guidelines
1. **[Unsafe Code Guidelines Introduction](./unsafe-code-guidelines-introduction.md)**
   - Overview of the Unsafe Code Guidelines Working Group efforts
   - Scope and purpose of unsafe code guidelines
   - Current status and recommendations

2. **[Unsafe Code Glossary](./unsafe-code-glossary.md)**
   - Key terminology: Undefined Behavior, Soundness, Invariants
   - Memory concepts: Pointer Provenance, Aliasing
   - Documentation requirements for each concept

3. **[Safe and Unsafe Meaning](./safe-unsafe-meaning.md)**
   - The two purposes of `unsafe` keyword
   - Trust relationships between safe and unsafe code
   - Standard unsafe traits (Send, Sync, GlobalAlloc)
   - Comprehensive documentation templates

### Implementation Guidelines
4. **[RFC 2585: Unsafe Block in Unsafe Function](./rfc-2585-unsafe-block-in-unsafe-fn.md)**
   - Requirement for explicit unsafe blocks in unsafe functions
   - Safety comment requirements
   - Migration strategies and examples

5. **[Working with Unsafe Code](./working-with-unsafe.md)**
   - Best practices for writing unsafe code
   - Safe abstraction patterns
   - Privacy and encapsulation strategies
   - SAFETY comment templates

### Advanced Safety Topics
6. **[Exception Safety](./exception-safety.md)**
   - Handling panics in unsafe code
   - Drop guards and cleanup patterns
   - Testing exception safety
   - The Hole pattern from BinaryHeap

7. **[Poisoning](./poisoning.md)**
   - Mutex poisoning mechanism
   - Implementing custom poisoning
   - Recovery strategies
   - When to use poisoning

### Library-Specific Patterns
8. **[Parser Library Patterns](./parser-library-patterns.md)**
   - Safety patterns in nom and pest parsers
   - Zero-copy parsing techniques
   - Testing strategies for parsers
   - Safe wrappers for performance-critical code

## 🔑 Key Safety Comment Requirements

### SAFETY Comment Template
```rust
unsafe {
    // SAFETY: [Brief explanation of why this is safe]
    // 
    // Preconditions:
    // - List all conditions that must be true
    // - Include any invariants being relied upon
    //
    // This operation is safe because:
    // - Detailed reasoning about safety
    // - References to where invariants are established
}
```

### Function Documentation Template
```rust
/// Brief description of the function.
///
/// # Safety
///
/// The caller must ensure that:
/// - `ptr` is valid for reads/writes of `size` bytes
/// - `ptr` is properly aligned for type `T`
/// - The memory referenced by `ptr` is initialized
/// - No other references exist to this memory (if exclusive)
///
/// # Panics
///
/// This function will panic if [conditions].
///
/// # Examples
/// ```
/// // Example usage
/// ```
unsafe fn dangerous_operation<T>(ptr: *mut T, size: usize) {
    // Implementation
}
```

## 📋 Quick Reference

### When to Document Safety
1. **Every `unsafe` block** must have a SAFETY comment
2. **Every `unsafe` function** must have a # Safety section
3. **Every `unsafe` trait** must document implementation requirements
4. **Every `unsafe impl`** must explain why it's safe

### What to Include in Safety Documentation
- **Preconditions**: What must be true before the operation
- **Invariants**: What properties are maintained
- **Postconditions**: What is guaranteed after the operation
- **Failure modes**: What could go wrong and consequences

### Common Safety Patterns
1. **Bounds Checking**: Verify indices/sizes before unsafe access
2. **Drop Guards**: Ensure cleanup on panic
3. **Private Fields**: Encapsulate unsafe internals
4. **Safe Wrappers**: Provide safe APIs over unsafe operations

## 🚀 Best Practices Summary

1. **Minimize Unsafe Code**: Use the smallest possible unsafe blocks
2. **Document Everything**: Every unsafe operation needs justification
3. **Test Thoroughly**: Use fuzzing, property testing, and panic injection
4. **Maintain Invariants**: Design APIs that can't be misused
5. **Use Established Patterns**: Follow patterns from std library and popular crates
6. **Enable Lints**: Use `#![warn(unsafe_op_in_unsafe_fn)]` and clippy lints
7. **Review Carefully**: Unsafe code requires extra scrutiny

## 🔗 Official Resources

- [The Rustonomicon](https://doc.rust-lang.org/nomicon/) - The Dark Arts of Unsafe Rust
- [Unsafe Code Guidelines](https://rust-lang.github.io/unsafe-code-guidelines/) - WG Reference
- [Standard Library Developer Guide](https://std-dev-guide.rust-lang.org/) - Safety policies
- [Clippy Lints](https://rust-lang.github.io/rust-clippy/master/index.html) - Unsafe code lints

## 📊 Research Summary

This research collection provides:
- **10+ pages** of comprehensive documentation
- **Official guidelines** from Rust language team
- **Real-world patterns** from production libraries
- **Practical examples** with full documentation
- **Testing strategies** for unsafe code
- **Migration paths** for improving existing code

Use these guidelines to write well-documented, safe, and maintainable unsafe Rust code.