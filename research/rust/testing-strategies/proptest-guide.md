# Proptest - Property-Based Testing for Rust

## Overview

Proptest is a property-based testing framework for Rust inspired by H<PERSON><PERSON>hesis (Python) and QuickCheck (Haskell). Instead of writing specific test cases, you describe properties that should hold for all inputs, and proptest generates random test cases to verify these properties.

## Key Concepts

### Property-Based Testing
- **Properties**: Universal statements about your code
- **Strategies**: Generators for test input data
- **Shrinking**: Automatic minimization of failing cases
- **Assumptions**: Filtering of invalid inputs

## Basic Usage

### Simple Property Test
```rust
use proptest::prelude::*;

proptest! {
    #[test]
    fn test_reverse_twice_is_identity(s: String) {
        let reversed: String = s.chars().rev().collect();
        let double_reversed: String = reversed.chars().rev().collect();
        prop_assert_eq!(s, double_reversed);
    }
}
```

### Using Strategies
```rust
proptest! {
    #[test]
    fn test_abs_is_non_negative(x in any::<i32>()) {
        prop_assert!(x.abs() >= 0);
    }
    
    #[test]
    fn test_multiplication_commutative(
        a in 0..100i32,
        b in 0..100i32
    ) {
        prop_assert_eq!(a * b, b * a);
    }
}
```

## Strategies

### Built-in Strategies

#### Numeric Strategies
```rust
use proptest::prelude::*;

proptest! {
    // Any integer
    #[test]
    fn test_with_any_int(x in any::<i32>()) {
        // Test with any i32
    }
    
    // Range
    #[test]
    fn test_with_range(x in 1..100i32) {
        prop_assert!(x >= 1 && x < 100);
    }
    
    // Specific values
    #[test]
    fn test_with_specific(x in prop::num::i32::ANY) {
        // Alternative syntax
    }
}
```

#### String Strategies
```rust
proptest! {
    // Any string
    #[test]
    fn test_any_string(s in any::<String>()) {
        // Test with any String
    }
    
    // Regex pattern
    #[test]
    fn test_regex_string(s in "[a-z]{5,10}") {
        prop_assert!(s.len() >= 5 && s.len() <= 10);
        prop_assert!(s.chars().all(|c| c.is_lowercase()));
    }
    
    // String of specific length
    #[test]
    fn test_fixed_length(s in prop::string::string_regex("[A-Z]{10}").unwrap()) {
        prop_assert_eq!(s.len(), 10);
    }
}
```

#### Collection Strategies
```rust
proptest! {
    // Vector of integers
    #[test]
    fn test_vec_operations(v in prop::collection::vec(any::<i32>(), 0..100)) {
        let len = v.len();
        let mut v2 = v.clone();
        v2.reverse();
        v2.reverse();
        prop_assert_eq!(v, v2);
        prop_assert_eq!(v.len(), len);
    }
    
    // HashSet
    #[test]
    fn test_set_properties(
        s in prop::collection::hash_set(any::<String>(), 0..50)
    ) {
        for item in &s {
            prop_assert_eq!(s.iter().filter(|&x| x == item).count(), 1);
        }
    }
    
    // HashMap
    #[test]
    fn test_map_properties(
        m in prop::collection::hash_map(
            any::<String>(),
            any::<i32>(),
            0..20
        )
    ) {
        for (k, v) in &m {
            prop_assert_eq!(m.get(k), Some(v));
        }
    }
}
```

### Custom Strategies

#### Deriving Arbitrary
```rust
use proptest::prelude::*;
use proptest_derive::Arbitrary;

#[derive(Debug, Clone, Arbitrary)]
struct User {
    #[proptest(strategy = "1..150i32")]
    age: i32,
    
    #[proptest(regex = "[a-zA-Z]{3,20}")]
    name: String,
    
    #[proptest(strategy = "prop::option::of(any::<String>())")]
    email: Option<String>,
}

proptest! {
    #[test]
    fn test_user_properties(user: User) {
        prop_assert!(user.age >= 1 && user.age < 150);
        prop_assert!(user.name.len() >= 3 && user.name.len() <= 20);
    }
}
```

#### Manual Strategy Implementation
```rust
fn point_strategy() -> impl Strategy<Value = Point> {
    (any::<f64>(), any::<f64>()).prop_map(|(x, y)| Point { x, y })
}

fn user_strategy() -> impl Strategy<Value = User> {
    (
        "[a-zA-Z]{5,20}",
        1..120u8,
        prop::option::of(prop::string::string_regex("[a-z]+@[a-z]+\\.[a-z]+").unwrap())
    ).prop_map(|(name, age, email)| User { name, age, email })
}

proptest! {
    #[test]
    fn test_with_custom_strategy(point in point_strategy()) {
        let distance = (point.x * point.x + point.y * point.y).sqrt();
        prop_assert!(distance >= 0.0);
    }
}
```

#### Strategy Combinators
```rust
use proptest::strategy::{Strategy, Just};

// Map strategy
let digit_char = (0..10u8).prop_map(|n| (n + b'0') as char);

// FlatMap strategy
let vec_of_length = (0..10usize).prop_flat_map(|len| {
    prop::collection::vec(any::<i32>(), len)
});

// Filter strategy
let even_numbers = any::<i32>().prop_filter("must be even", |x| x % 2 == 0);

// Union strategy
let bool_or_int = prop_oneof![
    any::<bool>().prop_map(|b| b.to_string()),
    any::<i32>().prop_map(|i| i.to_string()),
];
```

## Advanced Features

### Shrinking

Proptest automatically shrinks failing inputs to find minimal examples:

```rust
proptest! {
    #[test]
    fn test_no_panic_on_parse(s: String) {
        // If this fails, proptest will find the shortest string that causes failure
        let _ = s.parse::<i32>();
    }
}
```

### Custom Shrinking
```rust
use proptest::strategy::ValueTree;

struct MyShrinkingStrategy;

impl Strategy for MyShrinkingStrategy {
    type Tree = MyValueTree;
    type Value = MyType;
    
    fn new_tree(&self, runner: &mut TestRunner) -> NewTree<Self> {
        // Implementation
    }
}
```

### Assumptions and Filtering

```rust
proptest! {
    #[test]
    fn test_division(a: i32, b: i32) {
        // Skip test cases where b is 0
        prop_assume!(b != 0);
        
        let result = a / b;
        prop_assert_eq!(result * b + (a % b), a);
    }
    
    #[test]
    fn test_with_constraints(
        x in any::<i32>().prop_filter("positive", |&x| x > 0),
        y in any::<i32>().prop_filter("positive", |&y| y > 0)
    ) {
        prop_assert!(x + y > x);
        prop_assert!(x + y > y);
    }
}
```

### Configuration

#### Per-Test Configuration
```rust
proptest! {
    #![proptest_config(ProptestConfig {
        cases: 1000,
        max_shrink_iters: 10000,
        failure_persistence: None,
        ..ProptestConfig::default()
    })]
    
    #[test]
    fn test_with_many_cases(x: i32) {
        // This will run 1000 cases
    }
}
```

#### Global Configuration
```toml
# proptest.toml in project root
max_shrink_iters = 10000
cases = 256
max_global_rejects = 1024
```

### Regression Testing

Proptest saves failing cases for regression testing:

```rust
// After a test fails, proptest saves the failing input
// On next run, it will test the saved case first

// To disable persistence:
proptest! {
    #![proptest_config(ProptestConfig {
        failure_persistence: None,
        ..ProptestConfig::default()
    })]
    
    #[test]
    fn test_without_persistence(x: i32) {
        // Won't save failing cases
    }
}
```

## Common Patterns

### Testing Invariants
```rust
#[derive(Debug, Clone)]
struct SortedVec<T> {
    data: Vec<T>,
}

impl<T: Ord> SortedVec<T> {
    fn new() -> Self {
        Self { data: Vec::new() }
    }
    
    fn insert(&mut self, value: T) {
        let pos = self.data.binary_search(&value).unwrap_or_else(|e| e);
        self.data.insert(pos, value);
    }
    
    fn is_sorted(&self) -> bool {
        self.data.windows(2).all(|w| w[0] <= w[1])
    }
}

proptest! {
    #[test]
    fn test_sorted_vec_invariant(
        values in prop::collection::vec(any::<i32>(), 0..100)
    ) {
        let mut sv = SortedVec::new();
        
        for value in values {
            sv.insert(value);
            prop_assert!(sv.is_sorted());
        }
    }
}
```

### Round-Trip Testing
```rust
proptest! {
    #[test]
    fn test_serialization_roundtrip(value: MyStruct) {
        let serialized = serde_json::to_string(&value).unwrap();
        let deserialized: MyStruct = serde_json::from_str(&serialized).unwrap();
        prop_assert_eq!(value, deserialized);
    }
    
    #[test]
    fn test_encoding_roundtrip(data: Vec<u8>) {
        let encoded = base64::encode(&data);
        let decoded = base64::decode(&encoded).unwrap();
        prop_assert_eq!(data, decoded);
    }
}
```

### State Machine Testing
```rust
#[derive(Debug, Clone)]
enum Action {
    Push(i32),
    Pop,
    Clear,
}

proptest! {
    #[test]
    fn test_stack_model(
        actions in prop::collection::vec(
            prop_oneof![
                any::<i32>().prop_map(Action::Push),
                Just(Action::Pop),
                Just(Action::Clear),
            ],
            0..100
        )
    ) {
        let mut stack = Vec::new();
        let mut model = Vec::new();
        
        for action in actions {
            match action {
                Action::Push(x) => {
                    stack.push(x);
                    model.push(x);
                }
                Action::Pop => {
                    prop_assert_eq!(stack.pop(), model.pop());
                }
                Action::Clear => {
                    stack.clear();
                    model.clear();
                }
            }
            
            prop_assert_eq!(stack.len(), model.len());
        }
    }
}
```

### Testing Laws
```rust
// Monoid laws
proptest! {
    #[test]
    fn test_string_monoid_associative(a: String, b: String, c: String) {
        // (a + b) + c = a + (b + c)
        let left = format!("{}{}{}", a.clone(), b.clone(), c.clone());
        let right = format!("{}{}{}", a, b, c);
        prop_assert_eq!(left, right);
    }
    
    #[test]
    fn test_string_monoid_identity(s: String) {
        // "" + s = s = s + ""
        prop_assert_eq!(format!("{}{}", "", &s), s);
        prop_assert_eq!(format!("{}{}", &s, ""), s);
    }
}
```

### Database Testing
```rust
proptest! {
    #[test]
    fn test_database_operations(
        users in prop::collection::vec(user_strategy(), 0..50)
    ) {
        let mut db = TestDatabase::new();
        
        // Insert all users
        for user in &users {
            db.insert(user.clone()).unwrap();
        }
        
        // Verify all can be retrieved
        for user in &users {
            let retrieved = db.find_by_id(user.id).unwrap();
            prop_assert_eq!(Some(user), retrieved.as_ref());
        }
        
        // Verify count
        prop_assert_eq!(db.count(), users.len());
    }
}
```

## Integration Examples

### With Quickcheck
```rust
// Proptest can work alongside quickcheck
use quickcheck::{Arbitrary, Gen};

impl Arbitrary for MyType {
    fn arbitrary(g: &mut Gen) -> Self {
        // Quickcheck generation
    }
}

// Use in proptest
proptest! {
    #[test]
    fn test_with_quickcheck_type(value: MyType) {
        // Test properties
    }
}
```

### With Criterion Benchmarks
```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};
use proptest::prelude::*;

fn bench_with_proptest(c: &mut Criterion) {
    let mut runner = TestRunner::default();
    let strategy = prop::collection::vec(any::<i32>(), 100);
    
    c.bench_function("sort_random", |b| {
        b.iter_batched(
            || strategy.new_tree(&mut runner).unwrap().current(),
            |mut data| {
                data.sort();
                black_box(data)
            },
            criterion::BatchSize::SmallInput,
        )
    });
}
```

### With tokio
```rust
use tokio::test;

proptest! {
    #[test]
    async fn test_async_property(x: i32) {
        let result = async_function(x).await;
        prop_assert!(result.is_ok());
    }
}
```

## Best Practices

### 1. Start Simple
```rust
// Start with simple properties
proptest! {
    #[test]
    fn test_len_after_push(mut v: Vec<i32>, x: i32) {
        let old_len = v.len();
        v.push(x);
        prop_assert_eq!(v.len(), old_len + 1);
    }
}
```

### 2. Use Domain-Specific Strategies
```rust
fn valid_email_strategy() -> impl Strategy<Value = String> {
    "[a-z]{1,10}@[a-z]{1,10}\\.(com|org|net)"
}

fn valid_user_strategy() -> impl Strategy<Value = User> {
    (
        "[A-Z][a-z]{2,19}",  // Name
        18..100u8,           // Age  
        valid_email_strategy()
    ).prop_map(|(name, age, email)| User { name, age, email })
}
```

### 3. Combine with Traditional Tests
```rust
#[test]
fn test_specific_edge_case() {
    // Traditional test for known edge case
    assert_eq!(my_function(""), Some(0));
}

proptest! {
    #[test]
    fn test_general_property(s: String) {
        // Property test for general case
        let result = my_function(&s);
        if s.is_empty() {
            prop_assert_eq!(result, Some(0));
        } else {
            prop_assert!(result.is_some());
        }
    }
}
```

### 4. Document Properties
```rust
proptest! {
    /// Verifies that sorting is idempotent - sorting twice
    /// produces the same result as sorting once
    #[test]
    fn test_sort_idempotent(mut v: Vec<i32>) {
        v.sort();
        let once = v.clone();
        v.sort();
        prop_assert_eq!(once, v);
    }
}
```

### 5. Use Shrinking Effectively
```rust
// Proptest will try to find minimal failing case
proptest! {
    #[test]
    fn test_parser(input: String) {
        // If this fails on complex input, proptest will find
        // the shortest string that still causes the failure
        let result = parse(&input);
        prop_assert!(result.is_ok() || input.contains("invalid"));
    }
}
```

## Common Pitfalls and Solutions

### 1. Too Many Rejections
```rust
// Bad: Too many rejections
proptest! {
    #[test]
    fn test_prime(n: u64) {
        prop_assume!(is_prime(n)); // Might reject too many
        // ...
    }
}

// Good: Generate primes directly
fn prime_strategy() -> impl Strategy<Value = u64> {
    any::<u64>().prop_map(|n| nth_prime(n % 1000))
}
```

### 2. Non-Deterministic Tests
```rust
// Bad: Depends on current time
proptest! {
    #[test]
    fn test_with_time(x: i32) {
        let now = SystemTime::now();
        // Test might fail at different times
    }
}

// Good: Make time a parameter
proptest! {
    #[test]
    fn test_with_time(x: i32, timestamp: u64) {
        let time = UNIX_EPOCH + Duration::from_secs(timestamp);
        // Deterministic test
    }
}
```

### 3. Infinite Recursion in Strategies
```rust
// Bad: Can cause stack overflow
fn bad_tree_strategy() -> impl Strategy<Value = Tree> {
    prop_oneof![
        Just(Tree::Leaf(0)),
        (bad_tree_strategy(), bad_tree_strategy())
            .prop_map(|(l, r)| Tree::Node(Box::new(l), Box::new(r)))
    ]
}

// Good: Limit recursion depth
fn tree_strategy(depth: u32) -> impl Strategy<Value = Tree> {
    if depth == 0 {
        Just(Tree::Leaf(0)).boxed()
    } else {
        prop_oneof![
            Just(Tree::Leaf(0)),
            (tree_strategy(depth - 1), tree_strategy(depth - 1))
                .prop_map(|(l, r)| Tree::Node(Box::new(l), Box::new(r)))
        ].boxed()
    }
}
```

## Summary

Proptest enables powerful property-based testing in Rust:
- Automatically generates test cases
- Shrinks failures to minimal examples
- Integrates well with Rust's testing framework
- Supports complex custom strategies
- Helps find edge cases you might miss

Use it to complement traditional unit tests and increase confidence in your code's correctness.