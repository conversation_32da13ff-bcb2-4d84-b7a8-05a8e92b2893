# Mockall - Comprehensive Mocking for Rust

## Overview

Mockall is a powerful mocking library for Rust that provides a rich set of tools for creating test doubles. It supports mocking traits, structs, and even external modules with a clean, ergonomic API.

## Key Features

- **Automatic mock generation** with `#[automock]`
- **Manual mock creation** with `mock!` macro
- **Flexible expectations** with various matching and return options
- **Support for async traits**
- **Generic method and trait support**
- **Static method mocking**
- **Checkpoint system** for test organization

## Basic Usage

### Simple Trait Mocking
```rust
use mockall::*;

#[automock]
trait MyDatabase {
    fn get_user(&self, id: u64) -> Option<User>;
    fn save_user(&mut self, user: &User) -> Result<(), Error>;
}

#[test]
fn test_user_service() {
    let mut mock = MockMyDatabase::new();
    
    // Set expectation
    mock.expect_get_user()
        .with(eq(42))
        .times(1)
        .returning(|_| Some(User { id: 42, name: "<PERSON>".into() }));
    
    // Use mock in test
    let service = UserService::new(Box::new(mock));
    let user = service.find_user(42);
    assert_eq!(user.unwrap().name, "<PERSON>");
}
```

### Struct Mocking
```rust
#[automock]
impl HttpClient {
    pub fn get(&self, url: &str) -> Result<String, Error> {
        // Real implementation
    }
    
    pub fn post(&self, url: &str, body: &str) -> Result<String, Error> {
        // Real implementation
    }
}

#[test]
fn test_api_client() {
    let mut mock = MockHttpClient::new();
    
    mock.expect_get()
        .with(eq("https://api.example.com/users"))
        .returning(|_| Ok(r#"{"users": []}"#.to_string()));
}
```

## Expectation Configuration

### Return Values

#### Constant Returns
```rust
mock.expect_foo()
    .return_const(42);
```

#### Dynamic Returns
```rust
mock.expect_calculate()
    .returning(|x, y| x + y);
```

#### Single-Use Returns
```rust
mock.expect_get_config()
    .return_once(|| Config::default());
```

#### Sequential Returns
```rust
mock.expect_random()
    .return_const(1)
    .times(1)
    .then()
    .return_const(2)
    .times(1)
    .then()
    .return_const(3);
```

### Argument Matching

#### Exact Matching
```rust
use mockall::predicate::*;

mock.expect_process()
    .with(eq(42), eq("test"))
    .returning(|_, _| Ok(()));
```

#### Pattern Matching
```rust
mock.expect_log()
    .with(eq(LogLevel::Error), str::starts_with("Failed"))
    .returning(|_, _| ());
```

#### Custom Predicates
```rust
fn is_even(x: &i32) -> bool {
    x % 2 == 0
}

mock.expect_process_number()
    .with(function(is_even))
    .returning(|_| "even");
```

#### Any Value
```rust
mock.expect_save()
    .with(always(), eq(true))  // First arg can be anything
    .returning(|_, _| Ok(()));
```

### Call Count Expectations

```rust
// Exactly once (default)
mock.expect_foo().times(1);

// Never called
mock.expect_foo().never();

// Any number of times
mock.expect_foo().times(..);

// At least once
mock.expect_foo().times(1..);

// Between 2 and 5 times
mock.expect_foo().times(2..=5);
```

## Advanced Patterns

### Async Trait Mocking
```rust
#[automock]
#[async_trait]
trait AsyncDatabase {
    async fn get_user(&self, id: u64) -> Result<User, Error>;
}

#[tokio::test]
async fn test_async_service() {
    let mut mock = MockAsyncDatabase::new();
    
    mock.expect_get_user()
        .with(eq(1))
        .returning(|_| Box::pin(async { Ok(User::default()) }));
    
    let result = mock.get_user(1).await;
    assert!(result.is_ok());
}
```

### Generic Traits
```rust
#[automock]
trait Repository<T> {
    fn find(&self, id: u64) -> Option<T>;
    fn save(&mut self, item: T) -> Result<u64, Error>;
}

#[test]
fn test_generic_repository() {
    let mut mock = MockRepository::<User>::new();
    
    mock.expect_find()
        .with(eq(1))
        .returning(|_| Some(User::default()));
}
```

### Associated Types
```rust
#[automock]
trait Container {
    type Item;
    fn get(&self) -> Self::Item;
}

// When mocking, specify the associated type
let mut mock = MockContainer::<Item = String>::new();
mock.expect_get()
    .returning(|| "test".to_string());
```

### Static Methods
```rust
#[automock]
impl Configuration {
    pub fn load() -> Result<Config, Error> {
        // Real implementation
    }
}

#[test]
fn test_with_static_mock() {
    let ctx = MockConfiguration::load_context();
    ctx.expect()
        .returning(|| Ok(Config::default()));
    
    let config = MockConfiguration::load();
    assert!(config.is_ok());
}
```

### Mocking External Traits
```rust
mock! {
    MyIterator {}
    
    impl Iterator for MyIterator {
        type Item = u32;
        fn next(&mut self) -> Option<u32>;
    }
}

#[test]
fn test_iterator() {
    let mut mock = MockMyIterator::new();
    
    mock.expect_next()
        .return_const(Some(1))
        .times(3)
        .then()
        .return_const(None);
    
    let collected: Vec<_> = mock.collect();
    assert_eq!(collected, vec![1, 1, 1]);
}
```

## Complex Scenarios

### Sequences and Ordering
```rust
use mockall::Sequence;

#[test]
fn test_ordered_calls() {
    let mut mock = MockDatabase::new();
    let mut seq = Sequence::new();
    
    mock.expect_begin_transaction()
        .times(1)
        .in_sequence(&mut seq)
        .returning(|| Ok(()));
    
    mock.expect_insert()
        .times(1)
        .in_sequence(&mut seq)
        .returning(|_| Ok(1));
    
    mock.expect_commit()
        .times(1)
        .in_sequence(&mut seq)
        .returning(|| Ok(()));
    
    // Calls must happen in this order
    mock.begin_transaction().unwrap();
    mock.insert(&data).unwrap();
    mock.commit().unwrap();
}
```

### Checkpoints
```rust
#[test]
fn test_with_checkpoints() {
    let mut mock = MockService::new();
    
    // First phase expectations
    mock.expect_init()
        .times(1)
        .returning(|| Ok(()));
    
    mock.init().unwrap();
    
    // Verify first phase and reset
    mock.checkpoint();
    
    // Second phase expectations
    mock.expect_process()
        .times(2)
        .returning(|_| Ok(()));
    
    mock.process(&data1).unwrap();
    mock.process(&data2).unwrap();
}
```

### Mocking with State
```rust
#[test]
fn test_stateful_mock() {
    let mut mock = MockCounter::new();
    let counter = std::cell::RefCell::new(0);
    
    mock.expect_increment()
        .returning(move || {
            let mut c = counter.borrow_mut();
            *c += 1;
            *c
        });
    
    assert_eq!(mock.increment(), 1);
    assert_eq!(mock.increment(), 2);
    assert_eq!(mock.increment(), 3);
}
```

### Fallback Behavior
```rust
#[test]
fn test_with_fallback() {
    let mut mock = MockCache::new();
    
    // Specific expectation
    mock.expect_get()
        .with(eq("special"))
        .return_const(Some("special_value".to_string()));
    
    // Fallback for any other key
    mock.expect_get()
        .returning(|_| None);
    
    assert_eq!(mock.get("special"), Some("special_value".to_string()));
    assert_eq!(mock.get("other"), None);
}
```

## Best Practices

### 1. Use Specific Expectations
```rust
// Good: Specific expectation
mock.expect_save()
    .with(eq(&user))
    .times(1)
    .returning(|_| Ok(()));

// Avoid: Too general
mock.expect_save()
    .returning(|_| Ok(()));
```

### 2. Verify All Expectations
```rust
#[test]
fn test_expectations_met() {
    let mock = MockService::new();
    
    mock.expect_foo()
        .times(1)
        .returning(|| ());
    
    // Mock automatically verifies expectations on drop
    // Will panic if expectations not met
}
```

### 3. Use Checkpoints for Complex Tests
```rust
#[test]
fn test_multi_phase() {
    let mut mock = MockWorkflow::new();
    
    // Setup phase
    setup_expectations(&mut mock);
    run_setup(&mock);
    mock.checkpoint();
    
    // Process phase
    process_expectations(&mut mock);
    run_process(&mock);
    mock.checkpoint();
    
    // Cleanup phase
    cleanup_expectations(&mut mock);
    run_cleanup(&mock);
}
```

### 4. Mock at the Right Level
```rust
// Mock external dependencies, not internal implementation
#[automock]
trait ExternalApi {
    fn fetch_data(&self) -> Result<Data, Error>;
}

// Don't mock internal helpers
fn internal_helper(x: i32) -> i32 {
    x * 2
}
```

### 5. Use Builder Pattern for Complex Mocks
```rust
struct MockBuilder {
    mock: MockService,
}

impl MockBuilder {
    fn new() -> Self {
        Self {
            mock: MockService::new(),
        }
    }
    
    fn with_user(mut self, user: User) -> Self {
        self.mock.expect_get_user()
            .return_const(Some(user));
        self
    }
    
    fn with_error(mut self) -> Self {
        self.mock.expect_get_user()
            .return_const(None);
        self
    }
    
    fn build(self) -> MockService {
        self.mock
    }
}
```

## Common Patterns

### Repository Pattern Mock
```rust
#[automock]
trait UserRepository {
    fn find_by_id(&self, id: u64) -> Result<Option<User>, Error>;
    fn find_by_email(&self, email: &str) -> Result<Option<User>, Error>;
    fn save(&mut self, user: &User) -> Result<(), Error>;
    fn delete(&mut self, id: u64) -> Result<(), Error>;
}

fn create_mock_repo_with_users(users: Vec<User>) -> MockUserRepository {
    let mut mock = MockUserRepository::new();
    
    for user in users {
        let user_clone = user.clone();
        mock.expect_find_by_id()
            .with(eq(user.id))
            .returning(move |_| Ok(Some(user_clone.clone())));
    }
    
    mock
}
```

### HTTP Client Mock
```rust
#[automock]
trait HttpClient {
    fn request(&self, req: Request) -> Result<Response, Error>;
}

fn mock_successful_api() -> MockHttpClient {
    let mut mock = MockHttpClient::new();
    
    mock.expect_request()
        .withf(|req| req.method == Method::GET)
        .returning(|_| Ok(Response {
            status: 200,
            body: r#"{"success": true}"#.to_string(),
        }));
    
    mock
}
```

### Event Bus Mock
```rust
#[automock]
trait EventBus {
    fn publish(&mut self, event: Event) -> Result<(), Error>;
    fn subscribe(&mut self, handler: Box<dyn EventHandler>);
}

#[test]
fn test_event_publishing() {
    let mut mock = MockEventBus::new();
    let events = Arc::new(Mutex::new(Vec::new()));
    let events_clone = events.clone();
    
    mock.expect_publish()
        .returning(move |event| {
            events_clone.lock().unwrap().push(event);
            Ok(())
        });
    
    // Test and verify events
}
```

## Troubleshooting

### Common Issues

1. **"No matching expectation found"**
   - Check argument matchers match exactly
   - Verify number of calls matches expectation
   - Use `withf` for debugging

2. **"Expectation called fewer times than expected"**
   - Mock drops before all calls made
   - Add `.times(..)` for flexible counts

3. **Lifetime Issues**
   - Use `returning_st` for static lifetimes
   - Clone data when needed in closures

### Debug Helpers
```rust
// Print when expectation is called
mock.expect_foo()
    .withf(|x| {
        println!("Called with: {:?}", x);
        true
    })
    .returning(|_| ());

// Conditional expectations
mock.expect_process()
    .withf(|data: &Data| {
        data.validate().is_ok()
    })
    .returning(|_| Ok(()));
```

## Integration with Other Testing Tools

### With proptest
```rust
use proptest::prelude::*;

proptest! {
    #[test]
    fn test_with_random_data(
        id in 0u64..1000,
        name in "[a-z]{5,10}"
    ) {
        let mut mock = MockDatabase::new();
        
        mock.expect_save()
            .with(always())
            .returning(move |_| Ok(id));
        
        let result = mock.save(&User { id, name });
        assert_eq!(result.unwrap(), id);
    }
}
```

### With tokio-test
```rust
use tokio_test::block_on;

#[test]
fn test_async_mock() {
    let mut mock = MockAsyncService::new();
    
    mock.expect_fetch()
        .returning(|| Box::pin(async { Ok(data) }));
    
    block_on(async {
        let result = mock.fetch().await;
        assert!(result.is_ok());
    });
}
```

## Summary

Mockall provides a comprehensive mocking solution for Rust with:
- Automatic mock generation
- Flexible expectation configuration  
- Support for complex scenarios
- Integration with async code
- Type-safe API

Use it to isolate units under test and verify behavior without dependencies.