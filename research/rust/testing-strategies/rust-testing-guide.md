# Rust Testing Guide

## Overview

Testing in Rust is a first-class feature with built-in support for unit tests, integration tests, documentation tests, and benchmarks. Rust's testing framework helps ensure code correctness and prevents regressions.

> "Program testing can be a very effective way to show the presence of bugs, but it is hopelessly inadequate for showing their absence." - <PERSON><PERSON><PERSON>

## Test Fundamentals

### Basic Test Structure
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works() {
        let result = 2 + 2;
        assert_eq!(result, 4);
    }
}
```

### Test Attributes
- `#[test]` - Marks a function as a test
- `#[cfg(test)]` - Compiles module only for testing
- `#[should_panic]` - Test passes if it panics
- `#[ignore]` - Skip test unless explicitly run

## Writing Tests

### Assertion Macros

#### `assert!` - Boolean Assertions
```rust
#[test]
fn larger_can_hold_smaller() {
    let larger = Rectangle { width: 8, height: 7 };
    let smaller = Rectangle { width: 5, height: 1 };
    
    assert!(larger.can_hold(&smaller));
    assert!(!smaller.can_hold(&larger));
}
```

#### `assert_eq!` and `assert_ne!` - Equality Assertions
```rust
#[test]
fn test_add() {
    assert_eq!(add(2, 2), 4);
    assert_ne!(add(2, 2), 5);
}

// Custom types must implement PartialEq and Debug
#[derive(PartialEq, Debug)]
struct Rectangle {
    width: u32,
    height: u32,
}
```

### Custom Failure Messages
```rust
#[test]
fn greeting_contains_name() {
    let result = greeting("Carol");
    assert!(
        result.contains("Carol"),
        "Greeting did not contain name, value was `{}`",
        result
    );
}
```

### Testing Panics

#### Basic Panic Test
```rust
#[test]
#[should_panic]
fn greater_than_100() {
    Guess::new(200); // Should panic
}
```

#### Expected Panic Message
```rust
#[test]
#[should_panic(expected = "value must be between 1 and 100")]
fn greater_than_100() {
    Guess::new(200);
}
```

### Using Result<T, E> in Tests
```rust
#[test]
fn it_works() -> Result<(), String> {
    if 2 + 2 == 4 {
        Ok(())
    } else {
        Err(String::from("two plus two does not equal four"))
    }
}

// Using ? operator
#[test]
fn test_with_result() -> Result<(), Box<dyn std::error::Error>> {
    let contents = std::fs::read_to_string("test.txt")?;
    assert!(contents.contains("expected text"));
    Ok(())
}
```

## Test Organization

### Unit Tests
Located in the same file as the code being tested:

```rust
// src/lib.rs
pub fn add_two(a: i32) -> i32 {
    internal_adder(a, 2)
}

fn internal_adder(a: i32, b: i32) -> i32 {
    a + b
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_add_two() {
        assert_eq!(add_two(2), 4);
    }

    #[test]
    fn test_internal() {
        // Can test private functions
        assert_eq!(internal_adder(2, 2), 4);
    }
}
```

### Integration Tests
Located in the `tests` directory:

```rust
// tests/integration_test.rs
use my_crate;

#[test]
fn test_public_api() {
    let result = my_crate::public_function();
    assert_eq!(result, expected_value);
}
```

### Common Test Modules
```rust
// tests/common/mod.rs
pub fn setup() -> TestContext {
    // Common setup code
}

// tests/integration_test.rs
mod common;

#[test]
fn test_with_setup() {
    let context = common::setup();
    // Use context
}
```

## Advanced Testing Patterns

### Parameterized Tests
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_multiple_cases() {
        let test_cases = vec![
            (2, 2, 4),
            (0, 5, 5),
            (-1, 1, 0),
            (100, 200, 300),
        ];

        for (a, b, expected) in test_cases {
            assert_eq!(add(a, b), expected, "Failed for {} + {}", a, b);
        }
    }
}
```

### Test Fixtures
```rust
struct TestFixture {
    db: MockDatabase,
    cache: Cache,
}

impl TestFixture {
    fn new() -> Self {
        Self {
            db: MockDatabase::new(),
            cache: Cache::new(),
        }
    }
}

#[test]
fn test_with_fixture() {
    let fixture = TestFixture::new();
    // Use fixture
}
```

### Property-Based Testing (with proptest)
```rust
use proptest::prelude::*;

proptest! {
    #[test]
    fn test_add_commutative(a: i32, b: i32) {
        assert_eq!(add(a, b), add(b, a));
    }

    #[test]
    fn test_add_associative(a: i32, b: i32, c: i32) {
        assert_eq!(add(add(a, b), c), add(a, add(b, c)));
    }
}
```

### Async Tests
```rust
#[tokio::test]
async fn test_async_function() {
    let result = async_function().await;
    assert_eq!(result, expected);
}

#[async_std::test]
async fn test_with_async_std() {
    let result = async_operation().await;
    assert!(result.is_ok());
}
```

## Documentation Tests

Doc tests run code examples in documentation:

```rust
/// Adds two numbers together.
///
/// # Examples
///
/// ```
/// use my_crate::add;
///
/// assert_eq!(add(2, 2), 4);
/// assert_eq!(add(-1, 1), 0);
/// ```
pub fn add(a: i32, b: i32) -> i32 {
    a + b
}

/// This example should panic:
///
/// ```should_panic
/// use my_crate::divide;
///
/// divide(10, 0); // This will panic
/// ```
pub fn divide(a: i32, b: i32) -> i32 {
    if b == 0 {
        panic!("Division by zero");
    }
    a / b
}
```

### Hiding Doc Test Lines
```rust
/// ```
/// # use my_crate::MyStruct;
/// # let my_struct = MyStruct::new();
/// let result = my_struct.visible_method();
/// assert_eq!(result, 42);
/// ```
```

## Test Control and Filtering

### Running Specific Tests
```bash
# Run all tests
cargo test

# Run tests with name containing 'add'
cargo test add

# Run a specific test
cargo test tests::test_add_two

# Run ignored tests
cargo test -- --ignored

# Run all tests including ignored
cargo test -- --include-ignored
```

### Test Output Control
```bash
# Show printed output for passing tests
cargo test -- --show-output

# Run tests in single thread
cargo test -- --test-threads=1

# Don't capture output
cargo test -- --nocapture
```

## Benchmark Tests

Using the unstable `test` feature:

```rust
#![feature(test)]
extern crate test;

use test::Bencher;

#[bench]
fn bench_add(b: &mut Bencher) {
    b.iter(|| {
        add(2, 2)
    });
}
```

Using Criterion.rs (stable):

```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn fibonacci(n: u64) -> u64 {
    match n {
        0 => 1,
        1 => 1,
        n => fibonacci(n-1) + fibonacci(n-2),
    }
}

fn criterion_benchmark(c: &mut Criterion) {
    c.bench_function("fib 20", |b| b.iter(|| fibonacci(black_box(20))));
}

criterion_group!(benches, criterion_benchmark);
criterion_main!(benches);
```

## Testing Best Practices

### 1. Test Naming
```rust
#[test]
fn test_user_creation_with_valid_email() { /* ... */ }

#[test]
fn test_user_creation_fails_with_invalid_email() { /* ... */ }

#[test]
fn test_user_can_update_profile() { /* ... */ }
```

### 2. Arrange-Act-Assert Pattern
```rust
#[test]
fn test_shopping_cart_total() {
    // Arrange
    let mut cart = ShoppingCart::new();
    let item1 = Item::new("Book", 10.00);
    let item2 = Item::new("CD", 15.00);
    
    // Act
    cart.add(item1);
    cart.add(item2);
    let total = cart.calculate_total();
    
    // Assert
    assert_eq!(total, 25.00);
}
```

### 3. Test Isolation
```rust
#[test]
fn test_with_cleanup() {
    let temp_dir = TempDir::new().unwrap();
    let file_path = temp_dir.path().join("test.txt");
    
    // Test operations
    std::fs::write(&file_path, "test content").unwrap();
    
    // Cleanup happens automatically when temp_dir is dropped
}
```

### 4. Mock and Stub Usage
```rust
trait Database {
    fn get_user(&self, id: u64) -> Option<User>;
}

struct MockDatabase {
    users: HashMap<u64, User>,
}

impl Database for MockDatabase {
    fn get_user(&self, id: u64) -> Option<User> {
        self.users.get(&id).cloned()
    }
}

#[test]
fn test_with_mock_database() {
    let mut mock_db = MockDatabase {
        users: HashMap::new(),
    };
    mock_db.users.insert(1, User::new("Alice"));
    
    let service = UserService::new(Box::new(mock_db));
    assert_eq!(service.get_user_name(1), Some("Alice".to_string()));
}
```

### 5. Test Coverage
```bash
# Using tarpaulin for coverage
cargo install cargo-tarpaulin
cargo tarpaulin --out Html

# Using llvm-cov
cargo install cargo-llvm-cov
cargo llvm-cov --html
```

## Testing Strategies

### 1. Unit Testing Strategy
- Test individual functions and methods
- Focus on edge cases and error conditions
- Keep tests fast and isolated
- Mock external dependencies

### 2. Integration Testing Strategy
- Test module interactions
- Use real implementations where possible
- Test API contracts
- Verify system behavior

### 3. End-to-End Testing Strategy
- Test complete user workflows
- Include external systems
- Focus on critical paths
- Run in CI/CD pipeline

### 4. Regression Testing
- Add tests for every bug fix
- Prevent issues from reoccurring
- Document the original issue in test

### 5. Performance Testing
- Benchmark critical paths
- Set performance budgets
- Monitor for regressions
- Profile under realistic loads

## Common Testing Patterns

### Builder Pattern for Test Data
```rust
struct UserBuilder {
    name: String,
    email: String,
    age: u32,
}

impl UserBuilder {
    fn new() -> Self {
        Self {
            name: "Test User".to_string(),
            email: "<EMAIL>".to_string(),
            age: 25,
        }
    }
    
    fn with_name(mut self, name: &str) -> Self {
        self.name = name.to_string();
        self
    }
    
    fn build(self) -> User {
        User {
            name: self.name,
            email: self.email,
            age: self.age,
        }
    }
}

#[test]
fn test_with_builder() {
    let user = UserBuilder::new()
        .with_name("Alice")
        .build();
    
    assert_eq!(user.name, "Alice");
}
```

### Test Helpers
```rust
mod test_helpers {
    pub fn create_test_database() -> Database {
        let db = Database::new(":memory:");
        db.run_migrations();
        db
    }
    
    pub fn with_test_server<F>(test: F)
    where
        F: FnOnce(String),
    {
        let server = TestServer::new();
        let url = server.url();
        test(url);
        // Server is automatically shut down
    }
}
```

## Testing Tools and Libraries

### Essential Testing Crates
- `mockall` - Powerful mocking framework
- `proptest` - Property-based testing
- `criterion` - Benchmarking
- `fake` - Generate fake data
- `serial_test` - Run tests serially
- `tempfile` - Temporary file handling
- `wiremock` - HTTP mocking
- `tokio-test` - Async test utilities

### CI/CD Integration
```yaml
# GitHub Actions example
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions-rs/toolchain@v1
      - run: cargo test --all-features
      - run: cargo test --no-default-features
```

## Summary

Effective testing in Rust involves:
1. Writing clear, focused tests
2. Using appropriate test types (unit, integration, doc)
3. Leveraging Rust's type system with testing
4. Following consistent patterns and practices
5. Maintaining good test coverage
6. Running tests in CI/CD pipelines