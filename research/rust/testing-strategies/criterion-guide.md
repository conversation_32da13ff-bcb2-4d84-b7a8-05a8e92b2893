# Criterion - Rust Benchmarking Guide

## Overview

Criterion.rs is a statistics-driven micro-benchmarking library for Rust that provides:
- **Statistical confidence** in measurements
- **Performance regression detection**
- **Detailed HTML reports** with charts
- **Consistent, reliable results**

## Basic Setup

### Installation
```toml
[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }

[[bench]]
name = "my_benchmark"
harness = false
```

### Simple Benchmark
```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn fibonacci(n: u64) -> u64 {
    match n {
        0 => 1,
        1 => 1,
        n => fibonacci(n-1) + fibon<PERSON><PERSON>(n-2),
    }
}

fn criterion_benchmark(c: &mut Criterion) {
    c.bench_function("fib 20", |b| b.iter(|| fibonacci(black_box(20))));
}

criterion_group!(benches, criterion_benchmark);
criterion_main!(benches);
```

## Core Concepts

### Black Box
Prevents compiler optimizations from affecting benchmarks:
```rust
// Without black_box, compiler might optimize away
c.bench_function("simple calc", |b| {
    b.iter(|| {
        let x = black_box(10);
        let y = black_box(20);
        x + y
    })
});
```

### Benchmark Groups
Organize related benchmarks:
```rust
fn bench_sorting(c: &mut Criterion) {
    let mut group = c.benchmark_group("sorting");
    
    for size in [100, 1000, 10000].iter() {
        group.bench_with_input(
            BenchmarkId::new("vec sort", size), 
            size, 
            |b, &size| {
                let mut v: Vec<i32> = (0..size).collect();
                b.iter(|| {
                    v.shuffle(&mut thread_rng());
                    v.sort();
                });
            }
        );
    }
    
    group.finish();
}
```

## Benchmarking Patterns

### Input-Based Benchmarks
```rust
fn bench_with_inputs(c: &mut Criterion) {
    let mut group = c.benchmark_group("string processing");
    
    for size in [10, 100, 1000, 10000].iter() {
        let input = "a".repeat(*size);
        
        group.bench_with_input(
            BenchmarkId::new("reverse", size),
            &input,
            |b, s| b.iter(|| s.chars().rev().collect::<String>())
        );
        
        group.bench_with_input(
            BenchmarkId::new("uppercase", size),
            &input,
            |b, s| b.iter(|| s.to_uppercase())
        );
    }
    
    group.finish();
}
```

### Throughput Benchmarks
```rust
fn bench_throughput(c: &mut Criterion) {
    let mut group = c.benchmark_group("data processing");
    
    let kb = 1024;
    for size in [kb, 64 * kb, 1024 * kb].iter() {
        group.throughput(Throughput::Bytes(*size as u64));
        
        group.bench_with_input(
            BenchmarkId::new("compress", size),
            size,
            |b, &size| {
                let data = vec![0u8; size];
                b.iter(|| compress(&data))
            }
        );
    }
    
    group.finish();
}
```

### Comparative Benchmarks
```rust
fn bench_implementations(c: &mut Criterion) {
    let mut group = c.benchmark_group("search algorithms");
    let data: Vec<i32> = (0..1000).collect();
    let target = 750;
    
    group.bench_function("linear search", |b| {
        b.iter(|| {
            data.iter().position(|&x| x == target)
        })
    });
    
    group.bench_function("binary search", |b| {
        b.iter(|| {
            data.binary_search(&target)
        })
    });
    
    group.finish();
}
```

## Advanced Features

### Custom Measurements
```rust
use criterion::measurement::WallTime;
use std::time::Duration;

fn bench_with_setup(c: &mut Criterion) {
    c.bench_function("with setup", |b| {
        b.iter_custom(|iters| {
            let mut total = Duration::ZERO;
            
            for _ in 0..iters {
                // Setup (not measured)
                let data = setup_test_data();
                
                // Only measure this part
                let start = std::time::Instant::now();
                process_data(&data);
                total += start.elapsed();
            }
            
            total
        })
    });
}
```

### Async Benchmarks
```rust
use criterion::async_executor::FuturesExecutor;

fn bench_async(c: &mut Criterion) {
    c.benchmark_group("async operations")
        .bench_function("async fetch", |b| {
            b.to_async(FuturesExecutor).iter(|| async {
                fetch_data().await
            })
        });
}
```

### Parametric Benchmarks
```rust
fn bench_parametric(c: &mut Criterion) {
    let mut group = c.benchmark_group("matrix multiply");
    
    for size in [4, 8, 16, 32, 64].iter() {
        group.bench_with_input(
            BenchmarkId::from_parameter(size),
            size,
            |b, &size| {
                let a = Matrix::random(size, size);
                let b = Matrix::random(size, size);
                b.iter(|| &a * &b);
            }
        );
    }
    
    group.finish();
}
```

## Configuration

### Benchmark Configuration
```rust
fn configured_benchmark(c: &mut Criterion) {
    let mut group = c.benchmark_group("configured");
    
    // Configure the group
    group.warm_up_time(Duration::from_secs(1));
    group.measurement_time(Duration::from_secs(5));
    group.sample_size(200);
    group.confidence_level(0.99);
    group.significance_level(0.01);
    
    group.bench_function("test", |b| b.iter(|| expensive_function()));
    
    group.finish();
}
```

### Global Configuration
```rust
use criterion::{Criterion, PlotConfiguration};

fn custom_criterion() -> Criterion {
    Criterion::default()
        .sample_size(500)
        .warm_up_time(Duration::from_secs(3))
        .measurement_time(Duration::from_secs(10))
        .plot_config(PlotConfiguration::default())
}

fn main() {
    let mut criterion = custom_criterion();
    // Run benchmarks with custom configuration
}
```

## Analysis and Reporting

### Statistical Analysis
Criterion provides:
- **Mean and median** execution time
- **Standard deviation** and variance
- **Confidence intervals**
- **Linear regression** for detecting trends
- **Outlier detection**

### HTML Reports
Located in `target/criterion/`:
- **Summary plots**: Overview of all benchmarks
- **Individual reports**: Detailed analysis per benchmark
- **Comparison plots**: Performance over time
- **Regression plots**: Trend analysis

### Comparing Results
```rust
// Compare different implementations
fn bench_compare(c: &mut Criterion) {
    let mut group = c.benchmark_group("compare");
    
    // Baseline implementation
    group.bench_function("baseline", |b| {
        b.iter(|| baseline_implementation())
    });
    
    // Optimized implementation
    group.bench_function("optimized", |b| {
        b.iter(|| optimized_implementation())
    });
    
    group.finish();
}
```

## Best Practices

### 1. Isolate What You Measure
```rust
// Good: Only measures the operation
c.bench_function("parse", |b| {
    let input = prepare_input(); // Outside iter()
    b.iter(|| parse(&input))
});

// Bad: Includes setup in measurement
c.bench_function("parse", |b| {
    b.iter(|| {
        let input = prepare_input(); // Inside iter()
        parse(&input)
    })
});
```

### 2. Use Realistic Data
```rust
fn bench_real_world(c: &mut Criterion) {
    // Load real-world test data
    let real_data = load_production_dataset();
    
    c.bench_function("process real data", |b| {
        b.iter(|| process(&real_data))
    });
}
```

### 3. Prevent Optimization
```rust
// Use black_box to prevent optimization
c.bench_function("calculation", |b| {
    b.iter(|| {
        let result = calculate(black_box(42));
        black_box(result); // Prevent result from being optimized away
    })
});
```

### 4. Benchmark Different Scenarios
```rust
fn bench_scenarios(c: &mut Criterion) {
    let mut group = c.benchmark_group("cache performance");
    
    // Best case: all cache hits
    group.bench_function("cache hits", |b| {
        let mut cache = Cache::new();
        fill_cache(&mut cache);
        b.iter(|| cache.get("existing_key"))
    });
    
    // Worst case: all cache misses
    group.bench_function("cache misses", |b| {
        let cache = Cache::new();
        b.iter(|| cache.get("non_existing_key"))
    });
    
    // Realistic case: mixed
    group.bench_function("mixed access", |b| {
        let mut cache = Cache::new();
        partially_fill_cache(&mut cache);
        let mut rng = thread_rng();
        b.iter(|| {
            if rng.gen_bool(0.7) {
                cache.get("existing_key")
            } else {
                cache.get("missing_key")
            }
        })
    });
    
    group.finish();
}
```

### 5. Long-Running Benchmarks
```rust
fn bench_batch(c: &mut Criterion) {
    c.bench_function("batch process", |b| {
        b.iter_batched(
            || generate_batch_data(1000), // Setup
            |data| process_batch(data),    // Benchmark
            BatchSize::SmallInput           // Hint about input size
        )
    });
}
```

## Integration Examples

### With Cargo Features
```rust
#[cfg(feature = "simd")]
fn bench_simd(c: &mut Criterion) {
    c.bench_function("simd version", |b| {
        b.iter(|| simd_computation())
    });
}

#[cfg(not(feature = "simd"))]
fn bench_scalar(c: &mut Criterion) {
    c.bench_function("scalar version", |b| {
        b.iter(|| scalar_computation())
    });
}
```

### Custom Profiling
```rust
use criterion::profiler::Profiler;

struct FlameGraphProfiler;

impl Profiler for FlameGraphProfiler {
    fn start_profiling(&mut self, benchmark_id: &str, benchmark_dir: &Path) {
        // Start flamegraph profiling
    }
    
    fn stop_profiling(&mut self, benchmark_id: &str, benchmark_dir: &Path) {
        // Stop profiling and save results
    }
}

fn main() {
    let mut criterion = Criterion::default()
        .with_profiler(FlameGraphProfiler);
    // Run benchmarks
}
```

### Regression Testing
```rust
// Save baseline: cargo bench -- --save-baseline before
// Compare: cargo bench -- --baseline before

fn bench_for_regression(c: &mut Criterion) {
    c.bench_function("critical path", |b| {
        b.iter(|| critical_operation())
    });
}

// In CI:
// cargo bench -- --baseline main
// This compares against the 'main' baseline
```

## Common Patterns

### Memory Allocation Benchmarks
```rust
fn bench_allocation(c: &mut Criterion) {
    let mut group = c.benchmark_group("allocation");
    
    group.bench_function("vec preallocated", |b| {
        b.iter(|| {
            let mut v = Vec::with_capacity(1000);
            for i in 0..1000 {
                v.push(i);
            }
            v
        })
    });
    
    group.bench_function("vec growing", |b| {
        b.iter(|| {
            let mut v = Vec::new();
            for i in 0..1000 {
                v.push(i);
            }
            v
        })
    });
    
    group.finish();
}
```

### String Processing
```rust
fn bench_string_ops(c: &mut Criterion) {
    let text = "The quick brown fox jumps over the lazy dog".repeat(100);
    
    c.bench_function("string split", |b| {
        b.iter(|| text.split_whitespace().count())
    });
    
    c.bench_function("string replace", |b| {
        b.iter(|| text.replace("fox", "cat"))
    });
    
    c.bench_function("string to_lowercase", |b| {
        b.iter(|| text.to_lowercase())
    });
}
```

### Parsing Benchmarks
```rust
fn bench_parsing(c: &mut Criterion) {
    let json_str = r#"{"name":"John","age":30,"city":"New York"}"#;
    let csv_str = "name,age,city\nJohn,30,New York\n";
    
    let mut group = c.benchmark_group("parsing");
    
    group.bench_function("json", |b| {
        b.iter(|| serde_json::from_str::<Value>(json_str))
    });
    
    group.bench_function("csv", |b| {
        b.iter(|| parse_csv(csv_str))
    });
    
    group.finish();
}
```

## Performance Tips

### 1. Minimize Noise
- Close other applications
- Disable CPU frequency scaling
- Run on consistent hardware
- Use sufficient iterations

### 2. Benchmark Structure
```rust
criterion_group!{
    name = benches;
    config = Criterion::default().sample_size(1000);
    targets = bench_function_1, bench_function_2
}
```

### 3. Debugging Slow Benchmarks
```rust
// Use fewer iterations for debugging
#[cfg(debug_assertions)]
fn get_sample_size() -> usize { 10 }

#[cfg(not(debug_assertions))]
fn get_sample_size() -> usize { 100 }

fn bench_debug_friendly(c: &mut Criterion) {
    c.benchmark_group("debug")
        .sample_size(get_sample_size())
        .bench_function("test", |b| b.iter(|| operation()));
}
```

## Summary

Criterion provides:
- **Statistical rigor** in performance measurement
- **Beautiful reports** with actionable insights
- **Regression detection** to catch performance issues
- **Flexible configuration** for various scenarios
- **Integration** with Rust's ecosystem

Use Criterion to:
- Optimize critical code paths
- Prevent performance regressions
- Compare implementation alternatives
- Document performance characteristics
- Build performance-aware applications