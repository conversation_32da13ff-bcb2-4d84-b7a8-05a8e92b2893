# Cargo Continuous Integration Guide

## Overview

Continuous Integration (CI) testing is essential for maintaining code quality and ensuring your Rust project works across different environments and Rust versions.

## GitHub Actions

### Basic Workflow

```yaml
name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  CARGO_TERM_COLOR: always

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - uses: Swatinem/rust-cache@v2
      - run: cargo build --verbose
      - run: cargo test --verbose
```

### Matrix Testing

```yaml
name: CI

on: [push, pull_request]

jobs:
  test:
    name: Test
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        rust: [stable, beta, nightly]
        exclude:
          # Exclude nightly on Windows (example)
          - os: windows-latest
            rust: nightly
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ matrix.rust }}
      - uses: Swatinem/rust-cache@v2
      - run: cargo test --all-features
```

### Advanced GitHub Actions Workflow

```yaml
name: Rust CI

on:
  push:
    branches: [ main, staging ]
  pull_request:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1

jobs:
  # Check formatting
  fmt:
    name: Rustfmt
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt
      - run: cargo fmt --all -- --check

  # Lint with clippy
  clippy:
    name: Clippy
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
        with:
          components: clippy
      - uses: Swatinem/rust-cache@v2
      - run: cargo clippy --all-targets --all-features -- -D warnings

  # Test on multiple platforms
  test:
    name: Test Suite
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        rust: [stable, beta]
        include:
          - os: ubuntu-latest
            rust: nightly
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ matrix.rust }}
      - uses: Swatinem/rust-cache@v2
      - run: cargo test --all-features
      - run: cargo test --no-default-features

  # Security audit
  security_audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: rustsec/audit-check@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

  # Code coverage
  coverage:
    name: Code Coverage
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - uses: Swatinem/rust-cache@v2
      - uses: taiki-e/install-action@cargo-tarpaulin
      - run: cargo tarpaulin --verbose --all-features --workspace --timeout 120 --out xml
      - uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
```

## GitLab CI

### Basic Configuration

```yaml
# .gitlab-ci.yml
stages:
  - test

rust-latest:
  stage: test
  image: rust:latest
  script:
    - cargo build --verbose
    - cargo test --verbose

rust-nightly:
  stage: test
  image: rustlang/rust:nightly
  script:
    - cargo build --verbose
    - cargo test --verbose
  allow_failure: true
```

### Advanced GitLab CI

```yaml
variables:
  CARGO_HOME: $CI_PROJECT_DIR/.cargo

stages:
  - check
  - test
  - deploy

before_script:
  - apt-get update -yqq
  - apt-get install -yqq --no-install-recommends build-essential

.rust_cache:
  cache:
    key: "$CI_COMMIT_REF_SLUG-rust"
    paths:
      - .cargo/
      - target/

fmt:
  stage: check
  image: rust:latest
  extends: .rust_cache
  script:
    - rustup component add rustfmt
    - cargo fmt -- --check

clippy:
  stage: check
  image: rust:latest
  extends: .rust_cache
  script:
    - rustup component add clippy
    - cargo clippy --all-targets --all-features -- -D warnings

test:stable:
  stage: test
  image: rust:latest
  extends: .rust_cache
  script:
    - cargo test --all-features --verbose

test:nightly:
  stage: test
  image: rustlang/rust:nightly
  extends: .rust_cache
  script:
    - cargo test --all-features --verbose
  allow_failure: true
```

## Dependency Management in CI

### Verify Latest Dependencies

```yaml
# GitHub Actions job
verify-latest-deps:
  name: Verify Latest Dependencies
  runs-on: ubuntu-latest
  continue-on-error: true  # Don't fail the entire workflow
  steps:
    - uses: actions/checkout@v4
    - uses: dtolnay/rust-toolchain@stable
    - name: Update to latest dependencies
      run: |
        cargo update
        cargo build --all-targets
        cargo test --all-features
```

### Lock File Strategies

#### Option 1: Don't Check In Cargo.lock (Libraries)
```yaml
# Add to .gitignore for libraries
Cargo.lock
```

#### Option 2: Check In Cargo.lock (Applications)
```yaml
# CI verifies exact dependencies
test-locked:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    - run: cargo test --locked
```

#### Option 3: Scheduled Dependency Updates
```yaml
on:
  schedule:
    - cron: '0 0 * * MON'  # Weekly on Monday

jobs:
  update-deps:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: |
          cargo update
          cargo test
      - uses: peter-evans/create-pull-request@v5
        with:
          title: "chore: update dependencies"
          commit-message: "chore: update Cargo.lock"
```

## Rust Version Verification

### Minimum Supported Rust Version (MSRV)

```yaml
# Using cargo-msrv
msrv:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    - uses: taiki-e/install-action@cargo-msrv
    - run: cargo msrv verify

# Manual MSRV check
msrv-check:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    - uses: dtolnay/rust-toolchain@master
      with:
        toolchain: "1.70.0"  # Your MSRV
    - run: cargo check --all-features
```

## Cross-Compilation in CI

```yaml
cross-compile:
  runs-on: ubuntu-latest
  strategy:
    matrix:
      target:
        - x86_64-unknown-linux-musl
        - aarch64-unknown-linux-gnu
        - x86_64-pc-windows-gnu
  steps:
    - uses: actions/checkout@v4
    - uses: dtolnay/rust-toolchain@stable
      with:
        targets: ${{ matrix.target }}
    - uses: taiki-e/install-action@cross
    - run: cross build --target ${{ matrix.target }}
```

## Caching Strategies

### GitHub Actions Caching

```yaml
- uses: Swatinem/rust-cache@v2
  with:
    # Optional configurations
    cache-targets: true
    cache-on-failure: true
    cache-all-crates: true
    shared-key: "my-cache"
    key: ${{ matrix.os }}-${{ matrix.rust }}
```

### Manual Cache Configuration

```yaml
- uses: actions/cache@v3
  with:
    path: |
      ~/.cargo/bin/
      ~/.cargo/registry/index/
      ~/.cargo/registry/cache/
      ~/.cargo/git/db/
      target/
    key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
    restore-keys: |
      ${{ runner.os }}-cargo-
```

## Security Scanning

### Dependency Audit

```yaml
security:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    - uses: rustsec/audit-check@v1
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
```

### License Checking

```yaml
license-check:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    - uses: taiki-e/install-action@cargo-deny
    - run: cargo deny check licenses
```

## Performance Testing

### Benchmarks in CI

```yaml
benchmarks:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    - uses: dtolnay/rust-toolchain@stable
    - uses: Swatinem/rust-cache@v2
    - run: cargo bench --no-run  # Build benchmarks
    - run: cargo bench -- --output-format bencher | tee output.txt
    - uses: benchmark-action/github-action-benchmark@v1
      with:
        tool: 'cargo'
        output-file-path: output.txt
        github-token: ${{ secrets.GITHUB_TOKEN }}
        auto-push: true
```

## Docker-based CI

### Dockerfile for CI

```dockerfile
FROM rust:1.75 as builder

WORKDIR /app
COPY Cargo.toml Cargo.lock ./
COPY src ./src

RUN cargo build --release

FROM debian:bookworm-slim
COPY --from=builder /app/target/release/myapp /usr/local/bin/
CMD ["myapp"]
```

### CircleCI with Docker

```yaml
version: 2.1

executors:
  rust-executor:
    docker:
      - image: rust:1.75
    working_directory: ~/repo

jobs:
  test:
    executor: rust-executor
    steps:
      - checkout
      - restore_cache:
          keys:
            - v1-cargo-cache-{{ checksum "Cargo.lock" }}
      - run:
          name: Build
          command: cargo build --verbose
      - run:
          name: Test
          command: cargo test --verbose
      - save_cache:
          paths:
            - /usr/local/cargo/registry
            - target/debug/.fingerprint
            - target/debug/build
            - target/debug/deps
          key: v1-cargo-cache-{{ checksum "Cargo.lock" }}

workflows:
  version: 2
  test:
    jobs:
      - test
```

## Best Practices

### 1. Fast Feedback
- Run fast checks (fmt, clippy) first
- Use `cargo check` before `cargo build`
- Parallelize independent jobs

### 2. Resource Optimization
- Cache dependencies aggressively
- Use `sccache` for distributed caching
- Limit parallel jobs on resource-constrained runners

### 3. Comprehensive Testing
```yaml
test-all:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    - run: cargo test --all-features
    - run: cargo test --no-default-features
    - run: cargo test --features "feature1,feature2"
```

### 4. Documentation Testing
```yaml
doc-test:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    - run: cargo test --doc
    - run: cargo doc --no-deps --all-features
```

### 5. Release Automation
```yaml
release:
  if: startsWith(github.ref, 'refs/tags/')
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    - run: cargo publish --token ${{ secrets.CRATES_TOKEN }}
```

## Summary

Key CI practices for Rust:
1. Test across multiple platforms and Rust versions
2. Use caching to speed up builds
3. Run security audits regularly
4. Automate code quality checks
5. Consider MSRV for libraries
6. Monitor dependency updates
7. Set up comprehensive test matrices