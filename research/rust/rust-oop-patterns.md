# Object-Oriented Programming in Rust

## Overview

Rust is not a traditional object-oriented language, but it provides features that enable object-oriented design patterns:
- Structs and enums for data encapsulation
- Methods and associated functions via `impl` blocks
- Traits for defining shared behavior
- Trait objects for runtime polymorphism

## Encapsulation with Structs

### Basic Struct with Methods

```rust
pub struct Rectangle {
    width: f64,
    height: f64,
}

impl Rectangle {
    // Constructor (associated function)
    pub fn new(width: f64, height: f64) -> Self {
        Rectangle { width, height }
    }
    
    // Getter methods
    pub fn width(&self) -> f64 {
        self.width
    }
    
    pub fn height(&self) -> f64 {
        self.height
    }
    
    // Instance method
    pub fn area(&self) -> f64 {
        self.width * self.height
    }
    
    // Mutating method
    pub fn scale(&mut self, factor: f64) {
        self.width *= factor;
        self.height *= factor;
    }
    
    // Associated function (static method)
    pub fn square(size: f64) -> Self {
        Rectangle {
            width: size,
            height: size,
        }
    }
}

// Usage
let mut rect = Rectangle::new(10.0, 20.0);
println!("Area: {}", rect.area());
rect.scale(2.0);
let square = Rectangle::square(5.0);
```

### Privacy and Encapsulation

```rust
pub struct BankAccount {
    // Private fields
    account_number: String,
    balance: f64,
}

impl BankAccount {
    pub fn new(account_number: String, initial_balance: f64) -> Self {
        BankAccount {
            account_number,
            balance: initial_balance,
        }
    }
    
    // Public method to read balance
    pub fn balance(&self) -> f64 {
        self.balance
    }
    
    // Controlled modification through methods
    pub fn deposit(&mut self, amount: f64) -> Result<(), String> {
        if amount <= 0.0 {
            return Err("Deposit amount must be positive".to_string());
        }
        self.balance += amount;
        Ok(())
    }
    
    pub fn withdraw(&mut self, amount: f64) -> Result<(), String> {
        if amount > self.balance {
            return Err("Insufficient funds".to_string());
        }
        self.balance -= amount;
        Ok(())
    }
}
```

## Inheritance vs Composition

### Rust Prefers Composition

```rust
// Instead of inheritance, use composition
pub struct Engine {
    horsepower: u32,
    fuel_type: String,
}

impl Engine {
    pub fn start(&self) {
        println!("Engine starting...");
    }
    
    pub fn stop(&self) {
        println!("Engine stopping...");
    }
}

pub struct Car {
    engine: Engine,
    model: String,
    year: u32,
}

impl Car {
    pub fn new(model: String, year: u32, horsepower: u32, fuel_type: String) -> Self {
        Car {
            engine: Engine { horsepower, fuel_type },
            model,
            year,
        }
    }
    
    // Delegate to composed object
    pub fn start(&self) {
        self.engine.start();
        println!("{} {} is ready to drive", self.year, self.model);
    }
}
```

### Achieving Inheritance-like Behavior with Traits

```rust
// Base behavior
trait Vehicle {
    fn drive(&self);
    fn stop(&self);
}

// Extended behavior
trait ElectricVehicle: Vehicle {
    fn charge(&self);
    fn battery_level(&self) -> u8;
}

struct Tesla {
    battery: u8,
}

impl Vehicle for Tesla {
    fn drive(&self) {
        println!("Tesla driving silently");
    }
    
    fn stop(&self) {
        println!("Tesla stopping with regenerative braking");
    }
}

impl ElectricVehicle for Tesla {
    fn charge(&self) {
        println!("Charging at Supercharger");
    }
    
    fn battery_level(&self) -> u8 {
        self.battery
    }
}
```

## Polymorphism with Trait Objects

### Static Dispatch (Compile-time Polymorphism)

```rust
trait Draw {
    fn draw(&self);
}

struct Circle {
    radius: f64,
}

struct Square {
    side: f64,
}

impl Draw for Circle {
    fn draw(&self) {
        println!("Drawing circle with radius {}", self.radius);
    }
}

impl Draw for Square {
    fn draw(&self) {
        println!("Drawing square with side {}", self.side);
    }
}

// Generic function with static dispatch
fn draw_shape<T: Draw>(shape: &T) {
    shape.draw();
}
```

### Dynamic Dispatch (Runtime Polymorphism)

```rust
// Using trait objects for dynamic dispatch
pub struct Screen {
    components: Vec<Box<dyn Draw>>,
}

impl Screen {
    pub fn new() -> Self {
        Screen {
            components: Vec::new(),
        }
    }
    
    pub fn add_component(&mut self, component: Box<dyn Draw>) {
        self.components.push(component);
    }
    
    pub fn render(&self) {
        for component in &self.components {
            component.draw();
        }
    }
}

// Usage
let mut screen = Screen::new();
screen.add_component(Box::new(Circle { radius: 5.0 }));
screen.add_component(Box::new(Square { side: 10.0 }));
screen.render();
```

## Common OOP Design Patterns

### Factory Pattern

```rust
trait Animal {
    fn speak(&self);
}

struct Dog;
struct Cat;

impl Animal for Dog {
    fn speak(&self) {
        println!("Woof!");
    }
}

impl Animal for Cat {
    fn speak(&self) {
        println!("Meow!");
    }
}

// Factory
struct AnimalFactory;

impl AnimalFactory {
    fn create_animal(animal_type: &str) -> Option<Box<dyn Animal>> {
        match animal_type {
            "dog" => Some(Box::new(Dog)),
            "cat" => Some(Box::new(Cat)),
            _ => None,
        }
    }
}

// Usage
if let Some(animal) = AnimalFactory::create_animal("dog") {
    animal.speak();
}
```

### Builder Pattern

```rust
#[derive(Debug, Clone)]
pub struct Server {
    host: String,
    port: u16,
    timeout: Option<u64>,
    max_connections: Option<u32>,
}

pub struct ServerBuilder {
    host: String,
    port: u16,
    timeout: Option<u64>,
    max_connections: Option<u32>,
}

impl ServerBuilder {
    pub fn new(host: String, port: u16) -> Self {
        ServerBuilder {
            host,
            port,
            timeout: None,
            max_connections: None,
        }
    }
    
    pub fn timeout(mut self, timeout: u64) -> Self {
        self.timeout = Some(timeout);
        self
    }
    
    pub fn max_connections(mut self, max: u32) -> Self {
        self.max_connections = Some(max);
        self
    }
    
    pub fn build(self) -> Server {
        Server {
            host: self.host,
            port: self.port,
            timeout: self.timeout,
            max_connections: self.max_connections,
        }
    }
}

// Usage
let server = ServerBuilder::new("localhost".to_string(), 8080)
    .timeout(30)
    .max_connections(100)
    .build();
```

### State Pattern

```rust
// States
trait State {
    fn request_review(self: Box<Self>) -> Box<dyn State>;
    fn approve(self: Box<Self>) -> Box<dyn State>;
    fn content<'a>(&self, post: &'a Post) -> &'a str {
        ""
    }
}

struct Draft;
struct PendingReview;
struct Published;

impl State for Draft {
    fn request_review(self: Box<Self>) -> Box<dyn State> {
        Box::new(PendingReview)
    }
    
    fn approve(self: Box<Self>) -> Box<dyn State> {
        self
    }
}

impl State for PendingReview {
    fn request_review(self: Box<Self>) -> Box<dyn State> {
        self
    }
    
    fn approve(self: Box<Self>) -> Box<dyn State> {
        Box::new(Published)
    }
}

impl State for Published {
    fn request_review(self: Box<Self>) -> Box<dyn State> {
        self
    }
    
    fn approve(self: Box<Self>) -> Box<dyn State> {
        self
    }
    
    fn content<'a>(&self, post: &'a Post) -> &'a str {
        &post.content
    }
}

// Context
pub struct Post {
    state: Option<Box<dyn State>>,
    content: String,
}

impl Post {
    pub fn new() -> Post {
        Post {
            state: Some(Box::new(Draft)),
            content: String::new(),
        }
    }
    
    pub fn add_text(&mut self, text: &str) {
        self.content.push_str(text);
    }
    
    pub fn content(&self) -> &str {
        self.state.as_ref().unwrap().content(self)
    }
    
    pub fn request_review(&mut self) {
        if let Some(s) = self.state.take() {
            self.state = Some(s.request_review())
        }
    }
    
    pub fn approve(&mut self) {
        if let Some(s) = self.state.take() {
            self.state = Some(s.approve())
        }
    }
}
```

### Observer Pattern

```rust
use std::cell::RefCell;
use std::rc::{Rc, Weak};

trait Observer {
    fn update(&self, message: &str);
}

struct Subject {
    observers: RefCell<Vec<Weak<dyn Observer>>>,
}

impl Subject {
    fn new() -> Self {
        Subject {
            observers: RefCell::new(Vec::new()),
        }
    }
    
    fn attach(&self, observer: Weak<dyn Observer>) {
        self.observers.borrow_mut().push(observer);
    }
    
    fn notify(&self, message: &str) {
        self.observers.borrow_mut().retain(|observer| {
            if let Some(observer) = observer.upgrade() {
                observer.update(message);
                true
            } else {
                false
            }
        });
    }
}

struct ConcreteObserver {
    name: String,
}

impl Observer for ConcreteObserver {
    fn update(&self, message: &str) {
        println!("{} received: {}", self.name, message);
    }
}
```

### Strategy Pattern

```rust
trait PaymentStrategy {
    fn pay(&self, amount: f64);
}

struct CreditCard {
    number: String,
}

struct PayPal {
    email: String,
}

impl PaymentStrategy for CreditCard {
    fn pay(&self, amount: f64) {
        println!("Paid ${} using Credit Card {}", amount, self.number);
    }
}

impl PaymentStrategy for PayPal {
    fn pay(&self, amount: f64) {
        println!("Paid ${} using PayPal account {}", amount, self.email);
    }
}

struct ShoppingCart {
    items: Vec<f64>,
}

impl ShoppingCart {
    fn new() -> Self {
        ShoppingCart { items: Vec::new() }
    }
    
    fn add_item(&mut self, price: f64) {
        self.items.push(price);
    }
    
    fn calculate_total(&self) -> f64 {
        self.items.iter().sum()
    }
    
    fn checkout(&self, payment_method: &dyn PaymentStrategy) {
        let total = self.calculate_total();
        payment_method.pay(total);
    }
}
```

## Advanced OOP Concepts

### Multiple Trait Implementation

```rust
trait Printable {
    fn print(&self);
}

trait Saveable {
    fn save(&self);
}

struct Document {
    content: String,
}

impl Printable for Document {
    fn print(&self) {
        println!("Printing: {}", self.content);
    }
}

impl Saveable for Document {
    fn save(&self) {
        println!("Saving: {}", self.content);
    }
}

// Function accepting multiple traits
fn process_document<T: Printable + Saveable>(doc: &T) {
    doc.print();
    doc.save();
}
```

### Self Types and Method Chaining

```rust
struct QueryBuilder {
    query: String,
}

impl QueryBuilder {
    fn new() -> Self {
        QueryBuilder {
            query: String::new(),
        }
    }
    
    fn select(mut self, columns: &str) -> Self {
        self.query.push_str(&format!("SELECT {} ", columns));
        self
    }
    
    fn from(mut self, table: &str) -> Self {
        self.query.push_str(&format!("FROM {} ", table));
        self
    }
    
    fn where_clause(mut self, condition: &str) -> Self {
        self.query.push_str(&format!("WHERE {} ", condition));
        self
    }
    
    fn build(self) -> String {
        self.query
    }
}

// Usage
let query = QueryBuilder::new()
    .select("*")
    .from("users")
    .where_clause("age > 18")
    .build();
```

## Best Practices

1. **Prefer composition over inheritance**
2. **Use traits for shared behavior**
3. **Keep structs focused and cohesive**
4. **Use trait objects when runtime polymorphism is needed**
5. **Leverage Rust's type system for compile-time guarantees**
6. **Consider performance implications of dynamic dispatch**
7. **Use builder pattern for complex object construction**

## Summary

While Rust is not a traditional OOP language, it provides powerful features for object-oriented design:
- Encapsulation through modules and privacy
- Polymorphism through traits and generics
- Composition as a flexible alternative to inheritance
- Rich pattern support for common OOP designs