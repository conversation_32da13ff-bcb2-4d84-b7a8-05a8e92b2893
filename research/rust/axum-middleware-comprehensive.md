# Axum Middleware - Comprehensive Guide

## Overview

Axum doesn't have its own middleware system. Instead, it integrates with Tower's middleware ecosystem, allowing you to use any Tower-compatible middleware with your Axum applications.

## Creating Middleware

### 1. Using `from_fn` (Simplest Approach)

```rust
use axum::{
    middleware::{self, Next},
    response::Response,
    routing::get,
    Router,
    http::Request,
};

async fn my_middleware(
    request: Request,
    next: Next,
) -> Response {
    // Do something before the request
    println!("Before handling request");

    // Call the next middleware or handler
    let response = next.run(request).await;

    // Do something after the response
    println!("After handling request");

    response
}

let app = Router::new()
    .route("/", get(handler))
    .layer(middleware::from_fn(my_middleware));
```

### 2. Middleware with Error Handling

```rust
use axum::{
    http::StatusCode,
    middleware::{self, Next},
    response::{IntoResponse, Response},
    Router,
};

async fn auth_middleware(
    req: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let auth_header = req.headers()
        .get(http::header::AUTHORIZATION)
        .and_then(|header| header.to_str().ok());

    match auth_header {
        Some(auth) if auth.starts_with("Bearer ") => {
            // Valid authentication
            Ok(next.run(req).await)
        }
        _ => Err(StatusCode::UNAUTHORIZED),
    }
}

let app = Router::new()
    .route("/protected", get(protected_handler))
    .route_layer(middleware::from_fn(auth_middleware));
```

### 3. Middleware with State

```rust
use axum::{
    extract::State,
    middleware::{self, Next},
    response::Response,
    Router,
};

#[derive(Clone)]
struct AppState {
    api_key: String,
}

async fn api_key_middleware(
    State(state): State<AppState>,
    req: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let api_key = req.headers()
        .get("x-api-key")
        .and_then(|h| h.to_str().ok());

    if api_key == Some(&state.api_key) {
        Ok(next.run(req).await)
    } else {
        Err(StatusCode::UNAUTHORIZED)
    }
}

let state = AppState {
    api_key: "secret-key".to_string(),
};

let app = Router::new()
    .route("/api", get(handler))
    .layer(middleware::from_fn_with_state(state.clone(), api_key_middleware))
    .with_state(state);
```

### 4. Passing Data Between Middleware

```rust
use axum::{
    extract::Extension,
    middleware::{self, Next},
    response::Response,
    Router,
};

#[derive(Clone)]
struct CurrentUser {
    id: u64,
    name: String,
}

async fn auth_middleware(
    mut req: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Perform authentication...
    let user = CurrentUser {
        id: 123,
        name: "Alice".to_string(),
    };

    // Insert user into request extensions
    req.extensions_mut().insert(user);
    
    Ok(next.run(req).await)
}

async fn handler(Extension(user): Extension<CurrentUser>) -> String {
    format!("Hello, {}!", user.name)
}

let app = Router::new()
    .route("/", get(handler))
    .layer(middleware::from_fn(auth_middleware));
```

## Using Tower Middleware

### Compression Middleware

```rust
use tower_http::compression::CompressionLayer;

let app = Router::new()
    .route("/", get(handler))
    .layer(CompressionLayer::new());
```

### CORS Middleware

```rust
use tower_http::cors::{CorsLayer, Any};
use http::{Method, HeaderName};

let cors = CorsLayer::new()
    .allow_origin(Any)
    .allow_methods([Method::GET, Method::POST])
    .allow_headers([HeaderName::from_static("content-type")]);

let app = Router::new()
    .route("/api", get(handler))
    .layer(cors);
```

### Timeout Middleware

```rust
use tower::timeout::TimeoutLayer;
use std::time::Duration;

let app = Router::new()
    .route("/slow", get(slow_handler))
    .layer(TimeoutLayer::new(Duration::from_secs(10)));
```

### Rate Limiting Middleware

```rust
use tower::ServiceBuilder;
use tower_http::limit::RequestBodyLimitLayer;
use tower::load_shed::LoadShedLayer;

let app = Router::new()
    .route("/", get(handler))
    .layer(
        ServiceBuilder::new()
            .layer(LoadShedLayer::new()) // Shed load when overwhelmed
            .layer(RequestBodyLimitLayer::new(10 * 1024 * 1024)) // 10MB limit
    );
```

## Middleware Ordering

Middleware forms layers like an onion, with the request passing through outer layers first:

```rust
use tower::ServiceBuilder;
use tower_http::{
    trace::TraceLayer,
    compression::CompressionLayer,
    cors::CorsLayer,
};

let app = Router::new()
    .route("/", get(handler))
    .layer(
        ServiceBuilder::new()
            // Order matters! These run top to bottom for requests,
            // and bottom to top for responses
            .layer(TraceLayer::new_for_http())     // 1st in, 3rd out
            .layer(CorsLayer::permissive())         // 2nd in, 2nd out
            .layer(CompressionLayer::new())         // 3rd in, 1st out
    );
```

## Practical Examples

### Logging Middleware

```rust
use axum::{
    middleware::{self, Next},
    response::Response,
    Router,
};
use std::time::Instant;

async fn logging_middleware(
    req: Request,
    next: Next,
) -> Response {
    let method = req.method().clone();
    let uri = req.uri().clone();
    let start = Instant::now();

    let response = next.run(req).await;

    let duration = start.elapsed();
    let status = response.status();

    println!(
        "{} {} {} - {:?}",
        method,
        uri,
        status,
        duration
    );

    response
}

let app = Router::new()
    .route("/", get(handler))
    .layer(middleware::from_fn(logging_middleware));
```

### Request ID Middleware

```rust
use axum::{
    http::HeaderValue,
    middleware::{self, Next},
    response::Response,
    Router,
};
use uuid::Uuid;

async fn request_id_middleware(
    mut req: Request,
    next: Next,
) -> Response {
    let request_id = Uuid::new_v4().to_string();
    
    // Add to request extensions
    req.extensions_mut().insert(request_id.clone());
    
    // Add to request headers
    req.headers_mut().insert(
        "x-request-id",
        HeaderValue::from_str(&request_id).unwrap(),
    );

    let mut response = next.run(req).await;
    
    // Add to response headers
    response.headers_mut().insert(
        "x-request-id",
        HeaderValue::from_str(&request_id).unwrap(),
    );

    response
}
```

### Authentication with JWT

```rust
use axum::{
    extract::Extension,
    http::{header::AUTHORIZATION, StatusCode},
    middleware::{self, Next},
    response::Response,
    Router,
};
use jsonwebtoken::{decode, Validation, DecodingKey};

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,
    exp: usize,
}

async fn jwt_auth_middleware(
    mut req: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let auth_header = req.headers()
        .get(AUTHORIZATION)
        .and_then(|h| h.to_str().ok())
        .ok_or(StatusCode::UNAUTHORIZED)?;

    let token = auth_header
        .strip_prefix("Bearer ")
        .ok_or(StatusCode::UNAUTHORIZED)?;

    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_secret(b"secret"),
        &Validation::default(),
    ).map_err(|_| StatusCode::UNAUTHORIZED)?;

    // Add claims to request extensions
    req.extensions_mut().insert(token_data.claims);

    Ok(next.run(req).await)
}

async fn protected_handler(
    Extension(claims): Extension<Claims>,
) -> String {
    format!("Hello, {}!", claims.sub)
}

let app = Router::new()
    .route("/protected", get(protected_handler))
    .route_layer(middleware::from_fn(jwt_auth_middleware));
```

## Error Handling in Middleware

### Using HandleErrorLayer

```rust
use axum::{
    error_handling::HandleErrorLayer,
    http::StatusCode,
    BoxError, Router,
};
use tower::{timeout::TimeoutLayer, ServiceBuilder};
use std::time::Duration;

async fn handle_timeout_error(err: BoxError) -> (StatusCode, String) {
    if err.is::<tower::timeout::error::Elapsed>() {
        (
            StatusCode::REQUEST_TIMEOUT,
            "Request took too long".to_string(),
        )
    } else {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            format!("Unhandled internal error: {}", err),
        )
    }
}

let app = Router::new()
    .route("/slow", get(slow_handler))
    .layer(
        ServiceBuilder::new()
            .layer(HandleErrorLayer::new(handle_timeout_error))
            .layer(TimeoutLayer::new(Duration::from_secs(10)))
    );
```

## Creating Custom Tower Services

For more complex middleware, implement the Tower Service trait:

```rust
use tower::Service;
use std::{
    future::Future,
    pin::Pin,
    task::{Context, Poll},
};

#[derive(Clone)]
struct MyMiddleware<S> {
    inner: S,
}

impl<S> Service<Request> for MyMiddleware<S>
where
    S: Service<Request, Response = Response> + Clone + Send + 'static,
    S::Future: Send + 'static,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>> + Send>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, request: Request) -> Self::Future {
        let future = self.inner.call(request);
        
        Box::pin(async move {
            // Do something with the request/response
            let response = future.await?;
            Ok(response)
        })
    }
}

// Usage with Layer
use tower::Layer;

#[derive(Clone)]
struct MyLayer;

impl<S> Layer<S> for MyLayer {
    type Service = MyMiddleware<S>;

    fn layer(&self, inner: S) -> Self::Service {
        MyMiddleware { inner }
    }
}

let app = Router::new()
    .route("/", get(handler))
    .layer(MyLayer);
```

## Best Practices

1. **Order matters**: Apply middleware in the correct order
2. **Handle errors gracefully**: Don't let middleware errors crash the server
3. **Keep middleware focused**: Each middleware should have a single responsibility
4. **Use existing Tower middleware**: Don't reinvent the wheel
5. **Be mindful of performance**: Middleware runs on every request
6. **Use appropriate scoping**: Apply middleware only where needed with `route_layer`
7. **Document middleware behavior**: Especially side effects and requirements

## Common Middleware Stack

```rust
use tower::ServiceBuilder;
use tower_http::{
    trace::TraceLayer,
    compression::CompressionLayer,
    cors::CorsLayer,
    timeout::TimeoutLayer,
};
use std::time::Duration;

let middleware = ServiceBuilder::new()
    // Handle errors from other middleware
    .layer(HandleErrorLayer::new(handle_error))
    // Set a timeout
    .layer(TimeoutLayer::new(Duration::from_secs(10)))
    // Compress responses
    .layer(CompressionLayer::new())
    // Enable CORS
    .layer(CorsLayer::permissive())
    // Trace requests
    .layer(TraceLayer::new_for_http())
    // Custom authentication
    .layer(middleware::from_fn(auth_middleware));

let app = Router::new()
    .route("/api", get(handler))
    .layer(middleware);
```