# Rust Macros - Comprehensive Guide

## Overview

Macros in Rust are a way of writing code that writes code (metaprogramming). Rust has two types of macros:
1. **Declarative Macros** (`macro_rules!`)
2. **Procedural Macros** (custom derive, attribute-like, function-like)

## Declarative Macros (macro_rules!)

### Basic Syntax

```rust
macro_rules! say_hello {
    () => {
        println!("Hello!");
    };
}

say_hello!(); // Prints: Hello!
```

### Pattern Matching

```rust
macro_rules! create_function {
    // This macro takes an argument of designator `ident` and
    // creates a function with that name
    ($func_name:ident) => {
        fn $func_name() {
            println!("You called {:?}()", stringify!($func_name));
        }
    };
}

create_function!(foo);
create_function!(bar);

foo(); // Prints: You called "foo"()
bar(); // Prints: You called "bar"()
```

### Multiple Patterns

```rust
macro_rules! test {
    // Match a single expression
    ($left:expr; and $right:expr) => {
        println!("{:?} and {:?} is {:?}",
                 stringify!($left),
                 stringify!($right),
                 $left && $right)
    };
    // Match multiple expressions
    ($($arg:tt)*) => {
        println!("Multiple args: {:?}", stringify!($($arg)*));
    };
}

test!(1 + 1 == 2; and 2 * 2 == 4);
test!(true, false, 123, "hello");
```

### Repetition

```rust
macro_rules! vec_strs {
    (
        $($element:expr),* $(,)?
    ) => {
        vec![$(String::from($element)),*]
    };
}

let v = vec_strs!["hello", "world", "foo", "bar"];
// Equivalent to: vec![String::from("hello"), String::from("world"), ...]

// More complex repetition
macro_rules! hash_map {
    ($($key:expr => $value:expr),* $(,)?) => {
        {
            let mut map = std::collections::HashMap::new();
            $(
                map.insert($key, $value);
            )*
            map
        }
    };
}

let scores = hash_map! {
    "Alice" => 100,
    "Bob" => 87,
    "Charlie" => 95,
};
```

### Macro Designators

```rust
macro_rules! demonstrate_designators {
    // Different types of macro designators
    ($e:expr) => { println!("Expression: {}", $e); };
    ($i:ident) => { let $i = 42; };
    ($t:ty) => { let _x: $t = Default::default(); };
    ($p:pat) => { match Some(42) { $p => {} _ => {} } };
    ($s:stmt) => { $s };
    ($b:block) => { $b };
    ($l:literal) => { println!("Literal: {}", $l); };
    ($m:meta) => { #[$m] struct Dummy; };
    ($tt:tt) => { println!("Token tree: {}", stringify!($tt)); };
    ($v:vis) => { $v struct Visible; };
    ($lt:lifetime) => { struct Ref<$lt> { data: &$lt str } };
}
```

### Advanced Patterns

```rust
// Recursive macros
macro_rules! count_exprs {
    () => (0);
    ($head:expr) => (1);
    ($head:expr, $($tail:expr),*) => (1 + count_exprs!($($tail),*));
}

let count = count_exprs!(1, 2, 3, 4, 5);
println!("Number of expressions: {}", count); // 5

// Internal rules
macro_rules! impl_trait {
    // Public API
    ($t:ty) => {
        impl_trait!(@impl $t);
    };
    
    // Internal implementation
    (@impl $t:ty) => {
        impl MyTrait for $t {
            fn method(&self) {
                println!("MyTrait for {}", stringify!($t));
            }
        }
    };
}
```

### Macro Hygiene

```rust
macro_rules! using_a {
    ($e:expr) => {
        {
            let a = 42;
            $e
        }
    }
}

let a = 10;
let result = using_a!(a * 2); // Uses the outer 'a' (10), not the macro's 'a' (42)
println!("{}", result); // 20

// Using $crate for macro re-exports
#[macro_export]
macro_rules! my_vec {
    ($($x:expr),*) => {
        {
            let mut temp_vec = $crate::Vec::new();
            $(
                temp_vec.push($x);
            )*
            temp_vec
        }
    };
}
```

## Procedural Macros

### Setup

```toml
# Cargo.toml for proc macro crate
[package]
name = "my_macros"
version = "0.1.0"

[lib]
proc-macro = true

[dependencies]
syn = "2.0"
quote = "1.0"
proc-macro2 = "1.0"
```

### Custom Derive Macros

```rust
// In my_macros/src/lib.rs
use proc_macro::TokenStream;
use quote::quote;
use syn::{parse_macro_input, DeriveInput};

#[proc_macro_derive(MyTrait)]
pub fn my_trait_derive(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;
    
    let expanded = quote! {
        impl MyTrait for #name {
            fn my_method(&self) {
                println!("MyTrait implemented for {}", stringify!(#name));
            }
        }
    };
    
    TokenStream::from(expanded)
}

// Usage in another crate
use my_macros::MyTrait;

#[derive(MyTrait)]
struct MyStruct {
    field: i32,
}
```

### Attribute-like Macros

```rust
// Define the macro
#[proc_macro_attribute]
pub fn route(args: TokenStream, input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as ItemFn);
    let args = parse_macro_input!(args as AttributeArgs);
    
    // Parse route path from args
    let route_path = // ... extract from args
    
    let fn_name = &input.sig.ident;
    let fn_block = &input.block;
    
    let expanded = quote! {
        fn #fn_name() {
            register_route(#route_path);
            #fn_block
        }
    };
    
    TokenStream::from(expanded)
}

// Usage
#[route("/api/users")]
fn handle_users() {
    // Handler implementation
}
```

### Function-like Procedural Macros

```rust
// Define the macro
#[proc_macro]
pub fn sql(input: TokenStream) -> TokenStream {
    let input_str = input.to_string();
    
    // Validate SQL syntax
    validate_sql(&input_str);
    
    let expanded = quote! {
        SqlQuery::new(#input_str)
    };
    
    TokenStream::from(expanded)
}

// Usage
let query = sql!(SELECT * FROM users WHERE age > 18);
```

## Common Macro Patterns

### Builder Pattern Macro

```rust
macro_rules! builder {
    ($struct_name:ident { $($field:ident : $type:ty),* $(,)? }) => {
        pub struct $struct_name {
            $(pub $field: $type,)*
        }
        
        paste::paste! {
            pub struct [<$struct_name Builder>] {
                $($field: Option<$type>,)*
            }
            
            impl [<$struct_name Builder>] {
                pub fn new() -> Self {
                    Self {
                        $($field: None,)*
                    }
                }
                
                $(
                    pub fn $field(mut self, value: $type) -> Self {
                        self.$field = Some(value);
                        self
                    }
                )*
                
                pub fn build(self) -> Result<$struct_name, &'static str> {
                    Ok($struct_name {
                        $($field: self.$field.ok_or(concat!("Missing field: ", stringify!($field)))?,)*
                    })
                }
            }
        }
    };
}

builder! {
    Person {
        name: String,
        age: u32,
        email: String,
    }
}

let person = PersonBuilder::new()
    .name("Alice".to_string())
    .age(30)
    .email("<EMAIL>".to_string())
    .build()?;
```

### Debug Printing Macro

```rust
macro_rules! dbg_multi {
    ($($val:expr),* $(,)?) => {
        ($(dbg!($val)),*)
    };
}

let (a, b, c) = dbg_multi!(1 + 2, "hello", vec![1, 2, 3]);
```

### Enum Dispatch Macro

```rust
macro_rules! enum_dispatch {
    ($enum_name:ident, $method:ident, $($variant:ident),*) => {
        impl $enum_name {
            pub fn $method(&self) {
                match self {
                    $(Self::$variant(inner) => inner.$method(),)*
                }
            }
        }
    };
}

enum Animal {
    Dog(Dog),
    Cat(Cat),
    Bird(Bird),
}

enum_dispatch!(Animal, make_sound, Dog, Cat, Bird);
```

## Debugging Macros

### Compile-time Debugging

```rust
macro_rules! show_expansion {
    ($($tokens:tt)*) => {
        compile_error!(stringify!($($tokens)*));
    };
}

// This will show the expansion at compile time
// show_expansion!(1 + 2 * 3);
```

### Using trace_macros

```rust
#![feature(trace_macros)]

trace_macros!(true);
my_macro!(args);
trace_macros!(false);
```

### cargo expand

```bash
# Install cargo-expand
cargo install cargo-expand

# See macro expansions
cargo expand
```

### Testing Macros

```rust
#[cfg(test)]
mod tests {
    #[test]
    fn test_vec_macro() {
        let v = vec![1, 2, 3];
        assert_eq!(v.len(), 3);
        assert_eq!(v[0], 1);
    }
    
    #[test]
    fn test_custom_macro() {
        macro_rules! test_macro {
            ($x:expr) => { $x * 2 };
        }
        
        assert_eq!(test_macro!(5), 10);
    }
}
```

## Best Practices

### 1. Prefer Functions Over Macros

```rust
// Often better as a function
fn create_vec_of_strings(items: &[&str]) -> Vec<String> {
    items.iter().map(|s| s.to_string()).collect()
}

// Than as a macro
macro_rules! vec_of_strings {
    ($($item:expr),*) => {
        vec![$(String::from($item)),*]
    };
}
```

### 2. Use Clear Naming

```rust
// Good: Clear what it does
macro_rules! assert_positive {
    ($val:expr) => {
        assert!($val > 0, "{} is not positive", $val);
    };
}

// Bad: Unclear naming
macro_rules! chk {
    ($v:expr) => { assert!($v > 0); };
}
```

### 3. Document Macro Behavior

```rust
/// Creates a HashMap with the given key-value pairs.
/// 
/// # Examples
/// 
/// ```
/// let map = hash_map! {
///     "a" => 1,
///     "b" => 2,
/// };
/// ```
#[macro_export]
macro_rules! hash_map {
    ($($k:expr => $v:expr),* $(,)?) => {
        // Implementation
    };
}
```

### 4. Handle Edge Cases

```rust
macro_rules! min {
    // Handle single element
    ($x:expr) => { $x };
    // Handle two elements
    ($x:expr, $y:expr) => { if $x < $y { $x } else { $y } };
    // Handle multiple elements
    ($x:expr, $($rest:expr),+) => {
        std::cmp::min($x, min!($($rest),+))
    };
}
```

### 5. Use Internal Rules for Complex Macros

```rust
macro_rules! implement_numeric_ops {
    ($t:ty) => {
        implement_numeric_ops!(@impl Add, add, $t);
        implement_numeric_ops!(@impl Sub, sub, $t);
        implement_numeric_ops!(@impl Mul, mul, $t);
        implement_numeric_ops!(@impl Div, div, $t);
    };
    
    (@impl $trait:ident, $method:ident, $t:ty) => {
        impl std::ops::$trait for $t {
            type Output = Self;
            
            fn $method(self, rhs: Self) -> Self::Output {
                // Implementation
            }
        }
    };
}
```

## Summary

Macros are powerful tools in Rust that enable:
- Code generation and reduction of boilerplate
- Domain-specific languages (DSLs)
- Conditional compilation
- Custom syntax extensions

Key points:
- Use macros sparingly - prefer regular functions when possible
- Document macro behavior thoroughly
- Test macros comprehensively
- Use procedural macros for more complex transformations
- Leverage existing macro crates when appropriate