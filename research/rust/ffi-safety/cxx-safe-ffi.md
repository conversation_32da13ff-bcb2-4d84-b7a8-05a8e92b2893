# CXX: Safe FFI Between Rust and C++

## Overview
CXX provides a "safe mechanism for calling C++ code from Rust and Rust code from C++", achieving 100% safety on the Rust side by controlling both sides of the FFI boundary.

## Core Safety Principles

### 1. Controlled FFI Boundary
- Requires explicit definition of FFI boundary in a shared module
- Both Rust and C++ signatures must be explicitly defined
- Static analysis detects and prevents FFI-related errors

### 2. Safety Guarantees
- **100% Safe Rust Side**: No unsafe blocks needed in Rust code
- **Automatic ABI Handling**: Complex ABI compatibility managed automatically
- **Zero-Overhead**: Native performance with safety guarantees
- **Type Safety**: Prevents passing types that could cause memory unsafety

### 3. Design Philosophy
- Intentionally restrictive to ensure safety
- Supports "reasonably expressive set of functionality"
- Trade expressiveness for guaranteed safety
- "The core safety claim under this new model is that auditing just the C++ side would be sufficient to catch all problems"

## Key Safety Features

### Type Restrictions
- Prevents passing types with internal pointers by value
- Detects types that could be invalidated across FFI boundary
- Enforces ownership rules across languages

### Static Analysis
- Detects mismatched function signatures
- Validates type compatibility at compile time
- Prevents common FFI pitfalls automatically

### Supported Types
- Built-in support for common standard library types
- Safe handling of:
  - Strings (`String`, `&str`, `std::string`)
  - Vectors (`Vec<T>`, `std::vector<T>`)
  - Unique pointers (`Box<T>`, `std::unique_ptr<T>`)
  - Shared primitives

### Template Safety
- Handles C++ template instantiations safely
- Prevents template-related memory issues
- Explicit instantiation requirements

## Usage Pattern

```rust
#[cxx::bridge]
mod ffi {
    // Shared types visible to both languages
    struct SharedType {
        field: i32,
    }
    
    extern "Rust" {
        // Rust functions callable from C++
        fn rust_function(x: i32) -> i32;
    }
    
    extern "C++" {
        // C++ functions callable from Rust
        fn cpp_function(x: i32) -> i32;
    }
}
```

## Best Practices

1. **Define Clear Boundaries**: Use cxx::bridge modules to define FFI interfaces
2. **Leverage Type Safety**: Use CXX's built-in type mappings
3. **Avoid Raw Pointers**: Use smart pointers and references
4. **Explicit Lifetime Management**: Clear ownership semantics across boundary
5. **Compile-Time Validation**: Let CXX catch errors at compile time

## Advantages Over Traditional FFI
- No manual `unsafe` blocks needed
- Automatic memory management
- Type-safe by default
- Catches more errors at compile time
- Simpler mental model for developers