# Tree-sitter <PERSON><PERSON> Patterns and Safety Strategies

## Core FFI Safety Patterns

### 1. Extensive Use of Unsafe Blocks
- Wraps C functions with safe Rust abstractions
- Uses `NonNull` and `PhantomData` for type safety
- Carefully manages raw pointers and memory

### 2. Memory Management Strategies
- Implements `Drop` traits for automatic resource cleanup
- Uses `Box` for heap allocation of callbacks
- Explicitly frees C-allocated memory with custom free functions

### 3. Ownership and Lifetime Management
- Uses lifetime parameters to track object relationships
- Implements `Clone` and `Copy` for safe pointer duplication
- Provides safe wrappers around raw pointer operations

### 4. Error Handling Patterns
- Returns `Option` or `Result` types from potentially unsafe operations
- Validates pointer validity before dereferencing
- Provides detailed error types like `LanguageError`

## Example Safety Pattern

```rust
impl Language {
    fn new(builder: LanguageFn) -> Self {
        // Safely converts raw pointer to Rust struct
        Self(unsafe { builder.into_raw()().cast() })
    }
}
```

## Key Safety Techniques

### Pointer Management
- Uses `NonNull` for non-null pointer guarantees
- Validates pointers before dereferencing
- Wraps raw pointers in newtype patterns

### Resource Cleanup
- Automatic cleanup via `Drop` implementations
- Explicit free functions for C-allocated memory
- Careful tracking of ownership across FFI boundary

### Type Safety
- `PhantomData` for lifetime tracking
- Newtype wrappers around raw pointers
- Strong typing for FFI function signatures

## Best Practices Demonstrated
1. Minimize unsafe code scope
2. Document safety invariants
3. Use type system for compile-time guarantees
4. Provide safe public APIs over unsafe internals
5. Implement proper cleanup mechanisms