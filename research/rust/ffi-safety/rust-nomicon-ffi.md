# Rust Nomicon FFI Safety Documentation

## Key FFI Safety Principles

### 1. Unsafe Boundaries
- Foreign function calls are inherently unsafe
- "Foreign functions are assumed to be unsafe so calls to them need to be wrapped with `unsafe {}`"
- Requires explicit promises about memory safety and correct usage

### 2. Memory Layout and Representation
- Use `#[repr(C)]` to ensure struct compatibility with C
- "Rust guarantees that the layout of a `struct` is compatible with the platform's representation in C only if the `#[repr(C)]` attribute is applied"
- Critical for ABI compatibility

### 3. Pointer Handling
- Be cautious with raw pointers
- Leverage "nullable pointer optimization" for safe nullable function pointers
- Use `Option<T>` for potentially null pointers
- Example: `Option<extern "C" fn(c_int) -> c_int>` for nullable function pointers

### 4. Calling Conventions
- Default is C calling convention
- Support for multiple calling conventions:
  - `cdecl`
  - `stdcall`
  - `system`
  - `C-unwind`
- Specify calling convention explicitly for non-standard interfaces

### 5. Safe Abstraction Patterns
- Wrap unsafe FFI functions in safe Rust interfaces
- Validate inputs and manage memory allocation
- Use types like `CString` for NUL-terminated strings
- Example pattern:
  ```rust
  pub fn safe_wrapper(input: &str) -> Result<String, Error> {
      unsafe {
          // Validate and convert input
          // Call FFI function
          // Convert and validate output
      }
  }
  ```

### 6. Ownership and Resource Management
- Use destructors (`Drop` trait) to manage foreign resources
- Ensure proper resource release, especially during panics
- Pattern for resource management:
  ```rust
  struct ForeignResource(*mut c_void);
  
  impl Drop for ForeignResource {
      fn drop(&mut self) {
          unsafe { foreign_free(self.0); }
      }
  }
  ```

### 7. Callback Handling
- Use raw pointers to pass context to callbacks
- Implement synchronization mechanisms for asynchronous callbacks
- Unregister callbacks before Rust object destruction
- Critical: Ensure callback lifetime doesn't exceed Rust object lifetime

### 8. Unwinding Considerations
- Be aware of ABI boundaries and unwinding behavior
- Use appropriate ABI strings (`C-unwind` vs `C`)
- Handle potential panics with `catch_unwind()`
- Never let Rust panics cross FFI boundaries (unless using `C-unwind`)

## Best Practices

1. **Minimize Unsafe Code**: Keep unsafe blocks small and focused
2. **Create Safe Wrappers**: Build safe abstractions over unsafe FFI
3. **Validate Everything**: Check all inputs and outputs at FFI boundary
4. **Use Standard Types**: Leverage `libc` crate for C type definitions
5. **Document Assumptions**: Clearly document safety requirements
6. **Handle Errors Gracefully**: Convert foreign errors to Rust Result types
7. **Test Thoroughly**: Include edge cases and error conditions
8. **Consider Thread Safety**: Document and enforce thread safety requirements