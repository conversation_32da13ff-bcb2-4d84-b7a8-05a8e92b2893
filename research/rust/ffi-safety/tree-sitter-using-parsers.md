# Tree-sitter Using Parsers Documentation

## Overview
Tree-sitter's parsing functionality is primarily implemented through a C API, documented in the `tree_sitter/api.h` header file. Multiple language bindings provide idiomatic access patterns for different programming languages.

## Language Bindings
Official bindings are available for:
- Go
- Java
- JavaScript (Node.js)
- Kotlin
- Python
- **Rust** (our focus)
- Zig

## Key Concepts
- Tree-sitter uses fundamental concepts applicable across all language bindings
- C-specific implementation details form the foundation
- Language bindings wrap the C API with idiomatic patterns

## FFI Considerations
- The core parser is implemented in C
- All language bindings must interface with the C API
- Memory management and safety considerations vary by language binding

## Documentation Note
This page serves as an introduction. Detailed parser initialization, memory management, and FFI patterns require consulting:
- The C API header (`tree_sitter/api.h`)
- Language-specific binding documentation
- Implementation examples in each binding