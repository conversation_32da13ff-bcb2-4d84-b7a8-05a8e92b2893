# Tree-sitter Parser Safety Considerations

## Unsafe Operations and FFI

### Raw Pointer Management
The Parser struct provides unsafe methods for raw pointer conversion:

#### `from_raw()`
- **Safety Requirement**: "ptr must be non-null"
- Reconstructs a Parser from a raw pointer
- Used when receiving parser pointers from C code

#### `into_raw()`
- Consumes the Parser and returns a raw pointer
- **Safety Warning**: Potential use-after-free risks
- Caller must ensure proper cleanup via `ts_parser_delete()` or reconstructing

### Cancellation Flag
Both `cancellation_flag()` and `set_cancellation_flag()` are explicitly marked as unsafe:
- Involve direct FFI calls
- Manage parsing cancellation across thread boundaries
- Require careful synchronization

## Memory Management

### Automatic Cleanup
- Implements `Drop` trait for automatic resource cleanup
- Ensures C resources are properly freed when <PERSON><PERSON><PERSON> goes out of scope

### Thread Safety
- Implements both `Send` and `Sync` traits
- Parser can be safely shared across threads
- Internal synchronization handled by Tree-sitter C library

### State Management
- `reset()` method clears parser state between operations
- Important for reusing parsers across multiple parse operations
- Prevents state leakage between parses

## Safety Best Practices

### Error Handling
```rust
// Parsing returns Option<Tree> to handle failures
let tree = parser.parse(source_code, old_tree)?;
```

### Language Configuration
```rust
// Language setting can fail, always check Result
parser.set_language(&language)?;
```

### Resource Management
```rust
// Parser automatically cleans up when dropped
{
    let parser = Parser::new();
    // Use parser...
} // Automatic cleanup here
```

## Critical Safety Notes

### Caller Responsibilities
"It's a caller responsibility to adjust parser's state like disable logging or dot graphs printing if this may cause issues like use after free."

This highlights that certain parser configurations may create unsafe conditions that the caller must manage.

### Deprecated Methods
Several methods are deprecated in favor of safer alternatives:
- Use newer methods that return `Result` or `Option`
- Avoid methods that don't properly handle errors

## FFI Safety Patterns

### Safe Wrapper Pattern
```rust
pub fn parse(&mut self, text: impl AsRef<[u8]>, old_tree: Option<&Tree>) -> Option<Tree> {
    unsafe {
        // SAFETY: Tree-sitter guarantees the returned tree pointer is valid
        // and transfers ownership to us. The Tree wrapper will call
        // ts_tree_delete in its Drop implementation.
        let raw_tree = ts_parser_parse(...);
        NonNull::new(raw_tree).map(|ptr| Tree::from_raw(ptr))
    }
}
```

### Thread-Safe Access
```rust
// SAFETY: Tree-sitter's parser is thread-safe. The C library uses
// internal locking to ensure concurrent access is safe.
unsafe impl Send for Parser {}
unsafe impl Sync for Parser {}
```

## Common Pitfalls
1. Not checking language compatibility before parsing
2. Forgetting to handle parse failures (None returns)
3. Misconfiguring parser state that could cause use-after-free
4. Not properly managing parser lifetime when using raw pointers