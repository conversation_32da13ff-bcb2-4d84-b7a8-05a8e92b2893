# Send and Sync Traits for FFI Types

## Overview
Send and Sync are "unsafe traits" fundamental to <PERSON><PERSON>'s concurrency safety. When wrapping foreign resources, careful consideration is needed for thread safety.

## Key Principles

### Send Trait
- A type is `Send` if it can be safely transferred between threads
- Ownership can move from one thread to another
- Most types are automatically `Send` if their components are

### Sync Trait
- A type is `Sync` if it can be safely shared between threads
- References (`&T`) can be used concurrently from multiple threads
- `T` is `Sync` if and only if `&T` is `Send`

## FFI Considerations

### Raw Pointers
- Raw pointers (`*mut T`, `*const T`) are neither `Send` nor `Sync` by default
- Must explicitly implement if the underlying resource is thread-safe

### Foreign Resources
When wrapping foreign resources, consider:
1. **Thread Safety of C Library**: Is the library thread-safe?
2. **Ownership Model**: Can ownership be transferred between threads?
3. **Shared Access**: Can multiple threads safely access the resource?
4. **Memory Management**: Is allocation/deallocation thread-safe?

## Implementation Patterns

### Basic FFI Wrapper
```rust
struct ForeignResource(*mut c_void);

// Only implement if the C library guarantees thread safety
unsafe impl Send for ForeignResource {}
unsafe impl Sync for ForeignResource {}
```

### Conditional Thread Safety
```rust
struct ConditionalResource<T> {
    ptr: *mut T,
    _marker: PhantomData<T>,
}

// Only Send/Sync if T is Send/Sync
unsafe impl<T: Send> Send for ConditionalResource<T> {}
unsafe impl<T: Sync> Sync for ConditionalResource<T> {}
```

### Non-Thread-Safe Resources
```rust
struct NonThreadSafe {
    handle: *mut c_void,
    _not_send_sync: PhantomData<*const ()>, // Prevents Send/Sync
}
```

## Safety Requirements

### For Send Implementation
- Resource can be safely destroyed on any thread
- No thread-local state dependencies
- Memory can be safely deallocated from any thread

### For Sync Implementation
- Concurrent read access is safe
- Internal synchronization (if mutable)
- No data races possible

## Best Practices

1. **Default to Conservative**: Don't implement Send/Sync unless certain
2. **Document Thread Safety**: Include SAFETY comments explaining why implementation is safe
3. **Test Thoroughly**: Include multi-threaded tests
4. **Consider Interior Mutability**: Use `Mutex` or `RwLock` for shared mutable state
5. **Validate C Library Claims**: Verify thread safety claims in C documentation

## Example: Thread-Safe FFI Wrapper

```rust
/// Wrapper around thread-safe C library handle
pub struct SafeHandle {
    handle: NonNull<c_void>,
}

// SAFETY: The wrapped C library guarantees thread safety for all
// operations on the handle. The library uses internal locking.
unsafe impl Send for SafeHandle {}
unsafe impl Sync for SafeHandle {}

impl Drop for SafeHandle {
    fn drop(&mut self) {
        // SAFETY: Library guarantees safe cleanup from any thread
        unsafe { c_library_free(self.handle.as_ptr()) }
    }
}
```

## Common Pitfalls
- Assuming C libraries are thread-safe by default
- Forgetting about destruction/cleanup thread safety
- Not considering callback execution contexts
- Ignoring thread-local storage in C libraries