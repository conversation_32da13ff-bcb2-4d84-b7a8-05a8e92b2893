# libc Crate: Safe Usage Patterns

## Overview
The libc crate provides "Raw FFI bindings to platform libraries like libc", offering Rust developers direct interface with low-level system libraries across different platforms.

## Key Characteristics
- Raw bindings to system-level functions and constants
- Cross-platform support (multiple architectures and OS)
- Direct interaction with platform-specific APIs
- Extensive type definitions for C interoperability

## Common Types for FFI

### Primitive Types
- `c_void`: Equivalent to C's `void`
- `c_char`, `c_uchar`: Character types
- `c_int`, `c_uint`: Integer types
- `c_long`, `c_ulong`: Long integer types
- `c_float`, `c_double`: Floating-point types

### Pointer Types
- `size_t`, `ssize_t`: Size types
- `ptrdiff_t`: Pointer difference type
- `intptr_t`, `uintptr_t`: Pointer-sized integers

### System Structures
- Extensive collection of structs representing system structures
- Platform-specific definitions for compatibility
- File, network, and process management structures

## Memory Allocation Functions

### Standard Allocation
```rust
use libc::{malloc, free, calloc, realloc};

unsafe {
    // Allocate memory
    let ptr = malloc(size) as *mut u8;
    
    // Zero-initialized allocation
    let ptr = calloc(count, size) as *mut u8;
    
    // Resize allocation
    let new_ptr = realloc(ptr as *mut c_void, new_size);
    
    // Free memory
    free(ptr as *mut c_void);
}
```

### Safety Considerations
- Always check for null returns from allocation functions
- Match every `malloc` with corresponding `free`
- Never use freed memory
- Be careful with `realloc` - it may move memory

## Best Practices

### 1. Type Safety
```rust
use std::os::raw::{c_int, c_void};
use libc;

// Use proper C types for FFI
extern "C" {
    fn foreign_function(arg: c_int) -> *mut c_void;
}
```

### 2. Error Handling
```rust
use libc::{errno, EINVAL, ENOMEM};

unsafe {
    let result = libc::some_function();
    if result == -1 {
        match *libc::__errno_location() {
            EINVAL => // Invalid argument
            ENOMEM => // Out of memory
            _ => // Other error
        }
    }
}
```

### 3. Memory Management Pattern
```rust
use std::ptr::NonNull;

pub struct CBuffer {
    ptr: NonNull<u8>,
    len: usize,
}

impl CBuffer {
    pub fn new(size: usize) -> Option<Self> {
        unsafe {
            let ptr = libc::malloc(size) as *mut u8;
            NonNull::new(ptr).map(|ptr| CBuffer { ptr, len: size })
        }
    }
}

impl Drop for CBuffer {
    fn drop(&mut self) {
        unsafe {
            libc::free(self.ptr.as_ptr() as *mut libc::c_void);
        }
    }
}
```

### 4. String Handling
```rust
use std::ffi::{CStr, CString};

// Rust to C
let rust_str = "Hello";
let c_string = CString::new(rust_str).expect("CString::new failed");
let c_ptr = c_string.as_ptr();

// C to Rust
unsafe {
    let c_str = CStr::from_ptr(c_ptr);
    let rust_str = c_str.to_str().expect("Invalid UTF-8");
}
```

## Common Patterns

### File Operations
```rust
use libc::{open, close, read, write, O_RDONLY, O_WRONLY, O_CREAT};

unsafe {
    let fd = open(path.as_ptr(), O_RDONLY);
    if fd != -1 {
        // Use file descriptor
        close(fd);
    }
}
```

### Error Constants
- Comprehensive set of error constants (EACCES, EINVAL, etc.)
- Platform-specific error handling
- Thread-safe error retrieval

## Platform Considerations
- Use conditional compilation for platform-specific code
- Be aware of platform differences in type sizes
- Test on target platforms

## Safety Rules
1. All libc functions are `unsafe` - wrap in safe abstractions
2. Validate all inputs before passing to C functions
3. Check return values and handle errors appropriately
4. Ensure proper cleanup in all code paths
5. Document safety invariants with SAFETY comments