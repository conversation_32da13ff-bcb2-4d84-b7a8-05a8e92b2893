# Tree-sitter Rust Bindings Documentation

## Overview
The tree-sitter crate provides Rust bindings to the Tree-sitter parsing library, offering safe wrappers around the core C implementation.

## Build Targets and Features
- **Standard Library Support**: Default feature
- **WebAssembly Support**: Via `wasm` feature flag
- **Low-level FFI Module**: Direct C interoperability when needed

## Core API Patterns

### Parser Management
```rust
let mut parser = Parser::new();
parser.set_language(&tree_sitter_rust::LANGUAGE.into())
    .expect("Error loading Rust grammar");
```

### Parsing Options
1. **String/Slice Input**: Direct parsing of Rust strings
2. **Custom Text Provider**: Callback-based text provision
3. **Encoding Support**: Both UTF-8 and UTF-16

### Key Types
- `Parser`: Main parsing interface
- `Language`: Grammar representation
- `Tree`: Parse result container
- `InputEdit`: Incremental parsing support

## Safety Features

### Error Handling
- `LanguageError`: Language loading failures
- `IncludedRangesError`: Range specification errors
- `QueryError`: Query compilation errors
- Explicit error handling for language compatibility

### Memory Management
- Safe wrappers around C structures
- Ownership rules enforced through Rust's type system
- Automatic cleanup via Drop implementations

## FFI Considerations
- Low-level FFI module available for direct C interop
- Safe abstractions hide unsafe operations
- Compatible with multiple build targets (native, WASM)

## Incremental Parsing
Supports efficient re-parsing through:
- `InputEdit` for tracking changes
- Preserving parse state between edits
- Optimized for real-time parsing scenarios