# SAFETY Comment Guidelines

## Overview
The `undocumented_unsafe_blocks` Clippy lint enforces documentation for all unsafe code blocks, requiring explicit safety justifications.

## SAFETY Comment Requirements

### Format Requirements
- **Must use line comments**: `// SAFETY:`
- **Must appear immediately before unsafe block**: No code between comment and unsafe
- **Must start with "SAFETY:"**: Case-sensitive prefix required
- **Must explain safety justification**: Not just state that it's safe

### Valid Examples

```rust
// SAFETY: The caller guarantees that `ptr` is valid and aligned
unsafe { *ptr }

// SAFETY: We just checked that index < len, so this won't panic
unsafe { slice.get_unchecked(index) }

// SAFETY: The C library guarantees this pointer is non-null and
// remains valid for the lifetime of the handle
unsafe { NonNull::new_unchecked(ffi_ptr) }
```

### Invalid Examples

```rust
// Missing SAFETY comment
unsafe { *ptr }

/* SAFETY: Block comments not allowed */
unsafe { *ptr }

// Comment too far from unsafe block
let x = 5;
// SAFETY: This is wrong
let y = 6;
unsafe { *ptr }

// SAFETY comment inside doc comment doesn't count
/// ```
/// // SAFETY: doesn't work here
/// unsafe { ... }
/// ```
```

## Best Practices for SAFETY Comments

### 1. Explain the "Why", Not the "What"
```rust
// Bad: Just restates the operation
// SAFETY: Dereferencing a raw pointer
unsafe { *ptr }

// Good: Explains why it's safe
// SAFETY: `ptr` comes from a valid &T reference that outlives this operation
unsafe { *ptr }
```

### 2. Reference Invariants
```rust
// SAFETY: Tree-sitter guarantees the tree pointer is valid until
// ts_tree_delete is called. We ensure this by tying the lifetime
// to our Tree struct which calls ts_tree_delete in Drop.
unsafe { ts_tree_root_node(self.0.as_ptr()) }
```

### 3. Document Preconditions
```rust
// SAFETY: Caller must ensure:
// 1. `ptr` is properly aligned for type T
// 2. `ptr` points to a valid T that was allocated with the global allocator
// 3. No other references to this T exist
unsafe { Box::from_raw(ptr) }
```

### 4. Reference External Documentation
```rust
// SAFETY: According to the POSIX specification, close() is safe to call
// with any integer value. Invalid file descriptors return EBADF.
unsafe { libc::close(fd) }
```

### 5. Explain Thread Safety
```rust
// SAFETY: This static is only written once during initialization
// before any threads are spawned. All subsequent accesses are reads,
// making this safe under the Sync bound.
unsafe { &*GLOBAL_CONFIG.as_ptr() }
```

## FFI-Specific SAFETY Comments

### Memory Ownership
```rust
// SAFETY: The C library transfers ownership of this memory to us.
// We are responsible for freeing it with library_free().
unsafe { CString::from_raw(c_str_ptr) }
```

### Lifetime Guarantees
```rust
// SAFETY: The sqlite3 documentation guarantees this string pointer
// remains valid until the next call to sqlite3_column_* on the same
// statement. We ensure this by borrowing from the statement's lifetime.
unsafe { CStr::from_ptr(ptr).to_str().unwrap() }
```

### Callback Safety
```rust
// SAFETY: This callback will only be invoked from the C library's
// event loop thread. The context pointer is guaranteed to be valid
// because we unregister the callback before dropping the context.
unsafe { register_callback(callback, context_ptr) }
```

### Type Layout
```rust
// SAFETY: MyStruct is #[repr(C)] and has the exact same layout as
// the C struct my_struct_t. All fields are FFI-safe types.
unsafe { transmute::<my_struct_t, MyStruct>(c_struct) }
```

## Multi-Line SAFETY Comments

For complex safety justifications:

```rust
// SAFETY: Multiple conditions ensure this is safe:
// 1. The pointer comes from Vec::as_mut_ptr() which guarantees proper
//    alignment and validity for at least `capacity` elements
// 2. We're writing exactly `len` elements, which is <= capacity
// 3. We're not reading uninitialized memory, only writing
// 4. The Vec's destructor won't run on the copied elements because
//    we're using ptr::write which doesn't drop the old values
unsafe {
    ptr::copy_nonoverlapping(src, dst, len);
}
```

## Common Patterns

### NonNull Creation
```rust
// SAFETY: `reference` is a valid reference, so its address is non-null
unsafe { NonNull::new_unchecked(reference as *const T as *mut T) }
```

### Slice from Raw Parts
```rust
// SAFETY: `ptr` points to `len` consecutive valid T values, and
// both the pointer and length come from a Vec that we own
unsafe { slice::from_raw_parts(ptr, len) }
```

### FFI String Handling
```rust
// SAFETY: The C function is documented to return a valid UTF-8
// null-terminated string that we must not free
unsafe { CStr::from_ptr(c_str).to_str().expect("Invalid UTF-8") }
```