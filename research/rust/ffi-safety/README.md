# Tree-sitter FFI Safety Research

This directory contains comprehensive documentation on FFI patterns and memory safety for Tree-sitter integration in Rust.

## Documentation Structure

### 1. Tree-sitter Specific
- [`tree-sitter-using-parsers.md`](./tree-sitter-using-parsers.md) - Overview of Tree-sitter's multi-language parser architecture
- [`tree-sitter-rust-bindings.md`](./tree-sitter-rust-bindings.md) - Rust bindings API and safety features
- [`tree-sitter-ffi-patterns.md`](./tree-sitter-ffi-patterns.md) - FFI patterns from Tree-sitter source code
- [`tree-sitter-parser-safety.md`](./tree-sitter-parser-safety.md) - Parser struct safety considerations
- [`tree-sitter-tree-safety.md`](./tree-sitter-tree-safety.md) - Tree and Node memory management

### 2. General Rust FFI Safety
- [`rust-nomicon-ffi.md`](./rust-nomicon-ffi.md) - Comprehensive FFI safety from the Nomicon
- [`cxx-safe-ffi.md`](./cxx-safe-ffi.md) - CXX library's approach to 100% safe FFI
- [`send-sync-ffi.md`](./send-sync-ffi.md) - Thread safety across FFI boundaries
- [`libc-crate-usage.md`](./libc-crate-usage.md) - Safe patterns for libc usage
- [`safety-comment-guidelines.md`](./safety-comment-guidelines.md) - SAFETY comment best practices

## Key Takeaways

### Tree-sitter Safety Patterns
1. **Ownership Transfer**: Tree-sitter transfers ownership of allocated objects (Trees, Nodes) to Rust
2. **Lifetime Management**: Nodes borrow from Trees, enforced through Rust's lifetime system
3. **Thread Safety**: Trees are immutable and thread-safe once created
4. **Automatic Cleanup**: Drop implementations ensure proper resource cleanup

### FFI Best Practices
1. **Minimize Unsafe Scope**: Keep unsafe blocks small and focused
2. **Document Safety Invariants**: Every unsafe block needs a SAFETY comment
3. **Validate Everything**: Check nulls, bounds, and preconditions
4. **Use Safe Abstractions**: Wrap unsafe FFI in safe Rust APIs
5. **Leverage Type System**: Use NonNull, PhantomData, and lifetimes for safety

### Common Pitfalls to Avoid
1. Forgetting null checks on FFI returns
2. Incorrect lifetime management between Rust and C
3. Missing SAFETY documentation
4. Not handling FFI errors properly
5. Assuming C libraries are thread-safe by default

## Example: Safe Tree-sitter Wrapper

```rust
use tree_sitter::{Parser, Tree, Node};
use std::ptr::NonNull;

pub struct SafeParser {
    parser: Parser,
}

impl SafeParser {
    pub fn new() -> Self {
        Self {
            parser: Parser::new(),
        }
    }
    
    pub fn parse(&mut self, source: &str) -> Result<Tree, ParseError> {
        // SAFETY: Parser is valid and source is a valid UTF-8 string.
        // Tree-sitter will either return null (on failure) or a valid
        // tree pointer that we take ownership of.
        self.parser
            .parse(source, None)
            .ok_or(ParseError::Failed)
    }
}

impl Tree {
    pub fn walk(&self) -> TreeWalker<'_> {
        TreeWalker {
            // SAFETY: Tree guarantees root node validity for its lifetime
            current: self.root_node(),
            _tree: PhantomData,
        }
    }
}
```

## Resources
- [Tree-sitter Documentation](https://tree-sitter.github.io/tree-sitter/)
- [Rust FFI Nomicon](https://doc.rust-lang.org/nomicon/ffi.html)
- [CXX Safe FFI](https://cxx.rs/)
- [Rust API Guidelines](https://rust-lang.github.io/api-guidelines/)