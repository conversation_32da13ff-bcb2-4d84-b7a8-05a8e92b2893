# Tree-sitter Tree and Node Safety

## Tree Struct Memory Management

### Core Safety Features
- Private internal fields prevent direct manipulation
- Implements `Clone`, `Drop`, `Send`, and `Sync` traits
- Automatic memory management through RAII

### Raw Pointer Conversion

#### `into_raw()`
```rust
pub fn into_raw(self) -> *mut TSTree
```
- Consumes the Tree without running destructor
- Returns raw pointer for C interop
- Caller becomes responsible for cleanup

#### `from_raw()` - UNSAFE
```rust
pub unsafe fn from_raw(ptr: *mut TSTree) -> Self
```
- **Safety Requirement**: `ptr` must be non-null
- Reconstructs Tree from C pointer
- Takes ownership of the underlying resource

### Example Safe Wrapper Pattern
```rust
impl Tree {
    pub fn from_parser_output(raw: *mut TSTree) -> Option<Self> {
        NonNull::new(raw).map(|ptr| {
            // SAFETY: Parser guarantees non-null tree on success
            // We take ownership and will call ts_tree_delete on drop
            unsafe { Tree::from_raw(ptr.as_ptr()) }
        })
    }
}
```

## Node Safety Considerations

### Lifetime Guarantees
- Nodes borrow from their parent Tree
- Node lifetime tied to Tree lifetime
- Prevents use-after-free by design

```rust
impl Tree {
    pub fn root_node(&self) -> Node<'_> {
        // SAFETY: Tree-sitter guarantees root node validity
        // for the lifetime of the tree
        unsafe { Node::from_raw(ts_tree_root_node(self.0)) }
    }
}
```

### Thread Safety
```rust
// SAFETY: Tree-sitter trees are immutable once created.
// The C library guarantees thread-safe read access.
unsafe impl Send for Tree {}
unsafe impl Sync for Tree {}
```

## Ownership and Cloning

### Clone Implementation
- Deep clones the syntax tree
- Each clone has independent ownership
- Safe to use across threads

### Drop Implementation
```rust
impl Drop for Tree {
    fn drop(&mut self) {
        // SAFETY: We own this tree pointer and no other
        // references exist due to Rust's ownership rules
        unsafe { ts_tree_delete(self.0) }
    }
}
```

## Editing Trees

### Safe Editing Pattern
```rust
pub fn edit(&mut self, edit: &InputEdit) {
    // SAFETY: We have exclusive access (&mut self)
    // and edit contains valid byte/point positions
    unsafe { ts_tree_edit(self.0, edit.as_raw()) }
}
```

### Edit Safety Requirements
- Exclusive access through `&mut self`
- Edit positions must be valid
- Maintains tree consistency

## Common Safety Patterns

### Safe Tree Creation
```rust
pub fn parse(parser: &mut Parser, source: &str) -> Option<Tree> {
    let raw_tree = unsafe {
        ts_parser_parse_string(
            parser.as_raw(),
            std::ptr::null_mut(),
            source.as_ptr() as *const c_char,
            source.len() as u32,
        )
    };
    
    // SAFETY: ts_parser_parse_string returns null on failure,
    // non-null pointer to valid tree on success
    NonNull::new(raw_tree).map(|ptr| unsafe { Tree::from_raw(ptr.as_ptr()) })
}
```

### Working with Nodes
```rust
impl<'tree> Node<'tree> {
    pub fn child(&self, index: usize) -> Option<Node<'tree>> {
        if index < self.child_count() {
            // SAFETY: We verified index is in bounds.
            // Tree-sitter guarantees child node validity.
            Some(unsafe { self.child_unchecked(index) })
        } else {
            None
        }
    }
}
```

## Best Practices

1. **Always Check Null Pointers**: Use `NonNull::new()` for FFI returns
2. **Tie Node Lifetimes to Trees**: Use lifetime parameters
3. **Document Ownership Transfer**: Clear SAFETY comments
4. **Validate Indices**: Check bounds before unsafe access
5. **Leverage Type System**: Use Rust's ownership for safety

## Memory Safety Guarantees

### What Tree-sitter Guarantees
- Trees are immutable once created
- Thread-safe read access
- Valid node pointers for tree lifetime
- Proper cleanup on tree deletion

### What Rust Bindings Add
- Automatic memory management via Drop
- Lifetime tracking for nodes
- Safe abstraction over raw pointers
- Type-safe API with error handling