# Axum Routing - Detailed Guide

## Overview

Routing in Axum is the core mechanism for mapping HTTP requests to handler functions. It's designed for ergonomics and modularity, allowing you to build complex applications with clear, maintainable route structures.

## Basic Routing

### Creating a Router

```rust
use axum::{Router, routing::get};

let app = Router::new()
    .route("/", get(root_handler))
    .route("/hello", get(hello_handler));

async fn root_handler() -> &'static str {
    "Welcome to the API"
}

async fn hello_handler() -> &'static str {
    "Hello, World!"
}
```

### HTTP Method Routing

```rust
use axum::{
    Router,
    routing::{get, post, put, delete, patch},
};

let app = Router::new()
    .route("/users", get(list_users).post(create_user))
    .route("/users/:id", 
        get(get_user)
        .put(update_user)
        .delete(delete_user)
        .patch(patch_user)
    );
```

### All HTTP Methods

```rust
use axum::{
    Router,
    routing::{any, get, post, put, delete, head, options, trace, patch},
};

// Handle any HTTP method
let app = Router::new()
    .route("/any", any(handle_any_method));

// Or specific methods
let app = Router::new()
    .route("/api/resource",
        get(get_resource)
        .post(create_resource)
        .put(replace_resource)
        .delete(delete_resource)
        .head(head_resource)
        .options(options_resource)
        .trace(trace_resource)
        .patch(patch_resource)
    );
```

## Path Parameters

### Basic Path Parameters

```rust
use axum::{extract::Path, Router, routing::get};

async fn get_user(Path(user_id): Path<u32>) -> String {
    format!("User ID: {}", user_id)
}

async fn get_post(Path((user_id, post_id)): Path<(u32, u32)>) -> String {
    format!("User {} - Post {}", user_id, post_id)
}

let app = Router::new()
    .route("/users/:user_id", get(get_user))
    .route("/users/:user_id/posts/:post_id", get(get_post));
```

### Wildcard Routes

```rust
use axum::{extract::Path, Router, routing::get};

// Capture everything after /files/
async fn serve_file(Path(path): Path<String>) -> String {
    format!("Serving file: {}", path)
}

let app = Router::new()
    .route("/files/*path", get(serve_file));

// /files/documents/report.pdf → path = "documents/report.pdf"
```

## Nested Routing

### Basic Nesting

```rust
use axum::{Router, routing::get};

let user_routes = Router::new()
    .route("/", get(list_users))        // GET /users
    .route("/:id", get(get_user));      // GET /users/:id

let post_routes = Router::new()
    .route("/", get(list_posts))        // GET /posts
    .route("/:id", get(get_post));      // GET /posts/:id

let api_routes = Router::new()
    .nest("/users", user_routes)
    .nest("/posts", post_routes);

let app = Router::new()
    .nest("/api/v1", api_routes);       // All routes under /api/v1
```

### Nested with State

```rust
use axum::{Router, routing::get, extract::State};

#[derive(Clone)]
struct ApiState {
    db: DatabaseConnection,
}

let api_routes = Router::new()
    .route("/users", get(list_users))
    .with_state(ApiState { db: get_db() });

let app = Router::new()
    .nest("/api", api_routes);
```

### Service Nesting

```rust
use axum::Router;
use tower_http::services::ServeDir;

// Serve static files
let app = Router::new()
    .nest_service("/static", ServeDir::new("assets"));
```

## Fallback Routes

### Router Fallback

```rust
use axum::{Router, routing::get, http::StatusCode};

async fn fallback() -> (StatusCode, &'static str) {
    (StatusCode::NOT_FOUND, "Page not found")
}

let app = Router::new()
    .route("/", get(index))
    .route("/users", get(users))
    .fallback(fallback); // Catches all unmatched routes
```

### Nested Fallback

```rust
let api_routes = Router::new()
    .route("/users", get(users))
    .fallback(api_fallback); // Only for /api/* routes

let app = Router::new()
    .nest("/api", api_routes)
    .fallback(global_fallback); // For all other routes
```

## Merging Routers

```rust
use axum::{Router, routing::get};

let user_routes = Router::new()
    .route("/profile", get(profile))
    .route("/settings", get(settings));

let admin_routes = Router::new()
    .route("/dashboard", get(dashboard))
    .route("/users", get(admin_users));

// Merge routers - routes must not conflict
let app = Router::new()
    .merge(user_routes)
    .merge(admin_routes);
```

## Route Layers

### Applying Middleware to Routes

```rust
use axum::{
    Router,
    routing::get,
    middleware::{self, Next},
    response::Response,
    http::Request,
};
use tower::ServiceBuilder;
use tower_http::trace::TraceLayer;

async fn require_auth(req: Request, next: Next) -> Response {
    // Auth logic here
    next.run(req).await
}

let public_routes = Router::new()
    .route("/", get(home))
    .route("/about", get(about));

let protected_routes = Router::new()
    .route("/dashboard", get(dashboard))
    .route("/profile", get(profile))
    .layer(middleware::from_fn(require_auth)); // Apply to all routes in this router

let app = Router::new()
    .merge(public_routes)
    .merge(protected_routes)
    .layer(TraceLayer::new_for_http()); // Apply to entire app
```

### Route-Specific Layers

```rust
use axum::{Router, routing::get};
use tower::timeout::TimeoutLayer;
use std::time::Duration;

let app = Router::new()
    .route("/", get(fast_handler))
    .route("/slow", 
        get(slow_handler)
            .layer(TimeoutLayer::new(Duration::from_secs(30)))
    );
```

## Organizing Large Applications

### Module-Based Organization

```rust
// main.rs
mod routes;

use axum::Router;

#[tokio::main]
async fn main() {
    let app = routes::create_app();
    // Run server...
}

// routes/mod.rs
mod users;
mod posts;
mod admin;

use axum::Router;

pub fn create_app() -> Router {
    Router::new()
        .nest("/api", api_routes())
        .nest("/admin", admin::routes())
        .fallback(fallback)
}

fn api_routes() -> Router {
    Router::new()
        .nest("/users", users::routes())
        .nest("/posts", posts::routes())
}

// routes/users.rs
use axum::{Router, routing::get};

pub fn routes() -> Router {
    Router::new()
        .route("/", get(list_users))
        .route("/:id", get(get_user))
        .route("/:id/posts", get(user_posts))
}
```

### Feature-Based Organization

```rust
// features/auth/mod.rs
pub fn routes() -> Router {
    Router::new()
        .route("/login", post(login))
        .route("/logout", post(logout))
        .route("/register", post(register))
}

// features/blog/mod.rs
pub fn routes() -> Router {
    Router::new()
        .route("/posts", get(list_posts).post(create_post))
        .route("/posts/:id", get(get_post).put(update_post))
}

// main.rs
let app = Router::new()
    .nest("/auth", auth::routes())
    .nest("/blog", blog::routes());
```

## Advanced Routing Patterns

### Versioned APIs

```rust
let v1_routes = Router::new()
    .route("/users", get(v1::list_users));

let v2_routes = Router::new()
    .route("/users", get(v2::list_users));

let app = Router::new()
    .nest("/api/v1", v1_routes)
    .nest("/api/v2", v2_routes);
```

### Dynamic Route Registration

```rust
use std::collections::HashMap;

fn create_dynamic_routes(routes: HashMap<String, BoxedHandler>) -> Router {
    let mut router = Router::new();
    
    for (path, handler) in routes {
        router = router.route(&path, get(handler));
    }
    
    router
}
```

### Conditional Routes

```rust
use axum::Router;

fn create_app(enable_debug: bool) -> Router {
    let mut app = Router::new()
        .route("/", get(index))
        .route("/api", get(api));

    if enable_debug {
        app = app.route("/debug", get(debug_info));
    }

    app
}
```

## Route Testing

```rust
#[cfg(test)]
mod tests {
    use axum::{
        body::Body,
        http::{Request, StatusCode},
        Router,
    };
    use tower::ServiceExt;

    #[tokio::test]
    async fn test_route() {
        let app = create_app();

        let response = app
            .oneshot(
                Request::builder()
                    .uri("/users")
                    .body(Body::empty())
                    .unwrap()
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);
    }
}
```

## Best Practices

### 1. Keep Routes Organized

```rust
// Good: Organized by feature
let app = Router::new()
    .nest("/auth", auth_routes())
    .nest("/users", user_routes())
    .nest("/posts", post_routes());

// Avoid: Everything in one place
let app = Router::new()
    .route("/login", post(login))
    .route("/users", get(users))
    .route("/posts", get(posts))
    // ... many more routes
```

### 2. Use Type-Safe Path Parameters

```rust
// Good: Strongly typed
async fn get_user(Path(user_id): Path<Uuid>) -> Result<Json<User>, ApiError>

// Less ideal: String parameters
async fn get_user(Path(user_id): Path<String>) -> Result<Json<User>, ApiError>
```

### 3. Group Related Middleware

```rust
let protected_routes = Router::new()
    .route("/profile", get(profile))
    .route("/settings", get(settings))
    .layer(
        ServiceBuilder::new()
            .layer(middleware::from_fn(require_auth))
            .layer(middleware::from_fn(rate_limit))
    );
```

### 4. Document Route Structure

```rust
/// Creates the main application router
/// 
/// Route structure:
/// - `/` - Home page
/// - `/api/v1/users` - User management
/// - `/api/v1/posts` - Blog posts
/// - `/admin/*` - Admin panel (requires auth)
pub fn create_app() -> Router {
    // Implementation
}
```

### 5. Use Consistent Naming

```rust
// Good: Consistent REST conventions
.route("/users", get(list_users).post(create_user))
.route("/users/:id", get(get_user).put(update_user).delete(delete_user))

// Avoid: Inconsistent naming
.route("/users", get(fetch_all_users))
.route("/user/new", post(make_user))
.route("/remove-user/:id", delete(remove_user))
```

## Summary

Axum's routing system provides:
- Clear, type-safe route definitions
- Flexible composition through nesting and merging
- Powerful middleware integration
- Support for complex application structures
- Excellent performance with compile-time optimizations

The key to effective routing in Axum is to leverage Rust's type system and Axum's compositional design to create maintainable, scalable web applications.