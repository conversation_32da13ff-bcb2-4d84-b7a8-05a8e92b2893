# Clippy Agent PRP Generation Instructions

## Step 1: Generate the PRP

Run the following command to generate a comprehensive PRP from the INITIAL.md:

```bash
/generate-prp PRPs/initial-files/agent-clippy-warnings-INITIAL.md --persona-refactorer --persona-qa --seq --c7 --ultrathink
```

### Parameter Explanation:
- **`--persona-refactorer`**: Activates code quality and technical debt reduction focus
- **`--persona-qa`**: Adds quality assurance perspective for comprehensive validation
- **`--seq`**: Enables sequential thinking for systematic warning resolution
- **`--c7`**: Integrates Context7 MCP for official Rust/clippy documentation lookup
- **`--ultrathink`**: Deep reasoning to identify false positives and optimal fixes

## Step 2: Review the Generated PRP

The PRP will be created at: `PRPs/active/agent-clippy-warnings.md`

Review it to ensure it includes:
- Systematic approach to warning categories
- Validation loops after each batch
- Clear suppression criteria
- Evidence collection framework

## Step 3: Execute the PRP

Run the generated PRP with:

```bash
/execute-prp PRPs/active/agent-clippy-warnings.md --persona-refactorer --seq
```

### Execution Parameters:
- **`--persona-refactorer`**: Maintains code quality focus during execution
- **`--seq`**: Ensures systematic, step-by-step approach

## Step 4: Monitor Progress

The agent should:
1. Create initial clippy output capture
2. Categorize warnings by type
3. Fix warnings in priority order
4. Validate after each batch
5. Document all changes

## Alternative Approaches

### For Quick Assessment:
```bash
/analyze --rust --clippy --seq
```

### For Specific Warning Types:
```bash
/improve --rust --quality --focus="uninlined_format_args"
```

### For Maximum Context:
```bash
/generate-prp PRPs/initial-files/agent-clippy-warnings-INITIAL.md --persona-refactorer --persona-backend --persona-qa --seq --c7 --ultrathink
```

## Expected Outcomes

1. **Systematic Progress**: Agent works through categories methodically
2. **Validation Loops**: Tests run after each batch of fixes
3. **Documentation**: Comprehensive report of all changes
4. **Evidence Collection**: All validation results saved
5. **Future Prevention**: Guidelines to avoid new warnings

## Notes

- The INITIAL.md includes the context from our manual investigation
- The agent has access to the existing clippy warnings prompt
- All changes should maintain 100% test pass rate
- Focus on meaningful improvements, not just warning suppression