# /sync-findings

Synchronizes and aggregates findings across all Analysis-Engine production readiness agents.

## Usage
```
/sync-findings [--format <format>] [--output <path>]
```

## Options
- `--format <format>`: Output format (`summary`, `detailed`, `json`) - default: `summary`
- `--output <path>`: Save output to file (optional)

## Description
This command:
1. Collects findings from all agent tracker files
2. Updates the shared knowledge bank
3. Identifies conflicts or duplicates
4. Generates an aggregated report
5. Updates the main orchestration tracker

## Sync Process
1. Reads all agent trackers from `.claudedocs/orchestration/agents/`
2. Extracts findings, evidence, and recommendations
3. Merges into `.claude/memory/analysis-engine-prod-knowledge.json`
4. Resolves any conflicts between agent findings
5. Updates progress metrics in main tracker

## Example Output (Summary Format)
```
Analysis-Engine Findings Sync
============================
Timestamp: 2025-01-16 11:45

Phase 1 Progress: 40%
- Build errors: 3/10 fixed
- Format strings: 0/56 fixed
- Code patterns: 0/10 fixed
- Structural issues: 0/5 fixed

Critical Findings:
🔴 Security:
- 2 vulnerabilities (idna, protobuf)
- 22 undocumented unsafe blocks

🟡 Code Quality:
- 124 clippy warnings total
- 7 unmaintained dependencies

🟢 Architecture:
- Clean microservices design
- Good test coverage (78+ files)

Conflicts Detected: 0
New Evidence Files: 3

Next Sync: After Agent 05 validation
```

## Knowledge Bank Structure
```json
{
  "last_sync": "2025-01-16T11:45:00Z",
  "phases": {
    "phase1": {
      "progress": 0.4,
      "agents": {...}
    }
  },
  "findings": {
    "critical": [...],
    "warnings": [...],
    "info": [...]
  },
  "evidence": {
    "files": [...],
    "validations": [...]
  }
}
```

## Related Commands
- `/recover-analysis-engine` - Full context recovery
- `/agent-status` - Individual agent status