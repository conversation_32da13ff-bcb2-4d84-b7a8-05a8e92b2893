# CCL Project Structure

## Root Directory Organization

```
ccl/
├── services/              # Microservices (strict language boundaries)
├── contracts/             # Service integration contracts and schemas
├── infrastructure/        # Infrastructure as code and monitoring
├── docs/                  # Service-specific documentation
├── PRPs/                  # Project Requirements and Patterns
├── research/              # Context Engineering research documentation
├── scripts/               # Development and deployment scripts
├── docker/                # Docker Compose configurations
├── examples/              # Code examples and templates
├── tests/                 # Cross-service integration tests
├── tools/                 # Development tools and utilities
├── validation-results/    # Evidence-based validation results
└── .kiro/                 # Kiro steering rules and configuration
```

## Service Structure (Microservices)

### Analysis Engine (Rust) - **PRODUCTION DEPLOYED**
```
services/analysis-engine/
├── src/
│   ├── main.rs           # Axum application entry point
│   ├── lib.rs            # Library root with re-exports
│   ├── api/              # REST API handlers and middleware
│   │   ├── handlers.rs   # Route handlers
│   │   ├── auth_extractor.rs # JWT authentication
│   │   ├── rate_limit_extractor.rs # Rate limiting
│   │   └── middleware.rs # Security and metrics middleware
│   ├── parsers/          # Tree-sitter language parsers (25+ languages)
│   ├── analysis/         # Code analysis logic and AST processing
│   ├── models/           # Serde data models and schemas
│   ├── config/           # Configuration management
│   ├── metrics/          # Prometheus metrics
│   ├── errors/           # Error types and handling
│   └── utils/            # Utility functions
├── tests/                # Unit and integration tests
├── benches/              # Performance benchmarks
├── Cargo.toml            # Dependencies (axum, tokio, tree-sitter)
├── Dockerfile            # Multi-stage container build
└── README.md             # Service documentation
```

### Query Intelligence (Python) - **PRODUCTION READY**
```
services/query-intelligence/
├── src/query_intelligence/
│   ├── __init__.py
│   ├── main.py           # FastAPI application with lifespan management
│   ├── api/              # API routes and handlers
│   │   ├── query.py      # Query processing endpoints
│   │   ├── websocket.py  # WebSocket real-time communication
│   │   └── admin.py      # Admin dashboard API
│   ├── services/         # Business logic services
│   ├── models/           # Pydantic models and schemas
│   ├── clients/          # External service clients (Redis, Analysis Engine)
│   ├── middleware/       # Security and rate limiting middleware
│   ├── config/           # Settings and configuration
│   └── utils/            # Utility functions and circuit breakers
├── tests/                # Pytest test suite (85% coverage)
├── config/               # Configuration files
├── pyproject.toml        # Poetry dependencies (FastAPI, google-genai)
├── Dockerfile            # Container configuration
└── README.md             # Service documentation
```

### Pattern Mining (Python) - **80% COMPLETE**
```
services/pattern-mining/
├── src/pattern_mining/
│   ├── __init__.py
│   ├── main.py           # FastAPI application with async server
│   ├── api/              # API endpoints
│   │   └── main.py       # FastAPI app configuration
│   ├── ml/               # Machine learning models and algorithms
│   ├── features/         # Feature extraction from AST
│   ├── patterns/         # Pattern detection logic
│   ├── services/         # Core business services
│   ├── clients/          # BigQuery and external clients
│   └── config/           # Configuration management
├── tests/                # Test suite
├── notebooks/            # Jupyter notebooks for ML experimentation
├── pyproject.toml        # Dependencies (scikit-learn, google-genai)
└── README.md             # Documentation
```

### Marketplace (Go) - **75% COMPLETE**
```
services/marketplace/
├── cmd/
│   └── server/           # Application entry point (not yet implemented)
├── internal/
│   ├── api/              # HTTP handlers
│   ├── service/          # Business logic
│   ├── repository/       # Data access layer
│   └── models/           # Data models
├── pkg/                  # Public packages
├── tests/                # Test files
├── go.mod                # Go modules (gin, lib/pq)
├── Dockerfile.dev        # Development container
└── README.md             # Documentation
```

### Web (TypeScript) - **DESIGN PHASE**
```
services/web/
├── src/
│   ├── components/       # React components
│   ├── hooks/            # Custom hooks
│   ├── pages/            # Page components
│   ├── api/              # API client
│   └── utils/            # Utility functions
├── tests/                # Frontend tests
├── public/               # Static assets
├── package.json          # Dependencies (React, Next.js)
└── README.md             # Documentation
```

### Collaboration (TypeScript) - **PLANNED**
```
services/collaboration/
├── src/
│   ├── server.ts         # WebSocket server
│   ├── handlers/         # Event handlers
│   ├── models/           # Data models
│   └── utils/            # Utility functions
├── tests/                # Test suite
├── package.json          # Dependencies (ws, socket.io)
└── README.md             # Documentation
```

## Key Directories

### Contracts (`contracts/`) - **SERVICE INTEGRATION CRITICAL**
```
contracts/
├── schemas/              # JSON Schema definitions for service communication
│   ├── ast-output-v1.json        # Analysis Engine output format
│   ├── query-context-v1.json     # Query Intelligence input format
│   ├── pattern-input-v1.json     # Pattern Mining input format
│   ├── pattern-output-v1.json    # Pattern Mining output format
│   ├── marketplace-pattern-v1.json # Marketplace pattern format
│   ├── service-events-v1.json    # Event bus message format
│   └── error-response-v1.json    # Unified error response format
├── examples/             # Example payloads and integration flows
├── validation/           # Performance contracts and compatibility matrix
└── tests/                # Contract validation tests
```

### PRPs (`PRPs/`) - **COMPREHENSIVE SPECIFICATIONS**
```
PRPs/
├── architecture-patterns.md      # System architecture and patterns
├── implementation-guide.md       # Complete implementation guide
├── feature-specifications.md     # Feature details and requirements
├── api/                          # API design documents
├── security/                     # Security requirements and compliance
│   ├── zero-trust-architecture.md
│   ├── ML_AI_SECURITY_SUMMARY.md
│   └── compliance.md
├── services/                     # Service-specific PRPs
├── reviews/                      # Review and enhancement documents
└── templates/                    # PRP templates
```

### Research (`research/`) - **CONTEXT ENGINEERING**
```
research/
├── rust/                 # Rust ecosystem research (security, performance)
├── python/               # Python frameworks (FastAPI, ML libraries)
├── google-cloud/         # GCP services documentation
├── security/             # Security best practices and vulnerability management
├── performance/          # Performance optimization patterns
└── integration/          # Integration patterns and microservices
```

### Scripts (`scripts/`) - **DEVELOPMENT & DEPLOYMENT**
```
scripts/
├── dev/                  # Development scripts
│   ├── setup.sh          # Development environment setup
│   ├── start.sh          # Start all services
│   └── health-check.sh   # Service health verification
├── security/             # Security automation
│   ├── README.md         # Automated secret rotation system
│   └── rotate-secrets.sh # Zero-downtime secret rotation
├── standard/             # Standard operations
│   └── setup-alerting.sh # Production alerting setup
└── deployment/           # Deployment automation
```

### Docker (`docker/`) - **MODULAR COMPOSE SETUP**
```
docker/
├── docker-compose.base.yml       # Core infrastructure (postgres, redis)
├── docker-compose.dev.yml        # Development services
├── docker-compose.monitoring.yml # Observability stack (prometheus, grafana)
├── config/                       # Service configurations
└── init-scripts/                 # Database initialization scripts
```

### Infrastructure (`infrastructure/`) - **INFRASTRUCTURE AS CODE**
```
infrastructure/
├── monitoring/           # Observability configurations
│   ├── dashboards/       # Grafana dashboards
│   ├── alerts/           # Alerting rules
│   └── exporters/        # Prometheus exporters
├── terraform/            # Infrastructure as code
│   ├── environments/     # Environment-specific configs
│   └── modules/          # Reusable modules
└── kubernetes/           # K8s manifests and deployments
```

### Validation Results (`validation-results/`) - **EVIDENCE-BASED VALIDATION**
```
validation-results/
├── analysis-engine-prod-readiness/  # Production readiness evidence
│   ├── security-audit.md            # Security vulnerability findings
│   ├── performance-benchmarks.md    # Performance test results
│   └── validation-scripts/          # Automated validation scripts
└── service-integration-tests/       # Cross-service integration results
```

## Naming Conventions

### Files and Directories
- **Services**: kebab-case (`analysis-engine`, `query-intelligence`)
- **Rust files**: snake_case (`ast_parser.rs`, `pattern_detector.rs`)
- **Python files**: snake_case (`query_processor.py`, `gemini_client.py`)
- **Go files**: snake_case (`marketplace_handler.go`)
- **TypeScript files**: camelCase (`patternCard.tsx`, `apiClient.ts`)
- **Configuration**: kebab-case (`docker-compose.yml`, `cloudbuild.yaml`)
- **Documentation**: kebab-case (`implementation-guide.md`, `security-audit.md`)

### Code Conventions
- **Constants**: UPPER_SNAKE_CASE (`MAX_FILE_SIZE`, `DEFAULT_TIMEOUT`)
- **Environment Variables**: UPPER_SNAKE_CASE (`DATABASE_URL`, `REDIS_URL`)
- **API Endpoints**: kebab-case (`/api/v1/analysis-results`, `/patterns/detect`)
- **Database Tables**: snake_case (`analysis_results`, `pattern_detections`)
- **JSON Fields**: snake_case (`user_id`, `created_at`, `confidence_score`)

### Git Conventions
- **Branches**: `feature/analysis-api-enhancement`, `fix/security-vulnerability`
- **Commits**: `feat: add repository analysis caching`, `fix: resolve memory leak`
- **Tags**: `v1.2.3`, `analysis-engine-v2.1.0`

## Development Workflow

### Local Development Setup
```bash
# Clone and setup
git clone https://github.com/your-org/ccl.git
cd ccl

# Install dependencies
make install-deps

# Configure environment
cp .env.example .env
./scripts/setup-local-secrets.sh

# Start development environment
make dev-up

# Verify services
make health
```

### Service Development Pattern
```bash
# Work on specific service
cd services/analysis-engine

# Install dependencies
cargo build

# Run tests
cargo test

# Start service locally
cargo run

# Format and lint
cargo fmt
cargo clippy -- -D warnings
```

### Integration Testing
```bash
# Run cross-service tests
make test-integration

# Test specific service integration
make test-service-integration SERVICE=query-intelligence

# Validate contracts
make validate-contracts
```

## Production Deployment

### Current Status
- **Analysis Engine**: ✅ Deployed to Cloud Run (security issues block production use)
- **Query Intelligence**: ✅ Production ready with 85% test coverage
- **Pattern Mining**: 🔄 80% complete, Google AI integration active
- **Marketplace**: 🔄 75% complete, API design finalized
- **Web/Collaboration**: 📋 Design phase

### Deployment Commands
```bash
# Deploy specific service
make deploy SERVICE=analysis-engine ENV=production

# Deploy all services
make deploy-all ENV=production

# Validate deployment
make validate-deployment ENV=production
```

### Monitoring and Observability
```bash
# Check service health
make health-check ENV=production

# View logs
make logs SERVICE=analysis-engine ENV=production

# Access monitoring dashboards
make grafana    # Open Grafana dashboard
make prometheus # Open Prometheus metrics
```

## Critical Implementation Notes

### Security Requirements
- **Zero unsafe blocks** without SAFETY comments (Rust)
- **Input validation** on all API endpoints
- **Rate limiting** and authentication middleware
- **Secret management** via Google Secret Manager
- **Audit logging** for all security-relevant operations

### Performance Standards
- **Analysis Engine**: <30s for 1M LOC, 99.9% uptime
- **Query Intelligence**: <100ms response (p95), 99.95% uptime
- **Pattern Mining**: <5min processing, 99% uptime
- **Marketplace**: <50ms response (p95), 99.99% uptime

### Service Communication
- **Contracts First**: All service communication via defined contracts
- **Event-Driven**: Use Pub/Sub for async communication
- **Circuit Breakers**: Implement failure handling patterns
- **Graceful Degradation**: Handle service failures appropriately