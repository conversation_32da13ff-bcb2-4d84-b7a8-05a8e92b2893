# CCL Technology Stack

## Architecture
**Microservices architecture** with strict service boundaries and technology constraints.

## Technology Constraints (STRICTLY ENFORCED)
- **Rust**: analysis-engine only (AST parsing, code analysis)
- **Python**: query-intelligence, pattern-mining only (AI/ML operations)
- **Go**: marketplace only (high-performance commerce APIs)
- **TypeScript**: web, sdk, collaboration only (frontend and client libraries)

## Core Technologies

### Languages & Frameworks
- **Rust 1.75+**: Axum web framework, Tree-sitter parsing, Tokio async runtime
- **Python 3.11+**: FastAPI, Google GenAI SDK, LangChain, Poetry dependency management
- **Go 1.21+**: Gin web framework, standard library focus
- **TypeScript/Node.js 20+**: React, Next.js, WebSocket support

### Cloud Platform (Google Cloud Exclusive)
- **Runtime**: Cloud Run (serverless), Kubernetes (GKE Autopilot)
- **Databases**: Spanner (OLTP), BigQuery (OLAP), Firestore (real-time), Redis (caching)
- **AI/ML**: Google Gemini 2.5 Flash API (migrated from deprecated Vertex AI July 2025)
- **Storage**: Cloud Storage (artifacts), Artifact Registry (containers)
- **Messaging**: Pub/Sub (events), Cloud Tasks (async processing)
- **Security**: VPC Service Controls, Cloud KMS, Secret Manager

### Development Tools
- **Build System**: Make (primary), Docker Compose (local development)
- **CI/CD**: GitHub Actions, Cloud Build
- **Monitoring**: Prometheus, Grafana, OpenTelemetry, Jaeger
- **Testing**: Language-specific frameworks with >90% coverage requirement

## Common Commands

### Development Environment
```bash
# Setup
make setup                    # Set up development environment
make dev-up                   # Start all services
make dev-down                 # Stop all services

# Development
make logs                     # View service logs
make health                   # Check service health
make shell SVC=service-name   # Shell into service container
```

### Building & Testing
```bash
# Build
make build-all               # Build all services
make build-svc SVC=name      # Build specific service

# Testing
make test                    # Run all tests
make test-rust               # Test Rust services
make test-python             # Test Python services
make test-go                 # Test Go services
make test-typescript         # Test TypeScript services

# Quality
make lint                    # Run all linters
make format                  # Format all code
make security                # Run security scans
```

### Service-Specific Commands
```bash
# Analysis Engine (Rust)
cd services/analysis-engine
cargo test                   # Run tests
cargo clippy -- -D warnings  # Lint
cargo fmt                    # Format
cargo audit                  # Security audit

# Query Intelligence (Python)
cd services/query-intelligence
poetry install               # Install dependencies
poetry run pytest           # Run tests
poetry run ruff check .      # Lint
poetry run black .           # Format

# Marketplace (Go)
cd services/marketplace
go test ./...                # Run tests
golangci-lint run           # Lint
go fmt ./...                # Format
```

### Validation Commands (Critical)
```bash
# Always run before committing
make lint-ccl                # CCL-specific linting
make test-ccl                # Full test suite
make security-scan-ccl       # Security validation
make performance-test-ccl    # Performance benchmarks

# Security-specific validation (CRITICAL for Analysis Engine)
cargo audit                  # Check for security vulnerabilities
cargo clippy -- -D warnings # Lint with warnings as errors
find src/ -name "*.rs" -exec grep -l "unsafe" {} \; | xargs grep -L "SAFETY:" # Find undocumented unsafe blocks
```

## Key Dependencies

### Analysis Engine (Rust)
- **Web**: axum 0.8.4, tower, hyper
- **Parsing**: tree-sitter 0.22.6 + 25+ language parsers
- **Async**: tokio 1.46.1, futures
- **Serialization**: serde, serde_json
- **Error Handling**: anyhow, thiserror
- **GCP**: google-cloud-* crates
- **Auth**: jsonwebtoken
- **Caching**: redis

### Query Intelligence (Python)
- **Web**: fastapi, uvicorn
- **AI**: google-genai 0.5.0, langchain, sentence-transformers
- **GCP**: google-cloud-secret-manager, google-cloud-pubsub
- **Data**: pydantic, numpy
- **Async**: httpx, aiofiles
- **Monitoring**: prometheus-client, structlog

### Pattern Mining (Python)
- **ML**: scikit-learn, numpy, pandas
- **AI**: google-genai, langchain
- **Data Processing**: asyncio, concurrent.futures
- **Caching**: redis
- **Analytics**: google-cloud-bigquery

### Marketplace (Go)
- **Web**: gin-gonic/gin
- **Database**: lib/pq (PostgreSQL)
- **Observability**: opentelemetry
- **Config**: godotenv

## Performance Requirements
- **Analysis Engine**: <30s for 1M LOC, 99.9% uptime
- **Query Intelligence**: <100ms response (p95), 99.95% uptime
- **Pattern Mining**: <5min processing, 99% uptime
- **Marketplace**: <50ms response (p95), 99.99% uptime

## Security Standards
- **Authentication**: JWT tokens, OAuth2, Firebase Auth
- **Authorization**: Service-to-service authentication via GCP IAM
- **Encryption**: TLS 1.3 in transit, AES-256 at rest
- **Secrets**: Google Secret Manager (never hardcode)
- **Vulnerability Management**: Regular dependency updates, automated scanning