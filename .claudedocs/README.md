# Claude Documentation

## Active Documents

### Production Readiness
- **[PRODUCTION-READINESS-GUIDE.md](PRODUCTION-READINESS-GUIDE.md)** - Complete step-by-step guide for production deployment
- **[AGENT-REFERENCE.md](AGENT-REFERENCE.md)** - Agent deployment reference and capabilities

### Current Status
- **Overall Completion**: 69% (independently verified)
- **Critical Issue**: Core performance claim "1M LOC in <5 minutes" unverified
- **Next Step**: Fix 41 compilation errors in comprehensive_test_suite.rs

## Quick Start

### Check Current Status
```bash
cd services/analysis-engine
cargo check --all-targets
cargo test
```

### Deploy Next Agent
```bash
# Critical: Performance Validation
cat services/analysis-engine/agent-prompts/02-performance-validation-specialist.md

# Follow deployment instructions in prompt
```

### Run Validation
```bash
cd services/analysis-engine
./scripts/performance-validation/run-e2e-validation.sh
cargo run --bin performance_analyzer
```

## Archive

### Previous Orchestration (v1)
- **Location**: `archive/orchestration-v1/`
- **Status**: Archived for reference
- **Contains**: Previous agent tracking, phase documentation, detailed orchestration logs

## File Structure

```
.claudedocs/
├── README.md                           # This file
├── PRODUCTION-READINESS-GUIDE.md       # Main production guide
├── AGENT-REFERENCE.md                  # Agent deployment reference
└── archive/
    └── orchestration-v1/               # Archived orchestration docs
```

## Key Commands

### Build Validation
```bash
cargo clean && cargo check --all-targets && cargo build --release
```

### Performance Testing
```bash
./scripts/performance-validation/collect-test-repositories.sh
./scripts/performance-validation/run-e2e-validation.sh
```

### Evidence Collection
```bash
cargo run --bin performance_analyzer
cargo run --bin evidence_collector
```

---

**Last Updated**: 2025-01-17  
**Status**: Active Documentation  
**Next Review**: After major step completion
EOF < /dev/null